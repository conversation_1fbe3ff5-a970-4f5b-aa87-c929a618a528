﻿ ProductNormPriceId = entity.ProductNormPriceId,
 ProductId = entity.ProductId,
 CourseId = entity.CourseId,


 ProductNormPriceId = model.ProductNormPriceId,
 ProductId = model.ProductId,
 CourseId = model.CourseId,


 temp.ProductNormPriceId = model.ProductNormPriceId,
 temp.ProductId = model.ProductId,
 temp.CourseId = model.CourseId,

 ProductCourseId = item.ProductCourseId,
 ProductNormPriceId = item.ProductNormPriceId,
 ProductId = item.ProductId,
 CourseId = item.CourseId,

public class ProductCourseInputModel
{
 [Display(Name = "Id")] 
    public long ProductCourseId {get; set; }
    
 [Display(Name = "产品规格价格Id")] 
    public long ProductNormPriceId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "学科Id")] 
    public int CourseId {get; set; }
    
 }
 
 public class ProductCourseViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ProductCourseId {get; set; }
    
    /// <summary>
    /// 产品规格价格Id
    /// </summary>
    public long ProductNormPriceId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 学科Id
    /// </summary>
    public int CourseId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductNormPriceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductNormPriceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品规格价格Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科Id" } })                    
                </div>
           </div>
  




 { field: 'ProductNormPriceId', title: '产品规格价格Id', sortable: true },
                 
 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'CourseId', title: '学科Id', sortable: true },
                 
o.ProductNormPriceId,                 
o.ProductId,                 
o.CourseId,                 
        
        $('#ProductNormPriceId').val(d.data.rows.ProductNormPriceId);          
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#CourseId').val(d.data.rows.CourseId);          

 $('#th_ProductNormPriceId').html(' 产品规格价格Id');               
 $('#th_ProductId').html(' 产品Id');               
 $('#th_CourseId').html(' 学科Id');               
 
 $('#tr_ProductNormPriceId').hide();               
 $('#tr_ProductId').hide();               
 $('#tr_CourseId').hide();               

 , "ProductNormPriceId" : productNormPriceId
 , "ProductId" : productId
 , "CourseId" : courseId

 var productNormPriceId = $('#o_ProductNormPriceId').val();
 var productId = $('#o_ProductId').val();
 var courseId = $('#o_CourseId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品规格价格Id' : '产品名称', d.data.rows.ProductNormPriceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科Id' : '产品名称', d.data.rows.CourseId);



