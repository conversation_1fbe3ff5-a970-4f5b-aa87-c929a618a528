﻿ MallId = entity.MallId,
 UnitId = entity.UnitId,
 ShelvesNum = entity.ShelvesNum,
 OffShelvesNum = entity.OffShelvesNum,
 StopSaleNum = entity.StopSaleNum,
 SaleNum = entity.SaleNum,
 NoConfirmOrderNum = entity.NoConfirmOrderNum,
 CancelOrderNum = entity.CancelOrderNum,
 CancelContractNum = entity.CancelContractNum,


 MallId = model.MallId,
 UnitId = model.UnitId,
 ShelvesNum = model.ShelvesNum,
 OffShelvesNum = model.OffShelvesNum,
 StopSaleNum = model.StopSaleNum,
 SaleNum = model.SaleNum,
 NoConfirmOrderNum = model.NoConfirmOrderNum,
 CancelOrderNum = model.CancelOrderNum,
 CancelContractNum = model.CancelContractNum,


 temp.MallId = model.MallId,
 temp.UnitId = model.UnitId,
 temp.ShelvesNum = model.ShelvesNum,
 temp.OffShelvesNum = model.OffShelvesNum,
 temp.StopSaleNum = model.StopSaleNum,
 temp.SaleNum = model.SaleNum,
 temp.NoConfirmOrderNum = model.NoConfirmOrderNum,
 temp.CancelOrderNum = model.CancelOrderNum,
 temp.CancelContractNum = model.CancelContractNum,

 SupplierStatisticId = item.SupplierStatisticId,
 MallId = item.MallId,
 UnitId = item.UnitId,
 ShelvesNum = item.ShelvesNum,
 OffShelvesNum = item.OffShelvesNum,
 StopSaleNum = item.StopSaleNum,
 SaleNum = item.SaleNum,
 NoConfirmOrderNum = item.NoConfirmOrderNum,
 CancelOrderNum = item.CancelOrderNum,
 CancelContractNum = item.CancelContractNum,

public class SupplierStatisticInputModel
{
 [Display(Name = "Id")] 
    public int SupplierStatisticId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "供应商Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "上架数量")] 
    public int ShelvesNum {get; set; }
    
 [Display(Name = "下架数量")] 
    public int OffShelvesNum {get; set; }
    
 [Display(Name = "停售数量")] 
    public int StopSaleNum {get; set; }
    
 [Display(Name = "已销售数量")] 
    public int SaleNum {get; set; }
    
 [Display(Name = "未确认订单")] 
    public int NoConfirmOrderNum {get; set; }
    
 [Display(Name = "已取消订单")] 
    public int CancelOrderNum {get; set; }
    
 [Display(Name = "已取消合同")] 
    public int CancelContractNum {get; set; }
    
 }
 
 public class SupplierStatisticViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int SupplierStatisticId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 供应商Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 上架数量
    /// </summary>
    public int ShelvesNum {get; set; }
    
    /// <summary>
    /// 下架数量
    /// </summary>
    public int OffShelvesNum {get; set; }
    
    /// <summary>
    /// 停售数量
    /// </summary>
    public int StopSaleNum {get; set; }
    
    /// <summary>
    /// 已销售数量
    /// </summary>
    public int SaleNum {get; set; }
    
    /// <summary>
    /// 未确认订单
    /// </summary>
    public int NoConfirmOrderNum {get; set; }
    
    /// <summary>
    /// 已取消订单
    /// </summary>
    public int CancelOrderNum {get; set; }
    
    /// <summary>
    /// 已取消合同
    /// </summary>
    public int CancelContractNum {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ShelvesNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ShelvesNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上架数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OffShelvesNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OffShelvesNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入下架数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StopSaleNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StopSaleNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入停售数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SaleNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SaleNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入已销售数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.NoConfirmOrderNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.NoConfirmOrderNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入未确认订单" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CancelOrderNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CancelOrderNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入已取消订单" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CancelContractNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CancelContractNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入已取消合同" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'UnitId', title: '供应商Id', sortable: true },
                 
 { field: 'ShelvesNum', title: '上架数量', sortable: true },
                 
 { field: 'OffShelvesNum', title: '下架数量', sortable: true },
                 
 { field: 'StopSaleNum', title: '停售数量', sortable: true },
                 
 { field: 'SaleNum', title: '已销售数量', sortable: true },
                 
 { field: 'NoConfirmOrderNum', title: '未确认订单', sortable: true },
                 
 { field: 'CancelOrderNum', title: '已取消订单', sortable: true },
                 
 { field: 'CancelContractNum', title: '已取消合同', sortable: true },
                 
o.MallId,                 
o.UnitId,                 
o.ShelvesNum,                 
o.OffShelvesNum,                 
o.StopSaleNum,                 
o.SaleNum,                 
o.NoConfirmOrderNum,                 
o.CancelOrderNum,                 
o.CancelContractNum,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#ShelvesNum').val(d.data.rows.ShelvesNum);          
        $('#OffShelvesNum').val(d.data.rows.OffShelvesNum);          
        $('#StopSaleNum').val(d.data.rows.StopSaleNum);          
        $('#SaleNum').val(d.data.rows.SaleNum);          
        $('#NoConfirmOrderNum').val(d.data.rows.NoConfirmOrderNum);          
        $('#CancelOrderNum').val(d.data.rows.CancelOrderNum);          
        $('#CancelContractNum').val(d.data.rows.CancelContractNum);          

 $('#th_MallId').html(' 商城Id');               
 $('#th_UnitId').html(' 供应商Id');               
 $('#th_ShelvesNum').html(' 上架数量');               
 $('#th_OffShelvesNum').html(' 下架数量');               
 $('#th_StopSaleNum').html(' 停售数量');               
 $('#th_SaleNum').html(' 已销售数量');               
 $('#th_NoConfirmOrderNum').html(' 未确认订单');               
 $('#th_CancelOrderNum').html(' 已取消订单');               
 $('#th_CancelContractNum').html(' 已取消合同');               
 
 $('#tr_MallId').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_ShelvesNum').hide();               
 $('#tr_OffShelvesNum').hide();               
 $('#tr_StopSaleNum').hide();               
 $('#tr_SaleNum').hide();               
 $('#tr_NoConfirmOrderNum').hide();               
 $('#tr_CancelOrderNum').hide();               
 $('#tr_CancelContractNum').hide();               

 , "MallId" : mallId
 , "UnitId" : unitId
 , "ShelvesNum" : shelvesNum
 , "OffShelvesNum" : offShelvesNum
 , "StopSaleNum" : stopSaleNum
 , "SaleNum" : saleNum
 , "NoConfirmOrderNum" : noConfirmOrderNum
 , "CancelOrderNum" : cancelOrderNum
 , "CancelContractNum" : cancelContractNum

 var mallId = $('#o_MallId').val();
 var unitId = $('#o_UnitId').val();
 var shelvesNum = $('#o_ShelvesNum').val();
 var offShelvesNum = $('#o_OffShelvesNum').val();
 var stopSaleNum = $('#o_StopSaleNum').val();
 var saleNum = $('#o_SaleNum').val();
 var noConfirmOrderNum = $('#o_NoConfirmOrderNum').val();
 var cancelOrderNum = $('#o_CancelOrderNum').val();
 var cancelContractNum = $('#o_CancelContractNum').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上架数量' : '产品名称', d.data.rows.ShelvesNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '下架数量' : '产品名称', d.data.rows.OffShelvesNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '停售数量' : '产品名称', d.data.rows.StopSaleNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '已销售数量' : '产品名称', d.data.rows.SaleNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '未确认订单' : '产品名称', d.data.rows.NoConfirmOrderNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '已取消订单' : '产品名称', d.data.rows.CancelOrderNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '已取消合同' : '产品名称', d.data.rows.CancelContractNum);



