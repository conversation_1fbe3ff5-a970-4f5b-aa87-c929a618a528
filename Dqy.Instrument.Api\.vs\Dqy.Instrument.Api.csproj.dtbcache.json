{"RootPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Api", "ProjectFileName": "Dqy.Instrument.Api.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "App_Start\\AntiSqlInjectAttribute.cs"}, {"SourceFile": "App_Start\\ApiNinjectResolver.cs"}, {"SourceFile": "App_Start\\BundleConfig.cs"}, {"SourceFile": "App_Start\\FilterConfig.cs"}, {"SourceFile": "App_Start\\IdentityConfig.cs"}, {"SourceFile": "App_Start\\MvcNinjectResolver.cs"}, {"SourceFile": "App_Start\\NinjectWebCommon.cs"}, {"SourceFile": "App_Start\\RouteConfig.cs"}, {"SourceFile": "App_Start\\Startup.Auth.cs"}, {"SourceFile": "Containers\\EncryptHelper.cs"}, {"SourceFile": "Controllers\\ConfigController.cs"}, {"SourceFile": "Controllers\\ConfigSetController.cs"}, {"SourceFile": "Controllers\\ExperimentController.cs"}, {"SourceFile": "Controllers\\ExperimentSearchController.cs"}, {"SourceFile": "Controllers\\MessageBoardController.cs"}, {"SourceFile": "Controllers\\RecommendController.cs"}, {"SourceFile": "Controllers\\SolutionController.cs"}, {"SourceFile": "Controllers\\StatisticsController.cs"}, {"SourceFile": "Controllers\\WxOpenController.cs"}, {"SourceFile": "Filters\\ValidateModelAttribute.cs"}, {"SourceFile": "App_Start\\WebApiConfig.cs"}, {"SourceFile": "CommonLib\\FileUpload.cs"}, {"SourceFile": "CommonLib\\ProductRecordInsert.cs"}, {"SourceFile": "CommonLib\\ProductSearch.cs"}, {"SourceFile": "CommonLib\\SearchIndex.cs"}, {"SourceFile": "Containers\\SessionContainer.cs"}, {"SourceFile": "Controllers\\AccountController.cs"}, {"SourceFile": "Controllers\\ApprovalController.cs"}, {"SourceFile": "Controllers\\AreaController.cs"}, {"SourceFile": "Controllers\\ArticleController.cs"}, {"SourceFile": "Controllers\\AttachmentController.cs"}, {"SourceFile": "Controllers\\CkfinderController.cs"}, {"SourceFile": "Controllers\\CourseController.cs"}, {"SourceFile": "Controllers\\HomeController.cs"}, {"SourceFile": "Controllers\\LimitController.cs"}, {"SourceFile": "Controllers\\MainController.cs"}, {"SourceFile": "Controllers\\MallConfigureController.cs"}, {"SourceFile": "Controllers\\OrderController.cs"}, {"SourceFile": "Controllers\\ProductController.cs"}, {"SourceFile": "Controllers\\ProductDeclareController.cs"}, {"SourceFile": "Controllers\\ProductSearchController.cs"}, {"SourceFile": "Controllers\\PurchaseController.cs"}, {"SourceFile": "Controllers\\RegisterController.cs"}, {"SourceFile": "Controllers\\SchoolController.cs"}, {"SourceFile": "Controllers\\ShoppingCartController.cs"}, {"SourceFile": "Controllers\\SourceFundController.cs"}, {"SourceFile": "Controllers\\SupplierController.cs"}, {"SourceFile": "Controllers\\TestController.cs"}, {"SourceFile": "Controllers\\UnitController.cs"}, {"SourceFile": "Controllers\\UnitDeliveryAddreszController.cs"}, {"SourceFile": "Controllers\\UnitLinkController.cs"}, {"SourceFile": "Controllers\\UploadController.cs"}, {"SourceFile": "Controllers\\UploadFileController.cs"}, {"SourceFile": "Controllers\\UserController.cs"}, {"SourceFile": "Filters\\SiteAuthorizeAttribute.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Models\\AccountBindingModels.cs"}, {"SourceFile": "Models\\AccountViewModels.cs"}, {"SourceFile": "Models\\IdentityModels.cs"}, {"SourceFile": "Models\\UpLoadFileInfo.cs"}, {"SourceFile": "Models\\WxLoginInfo.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Providers\\ApplicationOAuthProvider.cs"}, {"SourceFile": "Results\\ChallengeResult.cs"}, {"SourceFile": "Startup.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Antlr.3.5.0.2\\lib\\Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\BinaryFormatter.3.0.0\\lib\\netstandard2.0\\BinaryFormatter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.ApplicationServices\\bin\\Debug\\Dqy.Instrument.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.ApplicationServices\\bin\\Debug\\Dqy.Instrument.ApplicationServices.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Infrastructure\\bin\\Debug\\Dqy.Instrument.CloudMarketing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Infrastructure\\bin\\Debug\\Dqy.Instrument.CloudMarketing.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.CommonTypes\\bin\\Debug\\Dqy.Instrument.CommonTypes.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.CommonTypes\\bin\\Debug\\Dqy.Instrument.CommonTypes.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Configurations\\bin\\Debug\\Dqy.Instrument.Configurations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Configurations\\bin\\Debug\\Dqy.Instrument.Configurations.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Framework.Component\\bin\\Debug\\Dqy.Instrument.Framework.Component.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Framework.Component\\bin\\Debug\\Dqy.Instrument.Framework.Component.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Search\\bin\\Debug\\Dqy.Instrument.Search.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Search\\bin\\Debug\\Dqy.Instrument.Search.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Services.ImplementedInterfaces\\bin\\Debug\\Dqy.Instrument.Services.ImplementedInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Services.ImplementedInterfaces\\bin\\Debug\\Dqy.Instrument.Services.ImplementedInterfaces.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.UI.InputModels\\bin\\Debug\\Dqy.Instrument.UI.InputModels.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.UI.InputModels\\bin\\Debug\\Dqy.Instrument.UI.InputModels.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.UI.ViewModels\\bin\\Debug\\Dqy.Instrument.UI.ViewModels.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.UI.ViewModels\\bin\\Debug\\Dqy.Instrument.UI.ViewModels.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\EntityFramework.6.4.4\\lib\\net45\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\EntityFramework.6.4.4\\lib\\net45\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Enyim.Caching.Web.1.0.0.1\\lib\\net40\\Enyim.Caching.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Membase.2.14\\lib\\net35\\Membase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Identity.Core.2.2.3\\lib\\net45\\Microsoft.AspNet.Identity.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Identity.EntityFramework.2.2.3\\lib\\net45\\Microsoft.AspNet.Identity.EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Identity.Owin.2.2.3\\lib\\net45\\Microsoft.AspNet.Identity.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNetCore.Http.Abstractions.2.2.0\\lib\\netstandard2.0\\Microsoft.AspNetCore.Http.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNetCore.Http.2.2.2\\lib\\netstandard2.0\\Microsoft.AspNetCore.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNetCore.Http.Features.5.0.17\\lib\\net461\\Microsoft.AspNetCore.Http.Features.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNetCore.WebUtilities.2.2.0\\lib\\netstandard2.0\\Microsoft.AspNetCore.WebUtilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Bcl.AsyncInterfaces.6.0.0\\lib\\net461\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\\lib\\net45\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Extensions.DependencyInjection.Abstractions.6.0.0\\lib\\net461\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Extensions.ObjectPool.6.0.8\\lib\\net461\\Microsoft.Extensions.ObjectPool.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Extensions.Options.6.0.0\\lib\\net461\\Microsoft.Extensions.Options.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Extensions.Primitives.6.0.0\\lib\\net461\\Microsoft.Extensions.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Net.Http.Headers.2.2.8\\lib\\netstandard2.0\\Microsoft.Net.Http.Headers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Owin.4.2.2\\lib\\net45\\Microsoft.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Owin.Host.SystemWeb.4.2.2\\lib\\net45\\Microsoft.Owin.Host.SystemWeb.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Owin.Security.Cookies.4.2.2\\lib\\net45\\Microsoft.Owin.Security.Cookies.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Owin.Security.4.2.2\\lib\\net45\\Microsoft.Owin.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Owin.Security.MicrosoftAccount.4.2.2\\lib\\net45\\Microsoft.Owin.Security.MicrosoftAccount.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Owin.Security.OAuth.4.2.2\\lib\\net45\\Microsoft.Owin.Security.OAuth.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.Web.Infrastructure.2.0.0\\lib\\net40\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Newtonsoft.Json.13.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Ninject.3.3.6\\lib\\net45\\Ninject.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Ninject.Web.Common.3.3.2\\lib\\net45\\Ninject.Web.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Ninject.Web.Common.WebHost.3.3.2\\lib\\net45\\Ninject.Web.Common.WebHost.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Owin.1.0\\lib\\net40\\Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\PlainElastic.Net.1.1.55\\lib\\Net40\\PlainElastic.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.CO2NET.APM.1.2.1\\lib\\net462\\Senparc.CO2NET.APM.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.CO2NET.AspNet.1.1.1\\lib\\net462\\Senparc.CO2NET.AspNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.CO2NET.Cache.Memcached.4.1.0\\lib\\net462\\Senparc.CO2NET.Cache.Memcached.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.CO2NET.2.1.2\\lib\\net462\\Senparc.CO2NET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.NeuChar.App.1.1.1.1\\lib\\net462\\Senparc.NeuChar.App.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.NeuChar.AspNet.1.1.1.1\\lib\\net462\\Senparc.NeuChar.AspNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.NeuChar.2.1.1.1\\lib\\net462\\Senparc.NeuChar.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.Weixin.AspNet.0.8.2\\lib\\net462\\Senparc.Weixin.AspNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.Weixin.Cache.Memcached.2.13.2\\lib\\net462\\Senparc.Weixin.Cache.Memcached.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.Weixin.6.15.5\\lib\\net462\\Senparc.Weixin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.Weixin.MP.16.18.6\\lib\\net462\\Senparc.Weixin.MP.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.Weixin.MP.MVC.7.12.2\\lib\\net462\\Senparc.Weixin.MP.MvcExtension.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Senparc.Weixin.MP.CommonService\\bin\\Debug\\Senparc.Weixin.MP.Sample.CommonService.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Senparc.Weixin.MP.CommonService\\bin\\Debug\\Senparc.Weixin.MP.Sample.CommonService.dll"}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Senparc.Weixin.WxOpen.3.15.7\\lib\\net462\\Senparc.Weixin.WxOpen.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.ComponentModel.Annotations.5.0.0\\lib\\net461\\System.ComponentModel.Annotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.IO.Pipelines.6.0.3\\lib\\net461\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.Memory.4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebApi.Client.5.2.9\\lib\\net45\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.WebRequest.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.Runtime.CompilerServices.Unsafe.6.0.0\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.Text.Encodings.Web.6.0.0\\lib\\net461\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Cors.5.2.9\\lib\\net45\\System.Web.Cors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebPages.3.2.9\\lib\\net45\\System.Web.Helpers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebApi.Cors.5.2.9\\lib\\net45\\System.Web.Http.Cors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebApi.Core.5.2.9\\lib\\net45\\System.Web.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebApi.Owin.5.2.9\\lib\\net45\\System.Web.Http.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebApi.WebHost.5.2.9\\lib\\net45\\System.Web.Http.WebHost.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Mvc.5.2.9\\lib\\net45\\System.Web.Mvc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Web.Optimization.1.1.3\\lib\\net40\\System.Web.Optimization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.Razor.3.2.9\\lib\\net45\\System.Web.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Routing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebPages.3.2.9\\lib\\net45\\System.Web.WebPages.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebPages.3.2.9\\lib\\net45\\System.Web.WebPages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\Microsoft.AspNet.WebPages.3.2.9\\lib\\net45\\System.Web.WebPages.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\WebActivatorEx.2.2.0\\lib\\net40\\WebActivatorEx.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\packages\\WebGrease.1.6.0\\lib\\WebGrease.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\工作管理\\项目研发\\仪器采购平台\\04系统开发\\云平台\\Dqy.Instrument.Api\\bin\\Dqy.Instrument.Api.dll", "OutputItemRelativePath": "Dqy.Instrument.Api.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}