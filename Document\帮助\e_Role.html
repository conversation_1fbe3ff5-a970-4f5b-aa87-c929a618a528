﻿ Name = entity.Name,
 Memo = entity.Memo,


 Name = model.Name,
 Memo = model.Memo,


 temp.Name = model.Name,
 temp.Memo = model.Memo,

 RoleId = item.RoleId,
 Name = item.Name,
 Memo = item.Memo,

public class RoleInputModel
{
 [Display(Name = "Id")] 
    public int RoleId {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 }
 
 public class RoleViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int RoleId {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
  




 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
o.Name,                 
o.Memo,                 
        
        $('#Name').val(d.data.rows.Name);          
        $('#Memo').val(d.data.rows.Memo);          

 $('#th_Name').html(' 名称');               
 $('#th_Memo').html(' 备注');               
 
 $('#tr_Name').hide();               
 $('#tr_Memo').hide();               

 , "Name" : name
 , "Memo" : memo

 var name = $('#o_Name').val();
 var memo = $('#o_Memo').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);



