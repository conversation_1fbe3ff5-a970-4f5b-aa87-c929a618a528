﻿ StartIp = entity.StartIp,
 EndIp = entity.EndIp,
 StartNum = entity.StartNum,
 EndNum = entity.EndNum,
 Land = entity.Land,
 Country = entity.Country,
 Prov = entity.Prov,
 City = entity.City,
 Dist = entity.Dist,
 Isp = entity.Isp,
 Code = entity.Code,
 EngLish = entity.EngLish,
 Cc = entity.Cc,
 Longitude = entity.Longitude,
 Latitude = entity.Latitude,
 StartIpV6 = entity.StartIpV6,
 EndIpV6 = entity.EndIpV6,
 StartNumV6 = entity.StartNumV6,
 EndNumV6 = entity.EndNumV6,


 StartIp = model.StartIp,
 EndIp = model.EndIp,
 StartNum = model.StartNum,
 EndNum = model.EndNum,
 Land = model.Land,
 Country = model.Country,
 Prov = model.Prov,
 City = model.City,
 Dist = model.Dist,
 Isp = model.Isp,
 Code = model.Code,
 EngLish = model.EngLish,
 Cc = model.Cc,
 Longitude = model.Longitude,
 Latitude = model.Latitude,
 StartIpV6 = model.StartIpV6,
 EndIpV6 = model.EndIpV6,
 StartNumV6 = model.StartNumV6,
 EndNumV6 = model.EndNumV6,


 temp.StartIp = model.StartIp,
 temp.EndIp = model.EndIp,
 temp.StartNum = model.StartNum,
 temp.EndNum = model.EndNum,
 temp.Land = model.Land,
 temp.Country = model.Country,
 temp.Prov = model.Prov,
 temp.City = model.City,
 temp.Dist = model.Dist,
 temp.Isp = model.Isp,
 temp.Code = model.Code,
 temp.EngLish = model.EngLish,
 temp.Cc = model.Cc,
 temp.Longitude = model.Longitude,
 temp.Latitude = model.Latitude,
 temp.StartIpV6 = model.StartIpV6,
 temp.EndIpV6 = model.EndIpV6,
 temp.StartNumV6 = model.StartNumV6,
 temp.EndNumV6 = model.EndNumV6,

 IpId = item.IpId,
 AreaId = item.AreaId,
 StartIp = item.StartIp,
 EndIp = item.EndIp,
 StartNum = item.StartNum,
 EndNum = item.EndNum,
 Land = item.Land,
 Country = item.Country,
 Prov = item.Prov,
 City = item.City,
 Dist = item.Dist,
 Isp = item.Isp,
 Code = item.Code,
 EngLish = item.EngLish,
 Cc = item.Cc,
 Longitude = item.Longitude,
 Latitude = item.Latitude,
 StartIpV6 = item.StartIpV6,
 EndIpV6 = item.EndIpV6,
 StartNumV6 = item.StartNumV6,
 EndNumV6 = item.EndNumV6,

public class IpAdreszInputModel
{
 [Display(Name = "Id")] 
    public long IpId {get; set; }
    
 [Display(Name = "区域Id")] 
    public int AreaId {get; set; }
    
 [Display(Name = "开始Ip")] 
    public string StartIp {get; set; }
    
 [Display(Name = "结束Ip")] 
    public string EndIp {get; set; }
    
 [Display(Name = "开始数字")] 
    public long StartNum {get; set; }
    
 [Display(Name = "结束数字")] 
    public long EndNum {get; set; }
    
 [Display(Name = "洲")] 
    public string Land {get; set; }
    
 [Display(Name = "国家")] 
    public string Country {get; set; }
    
 [Display(Name = "省")] 
    public string Prov {get; set; }
    
 [Display(Name = "市")] 
    public string City {get; set; }
    
 [Display(Name = "区")] 
    public string Dist {get; set; }
    
 [Display(Name = "运营商")] 
    public string Isp {get; set; }
    
 [Display(Name = "行政区域")] 
    public string Code {get; set; }
    
 [Display(Name = "英文名")] 
    public string EngLish {get; set; }
    
 [Display(Name = "代码")] 
    public string Cc {get; set; }
    
 [Display(Name = "经度")] 
    public string Longitude {get; set; }
    
 [Display(Name = "纬度")] 
    public string Latitude {get; set; }
    
 [Display(Name = "开始Ip6")] 
    public string StartIpV6 {get; set; }
    
 [Display(Name = "结束Ip6")] 
    public string EndIpV6 {get; set; }
    
 [Display(Name = "开始数字V6")] 
    public long StartNumV6 {get; set; }
    
 [Display(Name = "结束数字V6")] 
    public long EndNumV6 {get; set; }
    
 }
 
 public class IpAdreszViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long IpId {get; set; }
    
    /// <summary>
    /// 区域Id
    /// </summary>
    public int AreaId {get; set; }
    
    /// <summary>
    /// 开始Ip
    /// </summary>
    public string StartIp {get; set; }
    
    /// <summary>
    /// 结束Ip
    /// </summary>
    public string EndIp {get; set; }
    
    /// <summary>
    /// 开始数字
    /// </summary>
    public long StartNum {get; set; }
    
    /// <summary>
    /// 结束数字
    /// </summary>
    public long EndNum {get; set; }
    
    /// <summary>
    /// 洲
    /// </summary>
    public string Land {get; set; }
    
    /// <summary>
    /// 国家
    /// </summary>
    public string Country {get; set; }
    
    /// <summary>
    /// 省
    /// </summary>
    public string Prov {get; set; }
    
    /// <summary>
    /// 市
    /// </summary>
    public string City {get; set; }
    
    /// <summary>
    /// 区
    /// </summary>
    public string Dist {get; set; }
    
    /// <summary>
    /// 运营商
    /// </summary>
    public string Isp {get; set; }
    
    /// <summary>
    /// 行政区域
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 英文名
    /// </summary>
    public string EngLish {get; set; }
    
    /// <summary>
    /// 代码
    /// </summary>
    public string Cc {get; set; }
    
    /// <summary>
    /// 经度
    /// </summary>
    public string Longitude {get; set; }
    
    /// <summary>
    /// 纬度
    /// </summary>
    public string Latitude {get; set; }
    
    /// <summary>
    /// 开始Ip6
    /// </summary>
    public string StartIpV6 {get; set; }
    
    /// <summary>
    /// 结束Ip6
    /// </summary>
    public string EndIpV6 {get; set; }
    
    /// <summary>
    /// 开始数字V6
    /// </summary>
    public long StartNumV6 {get; set; }
    
    /// <summary>
    /// 结束数字V6
    /// </summary>
    public long EndNumV6 {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.StartIp, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StartIp, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入开始Ip" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EndIp, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EndIp, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入结束Ip" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StartNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StartNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入开始数字" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EndNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EndNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入结束数字" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Land, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Land, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入洲" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Country, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Country, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入国家" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Prov, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Prov, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入省" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.City, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.City, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Dist, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Dist, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Isp, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Isp, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运营商" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入行政区域" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EngLish, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EngLish, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入英文名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Cc, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Cc, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Longitude, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Longitude, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入经度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Latitude, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Latitude, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入纬度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StartIpV6, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StartIpV6, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入开始Ip6" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EndIpV6, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EndIpV6, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入结束Ip6" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StartNumV6, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StartNumV6, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入开始数字V6" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EndNumV6, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EndNumV6, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入结束数字V6" } })                    
                </div>
           </div>
  




 { field: 'StartIp', title: '开始Ip', sortable: true },
                 
 { field: 'EndIp', title: '结束Ip', sortable: true },
                 
 { field: 'StartNum', title: '开始数字', sortable: true },
                 
 { field: 'EndNum', title: '结束数字', sortable: true },
                 
 { field: 'Land', title: '洲', sortable: true },
                 
 { field: 'Country', title: '国家', sortable: true },
                 
 { field: 'Prov', title: '省', sortable: true },
                 
 { field: 'City', title: '市', sortable: true },
                 
 { field: 'Dist', title: '区', sortable: true },
                 
 { field: 'Isp', title: '运营商', sortable: true },
                 
 { field: 'Code', title: '行政区域', sortable: true },
                 
 { field: 'EngLish', title: '英文名', sortable: true },
                 
 { field: 'Cc', title: '代码', sortable: true },
                 
 { field: 'Longitude', title: '经度', sortable: true },
                 
 { field: 'Latitude', title: '纬度', sortable: true },
                 
 { field: 'StartIpV6', title: '开始Ip6', sortable: true },
                 
 { field: 'EndIpV6', title: '结束Ip6', sortable: true },
                 
 { field: 'StartNumV6', title: '开始数字V6', sortable: true },
                 
 { field: 'EndNumV6', title: '结束数字V6', sortable: true },
                 
o.StartIp,                 
o.EndIp,                 
o.StartNum,                 
o.EndNum,                 
o.Land,                 
o.Country,                 
o.Prov,                 
o.City,                 
o.Dist,                 
o.Isp,                 
o.Code,                 
o.EngLish,                 
o.Cc,                 
o.Longitude,                 
o.Latitude,                 
o.StartIpV6,                 
o.EndIpV6,                 
o.StartNumV6,                 
o.EndNumV6,                 
        
        $('#StartIp').val(d.data.rows.StartIp);          
        $('#EndIp').val(d.data.rows.EndIp);          
        $('#StartNum').val(d.data.rows.StartNum);          
        $('#EndNum').val(d.data.rows.EndNum);          
        $('#Land').val(d.data.rows.Land);          
        $('#Country').val(d.data.rows.Country);          
        $('#Prov').val(d.data.rows.Prov);          
        $('#City').val(d.data.rows.City);          
        $('#Dist').val(d.data.rows.Dist);          
        $('#Isp').val(d.data.rows.Isp);          
        $('#Code').val(d.data.rows.Code);          
        $('#EngLish').val(d.data.rows.EngLish);          
        $('#Cc').val(d.data.rows.Cc);          
        $('#Longitude').val(d.data.rows.Longitude);          
        $('#Latitude').val(d.data.rows.Latitude);          
        $('#StartIpV6').val(d.data.rows.StartIpV6);          
        $('#EndIpV6').val(d.data.rows.EndIpV6);          
        $('#StartNumV6').val(d.data.rows.StartNumV6);          
        $('#EndNumV6').val(d.data.rows.EndNumV6);          

 $('#th_StartIp').html(' 开始Ip');               
 $('#th_EndIp').html(' 结束Ip');               
 $('#th_StartNum').html(' 开始数字');               
 $('#th_EndNum').html(' 结束数字');               
 $('#th_Land').html(' 洲');               
 $('#th_Country').html(' 国家');               
 $('#th_Prov').html(' 省');               
 $('#th_City').html(' 市');               
 $('#th_Dist').html(' 区');               
 $('#th_Isp').html(' 运营商');               
 $('#th_Code').html(' 行政区域');               
 $('#th_EngLish').html(' 英文名');               
 $('#th_Cc').html(' 代码');               
 $('#th_Longitude').html(' 经度');               
 $('#th_Latitude').html(' 纬度');               
 $('#th_StartIpV6').html(' 开始Ip6');               
 $('#th_EndIpV6').html(' 结束Ip6');               
 $('#th_StartNumV6').html(' 开始数字V6');               
 $('#th_EndNumV6').html(' 结束数字V6');               
 
 $('#tr_StartIp').hide();               
 $('#tr_EndIp').hide();               
 $('#tr_StartNum').hide();               
 $('#tr_EndNum').hide();               
 $('#tr_Land').hide();               
 $('#tr_Country').hide();               
 $('#tr_Prov').hide();               
 $('#tr_City').hide();               
 $('#tr_Dist').hide();               
 $('#tr_Isp').hide();               
 $('#tr_Code').hide();               
 $('#tr_EngLish').hide();               
 $('#tr_Cc').hide();               
 $('#tr_Longitude').hide();               
 $('#tr_Latitude').hide();               
 $('#tr_StartIpV6').hide();               
 $('#tr_EndIpV6').hide();               
 $('#tr_StartNumV6').hide();               
 $('#tr_EndNumV6').hide();               

 , "StartIp" : startIp
 , "EndIp" : endIp
 , "StartNum" : startNum
 , "EndNum" : endNum
 , "Land" : land
 , "Country" : country
 , "Prov" : prov
 , "City" : city
 , "Dist" : dist
 , "Isp" : isp
 , "Code" : code
 , "EngLish" : engLish
 , "Cc" : cc
 , "Longitude" : longitude
 , "Latitude" : latitude
 , "StartIpV6" : startIpV6
 , "EndIpV6" : endIpV6
 , "StartNumV6" : startNumV6
 , "EndNumV6" : endNumV6

 var startIp = $('#o_StartIp').val();
 var endIp = $('#o_EndIp').val();
 var startNum = $('#o_StartNum').val();
 var endNum = $('#o_EndNum').val();
 var land = $('#o_Land').val();
 var country = $('#o_Country').val();
 var prov = $('#o_Prov').val();
 var city = $('#o_City').val();
 var dist = $('#o_Dist').val();
 var isp = $('#o_Isp').val();
 var code = $('#o_Code').val();
 var engLish = $('#o_EngLish').val();
 var cc = $('#o_Cc').val();
 var longitude = $('#o_Longitude').val();
 var latitude = $('#o_Latitude').val();
 var startIpV6 = $('#o_StartIpV6').val();
 var endIpV6 = $('#o_EndIpV6').val();
 var startNumV6 = $('#o_StartNumV6').val();
 var endNumV6 = $('#o_EndNumV6').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '开始Ip' : '产品名称', d.data.rows.StartIp);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '结束Ip' : '产品名称', d.data.rows.EndIp);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '开始数字' : '产品名称', d.data.rows.StartNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '结束数字' : '产品名称', d.data.rows.EndNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '洲' : '产品名称', d.data.rows.Land);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '国家' : '产品名称', d.data.rows.Country);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '省' : '产品名称', d.data.rows.Prov);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市' : '产品名称', d.data.rows.City);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区' : '产品名称', d.data.rows.Dist);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运营商' : '产品名称', d.data.rows.Isp);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '行政区域' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '英文名' : '产品名称', d.data.rows.EngLish);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '代码' : '产品名称', d.data.rows.Cc);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '经度' : '产品名称', d.data.rows.Longitude);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '纬度' : '产品名称', d.data.rows.Latitude);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '开始Ip6' : '产品名称', d.data.rows.StartIpV6);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '结束Ip6' : '产品名称', d.data.rows.EndIpV6);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '开始数字V6' : '产品名称', d.data.rows.StartNumV6);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '结束数字V6' : '产品名称', d.data.rows.EndNumV6);



