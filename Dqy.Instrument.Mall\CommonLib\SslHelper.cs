using System;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace Dqy.Instrument.Mall.CommonLib
{
    /// <summary>
    /// SSL/TLS配置帮助类，用于解决Windows Server 2016等环境下的SSL连接问题
    /// </summary>
    public static class SslHelper
    {
        private static bool _isConfigured = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 配置SSL/TLS协议以支持Windows Server 2016及更高版本
        /// 此方法应在应用程序启动时调用一次
        /// </summary>
        public static void ConfigureSslProtocols()
        {
            if (_isConfigured)
                return;

            lock (_lockObject)
            {
                if (_isConfigured)
                    return;

                try
                {
                    // 设置支持的SSL/TLS协议版本
                    // SecurityProtocolType.Tls13 = 12288 (0x3000)
                    // SecurityProtocolType.Tls12 = 3072 (0x0C00)
                    // SecurityProtocolType.Tls11 = 768 (0x0300)
                    // SecurityProtocolType.Tls = 192 (0x00C0)
                    // SecurityProtocolType.Ssl3 = 48 (0x0030)
                    
                    ServicePointManager.SecurityProtocol = 
                        SecurityProtocolType.Tls12 | 
                        SecurityProtocolType.Tls11 | 
                        SecurityProtocolType.Tls |
                        (SecurityProtocolType)3072; // TLS 1.2 显式指定

                    // 在.NET Framework 4.7及以上版本中，尝试启用TLS 1.3
                    try
                    {
                        ServicePointManager.SecurityProtocol |= (SecurityProtocolType)12288; // TLS 1.3
                    }
                    catch
                    {
                        // 如果不支持TLS 1.3，忽略错误
                    }

                    // 禁用证书吊销检查以提高性能和兼容性
                    ServicePointManager.CheckCertificateRevocationList = false;

                    // 设置默认连接限制
                    ServicePointManager.DefaultConnectionLimit = 100;

                    // 启用Expect100Continue以提高性能
                    ServicePointManager.Expect100Continue = true;

                    _isConfigured = true;
                }
                catch (Exception ex)
                {
                    // 记录配置错误，但不抛出异常
                    System.Diagnostics.Debug.WriteLine($"SSL配置失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 为特定URL配置SSL/TLS设置
        /// </summary>
        /// <param name="url">目标URL</param>
        /// <param name="ignoreCertificateErrors">是否忽略证书错误（仅用于开发/测试环境）</param>
        public static void ConfigureForUrl(string url, bool ignoreCertificateErrors = false)
        {
            if (string.IsNullOrEmpty(url) || !url.ToLower().StartsWith("https"))
                return;

            // 确保基本SSL配置已完成
            ConfigureSslProtocols();

            if (ignoreCertificateErrors)
            {
                // 设置证书验证回调（仅用于开发/测试环境）
                ServicePointManager.ServerCertificateValidationCallback = 
                    new RemoteCertificateValidationCallback(ValidateServerCertificate);
            }
        }

        /// <summary>
        /// 证书验证回调方法
        /// 注意：此方法会接受所有证书，仅用于开发/测试环境
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="certificate"></param>
        /// <param name="chain"></param>
        /// <param name="sslPolicyErrors"></param>
        /// <returns></returns>
        private static bool ValidateServerCertificate(
            object sender,
            X509Certificate certificate,
            X509Chain chain,
            SslPolicyErrors sslPolicyErrors)
        {
            // 在生产环境中，应该进行适当的证书验证
            // 这里为了解决连接问题，暂时返回true
            // 建议在生产环境中实现更严格的证书验证逻辑
            
            if (sslPolicyErrors == SslPolicyErrors.None)
                return true;

            // 记录证书错误信息
            System.Diagnostics.Debug.WriteLine($"证书验证错误: {sslPolicyErrors}");
            
            // 在开发/测试环境中可以返回true，生产环境建议返回false
            return true;
        }

        /// <summary>
        /// 重置SSL配置为默认状态
        /// </summary>
        public static void ResetSslConfiguration()
        {
            lock (_lockObject)
            {
                ServicePointManager.ServerCertificateValidationCallback = null;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                ServicePointManager.CheckCertificateRevocationList = true;
                _isConfigured = false;
            }
        }

        /// <summary>
        /// 获取当前支持的SSL/TLS协议信息
        /// </summary>
        /// <returns></returns>
        public static string GetSupportedProtocols()
        {
            return $"当前支持的协议: {ServicePointManager.SecurityProtocol}";
        }
    }
}
