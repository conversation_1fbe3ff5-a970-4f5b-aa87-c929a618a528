﻿ ObjId = entity.ObjId,
 ObjType = entity.ObjType,
 Pid = entity.Pid,
 Name = entity.Name,
 Url = entity.Url,
 SubOrder = entity.SubOrder,
 Depth = entity.Depth,
 Sort = entity.Sort,
 IsShow = entity.IsShow,
 Icon = entity.Icon,
 SmallIcon = entity.SmallIcon,
 Memo = entity.Memo,
 RegTime = entity.RegTime,
 CreatorId = entity.CreatorId,


 ObjId = model.ObjId,
 ObjType = model.ObjType,
 Pid = model.Pid,
 Name = model.Name,
 Url = model.Url,
 SubOrder = model.SubOrder,
 Depth = model.Depth,
 Sort = model.Sort,
 IsShow = model.IsShow,
 Icon = model.Icon,
 SmallIcon = model.SmallIcon,
 Memo = model.Memo,
 RegTime = model.RegTime,
 CreatorId = model.CreatorId,


 temp.ObjId = model.ObjId,
 temp.ObjType = model.ObjType,
 temp.Pid = model.Pid,
 temp.Name = model.Name,
 temp.Url = model.Url,
 temp.SubOrder = model.SubOrder,
 temp.Depth = model.Depth,
 temp.Sort = model.Sort,
 temp.IsShow = model.IsShow,
 temp.Icon = model.Icon,
 temp.SmallIcon = model.SmallIcon,
 temp.Memo = model.Memo,
 temp.RegTime = model.RegTime,
 temp.CreatorId = model.CreatorId,

 PhotoGalleryId = item.PhotoGalleryId,
 ObjId = item.ObjId,
 ObjType = item.ObjType,
 Pid = item.Pid,
 Name = item.Name,
 Url = item.Url,
 SubOrder = item.SubOrder,
 Depth = item.Depth,
 Sort = item.Sort,
 IsShow = item.IsShow,
 Icon = item.Icon,
 SmallIcon = item.SmallIcon,
 Memo = item.Memo,
 RegTime = item.RegTime,
 CreatorId = item.CreatorId,

public class PhotoGalleryInputModel
{
 [Display(Name = "编号")] 
    public long PhotoGalleryId {get; set; }
    
 [Display(Name = "所属对象Id")] 
    public long ObjId {get; set; }
    
 [Display(Name = "对象类型（0：UnitId；1：UserId）")] 
    public int ObjType {get; set; }
    
 [Display(Name = "父编号")] 
    public long Pid {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "封面")] 
    public string Url {get; set; }
    
 [Display(Name = "子菜单排序")] 
    public int SubOrder {get; set; }
    
 [Display(Name = "深度")] 
    public int Depth {get; set; }
    
 [Display(Name = "排序")] 
    public int Sort {get; set; }
    
 [Display(Name = "是否显示")] 
    public bool IsShow {get; set; }
    
 [Display(Name = "图标")] 
    public string Icon {get; set; }
    
 [Display(Name = "小图标")] 
    public string SmallIcon {get; set; }
    
 [Display(Name = "描述")] 
    public string Memo {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "创建人Id")] 
    public long CreatorId {get; set; }
    
 }
 
 public class PhotoGalleryViewModel
 {
    /// <summary>
    /// 编号
    /// </summary>
    public long PhotoGalleryId {get; set; }
    
    /// <summary>
    /// 所属对象Id
    /// </summary>
    public long ObjId {get; set; }
    
    /// <summary>
    /// 对象类型（0：UnitId；1：UserId）
    /// </summary>
    public int ObjType {get; set; }
    
    /// <summary>
    /// 父编号
    /// </summary>
    public long Pid {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 封面
    /// </summary>
    public string Url {get; set; }
    
    /// <summary>
    /// 子菜单排序
    /// </summary>
    public int SubOrder {get; set; }
    
    /// <summary>
    /// 深度
    /// </summary>
    public int Depth {get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort {get; set; }
    
    /// <summary>
    /// 是否显示
    /// </summary>
    public bool IsShow {get; set; }
    
    /// <summary>
    /// 图标
    /// </summary>
    public string Icon {get; set; }
    
    /// <summary>
    /// 小图标
    /// </summary>
    public string SmallIcon {get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入所属对象Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入对象类型（0：UnitId；1：UserId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Pid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Url, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Url, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入封面" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SubOrder, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SubOrder, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入子菜单排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入深度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sort, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sort, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsShow, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsShow, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否显示" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Icon, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Icon, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图标" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SmallIcon, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SmallIcon, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入小图标" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入描述" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreatorId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreatorId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建人Id" } })                    
                </div>
           </div>
  




 { field: 'ObjId', title: '所属对象Id', sortable: true },
                 
 { field: 'ObjType', title: '对象类型（0：UnitId；1：UserId）', sortable: true },
                 
 { field: 'Pid', title: '父编号', sortable: true },
                 
 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'Url', title: '封面', sortable: true },
                 
 { field: 'SubOrder', title: '子菜单排序', sortable: true },
                 
 { field: 'Depth', title: '深度', sortable: true },
                 
 { field: 'Sort', title: '排序', sortable: true },
                 
 { field: 'IsShow', title: '是否显示', sortable: true },
                 
 { field: 'Icon', title: '图标', sortable: true },
                 
 { field: 'SmallIcon', title: '小图标', sortable: true },
                 
 { field: 'Memo', title: '描述', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
 { field: 'CreatorId', title: '创建人Id', sortable: true },
                 
o.ObjId,                 
o.ObjType,                 
o.Pid,                 
o.Name,                 
o.Url,                 
o.SubOrder,                 
o.Depth,                 
o.Sort,                 
o.IsShow,                 
o.Icon,                 
o.SmallIcon,                 
o.Memo,                 
o.RegTime,                 
o.CreatorId,                 
        
        $('#ObjId').val(d.data.rows.ObjId);          
        $('#ObjType').val(d.data.rows.ObjType);          
        $('#Pid').val(d.data.rows.Pid);          
        $('#Name').val(d.data.rows.Name);          
        $('#Url').val(d.data.rows.Url);          
        $('#SubOrder').val(d.data.rows.SubOrder);          
        $('#Depth').val(d.data.rows.Depth);          
        $('#Sort').val(d.data.rows.Sort);          
        $('#IsShow').val(d.data.rows.IsShow);          
        $('#Icon').val(d.data.rows.Icon);          
        $('#SmallIcon').val(d.data.rows.SmallIcon);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#CreatorId').val(d.data.rows.CreatorId);          

 $('#th_ObjId').html(' 所属对象Id');               
 $('#th_ObjType').html(' 对象类型（0：UnitId；1：UserId）');               
 $('#th_Pid').html(' 父编号');               
 $('#th_Name').html(' 名称');               
 $('#th_Url').html(' 封面');               
 $('#th_SubOrder').html(' 子菜单排序');               
 $('#th_Depth').html(' 深度');               
 $('#th_Sort').html(' 排序');               
 $('#th_IsShow').html(' 是否显示');               
 $('#th_Icon').html(' 图标');               
 $('#th_SmallIcon').html(' 小图标');               
 $('#th_Memo').html(' 描述');               
 $('#th_RegTime').html(' 创建时间');               
 $('#th_CreatorId').html(' 创建人Id');               
 
 $('#tr_ObjId').hide();               
 $('#tr_ObjType').hide();               
 $('#tr_Pid').hide();               
 $('#tr_Name').hide();               
 $('#tr_Url').hide();               
 $('#tr_SubOrder').hide();               
 $('#tr_Depth').hide();               
 $('#tr_Sort').hide();               
 $('#tr_IsShow').hide();               
 $('#tr_Icon').hide();               
 $('#tr_SmallIcon').hide();               
 $('#tr_Memo').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_CreatorId').hide();               

 , "ObjId" : objId
 , "ObjType" : objType
 , "Pid" : pid
 , "Name" : name
 , "Url" : url
 , "SubOrder" : subOrder
 , "Depth" : depth
 , "Sort" : sort
 , "IsShow" : isShow
 , "Icon" : icon
 , "SmallIcon" : smallIcon
 , "Memo" : memo
 , "RegTime" : regTime
 , "CreatorId" : creatorId

 var objId = $('#o_ObjId').val();
 var objType = $('#o_ObjType').val();
 var pid = $('#o_Pid').val();
 var name = $('#o_Name').val();
 var url = $('#o_Url').val();
 var subOrder = $('#o_SubOrder').val();
 var depth = $('#o_Depth').val();
 var sort = $('#o_Sort').val();
 var isShow = $('#o_IsShow').val();
 var icon = $('#o_Icon').val();
 var smallIcon = $('#o_SmallIcon').val();
 var memo = $('#o_Memo').val();
 var regTime = $('#o_RegTime').val();
 var creatorId = $('#o_CreatorId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '所属对象Id' : '产品名称', d.data.rows.ObjId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '对象类型（0：UnitId；1：UserId）' : '产品名称', d.data.rows.ObjType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父编号' : '产品名称', d.data.rows.Pid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '封面' : '产品名称', d.data.rows.Url);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '子菜单排序' : '产品名称', d.data.rows.SubOrder);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '深度' : '产品名称', d.data.rows.Depth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '排序' : '产品名称', d.data.rows.Sort);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否显示' : '产品名称', d.data.rows.IsShow);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图标' : '产品名称', d.data.rows.Icon);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '小图标' : '产品名称', d.data.rows.SmallIcon);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '描述' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建人Id' : '产品名称', d.data.rows.CreatorId);



