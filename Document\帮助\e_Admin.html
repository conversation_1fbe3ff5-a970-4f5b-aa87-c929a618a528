﻿ LoginName = entity.LoginName,
 RealName = entity.RealName,
 Mobile = entity.Mobile,
 Email = entity.Email,
 Pwd = entity.Pwd,
 Salt = entity.Salt,
 RoleId = entity.RoleId,
 Statuz = entity.Statuz,
 UserType = entity.UserType,
 RegTime = entity.RegTime,
 CreatorId = entity.CreatorId,


 LoginName = model.LoginName,
 RealName = model.RealName,
 Mobile = model.Mobile,
 Email = model.Email,
 Pwd = model.Pwd,
 Salt = model.Salt,
 RoleId = model.RoleId,
 Statuz = model.Statuz,
 UserType = model.UserType,
 RegTime = model.RegTime,
 CreatorId = model.CreatorId,


 temp.LoginName = model.LoginName,
 temp.RealName = model.RealName,
 temp.Mobile = model.Mobile,
 temp.Email = model.Email,
 temp.Pwd = model.Pwd,
 temp.Salt = model.Salt,
 temp.RoleId = model.RoleId,
 temp.Statuz = model.Statuz,
 temp.UserType = model.UserType,
 temp.RegTime = model.RegTime,
 temp.CreatorId = model.CreatorId,

 AdminId = item.AdminId,
 LoginName = item.LoginName,
 RealName = item.RealName,
 Mobile = item.Mobile,
 Email = item.Email,
 Pwd = item.Pwd,
 Salt = item.Salt,
 RoleId = item.RoleId,
 Statuz = item.Statuz,
 UserType = item.UserType,
 RegTime = item.RegTime,
 CreatorId = item.CreatorId,

public class AdminInputModel
{
 [Display(Name = "Id")] 
    public long AdminId {get; set; }
    
 [Display(Name = "登录名")] 
    public string LoginName {get; set; }
    
 [Display(Name = "实名")] 
    public string RealName {get; set; }
    
 [Display(Name = "手机")] 
    public string Mobile {get; set; }
    
 [Display(Name = "邮箱")] 
    public string Email {get; set; }
    
 [Display(Name = "密码")] 
    public string Pwd {get; set; }
    
 [Display(Name = "加盐值")] 
    public string Salt {get; set; }
    
 [Display(Name = "角色Id")] 
    public int RoleId {get; set; }
    
 [Display(Name = "状态（0：禁用，1：正常，2：待审核）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "用户类型（1：超管：2：分角色管理；）")] 
    public int UserType {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "创建人Id")] 
    public long CreatorId {get; set; }
    
 }
 
 public class AdminViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long AdminId {get; set; }
    
    /// <summary>
    /// 登录名
    /// </summary>
    public string LoginName {get; set; }
    
    /// <summary>
    /// 实名
    /// </summary>
    public string RealName {get; set; }
    
    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile {get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email {get; set; }
    
    /// <summary>
    /// 密码
    /// </summary>
    public string Pwd {get; set; }
    
    /// <summary>
    /// 加盐值
    /// </summary>
    public string Salt {get; set; }
    
    /// <summary>
    /// 角色Id
    /// </summary>
    public int RoleId {get; set; }
    
    /// <summary>
    /// 状态（0：禁用，1：正常，2：待审核）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 用户类型（1：超管：2：分角色管理；）
    /// </summary>
    public int UserType {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.LoginName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LoginName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入登录名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RealName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RealName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入实名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Mobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Mobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Email, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮箱" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Pwd, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pwd, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入密码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Salt, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Salt, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入加盐值" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RoleId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RoleId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入角色Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（0：禁用，1：正常，2：待审核）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户类型（1：超管：2：分角色管理；）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreatorId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreatorId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建人Id" } })                    
                </div>
           </div>
  




 { field: 'LoginName', title: '登录名', sortable: true },
                 
 { field: 'RealName', title: '实名', sortable: true },
                 
 { field: 'Mobile', title: '手机', sortable: true },
                 
 { field: 'Email', title: '邮箱', sortable: true },
                 
 { field: 'Pwd', title: '密码', sortable: true },
                 
 { field: 'Salt', title: '加盐值', sortable: true },
                 
 { field: 'RoleId', title: '角色Id', sortable: true },
                 
 { field: 'Statuz', title: '状态（0：禁用，1：正常，2：待审核）', sortable: true },
                 
 { field: 'UserType', title: '用户类型（1：超管：2：分角色管理；）', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
 { field: 'CreatorId', title: '创建人Id', sortable: true },
                 
o.LoginName,                 
o.RealName,                 
o.Mobile,                 
o.Email,                 
o.Pwd,                 
o.Salt,                 
o.RoleId,                 
o.Statuz,                 
o.UserType,                 
o.RegTime,                 
o.CreatorId,                 
        
        $('#LoginName').val(d.data.rows.LoginName);          
        $('#RealName').val(d.data.rows.RealName);          
        $('#Mobile').val(d.data.rows.Mobile);          
        $('#Email').val(d.data.rows.Email);          
        $('#Pwd').val(d.data.rows.Pwd);          
        $('#Salt').val(d.data.rows.Salt);          
        $('#RoleId').val(d.data.rows.RoleId);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#UserType').val(d.data.rows.UserType);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#CreatorId').val(d.data.rows.CreatorId);          

 $('#th_LoginName').html(' 登录名');               
 $('#th_RealName').html(' 实名');               
 $('#th_Mobile').html(' 手机');               
 $('#th_Email').html(' 邮箱');               
 $('#th_Pwd').html(' 密码');               
 $('#th_Salt').html(' 加盐值');               
 $('#th_RoleId').html(' 角色Id');               
 $('#th_Statuz').html(' 状态（0：禁用，1：正常，2：待审核）');               
 $('#th_UserType').html(' 用户类型（1：超管：2：分角色管理；）');               
 $('#th_RegTime').html(' 创建时间');               
 $('#th_CreatorId').html(' 创建人Id');               
 
 $('#tr_LoginName').hide();               
 $('#tr_RealName').hide();               
 $('#tr_Mobile').hide();               
 $('#tr_Email').hide();               
 $('#tr_Pwd').hide();               
 $('#tr_Salt').hide();               
 $('#tr_RoleId').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_UserType').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_CreatorId').hide();               

 , "LoginName" : loginName
 , "RealName" : realName
 , "Mobile" : mobile
 , "Email" : email
 , "Pwd" : pwd
 , "Salt" : salt
 , "RoleId" : roleId
 , "Statuz" : statuz
 , "UserType" : userType
 , "RegTime" : regTime
 , "CreatorId" : creatorId

 var loginName = $('#o_LoginName').val();
 var realName = $('#o_RealName').val();
 var mobile = $('#o_Mobile').val();
 var email = $('#o_Email').val();
 var pwd = $('#o_Pwd').val();
 var salt = $('#o_Salt').val();
 var roleId = $('#o_RoleId').val();
 var statuz = $('#o_Statuz').val();
 var userType = $('#o_UserType').val();
 var regTime = $('#o_RegTime').val();
 var creatorId = $('#o_CreatorId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '登录名' : '产品名称', d.data.rows.LoginName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '实名' : '产品名称', d.data.rows.RealName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机' : '产品名称', d.data.rows.Mobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮箱' : '产品名称', d.data.rows.Email);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '密码' : '产品名称', d.data.rows.Pwd);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '加盐值' : '产品名称', d.data.rows.Salt);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '角色Id' : '产品名称', d.data.rows.RoleId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（0：禁用，1：正常，2：待审核）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户类型（1：超管：2：分角色管理；）' : '产品名称', d.data.rows.UserType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建人Id' : '产品名称', d.data.rows.CreatorId);



