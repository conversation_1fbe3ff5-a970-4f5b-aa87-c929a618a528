﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
//using Dqy.Instrument.Search;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Dqy.TrainManage.Base.Util;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class ExperimentController : ControllerMember
    {
        /// <summary>
        /// 实验列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> List(SearchExperimentInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/searchexperiment";
            var result = await WebApiHelper.SendAsync<QueryResult<ExperimentViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 录入、修改页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> Edit(long id = 0)
        {
            if (Operater == null)
            {
                return ApiTimeOut();
            }
            ExperimentInputModel model = new ExperimentInputModel();
            if (id > 0)
            {
                //编辑页面
                string url = Constant.ApiPath + "experiment/getexperimentbyid?id=" + id + "&token=" + Operater.Token;
                var result = await WebApiHelper.SendAsync<QueryResult<ExperimentInputModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
                if (result.flag == -1)
                {
                    return ApiTimeOut();
                }
                else if (result.flag == -2)
                {
                    return ReturnHome();
                }
                else if (result.flag == 0)
                {
                    model = null;
                }
                else if (result.flag == 1)
                {
                    model = result.Entity;
                }
            }
            string courseUrl = "course/getcourseall";
            var courseList = await WebApiHelper.SendAsync<List<SelectListViewModel>>(courseUrl, null, CommonTypes.CommonJsonSendType.GET);
            model.CourseList = courseList;

            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId;
            return View(model);
        }

        /// <summary>
        /// 选择仪器
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<JsonResult> SearchProductAllPlatformList(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = Constant.ApiPath + "supplier/searchproductallplatformlist";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);
            ViewBag.Args = args;
            return Json(result);
        }

        [HttpPost]
        public async Task<ActionResult> SaveExperiment(ExperimentInputModel model)
        {
            bool isSuccess = true;
            if (model.SuitGrade == 0)
            {
                isSuccess = false;
                ModelState.AddModelError("SuitGrade", "请选择适用年级");
            }
            if (model.SuitCourse == 0)
            {
                isSuccess = false;
                ModelState.AddModelError("SuitCourse", "请选择适用学科");
            }
            if (string.IsNullOrWhiteSpace(model.MainProductListIds))
            {
                isSuccess = false;
                ModelState.AddModelError("MainProductListIds", "请选择主要仪器");
            }
            if (!isSuccess)
            {
                string courseUrl = Constant.ApiPath + "course/getcourseall";
                var courseList = await WebApiHelper.SendAsync<List<SelectListViewModel>>(courseUrl, null, CommonTypes.CommonJsonSendType.GET);
                model.CourseList = courseList;
                return View("Edit", model);
            }

            model.BaseUserId = Operater.UserId;//
            model.BaseUnitId = Operater.UnitId;//
            model.BaseCurrentMallId = Operater.CurrentMallId;//
            model.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/saveexperiment";
            var result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("保存实验信息 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(result));
            Log.ExperimentLog(log, result.flag);
            if (result.flag == 1)
            {
                return RedirectToAction("List");
            }
            else
            {
                //删除微视频
                UploadOss.DeleteObject(model.VideoUrl);
                if (result.flag == -1)
                {
                    return this.ApiTimeOut();
                }
                else if (result.flag == -2)
                {
                    return this.ReturnHome();
                }
                else
                {
                    ModelState.AddModelError("Error", result.msg);
                    string courseUrl = Constant.ApiPath + "course/getcourseall";
                    var courseList = await WebApiHelper.SendAsync<List<SelectListViewModel>>(courseUrl, null, CommonTypes.CommonJsonSendType.GET);
                    model.CourseList = courseList;
                    return View("Edit", model);
                }
            }
        }

        /// <summary>
        /// 删除实验
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> DeleteExperiment(long id)
        {
            OptExperimentInputModel model = new OptExperimentInputModel
            {
                BaseCurrentMallId = Operater.CurrentMallId,
                BaseUnitId = Operater.UnitId,
                BaseUserId = Operater.UserId,
                Token = Operater.Token,
                ExperimentId = id
            };
            string url = Constant.ApiPath + "experiment/delete";
            var result = await WebApiHelper.SendAsync(url, model);
            //调用日志
            string log = string.Format("删除实验 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(result));
            Log.ExperimentLog(log, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 单个推荐实验
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> RecommendExperiment(long id)
        {
            PRecommendInputModel model = new PRecommendInputModel();
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.ObjId = id;
            model.ObjType = RecommendObjType.Experiment;
            model.MarketType = RecommendMarketType.Recommend;
            model.Statuz = RecommendStatuz.WaitAudit;

            string url = Constant.ApiPath + "recommend/apply";
            var result = await WebApiHelper.SendAsync(url, model);
            Log.Supplier(
                         new
                         {
                             User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                             Memo = "申请推荐实验",
                             Action = "recommend/apply",
                             Param = model,
                             Result = result
                         }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// 批量推荐实验
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> RecommendExperimentBatch(List<long> ids)
        {
            PRecommendInputModel model = new PRecommendInputModel();
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.ObjIdList = ids;
            model.ObjType = RecommendObjType.Experiment;
            model.MarketType = RecommendMarketType.Recommend;
            model.Statuz = RecommendStatuz.WaitAudit;

            string url = Constant.ApiPath + "recommend/batchapply";
            var result = await WebApiHelper.SendAsync(url, model);
            Log.Supplier(new
            {
                User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                Memo = "申请推荐实验",
                Action = "recommend/batchapply",
                Param = model,
                Result = result
            }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #region 视频管理


        /// <summary>
        /// 视频管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> VideoList(SearchVideoInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/searchvedio";
            var result = await WebApiHelper.SendAsync<QueryResult<TExperimentVideoViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 视频管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> VideoEdit(long id=0)
        {
            if (Operater == null)
            {
                return ApiTimeOut();
            }
            TExperimentVideoInputModel model = new TExperimentVideoInputModel();
            if (id > 0)
            {
                //编辑页面
                string url = Constant.ApiPath + "experiment/getvideomodel?id=" + id + "&token=" + Operater.Token;
                var result = await WebApiHelper.SendAsync<QueryResult<TExperimentVideoInputModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
                if (result.flag == -1)
                {
                    return ApiTimeOut();
                }
                else if (result.flag == -2)
                {
                    return ReturnHome();
                }
                else if (result.flag == 0)
                {
                    model = null;
                }
                else if (result.flag == 1)
                {
                    model = result.Entity;
                }
            } 
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId; 
            return View("_VideoEdit",model);
        }

        /// <summary>
        /// 保存，添加修改
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveVideo(TExperimentVideoInputModel model)
        {
            ReturnResult r = new ReturnResult();
            string errMsg = "";
           
            if (string.IsNullOrWhiteSpace(model.VideoName))
            {
                errMsg += "请填写实验视频名称</br>";
            }
            if (string.IsNullOrWhiteSpace(model.VideoUrl))
            {
                errMsg += "请上传实验视频</br>";               
            }
            //if (model.Sort ==null)
            //{
            //    errMsg += "请正确填写视频排序，顺序必须不小于</br>";
            //}
            if (errMsg.Length >0 )
            {
                r.flag = 0;
                r.msg = errMsg;
                return Json(r, JsonRequestBehavior.AllowGet); 
            }
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/savevideo";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("保存实验视频 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(r));
            Log.ExperimentLog(log, r.flag); 
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 设置启用、禁用
        /// </summary>
        /// <param name="id">视频标识Id</param>
        /// <param name="statuz">启用、禁用状态</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SetStatuz(int id, int statuz)
        {
            ReturnResult r = new ReturnResult();
            //string errMsg = ""; 
            TExperimentVideoInputModel model = new TExperimentVideoInputModel();
            model.ExperimentVideoId = id;
            model.Statuz = statuz;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/setvideostatuz";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("启用禁用实验视频 \r\n 操作人：【{0}-{1}】；数据信息：【id:{2},statuz:{3}】，执行结果：【{4}】", Operater.UnitId, Operater.UserId, id,statuz, ComLib.Object2JSON(r));
            Log.ExperimentLog(log, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 删除视频信息
        /// </summary>
        /// <param name="id">视频标识Id</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> DeleteVideo(int id)
        {
            ReturnResult r = new ReturnResult();
            //string errMsg = ""; 
            TExperimentVideoInputModel model = new TExperimentVideoInputModel();
            model.ExperimentVideoId = id;
            model.Statuz = -1;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/deletevideo";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("删除实验视频 \r\n 操作人：【{0}-{1}】；数据信息：【id:{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, id, ComLib.Object2JSON(r));
            Log.ExperimentLog(log, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 获取分类信息
        /// </summary>
        /// <param name="statuz">0：大于0 1：等于1</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> GetVideoCategory(int statuz)
        {
            ReturnResult r = new ReturnResult();
            //string errMsg = ""; 
            TExperimentVideoInputModel model = new TExperimentVideoInputModel();
            model.Statuz = statuz;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            string url = Constant.ApiPath + "experiment/getvideocategory";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("获取实验视频分类 \r\n 操作人：【{0}-{1}】；执行结果：【{2}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(r));
            Log.ExperimentLog(log, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }
        #endregion
    }
}