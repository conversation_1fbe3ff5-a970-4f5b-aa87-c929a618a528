@{
    ViewBag.Title = "SSL连接测试工具";
}

<div class="container">
    <h2>SSL/TLS连接测试工具</h2>
    <p class="text-muted">用于诊断和测试通过nginx代理的HTTPS API连接问题</p>
    
    <div class="row">
        <div class="col-md-8">
            <!-- 快速测试 -->
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h4>快速测试</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label>域名:</label>
                        <input type="text" id="domain" class="form-control" value="jzzxmall.czedu.cn" placeholder="输入域名">
                    </div>
                    <div class="form-group">
                        <label>用户ID:</label>
                        <input type="number" id="userId" class="form-control" value="0" placeholder="输入用户ID">
                    </div>
                    <button type="button" class="btn btn-primary" onclick="quickTest()">
                        <i class="fa fa-play"></i> 快速测试
                    </button>
                </div>
            </div>

            <!-- 自定义测试 -->
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4>自定义API测试</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label>API地址:</label>
                        <input type="text" id="apiUrl" class="form-control" 
                               value="https://jzzxmall.czedu.cn/api/api/main/index?domain=jzzxmall.czedu.cn&userid=0" 
                               placeholder="输入完整的API地址">
                    </div>
                    <button type="button" class="btn btn-info" onclick="testConnection()">
                        <i class="fa fa-test"></i> 测试连接
                    </button>
                </div>
            </div>

            <!-- SSL诊断 -->
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h4>SSL诊断</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label>诊断URL:</label>
                        <input type="text" id="diagnoseUrl" class="form-control" 
                               value="https://jzzxmall.czedu.cn" 
                               placeholder="输入要诊断的URL">
                    </div>
                    <button type="button" class="btn btn-warning" onclick="diagnoseSSL()">
                        <i class="fa fa-search"></i> 诊断SSL
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 系统信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>系统信息</h4>
                </div>
                <div class="panel-body">
                    <button type="button" class="btn btn-default btn-block" onclick="getSslInfo()">
                        <i class="fa fa-info"></i> 获取SSL配置信息
                    </button>
                    <button type="button" class="btn btn-success btn-block" onclick="reinitializeSSL()">
                        <i class="fa fa-refresh"></i> 重新初始化SSL
                    </button>
                </div>
            </div>

            <!-- 操作说明 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>操作说明</h4>
                </div>
                <div class="panel-body">
                    <ol>
                        <li>首先点击"重新初始化SSL"确保配置正确</li>
                        <li>使用"快速测试"测试默认API</li>
                        <li>如果失败，使用"SSL诊断"查看详细信息</li>
                        <li>根据诊断结果调整配置</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 结果显示区域 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>测试结果</h4>
                    <button type="button" class="btn btn-xs btn-default pull-right" onclick="clearResults()">
                        清空结果
                    </button>
                </div>
                <div class="panel-body">
                    <pre id="results" style="max-height: 500px; overflow-y: auto; background-color: #f5f5f5;">
点击上方按钮开始测试...
                    </pre>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showLoading() {
    document.getElementById('results').textContent = '正在测试，请稍候...';
}

function showResult(data) {
    var results = document.getElementById('results');
    var timestamp = new Date().toLocaleString();
    
    if (data.success) {
        results.textContent = '[' + timestamp + '] ' + data.message + '\n\n' + data.result;
    } else {
        results.textContent = '[' + timestamp + '] 错误: ' + data.message + '\n\n' + data.result;
    }
}

function quickTest() {
    showLoading();
    var domain = document.getElementById('domain').value;
    var userId = document.getElementById('userId').value;
    
    $.post('/SslTest/QuickTest', {
        domain: domain,
        userId: userId
    }, function(data) {
        showResult(data);
    }).fail(function(xhr) {
        showResult({
            success: false,
            message: '请求失败',
            result: xhr.responseText || '网络错误'
        });
    });
}

function testConnection() {
    showLoading();
    var apiUrl = document.getElementById('apiUrl').value;
    
    $.post('/SslTest/TestConnection', {
        apiUrl: apiUrl
    }, function(data) {
        showResult(data);
    }).fail(function(xhr) {
        showResult({
            success: false,
            message: '请求失败',
            result: xhr.responseText || '网络错误'
        });
    });
}

function diagnoseSSL() {
    showLoading();
    var url = document.getElementById('diagnoseUrl').value;
    
    $.post('/SslTest/DiagnoseSSL', {
        url: url
    }, function(data) {
        showResult(data);
    }).fail(function(xhr) {
        showResult({
            success: false,
            message: '请求失败',
            result: xhr.responseText || '网络错误'
        });
    });
}

function getSslInfo() {
    showLoading();
    
    $.get('/SslTest/GetSslInfo', function(data) {
        var result = 'SSL配置信息:\n\n';
        result += '初始化状态: ' + (data.IsInitialized ? '已初始化' : '未初始化') + '\n';
        result += '支持的协议: ' + data.SupportedProtocols + '\n';
        result += '当前时间: ' + data.CurrentTime + '\n\n';
        result += data.Recommendations;
        
        showResult({
            success: true,
            message: 'SSL信息获取成功',
            result: result
        });
    }).fail(function(xhr) {
        showResult({
            success: false,
            message: '获取SSL信息失败',
            result: xhr.responseText || '网络错误'
        });
    });
}

function reinitializeSSL() {
    showLoading();
    
    $.post('/SslTest/ReinitializeSSL', {}, function(data) {
        showResult(data);
    }).fail(function(xhr) {
        showResult({
            success: false,
            message: '重新初始化失败',
            result: xhr.responseText || '网络错误'
        });
    });
}

function clearResults() {
    document.getElementById('results').textContent = '结果已清空，点击上方按钮开始测试...';
}
</script>
