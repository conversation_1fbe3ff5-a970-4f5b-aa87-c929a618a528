﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    /// <summary>
    /// 申报模块
    /// </summary>
    public class ProductDeclareController : ControllerMember
    {
        //已申报页面缓存KEY
        private string CACHE_DECLARE = "DECLARE_CACHE";
        //已取消页面缓存KEY
        private string CACHE_CANCELED = "CANCELED_CACHE";
        //待评价页面缓存KEY
        private string CACHE_EVALUATE = "EVALUATE_CACHE";

        /// <summary>
        /// 已申报产品列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> DeclareList(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = "productdeclare/declarelist";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductDeclareViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            //查找缓存
            CACHE_DECLARE = CACHE_DECLARE + "_" + Operater.UserId.ToString();
            List<PageCacheInputModel> list = CacheOperationUtility.GetCache(CACHE_DECLARE);
            List<long> CacheIds = new List<long>();
            decimal CacheSum = 0;
            list.ForEach(f => { CacheIds.Add(f.Id); CacheSum += f.Sum; });
            ViewBag.CacheIds = CacheIds;
            ViewBag.CacheSum = CacheSum;
            return View(result);
        }

        /// <summary>
        /// 批量再次购买
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<JsonResult> DeclareAgainBuy(int pageType)
        {
            ReturnResult r = new ReturnResult();
            r.flag = 0;
            r.msg = "请选择需要操作的数据。";
            string cacheKey = "";
            if (pageType == 1)
            {
                cacheKey = CACHE_DECLARE + "_" + Operater.UserId;
            }
            else
            {
                cacheKey = CACHE_CANCELED + "_" + Operater.UserId;
            }
            List<PageCacheInputModel> list = CacheOperationUtility.GetCache(cacheKey);
            if (list.Count > 0)
            {
                List<long> ids = new List<long>();
                list.ForEach(f => { ids.Add(f.Id); });
                ProductDeclareOperationInputModel model = new ProductDeclareOperationInputModel
                {
                    ProductDeclareIds = ids,
                    BaseUnitId = Operater.UnitId,//
                    BaseUserId = Operater.UserId,//
                    Token = Operater.Token,
                    BaseCurrentMallId = Operater.CurrentMallId
                };
                string url = Constant.ApiPath + "productdeclare/againbuy";
                r = await WebApiHelper.SendAsync(url, model);
                //添加日志
                string log = string.Format("再次购买 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(r));
                Log.DeclareLog(log,r.flag);
                if (r.flag == 1)
                {
                    //销毁缓存
                    CacheOperationUtility.DestroyCache(cacheKey);
                }
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  单个再次购买
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<JsonResult> DeclareAggainBuyOne(List<long> ids)
        {
            ProductDeclareOperationInputModel model = new ProductDeclareOperationInputModel
            {
                ProductDeclareIds = ids,
                BaseUnitId = Operater.UnitId,//
                BaseUserId = Operater.UserId,//
                Token = Operater.Token,
                BaseCurrentMallId = Operater.CurrentMallId
            };
            string url = Constant.ApiPath + "productdeclare/againbuy";
            var r = await WebApiHelper.SendAsync(url, model);
            //添加日志
            string log = string.Format("再次购买 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(r));
            Log.DeclareLog(log,r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 已取消产品列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> CanceledList(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = "productdeclare/canceledlist";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductDeclareViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            //查找缓存
            CACHE_CANCELED = CACHE_CANCELED + "_" + Operater.UserId.ToString();
            List<PageCacheInputModel> list = CacheOperationUtility.GetCache(CACHE_CANCELED);
            List<long> CacheIds = new List<long>();
            decimal CacheSum = 0;
            list.ForEach(f => { CacheIds.Add(f.Id); CacheSum += f.Sum; });
            ViewBag.CacheIds = CacheIds;
            ViewBag.CacheSum = CacheSum;
            return View(result);
        }


        /// <summary>
        /// 待评价产品列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> WaitEvaluateList(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = "productdeclare/evaluatelist";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductWaitEvaluationViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            //查找缓存
            CACHE_EVALUATE = CACHE_EVALUATE + "_" + Operater.UserId.ToString();
            List<PageCacheInputModel> list = CacheOperationUtility.GetCache(CACHE_EVALUATE);
            List<long> CacheIds = new List<long>();
            decimal CacheSum = 0;
            list.ForEach(f => { CacheIds.Add(f.Id); CacheSum += f.Sum; });
            ViewBag.CacheIds = CacheIds;
            return View(result);
        }

        /// <summary>
        ///  评价页面
        /// </summary>
        /// <param name="productDeclareIds"></param>
        /// <returns></returns>
        public ActionResult Evaluation(string backUrl, long productDeclareId = 0)
        {
            if (productDeclareId > 0)
            {
                //如果是点击的单条评价，销毁所有选中的缓存
                CacheOperationUtility.DestroyCache(CACHE_EVALUATE + "_" + Operater.UserId.ToString());
                List<PageCacheInputModel> cache = new List<PageCacheInputModel>();
                cache.Add(new PageCacheInputModel { Id = productDeclareId });
                NextPageCache_Evaluate(cache, true);
            }
            ProductEvaluateInputModel model = new ProductEvaluateInputModel();
            model.IsAnonymous = true;
            if (!ComLib.IsValidUrl(backUrl, Request.Url.Host))
            {
                backUrl = "~/";
            }
            ViewBag.BackUrl = backUrl;
            return View(model);
        }

        /// <summary>
        /// 评价
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<JsonResult> ProductEvaluate(ProductEvaluateInputModel model)
        {
            ReturnResult r = new ReturnResult();
            CACHE_EVALUATE = CACHE_EVALUATE + "_" + Operater.UserId.ToString();
            //读取缓存
            List<PageCacheInputModel> list = CacheOperationUtility.GetCache(CACHE_EVALUATE);
            if (list.Count > 0)
            {
                model.BaseCurrentMallId = Operater.CurrentMallId;
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                List<string> orderDetailIdList = new List<string>();
                list.ForEach(f => orderDetailIdList.Add(f.Id.ToString()));
                model.OrderDetailIds = string.Join(",", orderDetailIdList);
                string url = Constant.ApiPath + "productdeclare/evaluate";
                r = await WebApiHelper.SendAsync(url, model);
                string log = string.Format("产品评价 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(r));
                Log.DeclareLog(log,r.flag);
                if (r.flag == 1)
                {
                    //销毁缓存
                    CacheOperationUtility.DestroyCache(CACHE_EVALUATE);
                }
            }
            else
            {
                r.flag = 0;
                r.msg = "请选择需要操作的数据。";
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  勾选存入缓存，已申报页面
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isChecked"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult NextPageCache_Declared(List<PageCacheInputModel> models, bool isChecked)
        {
            CACHE_DECLARE = CACHE_DECLARE + "_" + Operater.UserId;
            var r = CacheOperationUtility.UpdateCache(CACHE_DECLARE, models, isChecked);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  勾选存入缓存，已取消页面
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isChecked"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult NextPageCache_Canceled(List<PageCacheInputModel> models, bool isChecked)
        {
            CACHE_CANCELED = CACHE_CANCELED + "_" + Operater.UserId;
            var r = CacheOperationUtility.UpdateCache(CACHE_CANCELED, models, isChecked);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  勾选存入缓存，待评价页面
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isChecked"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult NextPageCache_Evaluate(List<PageCacheInputModel> models, bool isChecked)
        {
            CACHE_EVALUATE = CACHE_EVALUATE + "_" + Operater.UserId;
            var r = CacheOperationUtility.UpdateCache(CACHE_EVALUATE, models, isChecked);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  再次购买确认列表
        /// </summary>
        /// <param name="pageType">页面类型，1：已申报页面，2：已取消页面</param>
        /// <returns></returns>
        public async Task<ActionResult> ConfirmList_AgainBuy(int pageType)
        {
            string cacheKey = "";
            if (pageType == 1)
            {
                cacheKey = CACHE_DECLARE + "_" + Operater.UserId.ToString();
            }
            else
            {
                cacheKey = CACHE_CANCELED + "_" + Operater.UserId.ToString();
            }
            QueryResult<ProductDeclareViewModel> r = new QueryResult<ProductDeclareViewModel>();
            r.flag = 0;
            List<PageCacheInputModel> cacheList = CacheOperationUtility.GetCache(cacheKey);
            if (cacheList.Count > 0)
            {
                SearchCacheDataInputModel args = new SearchCacheDataInputModel();
                args.UserId = Operater.UserId;
                args.UnitId = Operater.UnitId;
                args.MallId = Operater.CurrentMallId;
                args.Token = Operater.Token;
                List<string> strIds = new List<string>();
                cacheList.ForEach(f => { strIds.Add(f.Id.ToString()); });
                args.Ids = string.Join(",", strIds);
                string url = Constant.ApiPath + "productdeclare/confirmlistagainbuy";
                r = await WebApiHelper.SendAsync<QueryResult<ProductDeclareViewModel>>(url, args);
                r.flag = 1;
            }
            ViewBag.PageType = pageType;
            return View(r);
        }

        /// <summary>
        ///  再次购买判断是否选中值
        /// </summary>
        /// <param name="pageType">页面类型，1：已申报页面，2：已取消页面，3：待评价页面</param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult RedirectAgainBuy(int pageType)
        {
            string cacheKey = "";
            if (pageType == 1)
            {
                cacheKey = CACHE_DECLARE + "_" + Operater.UserId.ToString();
            }
            else if (pageType == 2)
            {
                cacheKey = CACHE_CANCELED + "_" + Operater.UserId.ToString();
            }
            else
            {
                cacheKey = CACHE_EVALUATE + "_" + Operater.UserId.ToString();
            }
            ReturnResult r = new ReturnResult();
            r.flag = 0;
            r.msg = "请选择需要操作的数据。";

            List<PageCacheInputModel> list = CacheOperationUtility.GetCache(cacheKey);
            if (list.Count > 0)
            {
                r.flag = 1;
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  删除缓存中的值
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public ActionResult DeleteCache(long Id, int PageType)
        {
            string cacheKey = "";
            if (PageType == 1)
            {
                cacheKey = CACHE_DECLARE + "_" + Operater.UserId.ToString();
            }
            else
            {
                cacheKey = CACHE_CANCELED + "_" + Operater.UserId.ToString();
            }
            var cacheData = HttpRuntime.Cache.Get(cacheKey);
            if (cacheData != null)
            {
                List<PageCacheInputModel> list = (List<PageCacheInputModel>)cacheData;
                var model = list.Where(f => f.Id == Id).ToList();
                bool isSuccess = false;
                if (model.Count > 0)
                {
                    isSuccess = list.Remove(model.FirstOrDefault());
                }
                if (isSuccess)
                {
                    return Redirect("/Member/ProductDeclare/ConfirmList_AgainBuy?pageType=" + PageType);
                }
            }
            return View();
        }

        /// <summary>
        ///  评价确认列表
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> ConfirmList_Evaluate()
        {
            QueryResult<ProductWaitEvaluationViewModel> r = new QueryResult<ProductWaitEvaluationViewModel>();
            r.flag = 0;
            CACHE_EVALUATE = CACHE_EVALUATE + "_" + Operater.UserId.ToString();
            List<PageCacheInputModel> cacheList = CacheOperationUtility.GetCache(CACHE_EVALUATE);
            if (cacheList.Count > 0)
            {
                SearchCacheDataInputModel args = new SearchCacheDataInputModel();
                args.UserId = Operater.UserId;
                args.UnitId = Operater.UnitId;
                args.MallId = Operater.CurrentMallId;
                args.Token = Operater.Token;
                List<string> strIds = new List<string>();
                cacheList.ForEach(f => { strIds.Add(f.Id.ToString()); });
                args.Ids = string.Join(",", strIds);
                string url = Constant.ApiPath + "productdeclare/confirmlistevaluate";
                r = await WebApiHelper.SendAsync<QueryResult<ProductWaitEvaluationViewModel>>(url, args);
                r.flag = 1;
            }
            return View("ConfirmList_Evaluate", r);
        }

        /// <summary>
        ///  删除缓存中的值
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult DeleteCache_Evaluate(long Id)
        {
            ReturnResult r = new ReturnResult();
            r.flag = 0;
            r.msg = "删除失败。";
            CACHE_EVALUATE = CACHE_EVALUATE + "_" + Operater.UserId.ToString();
            var cacheData = HttpRuntime.Cache.Get(CACHE_EVALUATE);
            if (cacheData != null)
            {
                List<PageCacheInputModel> list = (List<PageCacheInputModel>)cacheData;
                var model = list.Where(f => f.Id == Id).ToList();
                bool isSuccess = false;
                if (model.Count > 0)
                {
                    isSuccess = list.Remove(model.FirstOrDefault());
                }
                if (isSuccess)
                {
                    HttpRuntime.Cache.Insert(CACHE_EVALUATE, list);
                    r.flag = 1;
                }
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 已申报产品清单导出
        /// </summary>
        /// <param name="year"></param>
        /// <returns></returns>
        public async Task<FileResult> OutputExecl(int year = 0)
        {
            SearchDeclareListInputModel args = new SearchDeclareListInputModel();
            //查找缓存
            CACHE_DECLARE = CACHE_DECLARE + "_" + Operater.UserId.ToString();
            List<PageCacheInputModel> listPage = CacheOperationUtility.GetCache(CACHE_DECLARE);
            string ids = "";
            listPage.ForEach(f => { ids += f.Id + ","; });
            ids = ids.TrimEnd(',');
            //先获取数据
            args.SearchDeclareIds = ids;
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.Limit = 1000000;
            args.Year = year;
            string url = Constant.ApiPath + "productdeclare/declarelist";
            QueryResult<ProductDeclareViewModel> listResult = await WebApiHelper.SendAsync<QueryResult<ProductDeclareViewModel>>(url, args);

            if (listResult.flag > -1)
            {
                ICell cell = null;
                HSSFWorkbook book = new HSSFWorkbook();
                ISheet sheet = book.CreateSheet("已申报产品清单");

                IFont font = book.CreateFont();
                IFont font1 = book.CreateFont();
                font.IsBold = true;
                font1.FontHeightInPoints = 9;
                ICellStyle cellstyle = book.CreateCellStyle();
                cellstyle.BorderTop = BorderStyle.Thin;
                cellstyle.BorderLeft = BorderStyle.Thin;
                cellstyle.BorderRight = BorderStyle.Thin;
                cellstyle.BorderBottom = BorderStyle.Thin;
                cellstyle.Alignment = HorizontalAlignment.Center;
                cellstyle.VerticalAlignment = VerticalAlignment.Center;
                cellstyle.SetFont(font);

                //居中显示
                ICellStyle cellstyle2 = book.CreateCellStyle();
                cellstyle2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle2.SetFont(font1);

                //居左显示
                ICellStyle cellstyle3 = book.CreateCellStyle();
                cellstyle3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellstyle3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle3.SetFont(font1);

                //居右显示
                ICellStyle cellstyle1 = book.CreateCellStyle();
                cellstyle1.BorderTop = BorderStyle.Thin;
                cellstyle1.BorderLeft = BorderStyle.Thin;
                cellstyle1.BorderRight = BorderStyle.Thin;
                cellstyle1.BorderBottom = BorderStyle.Thin;
                cellstyle1.Alignment = HorizontalAlignment.Right;
                cellstyle1.VerticalAlignment = VerticalAlignment.Center;
                cellstyle1.DataFormat = book.CreateDataFormat().GetFormat("0.00");
                cellstyle1.SetFont(font1);

                //添加标题
                List<ExeclFeildHeadViewModel> listHead = new List<ExeclFeildHeadViewModel>();
                List<ExeclFeildHeadViewModel> listGet = ExeclHead.GetHeadProductDeclare();
                listHead.AddRange(listGet);

                CellRangeAddress regionTitle = new CellRangeAddress(0, 0, 0, listHead.Count);
                sheet.AddMergedRegion(regionTitle);

                IRow row1 = sheet.CreateRow(0);
                row1.HeightInPoints = 34;
                cell = row1.CreateCell(0);
                cell.CellStyle = cellstyle;
                cell.SetCellValue(string.Format("{0}--已申报产品清单", Operater.UnitName));

                #region 区县、市级增加学校名称
                //if (Operater.UnitType == 1 || Operater.UnitType == 2)
                //{
                //    listHead.Add(new ExeclFeildHeadViewModel { Head = "学校名称", Width = 20 });
                //}
                #endregion

                row1 = sheet.CreateRow(1);
                row1.HeightInPoints = 25;
                cell = row1.CreateCell(0);
                cell.CellStyle = cellstyle;
                cell.SetCellValue("序号");

                for (int i = 0; i < listHead.Count; i++)
                {
                    cell = row1.CreateCell(i + 1);
                    cell.CellStyle = cellstyle;
                    sheet.SetColumnWidth(i + 1, listHead[i].Width * 256);
                    cell.SetCellValue(listHead[i].Head);
                }

                int k = 0;
                IList<ProductDeclareViewModel> list = listResult.Data;
                for (int i = 0; i < list.Count; i++)
                {
                    k = 0;
                    IRow rowTemp = sheet.CreateRow(i + 2);
                    rowTemp.HeightInPoints = 20;

                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(i + 1);
                    k++;

                    #region 区县、市级增加学校名称
                    //if (Operater.UnitType == 1 || Operater.UnitType == 2)
                    //{
                    //    cell = rowTemp.CreateCell(k);
                    //    cell.CellStyle = cellstyle3;
                    //    cell.SetCellValue(list[i].SchoolName);
                    //    k++;
                    //}
                    #endregion


                    //编号
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].Code);
                    k++;

                    //产品名称
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].ProductName);
                    k++;

                    //品牌
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].Brand);
                    k++;

                    //规格型号
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].ModelDescription);
                    k++;

                    //学科
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].CourseName);
                    k++;

                    //数量
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle1;
                    cell.SetCellValue((double)list[i].Num);
                    k++;

                    //单位
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle2;
                    cell.SetCellValue(list[i].UnitName);
                    k++;

                    //单价
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle1;
                    cell.SetCellValue((double)list[i].Price);
                    k++;

                    //金额
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle1;
                    cell.SetCellValue((double)list[i].Sum);
                    k++;

                    //状态
                    var statuzdesc = "";
                    if (list[i].OrderStatuz > 0)
                    {
                        statuzdesc = list[i].OrderStatuz.GetEnumDescription();
                    }
                    else
                    {
                        if (list[i].AuditStatuz == DeclareAuditStatuz.CityReturnDeclare || list[i].AuditStatuz == DeclareAuditStatuz.CountyReturnDeclare || list[i].AuditStatuz == DeclareAuditStatuz.SchoolReturnDeclare)
                        {
                            statuzdesc = "订单生成失败";
                        }
                        else if (list[i].AuditStatuz == DeclareAuditStatuz.SchoolNoPass || list[i].AuditStatuz == DeclareAuditStatuz.CountyNoPass
                                                       || list[i].AuditStatuz == DeclareAuditStatuz.CityNoPass)
                        {
                            //需要改变颜色
                            statuzdesc = list[i].AuditStatuz.GetEnumDescription();
                        }
                        else
                        {
                            statuzdesc = list[i].AuditStatuz.GetEnumDescription();
                        }
                    }
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle2;
                    cell.SetCellValue(statuzdesc);
                    k++;

                    //申报时间
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle2;
                    cell.SetCellValue(list[i].DeclareTime.ToString("yyyy-MM-dd HH:mm:ss"));
                }
                //写入客户端
                MemoryStream ms = new MemoryStream();
                book.Write(ms);
                ms.Seek(0, SeekOrigin.Begin);
                return File(ms, "application/vnd.ms-excel", "已申报的产品清单.xls");
            }
            else
            {
                return File("", "", "已申报的产品清单.xls");
            }
        }
    }
}