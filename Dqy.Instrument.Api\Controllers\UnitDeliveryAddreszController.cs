﻿using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Api.Filters;
using Dqy.Instrument.CloudMarketing.Service;
using Dqy.Instrument.CloudMarketing.Entity;

namespace Dqy.Instrument.Api.Controllers
{
    [RoutePrefix("api/unitaddress")]
    public class UnitDeliveryAddreszController : ApiController
    {
        private IUUnitDeliveryAddreszApplicationService _iUUnitDeliveryAddreszApplicationService;

        public UnitDeliveryAddreszController(IUUnitDeliveryAddreszApplicationService iUUnitDeliveryAddreszApplicationService)
        {
            _iUUnitDeliveryAddreszApplicationService = iUUnitDeliveryAddreszApplicationService;
        }

        #region 周跃峰(收货地址管理)
        /// <summary>
        /// 保存收货地址
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postadd")]
        [ValidateModel]
        public async Task<ReturnResult> PostAdd(UnitDeliveryAddreszInputModel u)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(u.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != u.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
               sessionBag.UserType != UserRoleType.CityAdmin.ToInt() &&
               sessionBag.UserType!=UserRoleType.CityAuditor.ToInt()&&
               sessionBag.UserType != UserRoleType.CountyAdmin.ToInt()&&
               sessionBag.UserType!=UserRoleType.CountyAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面。";
                    return r;
                }
                r = _iUUnitDeliveryAddreszApplicationService.Add(u);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 删除收货地址
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getdel")]
        public async Task<ReturnResult> GetDel(string token, long Id, long userId, long unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId || sessionBag.UserId != userId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
                sessionBag.UserType != UserRoleType.CityAdmin.ToInt() &&
                sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
                sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() &&
                sessionBag.UserType != UserRoleType.CountyAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面。";
                    return r;
                }
                var u = _iUUnitDeliveryAddreszApplicationService.GetModel(Id);
                if (u != null)
                {                   
                    if (u.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "您无权删除其它单位地址";
                        return r;
                    }
                    r = _iUUnitDeliveryAddreszApplicationService.Del(Id, userId, unitId);
                }
                
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取待修改的收货地址
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbyid")]
        public async Task<ReturnResult> GetById(string token, long id, long userId, long unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId || sessionBag.UserId != userId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
              sessionBag.UserType != UserRoleType.CityAdmin.ToInt() &&
              sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
              sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() &&
              sessionBag.UserType != UserRoleType.CountyAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面。";
                    return r;
                }
                r = _iUUnitDeliveryAddreszApplicationService.GetById(id, userId, unitId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取收货地址列表
        /// </summary>
        /// <param name="token"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getlist")]
        public async Task<ReturnResult> GetDeliveryAddressList(string token, long unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
               sessionBag.UserType != UserRoleType.CityAdmin.ToInt() &&
               sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
               sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() &&
               sessionBag.UserType != UserRoleType.CountyAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面。";
                    return r;
                }
                r = _iUUnitDeliveryAddreszApplicationService.GetDeliveryAddressList(unitId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 修改收货地址
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodify")]
        [ValidateModel]
        public async Task<ReturnResult> PostModify(UnitDeliveryAddreszInputModel u)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(u.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
                sessionBag.UserType != UserRoleType.CityAdmin.ToInt() &&
                sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
                sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() &&
                sessionBag.UserType != UserRoleType.CountyAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面。";
                    return r;
                }
                var uAddress = _iUUnitDeliveryAddreszApplicationService.GetModel(u.Id);
                if (uAddress != null)
                {
                    if (uAddress.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "您无权修改其它单位地址";
                        return r;
                    }
                    r = _iUUnitDeliveryAddreszApplicationService.Modify(u);
                }
                return r;

            });
            return result;
        }

        /// <summary>
        /// 设置默认收货地址
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsetdefault")]
        public async Task<ReturnResult> GetSetDefault(string token, long Id, long userId, long unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId || sessionBag.UserId != userId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
                    sessionBag.UserType != UserRoleType.CityAdmin.ToInt() &&
                    sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
                    sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() &&
                    sessionBag.UserType != UserRoleType.CountyAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面。";
                    return r;
                }

                var uAddress = _iUUnitDeliveryAddreszApplicationService.GetModel(Id);
                if (uAddress != null) 
                {
                    if (uAddress.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "您无权设置其它单位默认地址";
                        return r;
                    }
                    r = _iUUnitDeliveryAddreszApplicationService.SetDefault(Id, userId, unitId);
                }
                return r;
            });
            return result;
        }
        #endregion

    }
}
