﻿using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class UnitController : ControllerMember
    {
       
        /// <summary>
        /// 显示区县单位信息
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> CountyUnit()
        {
            //根据单位Id获取单位信息
            string url = $"unit/getcountyunit?token={Operater.Token}&unitId={Operater.UnitId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                return this.ApiTimeOut();
            }
            if (r.flag == -2 || r.flag==0)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            CountyCityInputModel sModel = ComLib.JSON2Object<CountyCityInputModel>(r.obj.ToString());
            return View(sModel);
        }

        /// <summary>
        /// 显示市级单位信息
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> CityUnit()
        {
            //根据单位Id获取单位信息
            string url = Constant.ApiPath + $"unit/getcityunit?token={Operater.Token}&unitId={Operater.UnitId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                return this.ApiTimeOut();
            }
            if (r.flag == -2 || r.flag == 0)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            CountyCityInputModel sModel = ComLib.JSON2Object<CountyCityInputModel>(r.obj.ToString());
            return View(sModel);
        }


        /// <summary>
        /// 保存区县单位信息
        /// </summary>
        /// <param name="countyInput"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveCountyUnit(CountyCityInputModel countyInput)
        {
            string url = Constant.ApiPath + "unit/postsavecounty";
            countyInput.Token = Operater.Token;
            var result = await WebApiHelper.SendAsync(url,countyInput);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 保存市级单位信息
        /// </summary>
        /// <param name="countyInput"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveCityUnit(CountyCityInputModel countyInput)
        {
            string url = Constant.ApiPath + "unit/postsavecity";
            countyInput.Token = Operater.Token;
            var result = await WebApiHelper.SendAsync(url, countyInput);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

    }
}