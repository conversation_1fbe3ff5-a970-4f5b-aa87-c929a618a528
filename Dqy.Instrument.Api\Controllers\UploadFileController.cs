﻿using Dqy.Instrument.Api.Common;
using Dqy.Instrument.Api.CommonLib;
using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.Api.Models;
using Dqy.Instrument.ApplicationServices;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Results;

namespace Dqy.Instrument.Api.Areas.HelpPage.Controllers
{
    [RoutePrefix("api/uploadfile")]
    public class UploadFileController : ApiController
    {
        private static readonly string ApiPath = ComLib.GetAppSetting<string>("Api.Path");
        private IUSupplierSchoolAuditApplicationService _iUSupplierSchoolAuditApplicationService;
        private IUAttachmentApplicationService _iUAttachmentApplicationService;
        private IUAttachmentTempApplicationService _iUAttachmentTempApplicationService;
        private readonly IBDictionaryApplicationService _dictionaryApplicationService;
        /// <summary>
        /// 
        /// </summary>
        public UploadFileController(IUSupplierSchoolAuditApplicationService iUSupplierSchoolAuditApplicationService, IUAttachmentApplicationService iUAttachmentApplicationService, IUAttachmentTempApplicationService iUAttachmentTempApplicationService,BDictionaryApplicationService iBDictionaryApplicationService)
        {
            _iUSupplierSchoolAuditApplicationService = iUSupplierSchoolAuditApplicationService;
            _iUAttachmentApplicationService = iUAttachmentApplicationService;
            _iUAttachmentTempApplicationService = iUAttachmentTempApplicationService;
            _dictionaryApplicationService = iBDictionaryApplicationService;
        }


      
        /// <summary>
        /// 处理文件上传
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("postfile")]
        public async Task<JsonResult<ReturnResult>> PostFile()
        {
            ReturnResult r = new ReturnResult();
            if (!Request.Content.IsMimeMultipartContent())
            {
                return Json(r);
            }
            string root = HttpContext.Current.Server.MapPath("~/UploadFile");
            var provider = new MultipartFormDataStreamProvider(root);
            DqyFileInfo ufi = new DqyFileInfo();
            try
            {
                await Request.Content.ReadAsMultipartAsync(provider);
                var token = provider.FormData["token"];
                long userId = long.Parse(provider.FormData["UserId"]);
                long unitId = long.Parse(provider.FormData["UnitId"]);
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return Json(r);
                }
                if (sessionBag.UserId != userId || sessionBag.UnitId != unitId)
                {
                    r.flag = 0;
                    r.msg = "您无权操作";
                    return Json(r);
                }

                var savePath = "UploadFile";
                var subdir = provider.FormData["subdir"];
                /*读取字典表配置校验上传目录以及文件类型是否合规*/
                var list = _dictionaryApplicationService.GetDictionaryList("4001");
                if (subdir != null)
                {
                    if (list.Count > 0)
                    {
                        //校验上传目录地址
                        DictionaryViewModel dic = list.FirstOrDefault(t => t.DicName == subdir && t.TypeCode == "4001");
                        if (dic == null)
                        {
                            r.flag = 0;
                            r.msg = "系统检测到您正恶意上传文件，已经阻止！";
                            return Json(r);
                        }
                        if (dic.DicValue != "")
                        {
                            string orfilename = StringFilter.FilterBadChar(provider.FileData[0].Headers.ContentDisposition.FileName).TrimStart('"').TrimEnd('"');
                            string fileExt = orfilename.Substring(orfilename.LastIndexOf('.')); //文件类型是否符合该目录地址允许上传的类型
                            if (!dic.DicValue.Contains(fileExt.ToLower()))
                            {
                                r.flag = 0;
                                r.msg = "非法文件，上传失败！";
                                return Json(r);
                            }
                        }
                    }
                    savePath = savePath + "/" + StringFilter.FilterBadChar(subdir);
                }
                if (subdir != null && (subdir.StartsWith(".") || subdir.StartsWith("/")))
                {
                    //ufi.error = "-1";
                    //ufi.msg = "系统检测到您正恶意上传文件，已经阻止！";
                    r.flag = 0;
                    r.msg = "系统检测到您正恶意上传文件，已经阻止！";
                    //r.data.rows = ufi;
                    return Json(r);
                }

                if (provider.FileData.Count > 0)
                {
                    ufi = FileUpload.Save(provider.FileData[0], savePath);
                    if (ufi.error == "-1")
                    {
                        r.flag = 0;
                        r.msg = ufi.msg;
                    }
                    else
                    {
                        r.flag = 1;
                        r.msg = "上传成功";

                        ufi.filePath = ApiPath + "/" + ufi.filePath;
                        ufi.filePathM = ApiPath + "/" + ufi.filePathM;
                        ufi.filePathS = ApiPath + "/" + ufi.filePathS;
                        r.data.rows = ufi;
                    }


                    //保存至临时表
                    UAttachmentTempInputModel attachTempInput = new UAttachmentTempInputModel();
                    attachTempInput.AttachPath = ufi.filePath;
                    attachTempInput.RegTime = DateTime.Now;
                    _iUAttachmentTempApplicationService.AddAttachTemp(attachTempInput);
                }


            }
            catch (Exception e)
            {
                r.msg = e.Message;
                return Json(r);
            }

            return Json(r);
        }


        /// <summary>
        /// 保存头像
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("postfilehead")]
        public async Task<JsonResult<ReturnResult>> PostFileHead()
        {
            ReturnResult r = new ReturnResult();
            if (!Request.Content.IsMimeMultipartContent())
            {
                return Json(r);
            }
            string root = HttpContext.Current.Server.MapPath("~/UploadFile");
            var provider = new MultipartFormDataStreamProvider(root);
            DqyFileInfo ufi = new DqyFileInfo();
            try
            {
                await Request.Content.ReadAsMultipartAsync(provider);
                var token = provider.FormData["token"];
                long userId = long.Parse(provider.FormData["UserId"]);
                long unitId = long.Parse(provider.FormData["UnitId"]);
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return Json(r);
                }
                if (sessionBag.UserId != userId || sessionBag.UnitId != unitId)
                {
                    r.flag = 0;
                    r.msg = "您无权操作";
                    return Json(r);
                }

                var savePath = "UploadFile";
                var subdir = provider.FormData["subdir"];
                /*读取字典表配置校验上传目录以及文件类型是否合规*/
                var list = _dictionaryApplicationService.GetDictionaryList("4001");
                if (subdir != null)
                {
                    if (list.Count > 0)
                    {
                        //校验上传目录地址
                        DictionaryViewModel dic = list.FirstOrDefault(t => t.DicName == subdir && t.TypeCode == "4001");
                        if (dic == null)
                        {
                            r.flag = 0;
                            r.msg = "系统检测到您正恶意上传文件，已经阻止！";
                            return Json(r);
                        }
                        if (dic.DicValue != "")
                        {
                            string orfilename = StringFilter.FilterBadChar(provider.FileData[0].Headers.ContentDisposition.FileName).TrimStart('"').TrimEnd('"');
                            string fileExt = orfilename.Substring(orfilename.LastIndexOf('.')); //文件类型是否符合该目录地址允许上传的类型
                            if (!dic.DicValue.Contains(fileExt.ToLower()))
                            {
                                r.flag = 0;
                                r.msg = "非法文件，上传失败！";
                                return Json(r);
                            }
                        }
                    }
                    savePath = savePath + "/" + StringFilter.FilterBadChar(subdir);
                }
                if (subdir != null && (subdir.StartsWith(".") || subdir.StartsWith("/")))
                {                  
                    r.flag = 0;
                    r.msg = "系统检测到您正恶意上传文件，已经阻止！";
                    //r.data.rows = ufi;
                    return Json(r);
                }

                if (provider.FileData.Count > 0)
                {
                    ufi = FileUpload.SaveHeadPhoto(provider.FileData[0], savePath);
                    if (ufi.error == "-1")
                    {
                        r.flag = 0;
                        r.msg = ufi.msg;
                    }
                    else
                    {
                        r.flag = 1;
                        r.msg = "上传成功";
                        ufi.filePath = ApiPath + "/" + ufi.filePath;
                        r.data.rows = ufi;
                    }
                 

                

                    //保存至临时表
                    UAttachmentTempInputModel attachTempInput = new UAttachmentTempInputModel();
                    attachTempInput.AttachPath = ufi.filePath;
                    attachTempInput.RegTime = DateTime.Now;
                    _iUAttachmentTempApplicationService.AddAttachTemp(attachTempInput);
                }

            }
            catch (System.Exception e)
            {
                r.msg = e.Message;
                return Json(r);
            }

            return Json(r);
        }

    }
}
