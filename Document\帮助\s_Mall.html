﻿ Name = entity.Name,
 Number = entity.Number,
 AreaId = entity.AreaId,
 AdminId = entity.AdminId,
 Logo = entity.Logo,
 ServiceArea = entity.ServiceArea,
 DomainName = entity.DomainName,
 Url = entity.Url,
 Statuz = entity.Statuz,
 CreateTime = entity.CreateTime,


 Name = model.Name,
 Number = model.Number,
 AreaId = model.AreaId,
 AdminId = model.AdminId,
 Logo = model.Logo,
 ServiceArea = model.ServiceArea,
 DomainName = model.DomainName,
 Url = model.Url,
 Statuz = model.Statuz,
 CreateTime = model.CreateTime,


 temp.Name = model.Name,
 temp.Number = model.Number,
 temp.AreaId = model.AreaId,
 temp.AdminId = model.AdminId,
 temp.Logo = model.Logo,
 temp.ServiceArea = model.ServiceArea,
 temp.DomainName = model.DomainName,
 temp.Url = model.Url,
 temp.Statuz = model.Statuz,
 temp.CreateTime = model.CreateTime,

 MallId = item.MallId,
 Name = item.Name,
 Number = item.Number,
 AreaId = item.AreaId,
 AdminId = item.AdminId,
 Logo = item.Logo,
 ServiceArea = item.ServiceArea,
 DomainName = item.DomainName,
 Url = item.Url,
 Statuz = item.Statuz,
 CreateTime = item.CreateTime,

public class MallInputModel
{
 [Display(Name = "Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "商城名称")] 
    public string Name {get; set; }
    
 [Display(Name = "商城编号")] 
    public string Number {get; set; }
    
 [Display(Name = "所属区域")] 
    public int AreaId {get; set; }
    
 [Display(Name = "管理员Id")] 
    public long AdminId {get; set; }
    
 [Display(Name = "商城logo")] 
    public string Logo {get; set; }
    
 [Display(Name = "服务范围( 类型 ：1：全国  2：全省  3：全市  4：全区)")] 
    public int ServiceArea {get; set; }
    
 [Display(Name = "商城域名")] 
    public string DomainName {get; set; }
    
 [Display(Name = "商城URL")] 
    public string Url {get; set; }
    
 [Display(Name = "状态")] 
    public int Statuz {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime CreateTime {get; set; }
    
 }
 
 public class MallViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 商城名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 商城编号
    /// </summary>
    public string Number {get; set; }
    
    /// <summary>
    /// 所属区域
    /// </summary>
    public int AreaId {get; set; }
    
    /// <summary>
    /// 管理员Id
    /// </summary>
    public long AdminId {get; set; }
    
    /// <summary>
    /// 商城logo
    /// </summary>
    public string Logo {get; set; }
    
    /// <summary>
    /// 服务范围( 类型 ：1：全国  2：全省  3：全市  4：全区)
    /// </summary>
    public int ServiceArea {get; set; }
    
    /// <summary>
    /// 商城域名
    /// </summary>
    public string DomainName {get; set; }
    
    /// <summary>
    /// 商城URL
    /// </summary>
    public string Url {get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Number, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Number, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AreaId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AreaId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入所属区域" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AdminId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AdminId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入管理员Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Logo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Logo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城logo" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ServiceArea, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ServiceArea, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入服务范围( 类型 ：1：全国  2：全省  3：全市  4：全区)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DomainName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DomainName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城域名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Url, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Url, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城URL" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreateTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreateTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
  




 { field: 'Name', title: '商城名称', sortable: true },
                 
 { field: 'Number', title: '商城编号', sortable: true },
                 
 { field: 'AreaId', title: '所属区域', sortable: true },
                 
 { field: 'AdminId', title: '管理员Id', sortable: true },
                 
 { field: 'Logo', title: '商城logo', sortable: true },
                 
 { field: 'ServiceArea', title: '服务范围( 类型 ：1：全国  2：全省  3：全市  4：全区)', sortable: true },
                 
 { field: 'DomainName', title: '商城域名', sortable: true },
                 
 { field: 'Url', title: '商城URL', sortable: true },
                 
 { field: 'Statuz', title: '状态', sortable: true },
                 
 { field: 'CreateTime', title: '创建时间', sortable: true },
                 
o.Name,                 
o.Number,                 
o.AreaId,                 
o.AdminId,                 
o.Logo,                 
o.ServiceArea,                 
o.DomainName,                 
o.Url,                 
o.Statuz,                 
o.CreateTime,                 
        
        $('#Name').val(d.data.rows.Name);          
        $('#Number').val(d.data.rows.Number);          
        $('#AreaId').val(d.data.rows.AreaId);          
        $('#AdminId').val(d.data.rows.AdminId);          
        $('#Logo').val(d.data.rows.Logo);          
        $('#ServiceArea').val(d.data.rows.ServiceArea);          
        $('#DomainName').val(d.data.rows.DomainName);          
        $('#Url').val(d.data.rows.Url);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#CreateTime').val(d.data.rows.CreateTime);          

 $('#th_Name').html(' 商城名称');               
 $('#th_Number').html(' 商城编号');               
 $('#th_AreaId').html(' 所属区域');               
 $('#th_AdminId').html(' 管理员Id');               
 $('#th_Logo').html(' 商城logo');               
 $('#th_ServiceArea').html(' 服务范围( 类型 ：1：全国  2：全省  3：全市  4：全区)');               
 $('#th_DomainName').html(' 商城域名');               
 $('#th_Url').html(' 商城URL');               
 $('#th_Statuz').html(' 状态');               
 $('#th_CreateTime').html(' 创建时间');               
 
 $('#tr_Name').hide();               
 $('#tr_Number').hide();               
 $('#tr_AreaId').hide();               
 $('#tr_AdminId').hide();               
 $('#tr_Logo').hide();               
 $('#tr_ServiceArea').hide();               
 $('#tr_DomainName').hide();               
 $('#tr_Url').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_CreateTime').hide();               

 , "Name" : name
 , "Number" : number
 , "AreaId" : areaId
 , "AdminId" : adminId
 , "Logo" : logo
 , "ServiceArea" : serviceArea
 , "DomainName" : domainName
 , "Url" : url
 , "Statuz" : statuz
 , "CreateTime" : createTime

 var name = $('#o_Name').val();
 var number = $('#o_Number').val();
 var areaId = $('#o_AreaId').val();
 var adminId = $('#o_AdminId').val();
 var logo = $('#o_Logo').val();
 var serviceArea = $('#o_ServiceArea').val();
 var domainName = $('#o_DomainName').val();
 var url = $('#o_Url').val();
 var statuz = $('#o_Statuz').val();
 var createTime = $('#o_CreateTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城编号' : '产品名称', d.data.rows.Number);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '所属区域' : '产品名称', d.data.rows.AreaId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '管理员Id' : '产品名称', d.data.rows.AdminId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城logo' : '产品名称', d.data.rows.Logo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '服务范围( 类型 ：1：全国  2：全省  3：全市  4：全区)' : '产品名称', d.data.rows.ServiceArea);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城域名' : '产品名称', d.data.rows.DomainName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城URL' : '产品名称', d.data.rows.Url);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.CreateTime);



