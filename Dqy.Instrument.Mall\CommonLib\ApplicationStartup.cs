using System;
using System.Web;

namespace Dqy.Instrument.Mall.CommonLib
{
    /// <summary>
    /// 应用程序启动配置类
    /// 用于在应用程序启动时进行必要的初始化配置
    /// </summary>
    public static class ApplicationStartup
    {
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 初始化应用程序配置
        /// 此方法应在Application_Start中调用
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            lock (_lockObject)
            {
                if (_isInitialized)
                    return;

                try
                {
                    // 配置SSL/TLS协议
                    SslHelper.ConfigureSslProtocols();

                    // 记录初始化信息
                    var protocolInfo = SslHelper.GetSupportedProtocols();
                    System.Diagnostics.Debug.WriteLine($"应用程序启动完成，{protocolInfo}");

                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    // 记录初始化错误
                    System.Diagnostics.Debug.WriteLine($"应用程序初始化失败: {ex.Message}");
                    
                    // 可以选择记录到日志文件
                    try
                    {
                        FileLog.SendExceptionLog($"应用程序初始化失败: {ex.Message}");
                    }
                    catch
                    {
                        // 忽略日志记录错误
                    }
                }
            }
        }

        /// <summary>
        /// 检查应用程序是否已初始化
        /// </summary>
        /// <returns></returns>
        public static bool IsInitialized()
        {
            return _isInitialized;
        }

        /// <summary>
        /// 重置初始化状态（主要用于测试）
        /// </summary>
        public static void Reset()
        {
            lock (_lockObject)
            {
                _isInitialized = false;
                SslHelper.ResetSslConfiguration();
            }
        }
    }

    /// <summary>
    /// HTTP模块，用于确保每个请求都有正确的SSL配置
    /// </summary>
    public class SslConfigurationModule : IHttpModule
    {
        public void Init(HttpApplication context)
        {
            // 确保应用程序已初始化
            ApplicationStartup.Initialize();

            // 可以在这里添加其他请求级别的配置
            context.BeginRequest += (sender, e) =>
            {
                // 每个请求开始时的处理逻辑
                // 通常不需要在这里重复配置SSL，因为ServicePointManager是全局的
            };
        }

        public void Dispose()
        {
            // 清理资源
        }
    }
}
