﻿using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Dqy.Instrument.UI.ViewModels.SearchViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Controllers
{
    public class SuppliersController : ControllerReception
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> List(MallDealerSearchInputModel args)
        {
            if (args == null)
            {
                args = new MallDealerSearchInputModel();
            }
            args.Limit = 20;
            args.MallId = MallId;

            ViewBag.Args = args;

            //获取列表信息
            string urlData = "supplier/postsupplierlist";
            var resultData = await WebApiHelper.SendAsync<DealerList>(urlData, args);
            return View(resultData);
        }

        public async Task<ActionResult> Detail(MallProductSearchInputModel args)
        {
            ViewBag.Tag = "暂无数据";
            args.Limit = 20;
            ViewBag.Args = args;
            string url = Constant.ApiPath + "Supplier/getsupplierdetail";
            try
            {
                var model = await WebApiHelper.SendAsync<SuppliersViewModel>(url,args);
                if (model != null)
                {
                    if (model.Supplier != null && !string.IsNullOrEmpty(model.Supplier.Desc))
                    {
                        model.Supplier.Desc = ComLib.NoHTML(model.Supplier.Desc);
                        if (model.Supplier.Desc.Length > 160)
                        {
                            model.Supplier.Desc = model.Supplier.Desc.Substring(0, 160) + "...";
                        }
                    }

                     ViewBag.Paging = PageStringUtilities.GetPage(args.CurrPage, args.Limit, model.Product.hits, Url.Action("Detail"), "");;
                    return View(model);
                }
                ViewBag.Tag = Constant.Request_Lose_Msg;

            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("供应商详情页请求异常:" + e.Message);
                ViewBag.Tag = Constant.Request_Ext_Msg;
            }
            return View();
        }

        public async Task<ActionResult> Desc(long id, int mallId)
        {
            ViewBag.Tag = "暂无信息";
            string url = Constant.ApiPath + "Supplier/getsupplierdesc?id=" + id + "&mallId=" + mallId;
            try
            {
                var model = await WebApiHelper.SendAsync<SuppliersViewModel>(url, null, CommonTypes.CommonJsonSendType.GET);
                if (model != null)
                {
                    return View(model);
                }
                ViewBag.Tag = Constant.Request_Lose_Msg;
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("供应商详情页请求异常:" + e.Message);
                ViewBag.Tag = Constant.Request_Ext_Msg;
            }
            return View();
        }

    }
}
