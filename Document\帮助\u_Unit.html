﻿ Code = entity.Code,
 Name = entity.Name,
 Brief = entity.Brief,
 PinYin = entity.PinYin,
 PinYinBrief = entity.PinYinBrief,
 AreaId = entity.AreaId,
 UnitType = entity.UnitType,
 ContractMainName = entity.ContractMainName,
 SocialCreditCode = entity.SocialCreditCode,
 BusLicense = entity.BusLicense,
 CountryId = entity.CountryId,
 ProvinceId = entity.ProvinceId,
 CityId = entity.CityId,
 CountyId = entity.CountyId,
 Address = entity.Address,
 ZipCode = entity.ZipCode,
 Email = entity.Email,
 TrafficMap = entity.TrafficMap,
 UnitsFax = entity.UnitsFax,
 Position = entity.Position,
 Statuz = entity.Statuz,
 Memo = entity.Memo,
 RegTime = entity.RegTime,
 CreatorId = entity.CreatorId,
 CertificationStatuz = entity.CertificationStatuz,
 Reason = entity.Reason,
 UserId = entity.UserId,
 CertificationTime = entity.CertificationTime,
 Introduction = entity.Introduction,
 Description = entity.Description,
 Legal = entity.Legal,
 RegistrationTime = entity.RegistrationTime,
 Longitude = entity.Longitude,
 Latitude = entity.Latitude,
 BusinessAddress = entity.BusinessAddress,
 RegisteredAddress = entity.RegisteredAddress,


 Code = model.Code,
 Name = model.Name,
 Brief = model.Brief,
 PinYin = model.PinYin,
 PinYinBrief = model.PinYinBrief,
 AreaId = model.AreaId,
 UnitType = model.UnitType,
 ContractMainName = model.ContractMainName,
 SocialCreditCode = model.SocialCreditCode,
 BusLicense = model.BusLicense,
 CountryId = model.CountryId,
 ProvinceId = model.ProvinceId,
 CityId = model.CityId,
 CountyId = model.CountyId,
 Address = model.Address,
 ZipCode = model.ZipCode,
 Email = model.Email,
 TrafficMap = model.TrafficMap,
 UnitsFax = model.UnitsFax,
 Position = model.Position,
 Statuz = model.Statuz,
 Memo = model.Memo,
 RegTime = model.RegTime,
 CreatorId = model.CreatorId,
 CertificationStatuz = model.CertificationStatuz,
 Reason = model.Reason,
 UserId = model.UserId,
 CertificationTime = model.CertificationTime,
 Introduction = model.Introduction,
 Description = model.Description,
 Legal = model.Legal,
 RegistrationTime = model.RegistrationTime,
 Longitude = model.Longitude,
 Latitude = model.Latitude,
 BusinessAddress = model.BusinessAddress,
 RegisteredAddress = model.RegisteredAddress,


 temp.Code = model.Code,
 temp.Name = model.Name,
 temp.Brief = model.Brief,
 temp.PinYin = model.PinYin,
 temp.PinYinBrief = model.PinYinBrief,
 temp.AreaId = model.AreaId,
 temp.UnitType = model.UnitType,
 temp.ContractMainName = model.ContractMainName,
 temp.SocialCreditCode = model.SocialCreditCode,
 temp.BusLicense = model.BusLicense,
 temp.CountryId = model.CountryId,
 temp.ProvinceId = model.ProvinceId,
 temp.CityId = model.CityId,
 temp.CountyId = model.CountyId,
 temp.Address = model.Address,
 temp.ZipCode = model.ZipCode,
 temp.Email = model.Email,
 temp.TrafficMap = model.TrafficMap,
 temp.UnitsFax = model.UnitsFax,
 temp.Position = model.Position,
 temp.Statuz = model.Statuz,
 temp.Memo = model.Memo,
 temp.RegTime = model.RegTime,
 temp.CreatorId = model.CreatorId,
 temp.CertificationStatuz = model.CertificationStatuz,
 temp.Reason = model.Reason,
 temp.UserId = model.UserId,
 temp.CertificationTime = model.CertificationTime,
 temp.Introduction = model.Introduction,
 temp.Description = model.Description,
 temp.Legal = model.Legal,
 temp.RegistrationTime = model.RegistrationTime,
 temp.Longitude = model.Longitude,
 temp.Latitude = model.Latitude,
 temp.BusinessAddress = model.BusinessAddress,
 temp.RegisteredAddress = model.RegisteredAddress,

 UnitId = item.UnitId,
 Code = item.Code,
 Name = item.Name,
 Brief = item.Brief,
 PinYin = item.PinYin,
 PinYinBrief = item.PinYinBrief,
 AreaId = item.AreaId,
 UnitType = item.UnitType,
 ContractMainName = item.ContractMainName,
 SocialCreditCode = item.SocialCreditCode,
 BusLicense = item.BusLicense,
 CountryId = item.CountryId,
 ProvinceId = item.ProvinceId,
 CityId = item.CityId,
 CountyId = item.CountyId,
 Address = item.Address,
 ZipCode = item.ZipCode,
 Email = item.Email,
 TrafficMap = item.TrafficMap,
 UnitsFax = item.UnitsFax,
 Position = item.Position,
 Statuz = item.Statuz,
 Memo = item.Memo,
 RegTime = item.RegTime,
 CreatorId = item.CreatorId,
 CertificationStatuz = item.CertificationStatuz,
 Reason = item.Reason,
 UserId = item.UserId,
 CertificationTime = item.CertificationTime,
 Introduction = item.Introduction,
 Description = item.Description,
 Legal = item.Legal,
 RegistrationTime = item.RegistrationTime,
 Longitude = item.Longitude,
 Latitude = item.Latitude,
 BusinessAddress = item.BusinessAddress,
 RegisteredAddress = item.RegisteredAddress,

public class UnitInputModel
{
 [Display(Name = "Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "单位编号")] 
    public string Code {get; set; }
    
 [Display(Name = "企业名称")] 
    public string Name {get; set; }
    
 [Display(Name = "简称")] 
    public string Brief {get; set; }
    
 [Display(Name = "全拼")] 
    public string PinYin {get; set; }
    
 [Display(Name = "简拼")] 
    public string PinYinBrief {get; set; }
    
 [Display(Name = "区域Id")] 
    public int AreaId {get; set; }
    
 [Display(Name = "性质类型：1市级、2区县、3学校、4代理商 5源厂商")] 
    public int UnitType {get; set; }
    
 [Display(Name = "合同主体名称")] 
    public string ContractMainName {get; set; }
    
 [Display(Name = "统一社会信用代码")] 
    public string SocialCreditCode {get; set; }
    
 [Display(Name = "营业执照")] 
    public string BusLicense {get; set; }
    
 [Display(Name = "国家Id")] 
    public int CountryId {get; set; }
    
 [Display(Name = "省Id")] 
    public int ProvinceId {get; set; }
    
 [Display(Name = "市Id")] 
    public int CityId {get; set; }
    
 [Display(Name = "区Id")] 
    public int CountyId {get; set; }
    
 [Display(Name = "地址")] 
    public string Address {get; set; }
    
 [Display(Name = "邮编")] 
    public string ZipCode {get; set; }
    
 [Display(Name = "邮箱")] 
    public string Email {get; set; }
    
 [Display(Name = "交通路线图")] 
    public string TrafficMap {get; set; }
    
 [Display(Name = "单位传真")] 
    public string UnitsFax {get; set; }
    
 [Display(Name = "地理位置打点")] 
    public string Position {get; set; }
    
 [Display(Name = "状态（1：启用  0：禁用）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "创建人Id")] 
    public long CreatorId {get; set; }
    
 [Display(Name = "认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)")] 
    public int CertificationStatuz {get; set; }
    
 [Display(Name = "不通过原因")] 
    public string Reason {get; set; }
    
 [Display(Name = "认证操作人，用户基础表Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "认证时间")] 
    public DateTime CertificationTime {get; set; }
    
 [Display(Name = "单位简介")] 
    public string Introduction {get; set; }
    
 [Display(Name = "单位描述")] 
    public string Description {get; set; }
    
 [Display(Name = "法人")] 
    public string Legal {get; set; }
    
 [Display(Name = "注册时间")] 
    public DateTime RegistrationTime {get; set; }
    
 [Display(Name = "经度")] 
    public string Longitude {get; set; }
    
 [Display(Name = "纬度")] 
    public string Latitude {get; set; }
    
 [Display(Name = "经营地址")] 
    public string BusinessAddress {get; set; }
    
 [Display(Name = "单位注册地址")] 
    public string RegisteredAddress {get; set; }
    
 }
 
 public class UnitViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 单位编号
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 企业名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 简称
    /// </summary>
    public string Brief {get; set; }
    
    /// <summary>
    /// 全拼
    /// </summary>
    public string PinYin {get; set; }
    
    /// <summary>
    /// 简拼
    /// </summary>
    public string PinYinBrief {get; set; }
    
    /// <summary>
    /// 区域Id
    /// </summary>
    public int AreaId {get; set; }
    
    /// <summary>
    /// 性质类型：1市级、2区县、3学校、4代理商 5源厂商
    /// </summary>
    public int UnitType {get; set; }
    
    /// <summary>
    /// 合同主体名称
    /// </summary>
    public string ContractMainName {get; set; }
    
    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string SocialCreditCode {get; set; }
    
    /// <summary>
    /// 营业执照
    /// </summary>
    public string BusLicense {get; set; }
    
    /// <summary>
    /// 国家Id
    /// </summary>
    public int CountryId {get; set; }
    
    /// <summary>
    /// 省Id
    /// </summary>
    public int ProvinceId {get; set; }
    
    /// <summary>
    /// 市Id
    /// </summary>
    public int CityId {get; set; }
    
    /// <summary>
    /// 区Id
    /// </summary>
    public int CountyId {get; set; }
    
    /// <summary>
    /// 地址
    /// </summary>
    public string Address {get; set; }
    
    /// <summary>
    /// 邮编
    /// </summary>
    public string ZipCode {get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email {get; set; }
    
    /// <summary>
    /// 交通路线图
    /// </summary>
    public string TrafficMap {get; set; }
    
    /// <summary>
    /// 单位传真
    /// </summary>
    public string UnitsFax {get; set; }
    
    /// <summary>
    /// 地理位置打点
    /// </summary>
    public string Position {get; set; }
    
    /// <summary>
    /// 状态（1：启用  0：禁用）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId {get; set; }
    
    /// <summary>
    /// 认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)
    /// </summary>
    public int CertificationStatuz {get; set; }
    
    /// <summary>
    /// 不通过原因
    /// </summary>
    public string Reason {get; set; }
    
    /// <summary>
    /// 认证操作人，用户基础表Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 认证时间
    /// </summary>
    public DateTime? CertificationTime {get; set; }
    
    /// <summary>
    /// 单位简介
    /// </summary>
    public string Introduction {get; set; }
    
    /// <summary>
    /// 单位描述
    /// </summary>
    public string Description {get; set; }
    
    /// <summary>
    /// 法人
    /// </summary>
    public string Legal {get; set; }
    
    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime? RegistrationTime {get; set; }
    
    /// <summary>
    /// 经度
    /// </summary>
    public string Longitude {get; set; }
    
    /// <summary>
    /// 纬度
    /// </summary>
    public string Latitude {get; set; }
    
    /// <summary>
    /// 经营地址
    /// </summary>
    public string BusinessAddress {get; set; }
    
    /// <summary>
    /// 单位注册地址
    /// </summary>
    public string RegisteredAddress {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入企业名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Brief, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Brief, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入简称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PinYin, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PinYin, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入全拼" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PinYinBrief, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PinYinBrief, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入简拼" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AreaId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AreaId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区域Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入性质类型：1市级、2区县、3学校、4代理商 5源厂商" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractMainName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractMainName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同主体名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SocialCreditCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SocialCreditCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入统一社会信用代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BusLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BusLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入营业执照" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountryId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountryId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入国家Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProvinceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProvinceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入省Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CityId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CityId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountyId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountyId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Address, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Address, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ZipCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ZipCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮编" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Email, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮箱" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.TrafficMap, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.TrafficMap, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入交通路线图" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitsFax, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitsFax, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位传真" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Position, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Position, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入地理位置打点" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（1：启用  0：禁用）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreatorId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreatorId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CertificationStatuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CertificationStatuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Reason, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Reason, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入不通过原因" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入认证操作人，用户基础表Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CertificationTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CertificationTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入认证时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Introduction, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Introduction, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位简介" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Description, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位描述" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Legal, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Legal, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入法人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegistrationTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegistrationTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入注册时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Longitude, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Longitude, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入经度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Latitude, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Latitude, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入纬度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BusinessAddress, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BusinessAddress, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入经营地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegisteredAddress, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegisteredAddress, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位注册地址" } })                    
                </div>
           </div>
  




 { field: 'Code', title: '单位编号', sortable: true },
                 
 { field: 'Name', title: '企业名称', sortable: true },
                 
 { field: 'Brief', title: '简称', sortable: true },
                 
 { field: 'PinYin', title: '全拼', sortable: true },
                 
 { field: 'PinYinBrief', title: '简拼', sortable: true },
                 
 { field: 'AreaId', title: '区域Id', sortable: true },
                 
 { field: 'UnitType', title: '性质类型：1市级、2区县、3学校、4代理商 5源厂商', sortable: true },
                 
 { field: 'ContractMainName', title: '合同主体名称', sortable: true },
                 
 { field: 'SocialCreditCode', title: '统一社会信用代码', sortable: true },
                 
 { field: 'BusLicense', title: '营业执照', sortable: true },
                 
 { field: 'CountryId', title: '国家Id', sortable: true },
                 
 { field: 'ProvinceId', title: '省Id', sortable: true },
                 
 { field: 'CityId', title: '市Id', sortable: true },
                 
 { field: 'CountyId', title: '区Id', sortable: true },
                 
 { field: 'Address', title: '地址', sortable: true },
                 
 { field: 'ZipCode', title: '邮编', sortable: true },
                 
 { field: 'Email', title: '邮箱', sortable: true },
                 
 { field: 'TrafficMap', title: '交通路线图', sortable: true },
                 
 { field: 'UnitsFax', title: '单位传真', sortable: true },
                 
 { field: 'Position', title: '地理位置打点', sortable: true },
                 
 { field: 'Statuz', title: '状态（1：启用  0：禁用）', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
 { field: 'CreatorId', title: '创建人Id', sortable: true },
                 
 { field: 'CertificationStatuz', title: '认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)', sortable: true },
                 
 { field: 'Reason', title: '不通过原因', sortable: true },
                 
 { field: 'UserId', title: '认证操作人，用户基础表Id', sortable: true },
                 
 { field: 'CertificationTime', title: '认证时间', sortable: true },
                 
 { field: 'Introduction', title: '单位简介', sortable: true },
                 
 { field: 'Description', title: '单位描述', sortable: true },
                 
 { field: 'Legal', title: '法人', sortable: true },
                 
 { field: 'RegistrationTime', title: '注册时间', sortable: true },
                 
 { field: 'Longitude', title: '经度', sortable: true },
                 
 { field: 'Latitude', title: '纬度', sortable: true },
                 
 { field: 'BusinessAddress', title: '经营地址', sortable: true },
                 
 { field: 'RegisteredAddress', title: '单位注册地址', sortable: true },
                 
o.Code,                 
o.Name,                 
o.Brief,                 
o.PinYin,                 
o.PinYinBrief,                 
o.AreaId,                 
o.UnitType,                 
o.ContractMainName,                 
o.SocialCreditCode,                 
o.BusLicense,                 
o.CountryId,                 
o.ProvinceId,                 
o.CityId,                 
o.CountyId,                 
o.Address,                 
o.ZipCode,                 
o.Email,                 
o.TrafficMap,                 
o.UnitsFax,                 
o.Position,                 
o.Statuz,                 
o.Memo,                 
o.RegTime,                 
o.CreatorId,                 
o.CertificationStatuz,                 
o.Reason,                 
o.UserId,                 
o.CertificationTime,                 
o.Introduction,                 
o.Description,                 
o.Legal,                 
o.RegistrationTime,                 
o.Longitude,                 
o.Latitude,                 
o.BusinessAddress,                 
o.RegisteredAddress,                 
        
        $('#Code').val(d.data.rows.Code);          
        $('#Name').val(d.data.rows.Name);          
        $('#Brief').val(d.data.rows.Brief);          
        $('#PinYin').val(d.data.rows.PinYin);          
        $('#PinYinBrief').val(d.data.rows.PinYinBrief);          
        $('#AreaId').val(d.data.rows.AreaId);          
        $('#UnitType').val(d.data.rows.UnitType);          
        $('#ContractMainName').val(d.data.rows.ContractMainName);          
        $('#SocialCreditCode').val(d.data.rows.SocialCreditCode);          
        $('#BusLicense').val(d.data.rows.BusLicense);          
        $('#CountryId').val(d.data.rows.CountryId);          
        $('#ProvinceId').val(d.data.rows.ProvinceId);          
        $('#CityId').val(d.data.rows.CityId);          
        $('#CountyId').val(d.data.rows.CountyId);          
        $('#Address').val(d.data.rows.Address);          
        $('#ZipCode').val(d.data.rows.ZipCode);          
        $('#Email').val(d.data.rows.Email);          
        $('#TrafficMap').val(d.data.rows.TrafficMap);          
        $('#UnitsFax').val(d.data.rows.UnitsFax);          
        $('#Position').val(d.data.rows.Position);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#CreatorId').val(d.data.rows.CreatorId);          
        $('#CertificationStatuz').val(d.data.rows.CertificationStatuz);          
        $('#Reason').val(d.data.rows.Reason);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#CertificationTime').val(d.data.rows.CertificationTime);          
        $('#Introduction').val(d.data.rows.Introduction);          
        $('#Description').val(d.data.rows.Description);          
        $('#Legal').val(d.data.rows.Legal);          
        $('#RegistrationTime').val(d.data.rows.RegistrationTime);          
        $('#Longitude').val(d.data.rows.Longitude);          
        $('#Latitude').val(d.data.rows.Latitude);          
        $('#BusinessAddress').val(d.data.rows.BusinessAddress);          
        $('#RegisteredAddress').val(d.data.rows.RegisteredAddress);          

 $('#th_Code').html(' 单位编号');               
 $('#th_Name').html(' 企业名称');               
 $('#th_Brief').html(' 简称');               
 $('#th_PinYin').html(' 全拼');               
 $('#th_PinYinBrief').html(' 简拼');               
 $('#th_AreaId').html(' 区域Id');               
 $('#th_UnitType').html(' 性质类型：1市级、2区县、3学校、4代理商 5源厂商');               
 $('#th_ContractMainName').html(' 合同主体名称');               
 $('#th_SocialCreditCode').html(' 统一社会信用代码');               
 $('#th_BusLicense').html(' 营业执照');               
 $('#th_CountryId').html(' 国家Id');               
 $('#th_ProvinceId').html(' 省Id');               
 $('#th_CityId').html(' 市Id');               
 $('#th_CountyId').html(' 区Id');               
 $('#th_Address').html(' 地址');               
 $('#th_ZipCode').html(' 邮编');               
 $('#th_Email').html(' 邮箱');               
 $('#th_TrafficMap').html(' 交通路线图');               
 $('#th_UnitsFax').html(' 单位传真');               
 $('#th_Position').html(' 地理位置打点');               
 $('#th_Statuz').html(' 状态（1：启用  0：禁用）');               
 $('#th_Memo').html(' 备注');               
 $('#th_RegTime').html(' 创建时间');               
 $('#th_CreatorId').html(' 创建人Id');               
 $('#th_CertificationStatuz').html(' 认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)');               
 $('#th_Reason').html(' 不通过原因');               
 $('#th_UserId').html(' 认证操作人，用户基础表Id');               
 $('#th_CertificationTime').html(' 认证时间');               
 $('#th_Introduction').html(' 单位简介');               
 $('#th_Description').html(' 单位描述');               
 $('#th_Legal').html(' 法人');               
 $('#th_RegistrationTime').html(' 注册时间');               
 $('#th_Longitude').html(' 经度');               
 $('#th_Latitude').html(' 纬度');               
 $('#th_BusinessAddress').html(' 经营地址');               
 $('#th_RegisteredAddress').html(' 单位注册地址');               
 
 $('#tr_Code').hide();               
 $('#tr_Name').hide();               
 $('#tr_Brief').hide();               
 $('#tr_PinYin').hide();               
 $('#tr_PinYinBrief').hide();               
 $('#tr_AreaId').hide();               
 $('#tr_UnitType').hide();               
 $('#tr_ContractMainName').hide();               
 $('#tr_SocialCreditCode').hide();               
 $('#tr_BusLicense').hide();               
 $('#tr_CountryId').hide();               
 $('#tr_ProvinceId').hide();               
 $('#tr_CityId').hide();               
 $('#tr_CountyId').hide();               
 $('#tr_Address').hide();               
 $('#tr_ZipCode').hide();               
 $('#tr_Email').hide();               
 $('#tr_TrafficMap').hide();               
 $('#tr_UnitsFax').hide();               
 $('#tr_Position').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_Memo').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_CreatorId').hide();               
 $('#tr_CertificationStatuz').hide();               
 $('#tr_Reason').hide();               
 $('#tr_UserId').hide();               
 $('#tr_CertificationTime').hide();               
 $('#tr_Introduction').hide();               
 $('#tr_Description').hide();               
 $('#tr_Legal').hide();               
 $('#tr_RegistrationTime').hide();               
 $('#tr_Longitude').hide();               
 $('#tr_Latitude').hide();               
 $('#tr_BusinessAddress').hide();               
 $('#tr_RegisteredAddress').hide();               

 , "Code" : code
 , "Name" : name
 , "Brief" : brief
 , "PinYin" : pinYin
 , "PinYinBrief" : pinYinBrief
 , "AreaId" : areaId
 , "UnitType" : unitType
 , "ContractMainName" : contractMainName
 , "SocialCreditCode" : socialCreditCode
 , "BusLicense" : busLicense
 , "CountryId" : countryId
 , "ProvinceId" : provinceId
 , "CityId" : cityId
 , "CountyId" : countyId
 , "Address" : address
 , "ZipCode" : zipCode
 , "Email" : email
 , "TrafficMap" : trafficMap
 , "UnitsFax" : unitsFax
 , "Position" : position
 , "Statuz" : statuz
 , "Memo" : memo
 , "RegTime" : regTime
 , "CreatorId" : creatorId
 , "CertificationStatuz" : certificationStatuz
 , "Reason" : reason
 , "UserId" : userId
 , "CertificationTime" : certificationTime
 , "Introduction" : introduction
 , "Description" : description
 , "Legal" : legal
 , "RegistrationTime" : registrationTime
 , "Longitude" : longitude
 , "Latitude" : latitude
 , "BusinessAddress" : businessAddress
 , "RegisteredAddress" : registeredAddress

 var code = $('#o_Code').val();
 var name = $('#o_Name').val();
 var brief = $('#o_Brief').val();
 var pinYin = $('#o_PinYin').val();
 var pinYinBrief = $('#o_PinYinBrief').val();
 var areaId = $('#o_AreaId').val();
 var unitType = $('#o_UnitType').val();
 var contractMainName = $('#o_ContractMainName').val();
 var socialCreditCode = $('#o_SocialCreditCode').val();
 var busLicense = $('#o_BusLicense').val();
 var countryId = $('#o_CountryId').val();
 var provinceId = $('#o_ProvinceId').val();
 var cityId = $('#o_CityId').val();
 var countyId = $('#o_CountyId').val();
 var address = $('#o_Address').val();
 var zipCode = $('#o_ZipCode').val();
 var email = $('#o_Email').val();
 var trafficMap = $('#o_TrafficMap').val();
 var unitsFax = $('#o_UnitsFax').val();
 var position = $('#o_Position').val();
 var statuz = $('#o_Statuz').val();
 var memo = $('#o_Memo').val();
 var regTime = $('#o_RegTime').val();
 var creatorId = $('#o_CreatorId').val();
 var certificationStatuz = $('#o_CertificationStatuz').val();
 var reason = $('#o_Reason').val();
 var userId = $('#o_UserId').val();
 var certificationTime = $('#o_CertificationTime').val();
 var introduction = $('#o_Introduction').val();
 var description = $('#o_Description').val();
 var legal = $('#o_Legal').val();
 var registrationTime = $('#o_RegistrationTime').val();
 var longitude = $('#o_Longitude').val();
 var latitude = $('#o_Latitude').val();
 var businessAddress = $('#o_BusinessAddress').val();
 var registeredAddress = $('#o_RegisteredAddress').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位编号' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '企业名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '简称' : '产品名称', d.data.rows.Brief);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '全拼' : '产品名称', d.data.rows.PinYin);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '简拼' : '产品名称', d.data.rows.PinYinBrief);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区域Id' : '产品名称', d.data.rows.AreaId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '性质类型：1市级、2区县、3学校、4代理商 5源厂商' : '产品名称', d.data.rows.UnitType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同主体名称' : '产品名称', d.data.rows.ContractMainName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '统一社会信用代码' : '产品名称', d.data.rows.SocialCreditCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '营业执照' : '产品名称', d.data.rows.BusLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '国家Id' : '产品名称', d.data.rows.CountryId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '省Id' : '产品名称', d.data.rows.ProvinceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市Id' : '产品名称', d.data.rows.CityId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区Id' : '产品名称', d.data.rows.CountyId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '地址' : '产品名称', d.data.rows.Address);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮编' : '产品名称', d.data.rows.ZipCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮箱' : '产品名称', d.data.rows.Email);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '交通路线图' : '产品名称', d.data.rows.TrafficMap);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位传真' : '产品名称', d.data.rows.UnitsFax);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '地理位置打点' : '产品名称', d.data.rows.Position);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（1：启用  0：禁用）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建人Id' : '产品名称', d.data.rows.CreatorId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)' : '产品名称', d.data.rows.CertificationStatuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '不通过原因' : '产品名称', d.data.rows.Reason);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '认证操作人，用户基础表Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '认证时间' : '产品名称', d.data.rows.CertificationTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位简介' : '产品名称', d.data.rows.Introduction);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位描述' : '产品名称', d.data.rows.Description);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '法人' : '产品名称', d.data.rows.Legal);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '注册时间' : '产品名称', d.data.rows.RegistrationTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '经度' : '产品名称', d.data.rows.Longitude);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '纬度' : '产品名称', d.data.rows.Latitude);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '经营地址' : '产品名称', d.data.rows.BusinessAddress);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位注册地址' : '产品名称', d.data.rows.RegisteredAddress);



