﻿ Pid = entity.Pid,
 Name = entity.Name,
 Url = entity.Url,
 SubOrder = entity.SubOrder,
 Depth = entity.Depth,
 Sort = entity.Sort,
 Code = entity.Code,
 PageType = entity.PageType,
 IsShow = entity.IsShow,
 Icon = entity.Icon,
 SmallIcon = entity.SmallIcon,
 Memo = entity.Memo,


 Pid = model.Pid,
 Name = model.Name,
 Url = model.Url,
 SubOrder = model.SubOrder,
 Depth = model.Depth,
 Sort = model.Sort,
 Code = model.Code,
 PageType = model.PageType,
 IsShow = model.IsShow,
 Icon = model.Icon,
 SmallIcon = model.SmallIcon,
 Memo = model.Memo,


 temp.Pid = model.Pid,
 temp.Name = model.Name,
 temp.Url = model.Url,
 temp.SubOrder = model.SubOrder,
 temp.Depth = model.Depth,
 temp.Sort = model.Sort,
 temp.Code = model.Code,
 temp.PageType = model.PageType,
 temp.IsShow = model.IsShow,
 temp.Icon = model.Icon,
 temp.SmallIcon = model.SmallIcon,
 temp.Memo = model.Memo,

 SysPageId = item.SysPageId,
 Pid = item.Pid,
 Name = item.Name,
 Url = item.Url,
 SubOrder = item.SubOrder,
 Depth = item.Depth,
 Sort = item.Sort,
 Code = item.Code,
 PageType = item.PageType,
 IsShow = item.IsShow,
 Icon = item.Icon,
 SmallIcon = item.SmallIcon,
 Memo = item.Memo,

public class SysPageInputModel
{
 [Display(Name = "编号")] 
    public int SysPageId {get; set; }
    
 [Display(Name = "父编号")] 
    public int Pid {get; set; }
    
 [Display(Name = "菜单名称")] 
    public string Name {get; set; }
    
 [Display(Name = "菜单路径")] 
    public string Url {get; set; }
    
 [Display(Name = "子菜单排序")] 
    public int SubOrder {get; set; }
    
 [Display(Name = "深度")] 
    public int Depth {get; set; }
    
 [Display(Name = "排序")] 
    public string Sort {get; set; }
    
 [Display(Name = "页面编码")] 
    public string Code {get; set; }
    
 [Display(Name = "页面类型")] 
    public int PageType {get; set; }
    
 [Display(Name = "是否显示(0：否 1：是)")] 
    public bool IsShow {get; set; }
    
 [Display(Name = "图标")] 
    public string Icon {get; set; }
    
 [Display(Name = "小图标")] 
    public string SmallIcon {get; set; }
    
 [Display(Name = "描述")] 
    public string Memo {get; set; }
    
 }
 
 public class SysPageViewModel
 {
    /// <summary>
    /// 编号
    /// </summary>
    public int SysPageId {get; set; }
    
    /// <summary>
    /// 父编号
    /// </summary>
    public int Pid {get; set; }
    
    /// <summary>
    /// 菜单名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 菜单路径
    /// </summary>
    public string Url {get; set; }
    
    /// <summary>
    /// 子菜单排序
    /// </summary>
    public int SubOrder {get; set; }
    
    /// <summary>
    /// 深度
    /// </summary>
    public int Depth {get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public string Sort {get; set; }
    
    /// <summary>
    /// 页面编码
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 页面类型
    /// </summary>
    public int PageType {get; set; }
    
    /// <summary>
    /// 是否显示(0：否 1：是)
    /// </summary>
    public bool IsShow {get; set; }
    
    /// <summary>
    /// 图标
    /// </summary>
    public string Icon {get; set; }
    
    /// <summary>
    /// 小图标
    /// </summary>
    public string SmallIcon {get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Memo {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Pid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入菜单名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Url, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Url, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入菜单路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SubOrder, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SubOrder, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入子菜单排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入深度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sort, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sort, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入页面编码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PageType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PageType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入页面类型" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsShow, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsShow, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否显示(0：否 1：是)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Icon, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Icon, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图标" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SmallIcon, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SmallIcon, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入小图标" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入描述" } })                    
                </div>
           </div>
  




 { field: 'Pid', title: '父编号', sortable: true },
                 
 { field: 'Name', title: '菜单名称', sortable: true },
                 
 { field: 'Url', title: '菜单路径', sortable: true },
                 
 { field: 'SubOrder', title: '子菜单排序', sortable: true },
                 
 { field: 'Depth', title: '深度', sortable: true },
                 
 { field: 'Sort', title: '排序', sortable: true },
                 
 { field: 'Code', title: '页面编码', sortable: true },
                 
 { field: 'PageType', title: '页面类型', sortable: true },
                 
 { field: 'IsShow', title: '是否显示(0：否 1：是)', sortable: true },
                 
 { field: 'Icon', title: '图标', sortable: true },
                 
 { field: 'SmallIcon', title: '小图标', sortable: true },
                 
 { field: 'Memo', title: '描述', sortable: true },
                 
o.Pid,                 
o.Name,                 
o.Url,                 
o.SubOrder,                 
o.Depth,                 
o.Sort,                 
o.Code,                 
o.PageType,                 
o.IsShow,                 
o.Icon,                 
o.SmallIcon,                 
o.Memo,                 
        
        $('#Pid').val(d.data.rows.Pid);          
        $('#Name').val(d.data.rows.Name);          
        $('#Url').val(d.data.rows.Url);          
        $('#SubOrder').val(d.data.rows.SubOrder);          
        $('#Depth').val(d.data.rows.Depth);          
        $('#Sort').val(d.data.rows.Sort);          
        $('#Code').val(d.data.rows.Code);          
        $('#PageType').val(d.data.rows.PageType);          
        $('#IsShow').val(d.data.rows.IsShow);          
        $('#Icon').val(d.data.rows.Icon);          
        $('#SmallIcon').val(d.data.rows.SmallIcon);          
        $('#Memo').val(d.data.rows.Memo);          

 $('#th_Pid').html(' 父编号');               
 $('#th_Name').html(' 菜单名称');               
 $('#th_Url').html(' 菜单路径');               
 $('#th_SubOrder').html(' 子菜单排序');               
 $('#th_Depth').html(' 深度');               
 $('#th_Sort').html(' 排序');               
 $('#th_Code').html(' 页面编码');               
 $('#th_PageType').html(' 页面类型');               
 $('#th_IsShow').html(' 是否显示(0：否 1：是)');               
 $('#th_Icon').html(' 图标');               
 $('#th_SmallIcon').html(' 小图标');               
 $('#th_Memo').html(' 描述');               
 
 $('#tr_Pid').hide();               
 $('#tr_Name').hide();               
 $('#tr_Url').hide();               
 $('#tr_SubOrder').hide();               
 $('#tr_Depth').hide();               
 $('#tr_Sort').hide();               
 $('#tr_Code').hide();               
 $('#tr_PageType').hide();               
 $('#tr_IsShow').hide();               
 $('#tr_Icon').hide();               
 $('#tr_SmallIcon').hide();               
 $('#tr_Memo').hide();               

 , "Pid" : pid
 , "Name" : name
 , "Url" : url
 , "SubOrder" : subOrder
 , "Depth" : depth
 , "Sort" : sort
 , "Code" : code
 , "PageType" : pageType
 , "IsShow" : isShow
 , "Icon" : icon
 , "SmallIcon" : smallIcon
 , "Memo" : memo

 var pid = $('#o_Pid').val();
 var name = $('#o_Name').val();
 var url = $('#o_Url').val();
 var subOrder = $('#o_SubOrder').val();
 var depth = $('#o_Depth').val();
 var sort = $('#o_Sort').val();
 var code = $('#o_Code').val();
 var pageType = $('#o_PageType').val();
 var isShow = $('#o_IsShow').val();
 var icon = $('#o_Icon').val();
 var smallIcon = $('#o_SmallIcon').val();
 var memo = $('#o_Memo').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父编号' : '产品名称', d.data.rows.Pid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '菜单名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '菜单路径' : '产品名称', d.data.rows.Url);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '子菜单排序' : '产品名称', d.data.rows.SubOrder);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '深度' : '产品名称', d.data.rows.Depth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '排序' : '产品名称', d.data.rows.Sort);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '页面编码' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '页面类型' : '产品名称', d.data.rows.PageType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否显示(0：否 1：是)' : '产品名称', d.data.rows.IsShow);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图标' : '产品名称', d.data.rows.Icon);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '小图标' : '产品名称', d.data.rows.SmallIcon);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '描述' : '产品名称', d.data.rows.Memo);



