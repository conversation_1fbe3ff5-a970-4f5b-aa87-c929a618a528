﻿ Name = entity.Name,
 ContractPerson = entity.ContractPerson,
 ContractNumber = entity.ContractNumber,
 UnitsTel = entity.UnitsTel,
 ContractAddress = entity.ContractAddress,


 Name = model.Name,
 ContractPerson = model.ContractPerson,
 ContractNumber = model.ContractNumber,
 UnitsTel = model.UnitsTel,
 ContractAddress = model.ContractAddress,


 temp.Name = model.Name,
 temp.ContractPerson = model.ContractPerson,
 temp.ContractNumber = model.ContractNumber,
 temp.UnitsTel = model.UnitsTel,
 temp.ContractAddress = model.ContractAddress,

 OperatorInfoId = item.OperatorInfoId,
 Name = item.Name,
 ContractPerson = item.ContractPerson,
 ContractNumber = item.ContractNumber,
 UnitsTel = item.UnitsTel,
 ContractAddress = item.ContractAddress,

public class OperatorInfoInputModel
{
 [Display(Name = "Id")] 
    public int OperatorInfoId {get; set; }
    
 [Display(Name = "企业全称")] 
    public string Name {get; set; }
    
 [Display(Name = "联系人")] 
    public string ContractPerson {get; set; }
    
 [Display(Name = "联系电话")] 
    public string ContractNumber {get; set; }
    
 [Display(Name = "单位电话")] 
    public string UnitsTel {get; set; }
    
 [Display(Name = "联系地址")] 
    public string ContractAddress {get; set; }
    
 }
 
 public class OperatorInfoViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int OperatorInfoId {get; set; }
    
    /// <summary>
    /// 企业全称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContractPerson {get; set; }
    
    /// <summary>
    /// 联系电话
    /// </summary>
    public string ContractNumber {get; set; }
    
    /// <summary>
    /// 单位电话
    /// </summary>
    public string UnitsTel {get; set; }
    
    /// <summary>
    /// 联系地址
    /// </summary>
    public string ContractAddress {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入企业全称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractPerson, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractPerson, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractNumber, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractNumber, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系电话" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitsTel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitsTel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位电话" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractAddress, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractAddress, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系地址" } })                    
                </div>
           </div>
  




 { field: 'Name', title: '企业全称', sortable: true },
                 
 { field: 'ContractPerson', title: '联系人', sortable: true },
                 
 { field: 'ContractNumber', title: '联系电话', sortable: true },
                 
 { field: 'UnitsTel', title: '单位电话', sortable: true },
                 
 { field: 'ContractAddress', title: '联系地址', sortable: true },
                 
o.Name,                 
o.ContractPerson,                 
o.ContractNumber,                 
o.UnitsTel,                 
o.ContractAddress,                 
        
        $('#Name').val(d.data.rows.Name);          
        $('#ContractPerson').val(d.data.rows.ContractPerson);          
        $('#ContractNumber').val(d.data.rows.ContractNumber);          
        $('#UnitsTel').val(d.data.rows.UnitsTel);          
        $('#ContractAddress').val(d.data.rows.ContractAddress);          

 $('#th_Name').html(' 企业全称');               
 $('#th_ContractPerson').html(' 联系人');               
 $('#th_ContractNumber').html(' 联系电话');               
 $('#th_UnitsTel').html(' 单位电话');               
 $('#th_ContractAddress').html(' 联系地址');               
 
 $('#tr_Name').hide();               
 $('#tr_ContractPerson').hide();               
 $('#tr_ContractNumber').hide();               
 $('#tr_UnitsTel').hide();               
 $('#tr_ContractAddress').hide();               

 , "Name" : name
 , "ContractPerson" : contractPerson
 , "ContractNumber" : contractNumber
 , "UnitsTel" : unitsTel
 , "ContractAddress" : contractAddress

 var name = $('#o_Name').val();
 var contractPerson = $('#o_ContractPerson').val();
 var contractNumber = $('#o_ContractNumber').val();
 var unitsTel = $('#o_UnitsTel').val();
 var contractAddress = $('#o_ContractAddress').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '企业全称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系人' : '产品名称', d.data.rows.ContractPerson);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系电话' : '产品名称', d.data.rows.ContractNumber);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位电话' : '产品名称', d.data.rows.UnitsTel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系地址' : '产品名称', d.data.rows.ContractAddress);



