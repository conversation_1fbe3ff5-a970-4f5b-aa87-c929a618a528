﻿ Name = entity.Name,
 IsEnable = entity.IsEnable,
 SectionType = entity.SectionType,


 Name = model.Name,
 IsEnable = model.IsEnable,
 SectionType = model.SectionType,


 temp.Name = model.Name,
 temp.IsEnable = model.IsEnable,
 temp.SectionType = model.SectionType,

 StudySectionId = item.StudySectionId,
 Name = item.Name,
 IsEnable = item.IsEnable,
 SectionType = item.SectionType,

public class StudySectionInputModel
{
 [Display(Name = "Id")] 
    public int StudySectionId {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "是否启用(0：否 1：是)")] 
    public int IsEnable {get; set; }
    
 [Display(Name = "类型(1：普教 2：高教)")] 
    public int SectionType {get; set; }
    
 }
 
 public class StudySectionViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int StudySectionId {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 是否启用(0：否 1：是)
    /// </summary>
    public int IsEnable {get; set; }
    
    /// <summary>
    /// 类型(1：普教 2：高教)
    /// </summary>
    public int SectionType {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsEnable, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsEnable, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否启用(0：否 1：是)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SectionType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SectionType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类型(1：普教 2：高教)" } })                    
                </div>
           </div>
  




 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'IsEnable', title: '是否启用(0：否 1：是)', sortable: true },
                 
 { field: 'SectionType', title: '类型(1：普教 2：高教)', sortable: true },
                 
o.Name,                 
o.IsEnable,                 
o.SectionType,                 
        
        $('#Name').val(d.data.rows.Name);          
        $('#IsEnable').val(d.data.rows.IsEnable);          
        $('#SectionType').val(d.data.rows.SectionType);          

 $('#th_Name').html(' 名称');               
 $('#th_IsEnable').html(' 是否启用(0：否 1：是)');               
 $('#th_SectionType').html(' 类型(1：普教 2：高教)');               
 
 $('#tr_Name').hide();               
 $('#tr_IsEnable').hide();               
 $('#tr_SectionType').hide();               

 , "Name" : name
 , "IsEnable" : isEnable
 , "SectionType" : sectionType

 var name = $('#o_Name').val();
 var isEnable = $('#o_IsEnable').val();
 var sectionType = $('#o_SectionType').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否启用(0：否 1：是)' : '产品名称', d.data.rows.IsEnable);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类型(1：普教 2：高教)' : '产品名称', d.data.rows.SectionType);



