{"format": 1, "restore": {"D:\\工作管理\\aliyun-oss-csharp-sdk-master\\sdk\\aliyun-oss-sdk-dotnetcore.csproj": {}}, "projects": {"D:\\工作管理\\aliyun-oss-csharp-sdk-master\\sdk\\aliyun-oss-sdk-dotnetcore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\aliyun-oss-csharp-sdk-master\\sdk\\aliyun-oss-sdk-dotnetcore.csproj", "projectName": "Aliyun.OSS.Core", "projectPath": "D:\\工作管理\\aliyun-oss-csharp-sdk-master\\sdk\\aliyun-oss-sdk-dotnetcore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\aliyun-oss-csharp-sdk-master\\sdk\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.0": {"dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\3.1.101\\RuntimeIdentifierGraph.json"}}}}}