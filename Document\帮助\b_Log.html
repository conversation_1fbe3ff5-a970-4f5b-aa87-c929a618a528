﻿ UserId = entity.UserId,
 ModuleId = entity.ModuleId,
 NodeId = entity.NodeId,
 LogType = entity.LogType,
 Memo = entity.Memo,
 Ip = entity.Ip,
 RegTime = entity.RegTime,
 Statuz = entity.Statuz,


 UserId = model.UserId,
 ModuleId = model.ModuleId,
 NodeId = model.NodeId,
 LogType = model.LogType,
 Memo = model.Memo,
 Ip = model.Ip,
 RegTime = model.RegTime,
 Statuz = model.Statuz,


 temp.UserId = model.UserId,
 temp.ModuleId = model.ModuleId,
 temp.NodeId = model.NodeId,
 temp.LogType = model.LogType,
 temp.Memo = model.Memo,
 temp.Ip = model.Ip,
 temp.RegTime = model.RegTime,
 temp.Statuz = model.Statuz,

 LogId = item.LogId,
 UserId = item.UserId,
 ModuleId = item.ModuleId,
 NodeId = item.NodeId,
 LogType = item.LogType,
 Memo = item.Memo,
 Ip = item.Ip,
 RegTime = item.RegTime,
 Statuz = item.Statuz,

public class LogInputModel
{
 [Display(Name = "ID")] 
    public long LogId {get; set; }
    
 [Display(Name = "用户Id（UserId）")] 
    public long UserId {get; set; }
    
 [Display(Name = "模块编号")] 
    public int ModuleId {get; set; }
    
 [Display(Name = "节点编号")] 
    public int NodeId {get; set; }
    
 [Display(Name = "类型（LogType）")] 
    public int LogType {get; set; }
    
 [Display(Name = "日志描述")] 
    public string Memo {get; set; }
    
 [Display(Name = "IP")] 
    public string Ip {get; set; }
    
 [Display(Name = "发生时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "操作状态")] 
    public int Statuz {get; set; }
    
 }
 
 public class LogViewModel
 {
    /// <summary>
    /// ID
    /// </summary>
    public long LogId {get; set; }
    
    /// <summary>
    /// 用户Id（UserId）
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 模块编号
    /// </summary>
    public int ModuleId {get; set; }
    
    /// <summary>
    /// 节点编号
    /// </summary>
    public int NodeId {get; set; }
    
    /// <summary>
    /// 类型（LogType）
    /// </summary>
    public int LogType {get; set; }
    
    /// <summary>
    /// 日志描述
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// IP
    /// </summary>
    public string Ip {get; set; }
    
    /// <summary>
    /// 发生时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 操作状态
    /// </summary>
    public int Statuz {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户Id（UserId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ModuleId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ModuleId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入模块编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.NodeId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.NodeId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入节点编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LogType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LogType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类型（LogType）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入日志描述" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Ip, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Ip, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入IP" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入发生时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入操作状态" } })                    
                </div>
           </div>
  




 { field: 'UserId', title: '用户Id（UserId）', sortable: true },
                 
 { field: 'ModuleId', title: '模块编号', sortable: true },
                 
 { field: 'NodeId', title: '节点编号', sortable: true },
                 
 { field: 'LogType', title: '类型（LogType）', sortable: true },
                 
 { field: 'Memo', title: '日志描述', sortable: true },
                 
 { field: 'Ip', title: 'IP', sortable: true },
                 
 { field: 'RegTime', title: '发生时间', sortable: true },
                 
 { field: 'Statuz', title: '操作状态', sortable: true },
                 
o.UserId,                 
o.ModuleId,                 
o.NodeId,                 
o.LogType,                 
o.Memo,                 
o.Ip,                 
o.RegTime,                 
o.Statuz,                 
        
        $('#UserId').val(d.data.rows.UserId);          
        $('#ModuleId').val(d.data.rows.ModuleId);          
        $('#NodeId').val(d.data.rows.NodeId);          
        $('#LogType').val(d.data.rows.LogType);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#Ip').val(d.data.rows.Ip);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#Statuz').val(d.data.rows.Statuz);          

 $('#th_UserId').html(' 用户Id（UserId）');               
 $('#th_ModuleId').html(' 模块编号');               
 $('#th_NodeId').html(' 节点编号');               
 $('#th_LogType').html(' 类型（LogType）');               
 $('#th_Memo').html(' 日志描述');               
 $('#th_Ip').html(' IP');               
 $('#th_RegTime').html(' 发生时间');               
 $('#th_Statuz').html(' 操作状态');               
 
 $('#tr_UserId').hide();               
 $('#tr_ModuleId').hide();               
 $('#tr_NodeId').hide();               
 $('#tr_LogType').hide();               
 $('#tr_Memo').hide();               
 $('#tr_Ip').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_Statuz').hide();               

 , "UserId" : userId
 , "ModuleId" : moduleId
 , "NodeId" : nodeId
 , "LogType" : logType
 , "Memo" : memo
 , "Ip" : ip
 , "RegTime" : regTime
 , "Statuz" : statuz

 var userId = $('#o_UserId').val();
 var moduleId = $('#o_ModuleId').val();
 var nodeId = $('#o_NodeId').val();
 var logType = $('#o_LogType').val();
 var memo = $('#o_Memo').val();
 var ip = $('#o_Ip').val();
 var regTime = $('#o_RegTime').val();
 var statuz = $('#o_Statuz').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户Id（UserId）' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '模块编号' : '产品名称', d.data.rows.ModuleId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '节点编号' : '产品名称', d.data.rows.NodeId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类型（LogType）' : '产品名称', d.data.rows.LogType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '日志描述' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'IP' : '产品名称', d.data.rows.Ip);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '发生时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '操作状态' : '产品名称', d.data.rows.Statuz);



