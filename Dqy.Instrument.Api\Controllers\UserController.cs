﻿using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Api.Filters;
using System.IO;
using Senparc.Weixin.Entities;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp;
using System.Configuration;
using Senparc.Weixin;
using Dqy.Instrument.CloudMarketing.Service;
using Dqy.Instrument.CloudMarketing.Entity;

namespace Dqy.Instrument.Api.Controllers
{
    [RoutePrefix("api/user")]
    public class UserController : ApiController
    {

        /// <summary>
        /// 
        /// </summary>
        private readonly IUUserApplicationService _iUserApplicationService;
        private readonly IUUserCourseApplicationService _iUUserCourseApplicationService;
        private readonly IAAuditLogApplicationService _auditLogApplicationService;
        private readonly IWxUserBindOpenidApplicationService _wxUserBindOpenidApplicationService;
        private readonly IWxBindFromPcApplicationService _wxBindFromPcApplicationService;
        private readonly IUUserApplicationService _userService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="iUserApplicationService"></param>
        public UserController(IUUserApplicationService iUserApplicationService, IUUserCourseApplicationService iUUserCourseApplicationService, IAAuditLogApplicationService auditLogApplicationService, IWxUserBindOpenidApplicationService wxUserBindOpenidApplicationService, IWxBindFromPcApplicationService wxBindFromPcApplicationService, IUUserApplicationService userService)
        {
            _iUserApplicationService = iUserApplicationService;
            _iUUserCourseApplicationService = iUUserCourseApplicationService;
            _auditLogApplicationService = auditLogApplicationService;
            _wxUserBindOpenidApplicationService = wxUserBindOpenidApplicationService;
            _wxBindFromPcApplicationService = wxBindFromPcApplicationService;
            _userService = userService;
        }

        #region 周跃峰(修改密码)
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postchangepswd")]
        [ValidateModel]
        public async Task<ReturnResult> ChangePswd(UserInputModel user)
        {

            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(user.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserId != user.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                UserDetailViewModel u = _userService.GetUserById(sessionBag.UserId);
                if (u != null)
                {
                    //判断原密码输入是否正确
                    string strOldPwd = SecurityHelper.MD5(user.OldPswd + u.Salt);
                    if (!strOldPwd.Equals(u.Pwd))
                    {
                        r.flag = 0;
                        r.msg = "原密码输入有误";
                        return r;
                    }
                    r = _iUserApplicationService.ChangePswd(user);
                }
               
                return r;
            });
            return result;
        }
        #endregion

        #region 周跃峰(个人信息设置)
        /// <summary>
        /// 根据用户Id获取用户信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getextensionbyuserid")]
        public async Task<ReturnResult> GetExtensionByUserId(string token, long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserId != userId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                r = _iUserApplicationService.GetExtensionByUserId(userId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 保存个人信息
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyextension")]
        [ValidateModel]
        public async Task<ReturnResult> ModifyUserExtension(UserExtensionInputModel user)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(user.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserId != user.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                r = _iUserApplicationService.ModifyUserExtension(user);
                return r;
            });
            return result;
        }
        #endregion

        #region 周跃峰(区县用户账户管理)
        /// <summary>
        /// 获取区县账号列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postusercountylist")]
        public async Task<QueryResult<UserAccountViewModel>> SearchUserCountyList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<UserAccountViewModel> listUser = new QueryResult<UserAccountViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listUser.flag = -1;
                    return listUser;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    listUser.flag = -2;
                    return listUser;
                }
                List<int> listRole = new List<int>();
                listRole.Add(UserRoleType.CountyAuditor.ToInt());
                listUser = _iUserApplicationService.SearchUserAccountList(args, listRole);
                return listUser;
            });
        }

        /// <summary>
        ///  添加区县用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postaddusercounty")]
        [ValidateModel]
        public async Task<ReturnResult> AddUserCounty(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                //判断手机号码是否存在
                if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                {
                    r.flag = 0;
                    r.msg = "手机号码已经存在";
                    r.data.header = 2;
                    return r;
                }
                //判断登录账号是否存在
                if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                {
                    r.flag = 0;
                    r.msg = "该账号已经存在";
                    r.data.header = 3;
                    return r;
                }
                userInput.UnitId = sessionBag.UnitId;
                userInput.MallId = sessionBag.CurrentMallId;
                userInput.UserRoleType = UserRoleType.CountyAuditor;
                userInput.UserType = UserRoleType.CountyAuditor.ToInt();
                r = _iUserApplicationService.AddUser(userInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取修改的区县用户账号信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getusermodelcounty")]
        public async Task<ReturnResult> GetUserModelCounty(string token, long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _iUserApplicationService.GetModel(userId, sessionBag.UnitId);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 修改用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyusercounty")]
        public async Task<ReturnResult> ModifyUserCounty(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(userInput.Name) || string.IsNullOrEmpty(userInput.Mobile)
                || string.IsNullOrEmpty(userInput.LoginName))
                {
                    r.flag = -2;
                    r.msg = "姓名、手机号码、账号不能为空";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserDetailViewModel user = _userService.GetUserById(userInput.UserId);
                UserAccountInputModel uModel = _userService.GetModelInfo(userInput.UserId);
                if (user != null && uModel != null)
                {
                    if (uModel.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "非法操作，您不能修改其它单位用户信息!";
                        return r;
                    }
                    //判断手机号码是否已经存在
                    if (userInput.Mobile != user.Mobile)
                    {
                        if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                        {
                            r.flag = 0;
                            r.msg = "手机号码已经存在";
                            r.data.header = 2;
                            return r;
                        }
                    }
                    //判断登录名是否存在
                    if (userInput.LoginName != user.LoginName)
                    {
                        if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                        {
                            r.flag = 0;
                            r.msg = "该账号已经存在";
                            r.data.header = 3;
                            return r;
                        }
                    }

                    r = _iUserApplicationService.ModifyUser(userInput);
                }
                return r;
            });
            return result;
        }

        /// <summary>
        /// 启用、禁用区县账号
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsuposeusercounty")]
        public async Task<ReturnResult> UserSuposeCounty(string token, long userId, int unitId, int statuz)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserAccountInputModel u = _iUserApplicationService.GetModelInfo(userId);
                if (u == null)
                {
                    r.flag = 0;
                    r.msg = "当前操作的对象不存在";
                    return r;
                }
                if (u.UnitId != sessionBag.UnitId)
                {
                    r.flag = -2;
                    r.msg = "您无权操作其它单位用户信息";
                    return r;
                }
                r = _iUserApplicationService.UserSupose(userId, (UserStatuz)statuz);
                return r;
            });
            return result;
        }
        #endregion

        #region 周跃峰(市级用户账户管理)
        /// <summary>
        /// 获取市级账号列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postusercitylist")]
        public async Task<QueryResult<UserAccountViewModel>> SearchUserCityList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<UserAccountViewModel> listUser = new QueryResult<UserAccountViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listUser.flag = -1;
                    return listUser;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    listUser.flag = -2;
                    return listUser;
                }
                List<int> listRole = new List<int>();
                listRole.Add(UserRoleType.CityAuditor.ToInt());
                return _iUserApplicationService.SearchUserAccountList(args, listRole);
            });
        }

        /// <summary>
        /// 获取修改的市级用户账号信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getusermodelcity")]
        public async Task<ReturnResult> GetUserModelCity(string token, int unitId, long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _iUserApplicationService.GetModel(userId, sessionBag.UnitId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 添加市级账号
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postaddusercity")]
        [ValidateModel]
        public async Task<ReturnResult> AddUserCity(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                //判断手机号码是否存在
                if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                {
                    r.flag = 0;
                    r.msg = "手机号码已经存在";
                    r.data.header = 2;
                    return r;
                }
                //判断登录账号是否存在
                if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                {
                    r.flag = 0;
                    r.msg = "该账号已经存在";
                    r.data.header = 3;
                    return r;
                }
                userInput.UnitId = sessionBag.UnitId;
                userInput.MallId = sessionBag.CurrentMallId;
                userInput.UserRoleType = UserRoleType.CityAuditor;
                userInput.UserType = UserRoleType.CityAuditor.ToInt();
                r = _iUserApplicationService.AddUser(userInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 修改用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyuserCity")]
        public async Task<ReturnResult> ModifyUserCity(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(userInput.Name) || string.IsNullOrEmpty(userInput.Mobile)
                || string.IsNullOrEmpty(userInput.LoginName))
                {
                    r.flag = -2;
                    r.msg = "姓名、手机号码、账号不能为空";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserDetailViewModel user = _userService.GetUserById(userInput.UserId);
                UserAccountInputModel uModel = _userService.GetModelInfo(userInput.UserId);
                if (user != null && uModel != null)
                {
                    if (uModel.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "非法操作，您不能修改其它单位用户信息!";
                        return r;
                    }
                    //判断手机号码是否已经存在
                    if (userInput.Mobile != user.Mobile)
                    {
                        if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                        {
                            r.flag = 0;
                            r.msg = "手机号码已经存在";
                            r.data.header = 2;
                            return r;
                        }
                    }
                    //判断登录名是否存在
                    if (userInput.LoginName != user.LoginName)
                    {
                        if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                        {
                            r.flag = 0;
                            r.msg = "该账号已经存在";
                            r.data.header = 3;
                            return r;
                        }
                    }

                    r = _iUserApplicationService.ModifyUser(userInput);
                }
                return r;
            });
            return result;
        }

        /// <summary>
        /// 启用、禁用市级账号
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsuposeusercity")]
        public async Task<ReturnResult> UserSuposeCity(string token, long userId, int unitId, int statuz)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserAccountInputModel u = _iUserApplicationService.GetModelInfo(userId);
                if (u == null)
                {
                    r.flag = 0;
                    r.msg = "当前操作的对象不存在";
                    return r;
                }
                if (u.UnitId != sessionBag.UnitId)
                {
                    r.flag = -2;
                    r.msg = "您无权操作其它单位用户信息";
                    return r;
                }
                r = _iUserApplicationService.UserSupose(userId, (UserStatuz)statuz);
                return r;
            });
            return result;
        }
        #endregion

        #region 周跃峰(学校用户账户管理)

        /// <summary>
        /// 获取学校账号信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postuserschoollist")]
        public async Task<QueryResult<UserAccountViewModel>> SearchUserSchoolList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<UserAccountViewModel> listUser = new QueryResult<UserAccountViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listUser.flag = -1;
                    return listUser;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt())
                {
                    listUser.flag = -2;
                    return listUser;
                }
                return _iUserApplicationService.SearchUserSchoolList(args);
            });
        }

        /// <summary>
        /// 根据用户Id获取选中学科信息
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getreportcourse")]
        public async Task<ReturnResult> GetCourseByUserId(long userId, int unitId)
        {
            var result = await Task.Run(() =>
            {
                return _iUUserCourseApplicationService.GetCourseList(userId, unitId);
            });
            return result;
        }

        /// <summary>
        /// 根据用户Id获取选中学科信息
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcourse")]
        public async Task<QueryResult<UserCourseInputModel>> GetCourse(string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<UserCourseInputModel> r = new QueryResult<UserCourseInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    return r;
                }

                var result = _iUUserCourseApplicationService.GetCourseList(sessionBag.UserId, sessionBag.UnitId);
                if (result!=null && result.flag==1)
                {
                    r.Data = result.data.rows as List<UserCourseInputModel>;
                    r.flag = 1;
                    r.msg = "查询成功。";
                    r.TotalCount = r.Data.Count;
                }
                return r;
            });
        }
        /// <summary>
        ///  添加学校用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postaddschooluser")]
        [ValidateModel]
        public async Task<ReturnResult> AddSchoolUser(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //验证角色
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面";
                    return r;
                }
                UserDetailViewModel u = _iUserApplicationService.GetUserById(userInput.CurrentOperateUserId);
                if (u.RecordUserId != 0)
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                //判断手机号码是否存在
                if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                {
                    r.flag = 0;
                    r.msg = "手机号码已经存在";
                    r.data.header = 2;
                    return r;
                }
                //判断登录账号是否存在
                if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                {
                    r.flag = 0;
                    r.msg = "该账号已经存在";
                    r.data.header = 3;
                    return r;
                }
                userInput.UnitId = sessionBag.UnitId;
                userInput.MallId = sessionBag.CurrentMallId;
                r = _iUserApplicationService.AddSchoolUser(userInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取修改的学校用户账号信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getusermodelschool")]
        public async Task<ReturnResult> GetUserModelSchool(string token, long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _iUserApplicationService.GetModel(userId, sessionBag.UnitId);
                return r;
            });
            return result;
        }


        /// <summary>
        ///  修改学校用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyschooluser")]
        public async Task<ReturnResult> ModifySchoolUser(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(userInput.Name) || string.IsNullOrEmpty(userInput.Mobile)
                || string.IsNullOrEmpty(userInput.LoginName))
                {
                    r.flag = -2;
                    r.msg = "姓名、手机号码、账号不能为空";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //验证角色
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此页面";
                    return r;
                }
                UserDetailViewModel u = _iUserApplicationService.GetUserById(userInput.CurrentOperateUserId);
                if (u.RecordUserId != 0)
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                if (userInput.UnitId == 0)
                {
                    r.flag = 0;
                    r.msg = "非法操作";
                    r.data.header = 3;
                    return r;
                }
                UserDetailViewModel user = _userService.GetUserById(userInput.UserId);
                UserAccountInputModel uModel = _userService.GetModelInfo(userInput.UserId);
                if (user != null && uModel != null)
                {
                    if(uModel.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "非法操作，您不能修改其它单位用户信息!";
                        return r;
                    }
                    //判断手机号码是否已经存在
                    if (userInput.Mobile != user.Mobile)
                    {
                        if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                        {
                            r.flag = 0;
                            r.msg = "手机号码已经存在";
                            r.data.header = 2;
                            return r;
                        }
                    }
                    //判断登录名是否存在
                    if (userInput.LoginName != user.LoginName)
                    {
                        if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                        {
                            r.flag = 0;
                            r.msg = "该账号已经存在";
                            r.data.header = 3;
                            return r;
                        }
                    }

                    r = _iUserApplicationService.ModifySchoolUser(userInput);
                }
                
                
                return r;
            });
            return result;
        }

        /// <summary>
        /// 启用、禁用学校账号
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsuposeuserschool")]
        public async Task<ReturnResult> UserSuposeSchool(string token, long userId, int unitId, int statuz, long CurrentOperateUserId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserAccountInputModel u = _iUserApplicationService.GetModelInfo(userId);
                if (u == null)
                {
                    r.flag = 0;
                    r.msg = "当前操作的对象不存在";
                    return r;
                }
                if (u.UnitId != sessionBag.UnitId)
                {
                    r.flag = -2;
                    r.msg = "您无权操作其它单位用户信息";
                    return r;
                }
                UserDetailViewModel uCurrent = _iUserApplicationService.GetUserById(CurrentOperateUserId);
                if (uCurrent.RecordUserId != 0)
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                r = _iUserApplicationService.UserSupose(userId, (UserStatuz)statuz);
                return r;
            });
            return result;
        }

        #endregion

        #region 周跃峰(企业用户账户管理)
        /// <summary>
        /// 获取企业账号列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postusersupplierlist")]
        public async Task<QueryResult<UserAccountViewModel>> SearchUserSupplierList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<UserAccountViewModel> listUser = new QueryResult<UserAccountViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listUser.flag = -1;
                    return listUser;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    listUser.flag = -2;
                    return listUser;
                }
                List<int> listRole = new List<int>();
                listRole.Add(UserRoleType.SellerAdmin.ToInt());
                listUser = _iUserApplicationService.SearchUserAccountList(args, listRole);
                return listUser;
            });
        }

        /// <summary>
        ///  添加企业用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postaddusersupplier")]
        [ValidateModel]
        public async Task<ReturnResult> AddUserSupplier(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                //判断手机号码是否存在
                if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                {
                    r.flag = 0;
                    r.msg = "手机号码已经存在";
                    r.data.header = 2;
                    return r;
                }
                //判断登录账号是否存在
                if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                {
                    r.flag = 0;
                    r.msg = "该账号已经存在";
                    r.data.header = 3;
                    return r;
                }
                //
                UserDetailViewModel u = _iUserApplicationService.GetUserById(userInput.CurrentOperateUserId);
                if (u.RecordUserId != 0)
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                //
                userInput.UnitId = sessionBag.UnitId;
                userInput.MallId = sessionBag.CurrentMallId;
                userInput.UserRoleType = UserRoleType.SellerAdmin;
                userInput.UserType = UserRoleType.SellerAdmin.ToInt();
                r = _iUserApplicationService.AddUser(userInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取修改的企业用户账号信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getusermodelsupplier")]
        public async Task<ReturnResult> GetUserModelSupplier(string token, int unitId, long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);

                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _iUserApplicationService.GetModel(userId, sessionBag.UnitId);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 修改用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyusersupplier")]
        public async Task<ReturnResult> ModifyUserSupplier(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(userInput.Name) || string.IsNullOrEmpty(userInput.Mobile)
                || string.IsNullOrEmpty(userInput.LoginName))
                {
                    r.flag = -2;
                    r.msg = "姓名、手机号码、账号不能为空";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserDetailViewModel u = _iUserApplicationService.GetUserById(userInput.CurrentOperateUserId);
                if (u.RecordUserId != 0)
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }

                UserDetailViewModel user = _userService.GetUserById(userInput.UserId);
                UserAccountInputModel uModel = _userService.GetModelInfo(userInput.UserId);
                if (user != null && uModel != null)
                {
                    if (uModel.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "非法操作，您不能修改其它单位用户信息!";
                        return r;
                    }
                    //判断手机号码是否已经存在
                    if (userInput.Mobile != user.Mobile)
                    {
                        if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                        {
                            r.flag = 0;
                            r.msg = "手机号码已经存在";
                            r.data.header = 2;
                            return r;
                        }
                    }
                    //判断登录名是否存在
                    if (userInput.LoginName != user.LoginName)
                    {
                        if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                        {
                            r.flag = 0;
                            r.msg = "该账号已经存在";
                            r.data.header = 3;
                            return r;
                        }
                    }

                    r = _iUserApplicationService.ModifyUser(userInput);
                }
                return r;
            });
            return result;
        }

        /// <summary>
        /// 启用、禁用企业账号
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsuposeusersupplier")]
        public async Task<ReturnResult> UserSuposeSupplier(string token, long userId, int unitId, int statuz, long CurrentOperateUserId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserAccountInputModel u = _iUserApplicationService.GetModelInfo(userId);
                if (u == null)
                {
                    r.flag = 0;
                    r.msg = "当前操作的对象不存在";
                    return r;
                }
                if (u.UnitId != sessionBag.UnitId)
                {
                    r.flag = -2;
                    r.msg = "您无权操作其它单位用户信息";
                    return r;
                }
                UserDetailViewModel uCurrent = _iUserApplicationService.GetUserById(CurrentOperateUserId);
                if (uCurrent.RecordUserId != 0)
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                r = _iUserApplicationService.UserSupose(userId, (UserStatuz)statuz);
                return r;
            });
            return result;
        }
        #endregion

        #region 周跃峰(平台管理员用户账户管理)
        /// <summary>
        /// 获取平台管理员账号列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postusermalllist")]
        public async Task<QueryResult<UserAccountViewModel>> SearchUserMallList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<UserAccountViewModel> listUser = new QueryResult<UserAccountViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listUser.flag = -1;
                    return listUser;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    listUser.flag = -2;
                    return listUser;
                }
                List<int> listRole = new List<int>();
                listRole.Add(UserRoleType.MallAdministrator.ToInt());
                listUser = _iUserApplicationService.SearchMallManagerList(args, listRole);
                return listUser;
            });
        }

        /// <summary>
        ///  添加平台管理员用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postaddusermall")]
        [ValidateModel]
        public async Task<ReturnResult> AddUserMall(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                userInput.UnitId = sessionBag.UnitId;
                userInput.MallId = sessionBag.CurrentMallId;
                userInput.UserRoleType = UserRoleType.MallAdministrator;
                userInput.UserType = UserRoleType.MallAdministrator.ToInt();
                r = _iUserApplicationService.AddUser(userInput);

                //添加日志
                AuditLogInputModel log = new AuditLogInputModel();
                log.MallId = sessionBag.CurrentMallId;
                log.ModuleType = ModuleType.MallAdminAddUser;
                log.NodeId = long.Parse(r.data.footer.ToString());
                log.UserId = sessionBag.UserId;
                log.Reason = "";
                log.Ip = userInput.IPAddress;
                log.LogContent = "" + sessionBag.LoginName + " 【添加平台管理员】" + userInput.LoginName + "";
                _auditLogApplicationService.AddLog(log);

                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取修改的平台管理员用户账号信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getusermodelmall")]
        public async Task<ReturnResult> GetUserModelMall(string token, int unitId, long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _iUserApplicationService.GetModel(userId, sessionBag.UnitId);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 修改用户
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyusermall")]
        public async Task<ReturnResult> ModifyUserMall(UserAccountInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(userInput.Name) || string.IsNullOrEmpty(userInput.Mobile)
                || string.IsNullOrEmpty(userInput.LoginName))
                {
                    r.flag = -2;
                    r.msg = "姓名、手机号码、账号不能为空";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }

                UserDetailViewModel user = _userService.GetUserById(userInput.UserId);
                UserAccountInputModel uModel = _userService.GetModelInfo(userInput.UserId);
                if (user != null && uModel != null)
                {
                    if (uModel.UnitId != sessionBag.UnitId)
                    {
                        r.flag = 0;
                        r.msg = "非法操作，您不能修改其它单位用户信息!";
                        return r;
                    }
                    //判断手机号码是否已经存在
                    if (userInput.Mobile != user.Mobile)
                    {
                        if (_iUserApplicationService.IsExistsMobile(userInput.Mobile))
                        {
                            r.flag = 0;
                            r.msg = "手机号码已经存在";
                            r.data.header = 2;
                            return r;
                        }
                    }
                    //判断登录名是否存在
                    if (userInput.LoginName != user.LoginName)
                    {
                        if (_iUserApplicationService.IsExistsLoginName(userInput.LoginName))
                        {
                            r.flag = 0;
                            r.msg = "该账号已经存在";
                            r.data.header = 3;
                            return r;
                        }
                    }

                    userInput.UserRoleType = UserRoleType.MallAdministrator;
                    r = _iUserApplicationService.ModifyUser(userInput);
                }

               
                //添加日志
                AuditLogInputModel log = new AuditLogInputModel();
                log.MallId = sessionBag.CurrentMallId;
                log.ModuleType = ModuleType.MallAdminAddUser;
                log.NodeId = userInput.UserId;
                log.UserId = sessionBag.UserId;
                log.Reason = "";
                log.Ip = userInput.IPAddress;
                log.LogContent = "" + sessionBag.LoginName + " 【修改平台管理员】" + userInput.LoginName + "";
                _auditLogApplicationService.AddLog(log);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 启用、禁用区县账号
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsuposeusermall")]
        [AntiSqlInject]
        public async Task<ReturnResult> UserSuposeMall(string token, long userId, int unitId, int statuz, string loginName, string IpAddress)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                UserAccountInputModel u = _iUserApplicationService.GetModelInfo(userId);
                if (u == null)
                {
                    r.flag = 0;
                    r.msg = "当前操作的对象不存在";
                    return r;
                }
                if (u.UnitId != sessionBag.UnitId)
                {
                    r.flag = -2;
                    r.msg = "您无权操作其它单位用户信息";
                    return r;
                }
                r = _iUserApplicationService.UserSupose(userId, (UserStatuz)statuz);
                //添加日志
                AuditLogInputModel log = new AuditLogInputModel();
                string strMsg = "禁用";
                if (statuz == UserStatuz.Normal.ToInt())
                {
                    strMsg = "启用";
                }
                log.MallId = sessionBag.CurrentMallId;
                log.ModuleType = ModuleType.MallAdminOperate;
                log.NodeId = userId;
                log.UserId = sessionBag.UserId;
                log.Reason = "";
                log.Ip = IpAddress;
                log.LogContent = "" + sessionBag.LoginName + " 【" + strMsg + "平台管理员】" + loginName + "";
                _auditLogApplicationService.AddLog(log);
                return r;
            });
            return result;
        }
        #endregion

        #region 离职 by jiangpeng 2018-9-26
        /// <summary>
        /// 离职
        /// </summary>
        /// <param name="token">令牌</param>
        /// <param name="userId">用户id</param>
        /// <param name="sk">密码</param>
        /// <param name="assignmentUserId">权限转交用户id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("quit")]
        [AntiSqlInject]
        public async Task<ReturnResult> Quit(string token, long userId, string sk,long assignmentUserId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (userId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                sessionBag.LoginName = StringFilter.SearchSql(sessionBag.LoginName);
                var userModel = _iUserApplicationService.GetUserByLoginName(sessionBag.LoginName);
                if (userModel == null)
                {
                    r.flag = -1;
                    r.msg = "账号不存在，请重新登录。";
                    return r;
                }
                string strUserPwd = SecurityHelper.MD5(sk + userModel.Salt);
                if (strUserPwd != userModel.Pwd)
                {
                    r.flag = 0;
                    r.msg = "密码错误。";
                    return r;
                }
                return _iUserApplicationService.UserQuit(userModel.UserId, assignmentUserId);
            });
            return result;
        }

        /// <summary>
        /// 重新入职
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("changequit")]
        public async Task<ReturnResult> ChangeQuit(string token,long userId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType  != UserRoleType.SchoolAuditor.ToInt() && sessionBag.UserType != UserRoleType.CountyAdmin.ToInt()
                    && sessionBag.UserType != UserRoleType.CityAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt()
                    && sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "无权限操作。";
                    return r;
                }
                return _iUserApplicationService.ChangeQuit(userId);
            });
            return result;
        }
        #endregion

        #region 微信账号绑定管理

        /// <summary>
        /// 微信账号绑定列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchuserbindopendid")]
        public async Task<QueryResult<UserBindOpenidViewModel>> SearchUserBindOpenId(SearchArgumentsInputModel args)
        {
            var result = await Task.Run(() =>
            {
                QueryResult<UserBindOpenidViewModel> r = new QueryResult<UserBindOpenidViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserId != args.UserId || sessionBag.UnitId != args.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                string where = string.Format(" UnitId = {0} AND UserId = {1}", args.UnitId, args.UserId);
                if (!string.IsNullOrWhiteSpace(args.Q))
                {
                    where += " AND WxAccount LIKE '%" + args.Q.Trim() + "%'";
                }
                r.Data = _wxUserBindOpenidApplicationService.GetUserBindOpenidInfo(where);
                r.flag = 1;
                r.msg = "查询成功。";
                return r;
            });
            return result;            
        }

        /// <summary>
        /// 微信账号解绑
        /// </summary>
        /// <param name="id"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("unbindopenid")]
        public async Task<ReturnResult> UpdateBindStatuz(long id,string token)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                r = _wxUserBindOpenidApplicationService.UnBindWxOpenid(id, sessionBag.UnitId, sessionBag.UserId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取微信登录小程序码
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getwxloginqrcode")]
        public async Task<ReturnResult> GetWxLoginQrCode(string token)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //生成确认收货二维码小程序
                string key = ComLib.GetGuid();
                string strTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                string cred_token = SecurityHelper.MD5(sessionBag.UserId + "|" + key + "|" + strTime, true);

                _wxBindFromPcApplicationService.Insert(new WxBindFromPcInputModel
                {
                    UserId = sessionBag.UserId,
                    SessionKey = key,
                    SessionTime = strTime,
                    EncodeKey = cred_token,
                    IpAddresz = "",
                    RegTime = DateTime.Now,
                    OpenId = "",
                    BindTime = null,
                    Statuz = 0
                });
                MemoryStream ms = new MemoryStream();
                WxJsonResult wxR = new WxJsonResult();
                try
                {
                    wxR = WxAppApi.GetWxaCodeUnlimit(Config.SenparcWeixinSetting.WxOpenAppId, ms, cred_token + "&" + sessionBag.UserId.ToString(), "pages/auth/scanLogin/scanLogin");
                }
                catch (Exception)
                {
                    //必须先注册，全局仅需注册一次
                    Senparc.Weixin.WxOpen.Containers.AccessTokenContainer.RegisterAsync(Config.SenparcWeixinSetting.WxOpenAppId, Config.SenparcWeixinSetting.WxOpenAppSecret);
                    wxR = WxAppApi.GetWxaCodeUnlimit(Config.SenparcWeixinSetting.WxOpenAppId, ms, cred_token + "&" + sessionBag.UserId.ToString(), "pages/auth/scanLogin/scanLogin");
                }
                if (wxR.errcode == Senparc.Weixin.ReturnCode.请求成功)
                {
                    r.msg = "data:image/png;base64," + Convert.ToBase64String(ms.GetBuffer());
                }
                r.flag = 1;
                return r;
            });
            return result;
        }

        #endregion

        #region 第三方登录
        /// <summary>
        /// 根据用户Id获取用户信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getuseridbythirduserid")]
        public async Task<long> GetUserIdByThirdUserId(string thirdUserId)
        {
            thirdUserId = StringFilter.SearchSql(thirdUserId);
            var UserId = await Task.Run(() =>
            {
                return _iUserApplicationService.GetUserIdByThirdUserId(thirdUserId);
            });
            return UserId;
        }

        #endregion 第三方登录    

        #region 获取用户信息 2023-04-11 by lss

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getinfo")]
        public async Task<QueryResult<UserDetailViewModel>> GetUserInfo(string token)
        {
            var result = await Task.Run(() =>
            {
                QueryResult<UserDetailViewModel> r = new QueryResult<UserDetailViewModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                r.Entity = _iUserApplicationService.GetUserById(sessionBag.UserId);
                r.flag = 1;
                r.msg = "";
                return r;
            });
            return result;
        }


        #endregion
    }
}
