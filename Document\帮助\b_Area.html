﻿ Pid = entity.Pid,
 Name = entity.Name,
 Brief = entity.Brief,
 EnName = entity.EnName,
 EnBrief = entity.EnBrief,
 Code = entity.Code,
 Zip = entity.Zip,
 Depth = entity.Depth,
 Sort = entity.Sort,
 Path = entity.Path,
 Remark = entity.Remark,


 Pid = model.Pid,
 Name = model.Name,
 Brief = model.Brief,
 EnName = model.EnName,
 EnBrief = model.EnBrief,
 Code = model.Code,
 Zip = model.Zip,
 Depth = model.Depth,
 Sort = model.Sort,
 Path = model.Path,
 Remark = model.Remark,


 temp.Pid = model.Pid,
 temp.Name = model.Name,
 temp.Brief = model.Brief,
 temp.EnName = model.EnName,
 temp.EnBrief = model.EnBrief,
 temp.Code = model.Code,
 temp.Zip = model.Zip,
 temp.Depth = model.Depth,
 temp.Sort = model.Sort,
 temp.Path = model.Path,
 temp.Remark = model.Remark,

 AreaId = item.AreaId,
 Pid = item.Pid,
 Name = item.Name,
 Brief = item.Brief,
 EnName = item.EnName,
 EnBrief = item.EnBrief,
 Code = item.Code,
 Zip = item.Zip,
 Depth = item.Depth,
 Sort = item.Sort,
 Path = item.Path,
 Remark = item.Remark,

public class AreaInputModel
{
 [Display(Name = "Id")] 
    public int AreaId {get; set; }
    
 [Display(Name = "父Id")] 
    public int Pid {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "简称")] 
    public string Brief {get; set; }
    
 [Display(Name = "英文")] 
    public string EnName {get; set; }
    
 [Display(Name = "英文简称")] 
    public string EnBrief {get; set; }
    
 [Display(Name = "编码")] 
    public string Code {get; set; }
    
 [Display(Name = "邮编")] 
    public string Zip {get; set; }
    
 [Display(Name = "深度")] 
    public int Depth {get; set; }
    
 [Display(Name = "排序")] 
    public int Sort {get; set; }
    
 [Display(Name = "路径")] 
    public string Path {get; set; }
    
 [Display(Name = "备注")] 
    public string Remark {get; set; }
    
 }
 
 public class AreaViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int AreaId {get; set; }
    
    /// <summary>
    /// 父Id
    /// </summary>
    public int Pid {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 简称
    /// </summary>
    public string Brief {get; set; }
    
    /// <summary>
    /// 英文
    /// </summary>
    public string EnName {get; set; }
    
    /// <summary>
    /// 英文简称
    /// </summary>
    public string EnBrief {get; set; }
    
    /// <summary>
    /// 编码
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 邮编
    /// </summary>
    public string Zip {get; set; }
    
    /// <summary>
    /// 深度
    /// </summary>
    public int Depth {get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort {get; set; }
    
    /// <summary>
    /// 路径
    /// </summary>
    public string Path {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Remark {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Pid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Brief, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Brief, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入简称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EnName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EnName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入英文" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EnBrief, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EnBrief, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入英文简称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入编码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Zip, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Zip, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮编" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入深度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sort, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sort, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Path, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Path, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Remark, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Remark, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
  




 { field: 'Pid', title: '父Id', sortable: true },
                 
 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'Brief', title: '简称', sortable: true },
                 
 { field: 'EnName', title: '英文', sortable: true },
                 
 { field: 'EnBrief', title: '英文简称', sortable: true },
                 
 { field: 'Code', title: '编码', sortable: true },
                 
 { field: 'Zip', title: '邮编', sortable: true },
                 
 { field: 'Depth', title: '深度', sortable: true },
                 
 { field: 'Sort', title: '排序', sortable: true },
                 
 { field: 'Path', title: '路径', sortable: true },
                 
 { field: 'Remark', title: '备注', sortable: true },
                 
o.Pid,                 
o.Name,                 
o.Brief,                 
o.EnName,                 
o.EnBrief,                 
o.Code,                 
o.Zip,                 
o.Depth,                 
o.Sort,                 
o.Path,                 
o.Remark,                 
        
        $('#Pid').val(d.data.rows.Pid);          
        $('#Name').val(d.data.rows.Name);          
        $('#Brief').val(d.data.rows.Brief);          
        $('#EnName').val(d.data.rows.EnName);          
        $('#EnBrief').val(d.data.rows.EnBrief);          
        $('#Code').val(d.data.rows.Code);          
        $('#Zip').val(d.data.rows.Zip);          
        $('#Depth').val(d.data.rows.Depth);          
        $('#Sort').val(d.data.rows.Sort);          
        $('#Path').val(d.data.rows.Path);          
        $('#Remark').val(d.data.rows.Remark);          

 $('#th_Pid').html(' 父Id');               
 $('#th_Name').html(' 名称');               
 $('#th_Brief').html(' 简称');               
 $('#th_EnName').html(' 英文');               
 $('#th_EnBrief').html(' 英文简称');               
 $('#th_Code').html(' 编码');               
 $('#th_Zip').html(' 邮编');               
 $('#th_Depth').html(' 深度');               
 $('#th_Sort').html(' 排序');               
 $('#th_Path').html(' 路径');               
 $('#th_Remark').html(' 备注');               
 
 $('#tr_Pid').hide();               
 $('#tr_Name').hide();               
 $('#tr_Brief').hide();               
 $('#tr_EnName').hide();               
 $('#tr_EnBrief').hide();               
 $('#tr_Code').hide();               
 $('#tr_Zip').hide();               
 $('#tr_Depth').hide();               
 $('#tr_Sort').hide();               
 $('#tr_Path').hide();               
 $('#tr_Remark').hide();               

 , "Pid" : pid
 , "Name" : name
 , "Brief" : brief
 , "EnName" : enName
 , "EnBrief" : enBrief
 , "Code" : code
 , "Zip" : zip
 , "Depth" : depth
 , "Sort" : sort
 , "Path" : path
 , "Remark" : remark

 var pid = $('#o_Pid').val();
 var name = $('#o_Name').val();
 var brief = $('#o_Brief').val();
 var enName = $('#o_EnName').val();
 var enBrief = $('#o_EnBrief').val();
 var code = $('#o_Code').val();
 var zip = $('#o_Zip').val();
 var depth = $('#o_Depth').val();
 var sort = $('#o_Sort').val();
 var path = $('#o_Path').val();
 var remark = $('#o_Remark').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父Id' : '产品名称', d.data.rows.Pid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '简称' : '产品名称', d.data.rows.Brief);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '英文' : '产品名称', d.data.rows.EnName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '英文简称' : '产品名称', d.data.rows.EnBrief);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '编码' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮编' : '产品名称', d.data.rows.Zip);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '深度' : '产品名称', d.data.rows.Depth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '排序' : '产品名称', d.data.rows.Sort);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '路径' : '产品名称', d.data.rows.Path);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Remark);



