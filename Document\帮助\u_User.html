﻿ LoginName = entity.LoginName,
 Mobile = entity.Mobile,
 Email = entity.Email,
 Pwd = entity.Pwd,
 Salt = entity.Salt,
 RegTime = entity.RegTime,
 Statuz = entity.Statuz,
 UserType = entity.UserType,
 VerifiedMobile = entity.VerifiedMobile,
 PromotionNum = entity.PromotionNum,
 SourceType = entity.SourceType,
 RecordUserId = entity.RecordUserId,
 MallId = entity.MallId,


 LoginName = model.LoginName,
 Mobile = model.Mobile,
 Email = model.Email,
 Pwd = model.Pwd,
 Salt = model.Salt,
 RegTime = model.RegTime,
 Statuz = model.Statuz,
 UserType = model.UserType,
 VerifiedMobile = model.VerifiedMobile,
 PromotionNum = model.PromotionNum,
 SourceType = model.SourceType,
 RecordUserId = model.RecordUserId,
 MallId = model.MallId,


 temp.LoginName = model.LoginName,
 temp.Mobile = model.Mobile,
 temp.Email = model.Email,
 temp.Pwd = model.Pwd,
 temp.Salt = model.Salt,
 temp.RegTime = model.RegTime,
 temp.Statuz = model.Statuz,
 temp.UserType = model.UserType,
 temp.VerifiedMobile = model.VerifiedMobile,
 temp.PromotionNum = model.PromotionNum,
 temp.SourceType = model.SourceType,
 temp.RecordUserId = model.RecordUserId,
 temp.MallId = model.MallId,

 UserId = item.UserId,
 LoginName = item.LoginName,
 Mobile = item.Mobile,
 Email = item.Email,
 Pwd = item.Pwd,
 Salt = item.Salt,
 RegTime = item.RegTime,
 Statuz = item.Statuz,
 UserType = item.UserType,
 VerifiedMobile = item.VerifiedMobile,
 PromotionNum = item.PromotionNum,
 SourceType = item.SourceType,
 RecordUserId = item.RecordUserId,
 MallId = item.MallId,

public class UserInputModel
{
 [Display(Name = "Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "登录名")] 
    public string LoginName {get; set; }
    
 [Display(Name = "手机")] 
    public string Mobile {get; set; }
    
 [Display(Name = "邮箱")] 
    public string Email {get; set; }
    
 [Display(Name = "密码")] 
    public string Pwd {get; set; }
    
 [Display(Name = "加盐值")] 
    public string Salt {get; set; }
    
 [Display(Name = "注册时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "状态（0：禁用，1：正常，2：待审核）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "用户类型（10：学校审核员（超管）：11：学校申报员； 20：卖家超管； 21：卖家平台管理员；22：卖家产品管理员；30：区县审核员（超级管理员）；40：市级审核员（超级管理员）；50：平台管理员）")] 
    public int UserType {get; set; }
    
 [Display(Name = "通过验证手机号")] 
    public string VerifiedMobile {get; set; }
    
 [Display(Name = "推广号")] 
    public string PromotionNum {get; set; }
    
 [Display(Name = "账号添加方式（1：注册；2：前台添加；3：后台添加）")] 
    public int SourceType {get; set; }
    
 [Display(Name = "录入人Id")] 
    public long RecordUserId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 }
 
 public class UserViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 登录名
    /// </summary>
    public string LoginName {get; set; }
    
    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile {get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email {get; set; }
    
    /// <summary>
    /// 密码
    /// </summary>
    public string Pwd {get; set; }
    
    /// <summary>
    /// 加盐值
    /// </summary>
    public string Salt {get; set; }
    
    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 状态（0：禁用，1：正常，2：待审核）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 用户类型（10：学校审核员（超管）：11：学校申报员； 20：卖家超管； 21：卖家平台管理员；22：卖家产品管理员；30：区县审核员（超级管理员）；40：市级审核员（超级管理员）；50：平台管理员）
    /// </summary>
    public int UserType {get; set; }
    
    /// <summary>
    /// 通过验证手机号
    /// </summary>
    public string VerifiedMobile {get; set; }
    
    /// <summary>
    /// 推广号
    /// </summary>
    public string PromotionNum {get; set; }
    
    /// <summary>
    /// 账号添加方式（1：注册；2：前台添加；3：后台添加）
    /// </summary>
    public int SourceType {get; set; }
    
    /// <summary>
    /// 录入人Id
    /// </summary>
    public long RecordUserId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.LoginName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LoginName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入登录名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Mobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Mobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Email, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮箱" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Pwd, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pwd, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入密码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Salt, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Salt, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入加盐值" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入注册时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（0：禁用，1：正常，2：待审核）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户类型（10：学校审核员（超管）：11：学校申报员； 20：卖家超管； 21：卖家平台管理员；22：卖家产品管理员；30：区县审核员（超级管理员）；40：市级审核员（超级管理员）；50：平台管理员）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.VerifiedMobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.VerifiedMobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入通过验证手机号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PromotionNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PromotionNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入推广号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SourceType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SourceType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入账号添加方式（1：注册；2：前台添加；3：后台添加）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RecordUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RecordUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入录入人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
  




 { field: 'LoginName', title: '登录名', sortable: true },
                 
 { field: 'Mobile', title: '手机', sortable: true },
                 
 { field: 'Email', title: '邮箱', sortable: true },
                 
 { field: 'Pwd', title: '密码', sortable: true },
                 
 { field: 'Salt', title: '加盐值', sortable: true },
                 
 { field: 'RegTime', title: '注册时间', sortable: true },
                 
 { field: 'Statuz', title: '状态（0：禁用，1：正常，2：待审核）', sortable: true },
                 
 { field: 'UserType', title: '用户类型（10：学校审核员（超管）：11：学校申报员； 20：卖家超管； 21：卖家平台管理员；22：卖家产品管理员；30：区县审核员（超级管理员）；40：市级审核员（超级管理员）；50：平台管理员）', sortable: true },
                 
 { field: 'VerifiedMobile', title: '通过验证手机号', sortable: true },
                 
 { field: 'PromotionNum', title: '推广号', sortable: true },
                 
 { field: 'SourceType', title: '账号添加方式（1：注册；2：前台添加；3：后台添加）', sortable: true },
                 
 { field: 'RecordUserId', title: '录入人Id', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
o.LoginName,                 
o.Mobile,                 
o.Email,                 
o.Pwd,                 
o.Salt,                 
o.RegTime,                 
o.Statuz,                 
o.UserType,                 
o.VerifiedMobile,                 
o.PromotionNum,                 
o.SourceType,                 
o.RecordUserId,                 
o.MallId,                 
        
        $('#LoginName').val(d.data.rows.LoginName);          
        $('#Mobile').val(d.data.rows.Mobile);          
        $('#Email').val(d.data.rows.Email);          
        $('#Pwd').val(d.data.rows.Pwd);          
        $('#Salt').val(d.data.rows.Salt);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#UserType').val(d.data.rows.UserType);          
        $('#VerifiedMobile').val(d.data.rows.VerifiedMobile);          
        $('#PromotionNum').val(d.data.rows.PromotionNum);          
        $('#SourceType').val(d.data.rows.SourceType);          
        $('#RecordUserId').val(d.data.rows.RecordUserId);          
        $('#MallId').val(d.data.rows.MallId);          

 $('#th_LoginName').html(' 登录名');               
 $('#th_Mobile').html(' 手机');               
 $('#th_Email').html(' 邮箱');               
 $('#th_Pwd').html(' 密码');               
 $('#th_Salt').html(' 加盐值');               
 $('#th_RegTime').html(' 注册时间');               
 $('#th_Statuz').html(' 状态（0：禁用，1：正常，2：待审核）');               
 $('#th_UserType').html(' 用户类型（10：学校审核员（超管）：11：学校申报员； 20：卖家超管； 21：卖家平台管理员；22：卖家产品管理员；30：区县审核员（超级管理员）；40：市级审核员（超级管理员）；50：平台管理员）');               
 $('#th_VerifiedMobile').html(' 通过验证手机号');               
 $('#th_PromotionNum').html(' 推广号');               
 $('#th_SourceType').html(' 账号添加方式（1：注册；2：前台添加；3：后台添加）');               
 $('#th_RecordUserId').html(' 录入人Id');               
 $('#th_MallId').html(' 商城Id');               
 
 $('#tr_LoginName').hide();               
 $('#tr_Mobile').hide();               
 $('#tr_Email').hide();               
 $('#tr_Pwd').hide();               
 $('#tr_Salt').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_UserType').hide();               
 $('#tr_VerifiedMobile').hide();               
 $('#tr_PromotionNum').hide();               
 $('#tr_SourceType').hide();               
 $('#tr_RecordUserId').hide();               
 $('#tr_MallId').hide();               

 , "LoginName" : loginName
 , "Mobile" : mobile
 , "Email" : email
 , "Pwd" : pwd
 , "Salt" : salt
 , "RegTime" : regTime
 , "Statuz" : statuz
 , "UserType" : userType
 , "VerifiedMobile" : verifiedMobile
 , "PromotionNum" : promotionNum
 , "SourceType" : sourceType
 , "RecordUserId" : recordUserId
 , "MallId" : mallId

 var loginName = $('#o_LoginName').val();
 var mobile = $('#o_Mobile').val();
 var email = $('#o_Email').val();
 var pwd = $('#o_Pwd').val();
 var salt = $('#o_Salt').val();
 var regTime = $('#o_RegTime').val();
 var statuz = $('#o_Statuz').val();
 var userType = $('#o_UserType').val();
 var verifiedMobile = $('#o_VerifiedMobile').val();
 var promotionNum = $('#o_PromotionNum').val();
 var sourceType = $('#o_SourceType').val();
 var recordUserId = $('#o_RecordUserId').val();
 var mallId = $('#o_MallId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '登录名' : '产品名称', d.data.rows.LoginName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机' : '产品名称', d.data.rows.Mobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮箱' : '产品名称', d.data.rows.Email);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '密码' : '产品名称', d.data.rows.Pwd);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '加盐值' : '产品名称', d.data.rows.Salt);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '注册时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（0：禁用，1：正常，2：待审核）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户类型（10：学校审核员（超管）：11：学校申报员； 20：卖家超管； 21：卖家平台管理员；22：卖家产品管理员；30：区县审核员（超级管理员）；40：市级审核员（超级管理员）；50：平台管理员）' : '产品名称', d.data.rows.UserType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '通过验证手机号' : '产品名称', d.data.rows.VerifiedMobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '推广号' : '产品名称', d.data.rows.PromotionNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '账号添加方式（1：注册；2：前台添加；3：后台添加）' : '产品名称', d.data.rows.SourceType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '录入人Id' : '产品名称', d.data.rows.RecordUserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);



