﻿ UnitId = entity.UnitId,
 ProductionLicense = entity.ProductionLicense,
 AgentLicense = entity.AgentLicense,
 EmployessNum = entity.EmployessNum,
 UnitNature = entity.UnitNature,
 Logo = entity.Logo,
 PageImgUrl = entity.PageImgUrl,
 VipGrade = entity.VipGrade,
 UnitsUrl = entity.UnitsUrl,
 LoginPic = entity.LoginPic,
 AccountBank = entity.AccountBank,
 BankAccount = entity.BankAccount,
 RegisteredCapital = entity.RegisteredCapital,
 Feature = entity.Feature,
 UnitsType = entity.UnitsType,
 IndustrySector = entity.IndustrySector,


 UnitId = model.UnitId,
 ProductionLicense = model.ProductionLicense,
 AgentLicense = model.AgentLicense,
 EmployessNum = model.EmployessNum,
 UnitNature = model.UnitNature,
 Logo = model.Logo,
 PageImgUrl = model.PageImgUrl,
 VipGrade = model.VipGrade,
 UnitsUrl = model.UnitsUrl,
 LoginPic = model.LoginPic,
 AccountBank = model.AccountBank,
 BankAccount = model.BankAccount,
 RegisteredCapital = model.RegisteredCapital,
 Feature = model.Feature,
 UnitsType = model.UnitsType,
 IndustrySector = model.IndustrySector,


 temp.UnitId = model.UnitId,
 temp.ProductionLicense = model.ProductionLicense,
 temp.AgentLicense = model.AgentLicense,
 temp.EmployessNum = model.EmployessNum,
 temp.UnitNature = model.UnitNature,
 temp.Logo = model.Logo,
 temp.PageImgUrl = model.PageImgUrl,
 temp.VipGrade = model.VipGrade,
 temp.UnitsUrl = model.UnitsUrl,
 temp.LoginPic = model.LoginPic,
 temp.AccountBank = model.AccountBank,
 temp.BankAccount = model.BankAccount,
 temp.RegisteredCapital = model.RegisteredCapital,
 temp.Feature = model.Feature,
 temp.UnitsType = model.UnitsType,
 temp.IndustrySector = model.IndustrySector,

 SupplierId = item.SupplierId,
 UnitId = item.UnitId,
 ProductionLicense = item.ProductionLicense,
 AgentLicense = item.AgentLicense,
 EmployessNum = item.EmployessNum,
 UnitNature = item.UnitNature,
 Logo = item.Logo,
 PageImgUrl = item.PageImgUrl,
 VipGrade = item.VipGrade,
 UnitsUrl = item.UnitsUrl,
 LoginPic = item.LoginPic,
 AccountBank = item.AccountBank,
 BankAccount = item.BankAccount,
 RegisteredCapital = item.RegisteredCapital,
 Feature = item.Feature,
 UnitsType = item.UnitsType,
 IndustrySector = item.IndustrySector,

public class SupplierInputModel
{
 [Display(Name = "id")] 
    public int SupplierId {get; set; }
    
 [Display(Name = "单位id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "生产许可证")] 
    public string ProductionLicense {get; set; }
    
 [Display(Name = "代理许可证")] 
    public string AgentLicense {get; set; }
    
 [Display(Name = "单位规模")] 
    public string EmployessNum {get; set; }
    
 [Display(Name = "单位性质")] 
    public string UnitNature {get; set; }
    
 [Display(Name = "Logo")] 
    public string Logo {get; set; }
    
 [Display(Name = "首页图片")] 
    public string PageImgUrl {get; set; }
    
 [Display(Name = "VIP等级")] 
    public int VipGrade {get; set; }
    
 [Display(Name = "单位网址")] 
    public string UnitsUrl {get; set; }
    
 [Display(Name = "登录界面图片")] 
    public string LoginPic {get; set; }
    
 [Display(Name = "开户银行")] 
    public string AccountBank {get; set; }
    
 [Display(Name = "银行帐号")] 
    public string BankAccount {get; set; }
    
 [Display(Name = "注册资金")] 
    public string RegisteredCapital {get; set; }
    
 [Display(Name = "单位特色")] 
    public string Feature {get; set; }
    
 [Display(Name = "单位类型")] 
    public string UnitsType {get; set; }
    
 [Display(Name = "行业类别")] 
    public string IndustrySector {get; set; }
    
 }
 
 public class SupplierViewModel
 {
    /// <summary>
    /// id
    /// </summary>
    public int SupplierId {get; set; }
    
    /// <summary>
    /// 单位id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 生产许可证
    /// </summary>
    public string ProductionLicense {get; set; }
    
    /// <summary>
    /// 代理许可证
    /// </summary>
    public string AgentLicense {get; set; }
    
    /// <summary>
    /// 单位规模
    /// </summary>
    public string EmployessNum {get; set; }
    
    /// <summary>
    /// 单位性质
    /// </summary>
    public string UnitNature {get; set; }
    
    /// <summary>
    /// Logo
    /// </summary>
    public string Logo {get; set; }
    
    /// <summary>
    /// 首页图片
    /// </summary>
    public string PageImgUrl {get; set; }
    
    /// <summary>
    /// VIP等级
    /// </summary>
    public int VipGrade {get; set; }
    
    /// <summary>
    /// 单位网址
    /// </summary>
    public string UnitsUrl {get; set; }
    
    /// <summary>
    /// 登录界面图片
    /// </summary>
    public string LoginPic {get; set; }
    
    /// <summary>
    /// 开户银行
    /// </summary>
    public string AccountBank {get; set; }
    
    /// <summary>
    /// 银行帐号
    /// </summary>
    public string BankAccount {get; set; }
    
    /// <summary>
    /// 注册资金
    /// </summary>
    public string RegisteredCapital {get; set; }
    
    /// <summary>
    /// 单位特色
    /// </summary>
    public string Feature {get; set; }
    
    /// <summary>
    /// 单位类型
    /// </summary>
    public string UnitsType {get; set; }
    
    /// <summary>
    /// 行业类别
    /// </summary>
    public string IndustrySector {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductionLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductionLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入生产许可证" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AgentLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AgentLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入代理许可证" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EmployessNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EmployessNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位规模" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitNature, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitNature, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位性质" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Logo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Logo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入Logo" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PageImgUrl, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PageImgUrl, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入首页图片" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.VipGrade, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.VipGrade, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入VIP等级" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitsUrl, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitsUrl, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位网址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LoginPic, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LoginPic, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入登录界面图片" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AccountBank, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AccountBank, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入开户银行" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BankAccount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BankAccount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入银行帐号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegisteredCapital, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegisteredCapital, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入注册资金" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Feature, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Feature, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位特色" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitsType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitsType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位类型" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IndustrySector, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IndustrySector, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入行业类别" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位id', sortable: true },
                 
 { field: 'ProductionLicense', title: '生产许可证', sortable: true },
                 
 { field: 'AgentLicense', title: '代理许可证', sortable: true },
                 
 { field: 'EmployessNum', title: '单位规模', sortable: true },
                 
 { field: 'UnitNature', title: '单位性质', sortable: true },
                 
 { field: 'Logo', title: 'Logo', sortable: true },
                 
 { field: 'PageImgUrl', title: '首页图片', sortable: true },
                 
 { field: 'VipGrade', title: 'VIP等级', sortable: true },
                 
 { field: 'UnitsUrl', title: '单位网址', sortable: true },
                 
 { field: 'LoginPic', title: '登录界面图片', sortable: true },
                 
 { field: 'AccountBank', title: '开户银行', sortable: true },
                 
 { field: 'BankAccount', title: '银行帐号', sortable: true },
                 
 { field: 'RegisteredCapital', title: '注册资金', sortable: true },
                 
 { field: 'Feature', title: '单位特色', sortable: true },
                 
 { field: 'UnitsType', title: '单位类型', sortable: true },
                 
 { field: 'IndustrySector', title: '行业类别', sortable: true },
                 
o.UnitId,                 
o.ProductionLicense,                 
o.AgentLicense,                 
o.EmployessNum,                 
o.UnitNature,                 
o.Logo,                 
o.PageImgUrl,                 
o.VipGrade,                 
o.UnitsUrl,                 
o.LoginPic,                 
o.AccountBank,                 
o.BankAccount,                 
o.RegisteredCapital,                 
o.Feature,                 
o.UnitsType,                 
o.IndustrySector,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#ProductionLicense').val(d.data.rows.ProductionLicense);          
        $('#AgentLicense').val(d.data.rows.AgentLicense);          
        $('#EmployessNum').val(d.data.rows.EmployessNum);          
        $('#UnitNature').val(d.data.rows.UnitNature);          
        $('#Logo').val(d.data.rows.Logo);          
        $('#PageImgUrl').val(d.data.rows.PageImgUrl);          
        $('#VipGrade').val(d.data.rows.VipGrade);          
        $('#UnitsUrl').val(d.data.rows.UnitsUrl);          
        $('#LoginPic').val(d.data.rows.LoginPic);          
        $('#AccountBank').val(d.data.rows.AccountBank);          
        $('#BankAccount').val(d.data.rows.BankAccount);          
        $('#RegisteredCapital').val(d.data.rows.RegisteredCapital);          
        $('#Feature').val(d.data.rows.Feature);          
        $('#UnitsType').val(d.data.rows.UnitsType);          
        $('#IndustrySector').val(d.data.rows.IndustrySector);          

 $('#th_UnitId').html(' 单位id');               
 $('#th_ProductionLicense').html(' 生产许可证');               
 $('#th_AgentLicense').html(' 代理许可证');               
 $('#th_EmployessNum').html(' 单位规模');               
 $('#th_UnitNature').html(' 单位性质');               
 $('#th_Logo').html(' Logo');               
 $('#th_PageImgUrl').html(' 首页图片');               
 $('#th_VipGrade').html(' VIP等级');               
 $('#th_UnitsUrl').html(' 单位网址');               
 $('#th_LoginPic').html(' 登录界面图片');               
 $('#th_AccountBank').html(' 开户银行');               
 $('#th_BankAccount').html(' 银行帐号');               
 $('#th_RegisteredCapital').html(' 注册资金');               
 $('#th_Feature').html(' 单位特色');               
 $('#th_UnitsType').html(' 单位类型');               
 $('#th_IndustrySector').html(' 行业类别');               
 
 $('#tr_UnitId').hide();               
 $('#tr_ProductionLicense').hide();               
 $('#tr_AgentLicense').hide();               
 $('#tr_EmployessNum').hide();               
 $('#tr_UnitNature').hide();               
 $('#tr_Logo').hide();               
 $('#tr_PageImgUrl').hide();               
 $('#tr_VipGrade').hide();               
 $('#tr_UnitsUrl').hide();               
 $('#tr_LoginPic').hide();               
 $('#tr_AccountBank').hide();               
 $('#tr_BankAccount').hide();               
 $('#tr_RegisteredCapital').hide();               
 $('#tr_Feature').hide();               
 $('#tr_UnitsType').hide();               
 $('#tr_IndustrySector').hide();               

 , "UnitId" : unitId
 , "ProductionLicense" : productionLicense
 , "AgentLicense" : agentLicense
 , "EmployessNum" : employessNum
 , "UnitNature" : unitNature
 , "Logo" : logo
 , "PageImgUrl" : pageImgUrl
 , "VipGrade" : vipGrade
 , "UnitsUrl" : unitsUrl
 , "LoginPic" : loginPic
 , "AccountBank" : accountBank
 , "BankAccount" : bankAccount
 , "RegisteredCapital" : registeredCapital
 , "Feature" : feature
 , "UnitsType" : unitsType
 , "IndustrySector" : industrySector

 var unitId = $('#o_UnitId').val();
 var productionLicense = $('#o_ProductionLicense').val();
 var agentLicense = $('#o_AgentLicense').val();
 var employessNum = $('#o_EmployessNum').val();
 var unitNature = $('#o_UnitNature').val();
 var logo = $('#o_Logo').val();
 var pageImgUrl = $('#o_PageImgUrl').val();
 var vipGrade = $('#o_VipGrade').val();
 var unitsUrl = $('#o_UnitsUrl').val();
 var loginPic = $('#o_LoginPic').val();
 var accountBank = $('#o_AccountBank').val();
 var bankAccount = $('#o_BankAccount').val();
 var registeredCapital = $('#o_RegisteredCapital').val();
 var feature = $('#o_Feature').val();
 var unitsType = $('#o_UnitsType').val();
 var industrySector = $('#o_IndustrySector').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '生产许可证' : '产品名称', d.data.rows.ProductionLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '代理许可证' : '产品名称', d.data.rows.AgentLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位规模' : '产品名称', d.data.rows.EmployessNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位性质' : '产品名称', d.data.rows.UnitNature);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'Logo' : '产品名称', d.data.rows.Logo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '首页图片' : '产品名称', d.data.rows.PageImgUrl);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'VIP等级' : '产品名称', d.data.rows.VipGrade);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位网址' : '产品名称', d.data.rows.UnitsUrl);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '登录界面图片' : '产品名称', d.data.rows.LoginPic);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '开户银行' : '产品名称', d.data.rows.AccountBank);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '银行帐号' : '产品名称', d.data.rows.BankAccount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '注册资金' : '产品名称', d.data.rows.RegisteredCapital);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位特色' : '产品名称', d.data.rows.Feature);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位类型' : '产品名称', d.data.rows.UnitsType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '行业类别' : '产品名称', d.data.rows.IndustrySector);



