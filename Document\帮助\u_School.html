﻿ UnitId = entity.UnitId,
 ClassNum = entity.ClassNum,
 StudentNum = entity.StudentNum,
 TeacherNum = entity.TeacherNum,
 Nature = entity.Nature,
 SchoolStage = entity.SchoolStage,
 FloorArea = entity.FloorArea,
 BuildArea = entity.BuildArea,
 BeLongUnit = entity.BeLongUnit,
 HeadMaster = entity.HeadMaster,
 IsCloseDeclare = entity.IsCloseDeclare,


 UnitId = model.UnitId,
 ClassNum = model.ClassNum,
 StudentNum = model.StudentNum,
 TeacherNum = model.TeacherNum,
 Nature = model.Nature,
 SchoolStage = model.SchoolStage,
 FloorArea = model.FloorArea,
 BuildArea = model.BuildArea,
 BeLongUnit = model.BeLongUnit,
 HeadMaster = model.HeadMaster,
 IsCloseDeclare = model.IsCloseDeclare,


 temp.UnitId = model.UnitId,
 temp.ClassNum = model.ClassNum,
 temp.StudentNum = model.StudentNum,
 temp.TeacherNum = model.TeacherNum,
 temp.Nature = model.Nature,
 temp.SchoolStage = model.SchoolStage,
 temp.FloorArea = model.FloorArea,
 temp.BuildArea = model.BuildArea,
 temp.BeLongUnit = model.BeLongUnit,
 temp.HeadMaster = model.HeadMaster,
 temp.IsCloseDeclare = model.IsCloseDeclare,

 SchoolEId = item.SchoolEId,
 UnitId = item.UnitId,
 ClassNum = item.ClassNum,
 StudentNum = item.StudentNum,
 TeacherNum = item.TeacherNum,
 Nature = item.Nature,
 SchoolStage = item.SchoolStage,
 FloorArea = item.FloorArea,
 BuildArea = item.BuildArea,
 BeLongUnit = item.BeLongUnit,
 HeadMaster = item.HeadMaster,
 IsCloseDeclare = item.IsCloseDeclare,

public class SchoolInputModel
{
 [Display(Name = "id")] 
    public long SchoolEId {get; set; }
    
 [Display(Name = "单位id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "班级数")] 
    public int ClassNum {get; set; }
    
 [Display(Name = "在校学生数")] 
    public int StudentNum {get; set; }
    
 [Display(Name = "教职工人数")] 
    public int TeacherNum {get; set; }
    
 [Display(Name = "学校性质(1：公办  2：民办)")] 
    public int Nature {get; set; }
    
 [Display(Name = "学段")] 
    public int SchoolStage {get; set; }
    
 [Display(Name = "占地面积")] 
    public decimal FloorArea {get; set; }
    
 [Display(Name = "建筑面积")] 
    public decimal BuildArea {get; set; }
    
 [Display(Name = "是否市直属(1：是，0:否)")] 
    public bool BeLongUnit {get; set; }
    
 [Display(Name = "校长")] 
    public string HeadMaster {get; set; }
    
 [Display(Name = "学校是否禁止申报（0：否 ，开启  1：是，禁止）")] 
    public int IsCloseDeclare {get; set; }
    
 }
 
 public class SchoolViewModel
 {
    /// <summary>
    /// id
    /// </summary>
    public long SchoolEId {get; set; }
    
    /// <summary>
    /// 单位id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 班级数
    /// </summary>
    public int ClassNum {get; set; }
    
    /// <summary>
    /// 在校学生数
    /// </summary>
    public int StudentNum {get; set; }
    
    /// <summary>
    /// 教职工人数
    /// </summary>
    public int TeacherNum {get; set; }
    
    /// <summary>
    /// 学校性质(1：公办  2：民办)
    /// </summary>
    public int Nature {get; set; }
    
    /// <summary>
    /// 学段
    /// </summary>
    public int SchoolStage {get; set; }
    
    /// <summary>
    /// 占地面积
    /// </summary>
    public decimal FloorArea {get; set; }
    
    /// <summary>
    /// 建筑面积
    /// </summary>
    public decimal BuildArea {get; set; }
    
    /// <summary>
    /// 是否市直属(1：是，0:否)
    /// </summary>
    public bool BeLongUnit {get; set; }
    
    /// <summary>
    /// 校长
    /// </summary>
    public string HeadMaster {get; set; }
    
    /// <summary>
    /// 学校是否禁止申报（0：否 ，开启  1：是，禁止）
    /// </summary>
    public int IsCloseDeclare {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ClassNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ClassNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入班级数" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StudentNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StudentNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入在校学生数" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.TeacherNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.TeacherNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入教职工人数" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Nature, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Nature, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校性质(1：公办  2：民办)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolStage, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolStage, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学段" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FloorArea, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FloorArea, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入占地面积" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BuildArea, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BuildArea, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入建筑面积" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BeLongUnit, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BeLongUnit, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否市直属(1：是，0:否)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.HeadMaster, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.HeadMaster, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入校长" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsCloseDeclare, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsCloseDeclare, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校是否禁止申报（0：否 ，开启  1：是，禁止）" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位id', sortable: true },
                 
 { field: 'ClassNum', title: '班级数', sortable: true },
                 
 { field: 'StudentNum', title: '在校学生数', sortable: true },
                 
 { field: 'TeacherNum', title: '教职工人数', sortable: true },
                 
 { field: 'Nature', title: '学校性质(1：公办  2：民办)', sortable: true },
                 
 { field: 'SchoolStage', title: '学段', sortable: true },
                 
 { field: 'FloorArea', title: '占地面积', sortable: true },
                 
 { field: 'BuildArea', title: '建筑面积', sortable: true },
                 
 { field: 'BeLongUnit', title: '是否市直属(1：是，0:否)', sortable: true },
                 
 { field: 'HeadMaster', title: '校长', sortable: true },
                 
 { field: 'IsCloseDeclare', title: '学校是否禁止申报（0：否 ，开启  1：是，禁止）', sortable: true },
                 
o.UnitId,                 
o.ClassNum,                 
o.StudentNum,                 
o.TeacherNum,                 
o.Nature,                 
o.SchoolStage,                 
o.FloorArea,                 
o.BuildArea,                 
o.BeLongUnit,                 
o.HeadMaster,                 
o.IsCloseDeclare,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#ClassNum').val(d.data.rows.ClassNum);          
        $('#StudentNum').val(d.data.rows.StudentNum);          
        $('#TeacherNum').val(d.data.rows.TeacherNum);          
        $('#Nature').val(d.data.rows.Nature);          
        $('#SchoolStage').val(d.data.rows.SchoolStage);          
        $('#FloorArea').val(d.data.rows.FloorArea);          
        $('#BuildArea').val(d.data.rows.BuildArea);          
        $('#BeLongUnit').val(d.data.rows.BeLongUnit);          
        $('#HeadMaster').val(d.data.rows.HeadMaster);          
        $('#IsCloseDeclare').val(d.data.rows.IsCloseDeclare);          

 $('#th_UnitId').html(' 单位id');               
 $('#th_ClassNum').html(' 班级数');               
 $('#th_StudentNum').html(' 在校学生数');               
 $('#th_TeacherNum').html(' 教职工人数');               
 $('#th_Nature').html(' 学校性质(1：公办  2：民办)');               
 $('#th_SchoolStage').html(' 学段');               
 $('#th_FloorArea').html(' 占地面积');               
 $('#th_BuildArea').html(' 建筑面积');               
 $('#th_BeLongUnit').html(' 是否市直属(1：是，0:否)');               
 $('#th_HeadMaster').html(' 校长');               
 $('#th_IsCloseDeclare').html(' 学校是否禁止申报（0：否 ，开启  1：是，禁止）');               
 
 $('#tr_UnitId').hide();               
 $('#tr_ClassNum').hide();               
 $('#tr_StudentNum').hide();               
 $('#tr_TeacherNum').hide();               
 $('#tr_Nature').hide();               
 $('#tr_SchoolStage').hide();               
 $('#tr_FloorArea').hide();               
 $('#tr_BuildArea').hide();               
 $('#tr_BeLongUnit').hide();               
 $('#tr_HeadMaster').hide();               
 $('#tr_IsCloseDeclare').hide();               

 , "UnitId" : unitId
 , "ClassNum" : classNum
 , "StudentNum" : studentNum
 , "TeacherNum" : teacherNum
 , "Nature" : nature
 , "SchoolStage" : schoolStage
 , "FloorArea" : floorArea
 , "BuildArea" : buildArea
 , "BeLongUnit" : beLongUnit
 , "HeadMaster" : headMaster
 , "IsCloseDeclare" : isCloseDeclare

 var unitId = $('#o_UnitId').val();
 var classNum = $('#o_ClassNum').val();
 var studentNum = $('#o_StudentNum').val();
 var teacherNum = $('#o_TeacherNum').val();
 var nature = $('#o_Nature').val();
 var schoolStage = $('#o_SchoolStage').val();
 var floorArea = $('#o_FloorArea').val();
 var buildArea = $('#o_BuildArea').val();
 var beLongUnit = $('#o_BeLongUnit').val();
 var headMaster = $('#o_HeadMaster').val();
 var isCloseDeclare = $('#o_IsCloseDeclare').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '班级数' : '产品名称', d.data.rows.ClassNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '在校学生数' : '产品名称', d.data.rows.StudentNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '教职工人数' : '产品名称', d.data.rows.TeacherNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校性质(1：公办  2：民办)' : '产品名称', d.data.rows.Nature);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学段' : '产品名称', d.data.rows.SchoolStage);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '占地面积' : '产品名称', d.data.rows.FloorArea);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '建筑面积' : '产品名称', d.data.rows.BuildArea);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否市直属(1：是，0:否)' : '产品名称', d.data.rows.BeLongUnit);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '校长' : '产品名称', d.data.rows.HeadMaster);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校是否禁止申报（0：否 ，开启  1：是，禁止）' : '产品名称', d.data.rows.IsCloseDeclare);



