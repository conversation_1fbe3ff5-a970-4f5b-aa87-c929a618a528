﻿ ProductId = entity.ProductId,
 InstrumentModelId = entity.InstrumentModelId,
 Model = entity.Model,
 Price = entity.Price,
 Stock = entity.Stock,


 ProductId = model.ProductId,
 InstrumentModelId = model.InstrumentModelId,
 Model = model.Model,
 Price = model.Price,
 Stock = model.Stock,


 temp.ProductId = model.ProductId,
 temp.InstrumentModelId = model.InstrumentModelId,
 temp.Model = model.Model,
 temp.Price = model.Price,
 temp.Stock = model.Stock,

 ProductNormPriceId = item.ProductNormPriceId,
 ProductId = item.ProductId,
 InstrumentModelId = item.InstrumentModelId,
 Model = item.Model,
 Price = item.Price,
 Stock = item.Stock,

public class ProductNormPriceInputModel
{
 [Display(Name = "Id")] 
    public long ProductNormPriceId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "规格型号Id")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "规格型号")] 
    public string Model {get; set; }
    
 [Display(Name = "单价")] 
    public decimal Price {get; set; }
    
 [Display(Name = "库存")] 
    public decimal Stock {get; set; }
    
 }
 
 public class ProductNormPriceViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ProductNormPriceId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 规格型号Id
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 规格型号
    /// </summary>
    public string Model {get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public decimal Price {get; set; }
    
    /// <summary>
    /// 库存
    /// </summary>
    public decimal Stock {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentModelId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentModelId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Model, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Model, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Price, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Price, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Stock, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Stock, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入库存" } })                    
                </div>
           </div>
  




 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'InstrumentModelId', title: '规格型号Id', sortable: true },
                 
 { field: 'Model', title: '规格型号', sortable: true },
                 
 { field: 'Price', title: '单价', sortable: true },
                 
 { field: 'Stock', title: '库存', sortable: true },
                 
o.ProductId,                 
o.InstrumentModelId,                 
o.Model,                 
o.Price,                 
o.Stock,                 
        
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#InstrumentModelId').val(d.data.rows.InstrumentModelId);          
        $('#Model').val(d.data.rows.Model);          
        $('#Price').val(d.data.rows.Price);          
        $('#Stock').val(d.data.rows.Stock);          

 $('#th_ProductId').html(' 产品Id');               
 $('#th_InstrumentModelId').html(' 规格型号Id');               
 $('#th_Model').html(' 规格型号');               
 $('#th_Price').html(' 单价');               
 $('#th_Stock').html(' 库存');               
 
 $('#tr_ProductId').hide();               
 $('#tr_InstrumentModelId').hide();               
 $('#tr_Model').hide();               
 $('#tr_Price').hide();               
 $('#tr_Stock').hide();               

 , "ProductId" : productId
 , "InstrumentModelId" : instrumentModelId
 , "Model" : model
 , "Price" : price
 , "Stock" : stock

 var productId = $('#o_ProductId').val();
 var instrumentModelId = $('#o_InstrumentModelId').val();
 var model = $('#o_Model').val();
 var price = $('#o_Price').val();
 var stock = $('#o_Stock').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号Id' : '产品名称', d.data.rows.InstrumentModelId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号' : '产品名称', d.data.rows.Model);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单价' : '产品名称', d.data.rows.Price);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '库存' : '产品名称', d.data.rows.Stock);



