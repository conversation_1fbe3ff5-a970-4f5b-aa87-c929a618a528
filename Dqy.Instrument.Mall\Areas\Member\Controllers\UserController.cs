﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Microsoft.AspNet.Identity;
using Microsoft.Owin.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class UserController : ControllerMember
    {
        private static readonly string PwdVerificationMsg = ComLib.GetAppSetting<string>("Pwd.Verification.Msg");
        private static readonly string PwdVerificationWay = ComLib.GetAppSetting<string>("Pwd.Verification.Way");

        #region 周跃峰(密码修改)

        /// <summary>
        /// 成功错误页面
        /// </summary>
        /// <returns></returns>
        public ActionResult RegSucc(ReturnResult r)
        {
            return View(r);
        }

        /// <summary>
        /// 修改密码页面
        /// </summary>
        /// <returns></returns>
        public ActionResult AmendPswd()
        {
            if (Operater == null)
            {
                //ReturnResult r = new ReturnResult();
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("RegSucc", r);
                return this.ApiTimeOut();
            }
            UserInputModel u = new UserInputModel()
            {
                UserId = Operater.UserId,
                LoginName = Operater.Name
            };
            return View(u);
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="txtAccount"></param>
        /// <param name="txtOldPswd"></param>
        /// <param name="txtNewPswd"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> ChangeAmendPswd(UserInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.Token = Operater.Token;
            u.Salt = ComLib.GetGuid();
            u.NewPswd = SecurityHelper.MD5(u.NewPswd + u.Salt);
            u.NewPswdConfirm = u.NewPswd;
            string url = Constant.ApiPath + "user/postchangepswd";
            var result = await WebApiHelper.SendAsync(url, u);
            //if (result.flag == -1)
            //{
            //    r.Url = Constant.Current_Local_Url + "/Account/Login";
            //    return View("RegSucc", r);
            //}
            Log.UnitInfo(u, result, "用户‘" + Operater.Name + "’修改密码", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 
        /// </summary>
        private IAuthenticationManager AuthenticationManager { get { return HttpContext.GetOwinContext().Authentication; } }

        /// <summary>
        /// 退出登录
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> SignOut()
        {
            var sessionId = AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"Token").Value;
            string url = Constant.ApiPath + "account/logout?sessionId=" + sessionId;
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            AuthenticationManager.SignOut(DefaultAuthenticationTypes.ApplicationCookie);
            return RedirectToAction("Login", "Account", new { area = "" });
        }

        #endregion

        #region 周跃峰(个人信息设置)
        /// <summary>
        /// 个人信息
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> PersonInfo()
        {
            //API Server服务器地址
            ViewBag.ServicePath = Constant.ApiPath.Replace("api/", "");
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId;
            //根据单位Id获取单位信息
            string url = $"user/getextensionbyuserid?token={Operater.Token}&userId={Operater.UserId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("RegSucc", r);
                return this.ApiTimeOut();
            }
            if (r.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("RegSucc", r);
            }
            if (r.obj == null)
            {
                r.Url = Constant.Current_Local_Url + "/Account/Login";
                return View("RegSucc", r);
            }
            UserExtensionInputModel sModel = ComLib.JSON2Object<UserExtensionInputModel>(r.obj.ToString());
            return View(sModel);
        }

        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="txtAccount"></param>
        /// <param name="txtOldPswd"></param>
        /// <param name="txtNewPswd"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SavePersonInfo(UserExtensionInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.Token = Operater.Token;
            u.UserId = u.UserId;
            string url = Constant.ApiPath + "user/postmodifyextension";
            var result = await WebApiHelper.SendAsync(url, u);
            //if (result.flag == -1)
            //{
            //    r.Url = Constant.Current_Local_Url + "/Account/Login";
            //    return View("RegSucc", r);
            //}
            Log.UnitInfo(u, result, "用户‘" + Operater.Name + "’保存个人信息", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 周跃峰(区县用户管理)

        /// <summary>
        ///  区县用户账户管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> AccountCounty(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = Constant.ApiPath + "user/postusercountylist";
            var result = await WebApiHelper.SendAsync<QueryResult<UserAccountViewModel>>(url, args);
            if (result.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //r.flag = 0;
                //r.msg = "登录超时，请重新登录。";
                //return View("RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 获取修改区县用户数据
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetUserModelCounty(long userId)
        {
            string url = $"user/getusermodelcounty?token={Operater.Token}&userId={userId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            r.obj = ComLib.Object2JSON(r.obj);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 保存区县用户
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveCountyAccount(UserAccountInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.Token = Operater.Token;
            u.UnitId = Operater.UnitId;
            u.Salt = ComLib.GetGuid();
            u.RecordUserId = Operater.UserId;
            u.MallId = Operater.CurrentMallId;
            u.LoginName = u.LoginName.Trim();
            if (!string.IsNullOrEmpty(u.Pswd))
            {
                u.Pswd = SecurityHelper.MD5(u.Pswd + u.Salt);
            }
            if (u.UserId == 0)
            {
                string url = Constant.ApiPath + "user/postaddusercounty";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(区县)用户‘" + Operater.Name + "’添加账号", "UnitInfo", r.flag);
            }
            else
            {
                string url = Constant.ApiPath + "user/postmodifyusercounty";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(区县)用户‘" + Operater.Name + "’修改账号", "UnitInfo", r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 启用禁用区县账号
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task<JsonResult> AccountSuposeCounty(long userId, int statuz)
        {
            string url = Constant.ApiPath + "user/getsuposeusercounty?token=" + Operater.Token + "&userId=" + userId + "&unitId=" + Operater.UnitId + "&statuz=" + statuz + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = userId }, result, "(区县)用户‘" + Operater.Name + "’启用、禁用账号", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region 周跃峰(市级用户管理)

        /// <summary>
        ///  市级用户账户管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> AccountCity(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = Constant.ApiPath + "user/postusercitylist";
            var result = await WebApiHelper.SendAsync<QueryResult<UserAccountViewModel>>(url, args);
            if (result.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //r.flag = 0;
                //r.msg = "登录超时，请重新登录。";
                //return View("RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 获取修改市级用户数据
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetUserModelCity(long userId)
        {
            string url = $"user/getusermodelcity?token={Operater.Token}&unitId={Operater.UnitId}&userId={userId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            r.obj = ComLib.Object2JSON(r.obj);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 保存市级用户
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveCityAccount(UserAccountInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.Token = Operater.Token;
            u.UnitId = Operater.UnitId;
            u.Salt = ComLib.GetGuid();
            u.MallId = Operater.CurrentMallId;
            u.RecordUserId = Operater.UserId;
            u.LoginName = u.LoginName.Trim();
            if (!string.IsNullOrEmpty(u.Pswd))
            {
                u.Pswd = SecurityHelper.MD5(u.Pswd + u.Salt);
            }
            if (u.UserId == 0)
            {
                string url = Constant.ApiPath + "user/postaddusercity";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(市级)用户‘" + Operater.Name + "’添加账号", "UnitInfo", r.flag);
            }
            else
            {
                string url = Constant.ApiPath + "user/postmodifyuserCity";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(市级)用户‘" + Operater.Name + "’修改账号", "UnitInfo", r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 启用禁用市级账号
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task<JsonResult> AccountSuposeCity(long userId, int statuz)
        {
            string url = Constant.ApiPath + "user/getsuposeusercity?token=" + Operater.Token + "&userId=" + userId + "&unitId=" + Operater.UnitId + "&statuz=" + statuz + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = userId }, result, "(市级)用户‘" + Operater.Name + "’启用、禁用账号", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 周跃峰(学校用户管理)

        /// <summary>
        ///  学校用户账户管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> AccountSchool(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.UnitId = Operater.UnitId;
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.RecordUserId = Operater.RecordUserId;
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.UserId = Operater.UserId;
            string url = Constant.ApiPath + "user/postuserschoollist";
            var result = await WebApiHelper.SendAsync<QueryResult<UserAccountViewModel>>(url, args);
            if (result.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //r.flag = 0;
                //r.msg = "登录超时，请重新登录。";
                //return View("RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 获取修改学校用户数据
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetUserModelSchool(long userId)
        {
            string url = Constant.ApiPath + $"user/getusermodelschool?token={Operater.Token}&userId={userId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            r.obj = ComLib.Object2JSON(r.obj);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 保存学校用户
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveSchoolAccount(UserAccountInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.CurrentOperateUserId = Operater.UserId;
            u.Token = Operater.Token;
            u.UnitId = Operater.UnitId;
            u.Salt = ComLib.GetGuid();
            u.MallId = Operater.CurrentMallId;
            u.RecordUserId = Operater.UserId;
            u.LoginName = u.LoginName.Trim();
            if (!string.IsNullOrEmpty(u.Pswd))
            {
                u.Pswd = SecurityHelper.MD5(u.Pswd + u.Salt);
            }
            if (u.UserId == 0)
            {
                string url = Constant.ApiPath + "user/postaddschooluser";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(学校)用户‘" + Operater.Name + "’添加账号", "UnitInfo", r.flag);
            }
            else
            {
                string url = Constant.ApiPath + "user/postmodifyschooluser";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(学校)用户‘" + Operater.Name + "’修改账号", "UnitInfo", r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 启用禁用学校账号
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task<JsonResult> AccountSuposeSchool(long userId, int statuz)
        {
            string url = Constant.ApiPath + "user/getsuposeuserschool?token=" + Operater.Token + "&userId=" + userId + "&unitId=" + Operater.UnitId + "&statuz=" + statuz + "&CurrentOperateUserId=" + Operater.UserId + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = userId }, result, "(学校)用户‘" + Operater.Name + "’启用、禁用账号", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region 周跃峰(单位用户账号)
        /// <summary>
        ///  单位用户账户管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> AccountSupplier(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.RecordUserId = Operater.RecordUserId;
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.UserId = Operater.UserId;
            string url = Constant.ApiPath + "user/postusersupplierlist";
            var result = await WebApiHelper.SendAsync<QueryResult<UserAccountViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 获取修改单位用户数据
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetUserModelSupplier(long userId)
        {
            string url = Constant.ApiPath + $"user/getusermodelsupplier?token={Operater.Token}&unitId={Operater.UnitId}&userId={userId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            r.obj = ComLib.Object2JSON(r.obj);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 保存单位用户
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveSupplierAccount(UserAccountInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.CurrentOperateUserId = Operater.UserId;
            u.Token = Operater.Token;
            u.UnitId = Operater.UnitId;
            u.Salt = ComLib.GetGuid();
            u.RecordUserId = Operater.UserId;
            u.MallId = Operater.CurrentMallId;
            u.LoginName = u.LoginName.Trim();
            if (!string.IsNullOrEmpty(u.Pswd))
            {
                u.Pswd = SecurityHelper.MD5(u.Pswd + u.Salt);
            }

            if (u.UserId == 0)
            {
                string url = Constant.ApiPath + "user/postaddusersupplier";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(企业)用户‘" + Operater.Name + "’添加账号", "UnitInfo", r.flag);
            }
            else
            {
                string url = Constant.ApiPath + "user/postmodifyusersupplier";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(企业)用户‘" + Operater.Name + "’修改账号", "UnitInfo", r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 启用禁用单位账号
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task<JsonResult> AccountSuposeSupplier(long userId, int statuz)
        {
            string url = Constant.ApiPath + "user/getsuposeusersupplier?token=" + Operater.Token + "&userId=" + userId + "&unitId=" + Operater.UnitId + "&statuz=" + statuz + "&CurrentOperateUserId=" + Operater.UserId + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = userId }, result, "(企业)用户‘" + Operater.Name + "’启用、禁用账号", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region 周跃峰(平台管理员用户管理)

        /// <summary>
        ///  平台管理员账户管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> AccountMall(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.RecordUserId = Operater.RecordUserId;
            string url = Constant.ApiPath + "user/postusermalllist";
            var result = await WebApiHelper.SendAsync<QueryResult<UserAccountViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }


        /// <summary>
        /// 获取修改平台管理员用户数据
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetUserModelMall(long userId)
        {
            string url = $"user/getusermodelmall?token={Operater.Token}&unitId={Operater.UnitId}&userId={userId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            r.obj = ComLib.Object2JSON(r.obj);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 保存平台管理员用户
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveMallAccount(UserAccountInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.Token = Operater.Token;
            u.UnitId = Operater.UnitId;
            u.Salt = ComLib.GetGuid();
            u.RecordUserId = Operater.UserId;
            u.MallId = Operater.CurrentMallId;
            u.LoginName = u.LoginName.Trim();
            u.IPAddress = IPOperate.GetIP();
            if (Operater.RecordUserId != 0)
            {
                r.flag = -2;
                r.msg = "您无权限操作此功能";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            if (!string.IsNullOrEmpty(u.Pswd))
            {
                u.Pswd = SecurityHelper.MD5(u.Pswd + u.Salt);
            }
            if (u.UserId == 0)
            {
                string url = Constant.ApiPath + "user/postaddusermall";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(平台管理员)用户‘" + Operater.Name + "’添加账号", "UnitInfo", r.flag);
            }
            else
            {
                string url = Constant.ApiPath + "user/postmodifyusermall";
                r = await WebApiHelper.SendAsync(url, u);
                Log.UnitInfo(u, r, "(平台管理员)用户‘" + Operater.Name + "’修改账号", "UnitInfo", r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 启用禁用平台管理员账号
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task<JsonResult> AccountSuposeMall(long userId, int statuz, string loginName)
        {
            ReturnResult r = new ReturnResult();
            if (Operater.RecordUserId != 0)
            {
                r.flag = -2;
                r.msg = "您无权限操作此功能";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            string url = Constant.ApiPath + "user/getsuposeusermall?token=" + Operater.Token + "&userId=" + userId + "&unitId=" + Operater.UnitId + "&statuz=" + statuz + "&loginName=" + loginName + "&IpAddress=" + IPOperate.GetIP() + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = userId }, result, "(平台管理员)用户‘" + Operater.Name + "’启用、禁用账号", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 离职功能 by jiangpeng 2018-9-26
        public ActionResult Quit(string backUrl, long assignmentUserId = 0)
        {
            if (!ComLib.IsValidUrl(backUrl, Request.Url.Host))
            {
                backUrl = "~/";
            }
            ViewBag.BackUrl = backUrl;
            ViewBag.AssignmentUserId = assignmentUserId;
            return View();
        }

        public async Task<JsonResult> QuitSubmit(string sk, long assignmentUserId)
        {
            //验证密码
            ReturnResult r = new ReturnResult();
            if (string.IsNullOrWhiteSpace(sk))
            {
                r.flag = 0;
                r.msg = "请输入登录密码。";
                return Json(r);
            }
            string url = Constant.ApiPath + "user/quit?token=" + Operater.Token + "&userId=" + Operater.UserId + "&sk=" + sk + "&assignmentUserId=" + assignmentUserId;
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = Operater.UserId }, result, "用户‘" + Operater.Name + "’离职", "UnitInfo", result.flag);
            return Json(result);
        }

        /// <summary>
        /// 重新入职
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> ChangeQuit(long userId)
        {
            string url = Constant.ApiPath + "user/changequit?token=" + Operater.Token + "&userId=" + userId;
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = userId }, result, "用户‘" + Operater.Name + "’重新入职", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 判断该员工离职是否需要将拥有权限转让
        /// </summary>
        /// <returns></returns>
        public JsonResult IsNeedAssignment()
        {
            if ((Operater.UserType == UserRoleType.CityAdmin.ToInt() || Operater.UserType == UserRoleType.CountyAdmin.ToInt()
                || Operater.UserType == UserRoleType.SchoolAuditor.ToInt() || Operater.UserType == UserRoleType.SellerAdmin.ToInt()
                || Operater.UserType == UserRoleType.MallAdministrator.ToInt()) && Operater.RecordUserId == 0)
            {
                return Json(true, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(false, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 显示转让员工列表
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> ShowAssignmentUserList()
        {
            SearchArgumentsInputModel args = new SearchArgumentsInputModel();
            ReturnResult r = new ReturnResult();
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.UnitId = Operater.UnitId;
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.RecordUserId = Operater.RecordUserId;
            args.Limit = int.MaxValue;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.UserId = Operater.UserId;
            string url = "";
            if (Operater.UserType == UserRoleType.CountyAdmin.ToInt())
            {
                url = Constant.ApiPath + "user/postusercountylist";
            }
            else if (Operater.UserType == UserRoleType.CityAdmin.ToInt())
            {
                url = Constant.ApiPath + "user/postusercitylist";
            }
            else if (Operater.UserType == UserRoleType.SchoolAuditor.ToInt())
            {
                url = Constant.ApiPath + "user/postuserschoollist";
            }
            else if (Operater.UserType == UserRoleType.SellerAdmin.ToInt())
            {
                url = Constant.ApiPath + "user/postusersupplierlist";
            }
            else
            {
                url = Constant.ApiPath + "user/postusermalllist";
            }
            var result = await WebApiHelper.SendAsync<QueryResult<UserAccountViewModel>>(url, args);
            if(result != null && result.Data != null)
            {
                result.Data = result.Data.Where(f => f.Statuz == UserStatuz.Normal.ToInt() && f.ValidDate > DateTime.Now).ToList();
            }
            return View("AssignmentUserList", result);
        }
        #endregion

        #region 微信账号绑定管理

        /// <summary>
        ///  微信绑定账号管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> UserBindOpenid(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = int.MaxValue;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.UserId = Operater.UserId;
            string url = "user/searchuserbindopendid";
            var result = await WebApiHelper.SendAsync<QueryResult<UserBindOpenidViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 微信账号解绑
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task<JsonResult> UpdateBindOpenStatuz(long id)
        {
            //验证密码
            string url = Constant.ApiPath + "user/unbindopenid?token=" + Operater.Token + "&id=" + id;
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = Operater.UserId }, result, "用户‘" + Operater.Name + "’解绑微信账号", "UnitInfo", result.flag);
            return Json(result,JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 生成微信小程序登录码
        /// </summary>
        /// <returns></returns>
        public async Task<JsonResult> GetWxLoginQrcode()
        {
            string url = Constant.ApiPath + "user/getwxloginqrcode?token=" + Operater.Token;
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(new { UserId = Operater.UserId }, result, "用户‘" + Operater.Name + "’生成微信小程序登录码", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion

        /// <summary>
        /// mobile是否可以注册
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public JsonResult GetPwdVerification()//参数名称必须与字段名相同
        {
            var data = new
            {
                PwdVerificationMsg = PwdVerificationMsg,
                PwdVerificationWay = PwdVerificationWay
            };
            return Json(data, JsonRequestBehavior.AllowGet);
        }
    }
}