﻿ ArticleType = entity.ArticleType,
 Cid = entity.Cid,
 Sid = entity.Sid,
 Title = entity.Title,
 ShortTitle = entity.ShortTitle,
 HasImage = entity.HasImage,
 ImageUrl = entity.ImageUrl,
 RegDate = entity.RegDate,
 UnitId = entity.UnitId,
 DisplayRange = entity.DisplayRange,
 Tagz = entity.Tagz,
 Keywords = entity.Keywords,
 Statuz = entity.Statuz,
 IsRecommend = entity.IsRecommend,
 IsHot = entity.IsHot,
 IsFoc = entity.IsFoc,
 Hits = entity.Hits,
 Subtitle = entity.Subtitle,
 Attachment = entity.Attachment,
 Source = entity.Source,
 Author = entity.Author,
 Sort = entity.Sort,
 UserId = entity.UserId,
 EditDate = entity.EditDate,
 AttachDownCount = entity.AttachDownCount,
 CommentCount = entity.CommentCount,
 AuditorId = entity.AuditorId,
 AuditDate = entity.AuditDate,
 Description = entity.Description,
 BeginTime = entity.BeginTime,
 EndTime = entity.EndTime,
 AreaId = entity.AreaId,


 ArticleType = model.ArticleType,
 Cid = model.Cid,
 Sid = model.Sid,
 Title = model.Title,
 ShortTitle = model.ShortTitle,
 HasImage = model.HasImage,
 ImageUrl = model.ImageUrl,
 RegDate = model.RegDate,
 UnitId = model.UnitId,
 DisplayRange = model.DisplayRange,
 Tagz = model.Tagz,
 Keywords = model.Keywords,
 Statuz = model.Statuz,
 IsRecommend = model.IsRecommend,
 IsHot = model.IsHot,
 IsFoc = model.IsFoc,
 Hits = model.Hits,
 Subtitle = model.Subtitle,
 Attachment = model.Attachment,
 Source = model.Source,
 Author = model.Author,
 Sort = model.Sort,
 UserId = model.UserId,
 EditDate = model.EditDate,
 AttachDownCount = model.AttachDownCount,
 CommentCount = model.CommentCount,
 AuditorId = model.AuditorId,
 AuditDate = model.AuditDate,
 Description = model.Description,
 BeginTime = model.BeginTime,
 EndTime = model.EndTime,
 AreaId = model.AreaId,


 temp.ArticleType = model.ArticleType,
 temp.Cid = model.Cid,
 temp.Sid = model.Sid,
 temp.Title = model.Title,
 temp.ShortTitle = model.ShortTitle,
 temp.HasImage = model.HasImage,
 temp.ImageUrl = model.ImageUrl,
 temp.RegDate = model.RegDate,
 temp.UnitId = model.UnitId,
 temp.DisplayRange = model.DisplayRange,
 temp.Tagz = model.Tagz,
 temp.Keywords = model.Keywords,
 temp.Statuz = model.Statuz,
 temp.IsRecommend = model.IsRecommend,
 temp.IsHot = model.IsHot,
 temp.IsFoc = model.IsFoc,
 temp.Hits = model.Hits,
 temp.Subtitle = model.Subtitle,
 temp.Attachment = model.Attachment,
 temp.Source = model.Source,
 temp.Author = model.Author,
 temp.Sort = model.Sort,
 temp.UserId = model.UserId,
 temp.EditDate = model.EditDate,
 temp.AttachDownCount = model.AttachDownCount,
 temp.CommentCount = model.CommentCount,
 temp.AuditorId = model.AuditorId,
 temp.AuditDate = model.AuditDate,
 temp.Description = model.Description,
 temp.BeginTime = model.BeginTime,
 temp.EndTime = model.EndTime,
 temp.AreaId = model.AreaId,

 Id = item.Id,
 ArticleType = item.ArticleType,
 Cid = item.Cid,
 Sid = item.Sid,
 Title = item.Title,
 ShortTitle = item.ShortTitle,
 HasImage = item.HasImage,
 ImageUrl = item.ImageUrl,
 RegDate = item.RegDate,
 UnitId = item.UnitId,
 DisplayRange = item.DisplayRange,
 Tagz = item.Tagz,
 Keywords = item.Keywords,
 Statuz = item.Statuz,
 IsRecommend = item.IsRecommend,
 IsHot = item.IsHot,
 IsFoc = item.IsFoc,
 Hits = item.Hits,
 Subtitle = item.Subtitle,
 Attachment = item.Attachment,
 Source = item.Source,
 Author = item.Author,
 Sort = item.Sort,
 UserId = item.UserId,
 EditDate = item.EditDate,
 AttachDownCount = item.AttachDownCount,
 CommentCount = item.CommentCount,
 AuditorId = item.AuditorId,
 AuditDate = item.AuditDate,
 Description = item.Description,
 BeginTime = item.BeginTime,
 EndTime = item.EndTime,
 AreaId = item.AreaId,

public class ArticleInputModel
{
 [Display(Name = "主键")] 
    public int Id {get; set; }
    
 [Display(Name = "资讯类型(0:内容；1：图片；3：链接)")] 
    public int ArticleType {get; set; }
    
 [Display(Name = "资讯类型ID")] 
    public int Cid {get; set; }
    
 [Display(Name = "所属专题ID")] 
    public int Sid {get; set; }
    
 [Display(Name = "标题")] 
    public string Title {get; set; }
    
 [Display(Name = "资讯短标题")] 
    public string ShortTitle {get; set; }
    
 [Display(Name = "是否有图片")] 
    public bool HasImage {get; set; }
    
 [Display(Name = "b标识图片路径")] 
    public string ImageUrl {get; set; }
    
 [Display(Name = "发布时间")] 
    public DateTime RegDate {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "展示范围（1：当前商城；2：所有商城；）")] 
    public int DisplayRange {get; set; }
    
 [Display(Name = "标签")] 
    public string Tagz {get; set; }
    
 [Display(Name = "关键字")] 
    public string Keywords {get; set; }
    
 [Display(Name = "状态（0:：草稿；1：提交；2：发布；3： 暂停；7：区县审核不通过；8：系统审核不通过）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "是否推荐")] 
    public bool IsRecommend {get; set; }
    
 [Display(Name = "是否热门(1：是，0：否)")] 
    public bool IsHot {get; set; }
    
 [Display(Name = "是否焦点(1：是，0：否)")] 
    public bool IsFoc {get; set; }
    
 [Display(Name = "点击量")] 
    public int Hits {get; set; }
    
 [Display(Name = "副标题")] 
    public string Subtitle {get; set; }
    
 [Display(Name = "附件")] 
    public string Attachment {get; set; }
    
 [Display(Name = "资讯来源")] 
    public string Source {get; set; }
    
 [Display(Name = "作者")] 
    public string Author {get; set; }
    
 [Display(Name = "排序")] 
    public int Sort {get; set; }
    
 [Display(Name = "发布者")] 
    public int UserId {get; set; }
    
 [Display(Name = "更新日期")] 
    public DateTime EditDate {get; set; }
    
 [Display(Name = "附件下载数")] 
    public int AttachDownCount {get; set; }
    
 [Display(Name = "评论数")] 
    public int CommentCount {get; set; }
    
 [Display(Name = "审核人")] 
    public int AuditorId {get; set; }
    
 [Display(Name = "审核时间")] 
    public DateTime AuditDate {get; set; }
    
 [Display(Name = "审核说明")] 
    public string Description {get; set; }
    
 [Display(Name = "显示时间")] 
    public DateTime BeginTime {get; set; }
    
 [Display(Name = "到期时间")] 
    public DateTime EndTime {get; set; }
    
 [Display(Name = "发布范围0：全部；1 全区")] 
    public int AreaId {get; set; }
    
 }
 
 public class ArticleViewModel
 {
    /// <summary>
    /// 主键
    /// </summary>
    public int Id {get; set; }
    
    /// <summary>
    /// 资讯类型(0:内容；1：图片；3：链接)
    /// </summary>
    public int ArticleType {get; set; }
    
    /// <summary>
    /// 资讯类型ID
    /// </summary>
    public int Cid {get; set; }
    
    /// <summary>
    /// 所属专题ID
    /// </summary>
    public int Sid {get; set; }
    
    /// <summary>
    /// 标题
    /// </summary>
    public string Title {get; set; }
    
    /// <summary>
    /// 资讯短标题
    /// </summary>
    public string ShortTitle {get; set; }
    
    /// <summary>
    /// 是否有图片
    /// </summary>
    public bool HasImage {get; set; }
    
    /// <summary>
    /// b标识图片路径
    /// </summary>
    public string ImageUrl {get; set; }
    
    /// <summary>
    /// 发布时间
    /// </summary>
    public DateTime RegDate {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 展示范围（1：当前商城；2：所有商城；）
    /// </summary>
    public int DisplayRange {get; set; }
    
    /// <summary>
    /// 标签
    /// </summary>
    public string Tagz {get; set; }
    
    /// <summary>
    /// 关键字
    /// </summary>
    public string Keywords {get; set; }
    
    /// <summary>
    /// 状态（0:：草稿；1：提交；2：发布；3： 暂停；7：区县审核不通过；8：系统审核不通过）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 是否推荐
    /// </summary>
    public bool IsRecommend {get; set; }
    
    /// <summary>
    /// 是否热门(1：是，0：否)
    /// </summary>
    public bool IsHot {get; set; }
    
    /// <summary>
    /// 是否焦点(1：是，0：否)
    /// </summary>
    public bool IsFoc {get; set; }
    
    /// <summary>
    /// 点击量
    /// </summary>
    public int Hits {get; set; }
    
    /// <summary>
    /// 副标题
    /// </summary>
    public string Subtitle {get; set; }
    
    /// <summary>
    /// 附件
    /// </summary>
    public string Attachment {get; set; }
    
    /// <summary>
    /// 资讯来源
    /// </summary>
    public string Source {get; set; }
    
    /// <summary>
    /// 作者
    /// </summary>
    public string Author {get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort {get; set; }
    
    /// <summary>
    /// 发布者
    /// </summary>
    public int UserId {get; set; }
    
    /// <summary>
    /// 更新日期
    /// </summary>
    public DateTime? EditDate {get; set; }
    
    /// <summary>
    /// 附件下载数
    /// </summary>
    public int AttachDownCount {get; set; }
    
    /// <summary>
    /// 评论数
    /// </summary>
    public int CommentCount {get; set; }
    
    /// <summary>
    /// 审核人
    /// </summary>
    public int AuditorId {get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime AuditDate {get; set; }
    
    /// <summary>
    /// 审核说明
    /// </summary>
    public string Description {get; set; }
    
    /// <summary>
    /// 显示时间
    /// </summary>
    public DateTime BeginTime {get; set; }
    
    /// <summary>
    /// 到期时间
    /// </summary>
    public DateTime EndTime {get; set; }
    
    /// <summary>
    /// 发布范围0：全部；1 全区
    /// </summary>
    public int AreaId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ArticleType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ArticleType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯类型(0:内容；1：图片；3：链接)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Cid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Cid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯类型ID" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入所属专题ID" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Title, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Title, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入标题" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ShortTitle, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ShortTitle, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯短标题" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.HasImage, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.HasImage, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否有图片" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImageUrl, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImageUrl, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入b标识图片路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegDate, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegDate, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入发布时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DisplayRange, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DisplayRange, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入展示范围（1：当前商城；2：所有商城；）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Tagz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Tagz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入标签" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Keywords, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Keywords, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入关键字" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（0:：草稿；1：提交；2：发布；3： 暂停；7：区县审核不通过；8：系统审核不通过）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsRecommend, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsRecommend, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否推荐" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsHot, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsHot, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否热门(1：是，0：否)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsFoc, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsFoc, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否焦点(1：是，0：否)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Hits, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Hits, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入点击量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Subtitle, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Subtitle, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入副标题" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Attachment, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Attachment, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入附件" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Source, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Source, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯来源" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Author, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Author, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入作者" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sort, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sort, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入发布者" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EditDate, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EditDate, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入更新日期" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AttachDownCount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AttachDownCount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入附件下载数" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CommentCount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CommentCount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入评论数" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditorId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditorId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditDate, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditDate, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Description, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BeginTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BeginTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入显示时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EndTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EndTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入到期时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AreaId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AreaId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入发布范围0：全部；1 全区" } })                    
                </div>
           </div>
  




 { field: 'ArticleType', title: '资讯类型(0:内容；1：图片；3：链接)', sortable: true },
                 
 { field: 'Cid', title: '资讯类型ID', sortable: true },
                 
 { field: 'Sid', title: '所属专题ID', sortable: true },
                 
 { field: 'Title', title: '标题', sortable: true },
                 
 { field: 'ShortTitle', title: '资讯短标题', sortable: true },
                 
 { field: 'HasImage', title: '是否有图片', sortable: true },
                 
 { field: 'ImageUrl', title: 'b标识图片路径', sortable: true },
                 
 { field: 'RegDate', title: '发布时间', sortable: true },
                 
 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'DisplayRange', title: '展示范围（1：当前商城；2：所有商城；）', sortable: true },
                 
 { field: 'Tagz', title: '标签', sortable: true },
                 
 { field: 'Keywords', title: '关键字', sortable: true },
                 
 { field: 'Statuz', title: '状态（0:：草稿；1：提交；2：发布；3： 暂停；7：区县审核不通过；8：系统审核不通过）', sortable: true },
                 
 { field: 'IsRecommend', title: '是否推荐', sortable: true },
                 
 { field: 'IsHot', title: '是否热门(1：是，0：否)', sortable: true },
                 
 { field: 'IsFoc', title: '是否焦点(1：是，0：否)', sortable: true },
                 
 { field: 'Hits', title: '点击量', sortable: true },
                 
 { field: 'Subtitle', title: '副标题', sortable: true },
                 
 { field: 'Attachment', title: '附件', sortable: true },
                 
 { field: 'Source', title: '资讯来源', sortable: true },
                 
 { field: 'Author', title: '作者', sortable: true },
                 
 { field: 'Sort', title: '排序', sortable: true },
                 
 { field: 'UserId', title: '发布者', sortable: true },
                 
 { field: 'EditDate', title: '更新日期', sortable: true },
                 
 { field: 'AttachDownCount', title: '附件下载数', sortable: true },
                 
 { field: 'CommentCount', title: '评论数', sortable: true },
                 
 { field: 'AuditorId', title: '审核人', sortable: true },
                 
 { field: 'AuditDate', title: '审核时间', sortable: true },
                 
 { field: 'Description', title: '审核说明', sortable: true },
                 
 { field: 'BeginTime', title: '显示时间', sortable: true },
                 
 { field: 'EndTime', title: '到期时间', sortable: true },
                 
 { field: 'AreaId', title: '发布范围0：全部；1 全区', sortable: true },
                 
o.ArticleType,                 
o.Cid,                 
o.Sid,                 
o.Title,                 
o.ShortTitle,                 
o.HasImage,                 
o.ImageUrl,                 
o.RegDate,                 
o.UnitId,                 
o.DisplayRange,                 
o.Tagz,                 
o.Keywords,                 
o.Statuz,                 
o.IsRecommend,                 
o.IsHot,                 
o.IsFoc,                 
o.Hits,                 
o.Subtitle,                 
o.Attachment,                 
o.Source,                 
o.Author,                 
o.Sort,                 
o.UserId,                 
o.EditDate,                 
o.AttachDownCount,                 
o.CommentCount,                 
o.AuditorId,                 
o.AuditDate,                 
o.Description,                 
o.BeginTime,                 
o.EndTime,                 
o.AreaId,                 
        
        $('#ArticleType').val(d.data.rows.ArticleType);          
        $('#Cid').val(d.data.rows.Cid);          
        $('#Sid').val(d.data.rows.Sid);          
        $('#Title').val(d.data.rows.Title);          
        $('#ShortTitle').val(d.data.rows.ShortTitle);          
        $('#HasImage').val(d.data.rows.HasImage);          
        $('#ImageUrl').val(d.data.rows.ImageUrl);          
        $('#RegDate').val(d.data.rows.RegDate);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#DisplayRange').val(d.data.rows.DisplayRange);          
        $('#Tagz').val(d.data.rows.Tagz);          
        $('#Keywords').val(d.data.rows.Keywords);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#IsRecommend').val(d.data.rows.IsRecommend);          
        $('#IsHot').val(d.data.rows.IsHot);          
        $('#IsFoc').val(d.data.rows.IsFoc);          
        $('#Hits').val(d.data.rows.Hits);          
        $('#Subtitle').val(d.data.rows.Subtitle);          
        $('#Attachment').val(d.data.rows.Attachment);          
        $('#Source').val(d.data.rows.Source);          
        $('#Author').val(d.data.rows.Author);          
        $('#Sort').val(d.data.rows.Sort);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#EditDate').val(d.data.rows.EditDate);          
        $('#AttachDownCount').val(d.data.rows.AttachDownCount);          
        $('#CommentCount').val(d.data.rows.CommentCount);          
        $('#AuditorId').val(d.data.rows.AuditorId);          
        $('#AuditDate').val(d.data.rows.AuditDate);          
        $('#Description').val(d.data.rows.Description);          
        $('#BeginTime').val(d.data.rows.BeginTime);          
        $('#EndTime').val(d.data.rows.EndTime);          
        $('#AreaId').val(d.data.rows.AreaId);          

 $('#th_ArticleType').html(' 资讯类型(0:内容；1：图片；3：链接)');               
 $('#th_Cid').html(' 资讯类型ID');               
 $('#th_Sid').html(' 所属专题ID');               
 $('#th_Title').html(' 标题');               
 $('#th_ShortTitle').html(' 资讯短标题');               
 $('#th_HasImage').html(' 是否有图片');               
 $('#th_ImageUrl').html(' b标识图片路径');               
 $('#th_RegDate').html(' 发布时间');               
 $('#th_UnitId').html(' 单位Id');               
 $('#th_DisplayRange').html(' 展示范围（1：当前商城；2：所有商城；）');               
 $('#th_Tagz').html(' 标签');               
 $('#th_Keywords').html(' 关键字');               
 $('#th_Statuz').html(' 状态（0:：草稿；1：提交；2：发布；3： 暂停；7：区县审核不通过；8：系统审核不通过）');               
 $('#th_IsRecommend').html(' 是否推荐');               
 $('#th_IsHot').html(' 是否热门(1：是，0：否)');               
 $('#th_IsFoc').html(' 是否焦点(1：是，0：否)');               
 $('#th_Hits').html(' 点击量');               
 $('#th_Subtitle').html(' 副标题');               
 $('#th_Attachment').html(' 附件');               
 $('#th_Source').html(' 资讯来源');               
 $('#th_Author').html(' 作者');               
 $('#th_Sort').html(' 排序');               
 $('#th_UserId').html(' 发布者');               
 $('#th_EditDate').html(' 更新日期');               
 $('#th_AttachDownCount').html(' 附件下载数');               
 $('#th_CommentCount').html(' 评论数');               
 $('#th_AuditorId').html(' 审核人');               
 $('#th_AuditDate').html(' 审核时间');               
 $('#th_Description').html(' 审核说明');               
 $('#th_BeginTime').html(' 显示时间');               
 $('#th_EndTime').html(' 到期时间');               
 $('#th_AreaId').html(' 发布范围0：全部；1 全区');               
 
 $('#tr_ArticleType').hide();               
 $('#tr_Cid').hide();               
 $('#tr_Sid').hide();               
 $('#tr_Title').hide();               
 $('#tr_ShortTitle').hide();               
 $('#tr_HasImage').hide();               
 $('#tr_ImageUrl').hide();               
 $('#tr_RegDate').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_DisplayRange').hide();               
 $('#tr_Tagz').hide();               
 $('#tr_Keywords').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_IsRecommend').hide();               
 $('#tr_IsHot').hide();               
 $('#tr_IsFoc').hide();               
 $('#tr_Hits').hide();               
 $('#tr_Subtitle').hide();               
 $('#tr_Attachment').hide();               
 $('#tr_Source').hide();               
 $('#tr_Author').hide();               
 $('#tr_Sort').hide();               
 $('#tr_UserId').hide();               
 $('#tr_EditDate').hide();               
 $('#tr_AttachDownCount').hide();               
 $('#tr_CommentCount').hide();               
 $('#tr_AuditorId').hide();               
 $('#tr_AuditDate').hide();               
 $('#tr_Description').hide();               
 $('#tr_BeginTime').hide();               
 $('#tr_EndTime').hide();               
 $('#tr_AreaId').hide();               

 , "ArticleType" : articleType
 , "Cid" : cid
 , "Sid" : sid
 , "Title" : title
 , "ShortTitle" : shortTitle
 , "HasImage" : hasImage
 , "ImageUrl" : imageUrl
 , "RegDate" : regDate
 , "UnitId" : unitId
 , "DisplayRange" : displayRange
 , "Tagz" : tagz
 , "Keywords" : keywords
 , "Statuz" : statuz
 , "IsRecommend" : isRecommend
 , "IsHot" : isHot
 , "IsFoc" : isFoc
 , "Hits" : hits
 , "Subtitle" : subtitle
 , "Attachment" : attachment
 , "Source" : source
 , "Author" : author
 , "Sort" : sort
 , "UserId" : userId
 , "EditDate" : editDate
 , "AttachDownCount" : attachDownCount
 , "CommentCount" : commentCount
 , "AuditorId" : auditorId
 , "AuditDate" : auditDate
 , "Description" : description
 , "BeginTime" : beginTime
 , "EndTime" : endTime
 , "AreaId" : areaId

 var articleType = $('#o_ArticleType').val();
 var cid = $('#o_Cid').val();
 var sid = $('#o_Sid').val();
 var title = $('#o_Title').val();
 var shortTitle = $('#o_ShortTitle').val();
 var hasImage = $('#o_HasImage').val();
 var imageUrl = $('#o_ImageUrl').val();
 var regDate = $('#o_RegDate').val();
 var unitId = $('#o_UnitId').val();
 var displayRange = $('#o_DisplayRange').val();
 var tagz = $('#o_Tagz').val();
 var keywords = $('#o_Keywords').val();
 var statuz = $('#o_Statuz').val();
 var isRecommend = $('#o_IsRecommend').val();
 var isHot = $('#o_IsHot').val();
 var isFoc = $('#o_IsFoc').val();
 var hits = $('#o_Hits').val();
 var subtitle = $('#o_Subtitle').val();
 var attachment = $('#o_Attachment').val();
 var source = $('#o_Source').val();
 var author = $('#o_Author').val();
 var sort = $('#o_Sort').val();
 var userId = $('#o_UserId').val();
 var editDate = $('#o_EditDate').val();
 var attachDownCount = $('#o_AttachDownCount').val();
 var commentCount = $('#o_CommentCount').val();
 var auditorId = $('#o_AuditorId').val();
 var auditDate = $('#o_AuditDate').val();
 var description = $('#o_Description').val();
 var beginTime = $('#o_BeginTime').val();
 var endTime = $('#o_EndTime').val();
 var areaId = $('#o_AreaId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯类型(0:内容；1：图片；3：链接)' : '产品名称', d.data.rows.ArticleType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯类型ID' : '产品名称', d.data.rows.Cid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '所属专题ID' : '产品名称', d.data.rows.Sid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '标题' : '产品名称', d.data.rows.Title);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯短标题' : '产品名称', d.data.rows.ShortTitle);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否有图片' : '产品名称', d.data.rows.HasImage);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'b标识图片路径' : '产品名称', d.data.rows.ImageUrl);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '发布时间' : '产品名称', d.data.rows.RegDate);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '展示范围（1：当前商城；2：所有商城；）' : '产品名称', d.data.rows.DisplayRange);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '标签' : '产品名称', d.data.rows.Tagz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '关键字' : '产品名称', d.data.rows.Keywords);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（0:：草稿；1：提交；2：发布；3： 暂停；7：区县审核不通过；8：系统审核不通过）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否推荐' : '产品名称', d.data.rows.IsRecommend);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否热门(1：是，0：否)' : '产品名称', d.data.rows.IsHot);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否焦点(1：是，0：否)' : '产品名称', d.data.rows.IsFoc);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '点击量' : '产品名称', d.data.rows.Hits);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '副标题' : '产品名称', d.data.rows.Subtitle);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '附件' : '产品名称', d.data.rows.Attachment);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯来源' : '产品名称', d.data.rows.Source);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '作者' : '产品名称', d.data.rows.Author);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '排序' : '产品名称', d.data.rows.Sort);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '发布者' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '更新日期' : '产品名称', d.data.rows.EditDate);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '附件下载数' : '产品名称', d.data.rows.AttachDownCount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '评论数' : '产品名称', d.data.rows.CommentCount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核人' : '产品名称', d.data.rows.AuditorId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核时间' : '产品名称', d.data.rows.AuditDate);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核说明' : '产品名称', d.data.rows.Description);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '显示时间' : '产品名称', d.data.rows.BeginTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '到期时间' : '产品名称', d.data.rows.EndTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '发布范围0：全部；1 全区' : '产品名称', d.data.rows.AreaId);



