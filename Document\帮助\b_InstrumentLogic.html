﻿ InstrumentStandardId = entity.InstrumentStandardId,
 Pid = entity.Pid,
 ProductCode = entity.ProductCode,
 ProductName = entity.ProductName,
 UnitName = entity.UnitName,
 Abbreviation = entity.Abbreviation,
 EduCode = entity.EduCode,
 Remark = entity.Remark,
 Depth = entity.Depth,
 Icon = entity.Icon,


 InstrumentStandardId = model.InstrumentStandardId,
 Pid = model.Pid,
 ProductCode = model.ProductCode,
 ProductName = model.ProductName,
 UnitName = model.UnitName,
 Abbreviation = model.Abbreviation,
 EduCode = model.EduCode,
 Remark = model.Remark,
 Depth = model.Depth,
 Icon = model.Icon,


 temp.InstrumentStandardId = model.InstrumentStandardId,
 temp.Pid = model.Pid,
 temp.ProductCode = model.ProductCode,
 temp.ProductName = model.ProductName,
 temp.UnitName = model.UnitName,
 temp.Abbreviation = model.Abbreviation,
 temp.EduCode = model.EduCode,
 temp.Remark = model.Remark,
 temp.Depth = model.Depth,
 temp.Icon = model.Icon,

 InstrumentLogicId = item.InstrumentLogicId,
 InstrumentStandardId = item.InstrumentStandardId,
 Pid = item.Pid,
 ProductCode = item.ProductCode,
 ProductName = item.ProductName,
 UnitName = item.UnitName,
 Abbreviation = item.Abbreviation,
 EduCode = item.EduCode,
 Remark = item.Remark,
 Depth = item.Depth,
 Icon = item.Icon,

public class InstrumentLogicInputModel
{
 [Display(Name = "Id")] 
    public int InstrumentLogicId {get; set; }
    
 [Display(Name = "国家库Id")] 
    public int InstrumentStandardId {get; set; }
    
 [Display(Name = "父级Id")] 
    public int Pid {get; set; }
    
 [Display(Name = "产品代码")] 
    public string ProductCode {get; set; }
    
 [Display(Name = "产品名称")] 
    public string ProductName {get; set; }
    
 [Display(Name = "单位")] 
    public string UnitName {get; set; }
    
 [Display(Name = "缩写")] 
    public string Abbreviation {get; set; }
    
 [Display(Name = "教育部编码")] 
    public string EduCode {get; set; }
    
 [Display(Name = "备注")] 
    public string Remark {get; set; }
    
 [Display(Name = "深度")] 
    public int Depth {get; set; }
    
 [Display(Name = "")] 
    public string Icon {get; set; }
    
 }
 
 public class InstrumentLogicViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
    /// <summary>
    /// 国家库Id
    /// </summary>
    public int InstrumentStandardId {get; set; }
    
    /// <summary>
    /// 父级Id
    /// </summary>
    public int Pid {get; set; }
    
    /// <summary>
    /// 产品代码
    /// </summary>
    public string ProductCode {get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName {get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName {get; set; }
    
    /// <summary>
    /// 缩写
    /// </summary>
    public string Abbreviation {get; set; }
    
    /// <summary>
    /// 教育部编码
    /// </summary>
    public string EduCode {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Remark {get; set; }
    
    /// <summary>
    /// 深度
    /// </summary>
    public int Depth {get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string Icon {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentStandardId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentStandardId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入国家库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Pid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父级Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Abbreviation, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Abbreviation, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入缩写" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EduCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EduCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入教育部编码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Remark, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Remark, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入深度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Icon, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Icon, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入" } })                    
                </div>
           </div>
  




 { field: 'InstrumentStandardId', title: '国家库Id', sortable: true },
                 
 { field: 'Pid', title: '父级Id', sortable: true },
                 
 { field: 'ProductCode', title: '产品代码', sortable: true },
                 
 { field: 'ProductName', title: '产品名称', sortable: true },
                 
 { field: 'UnitName', title: '单位', sortable: true },
                 
 { field: 'Abbreviation', title: '缩写', sortable: true },
                 
 { field: 'EduCode', title: '教育部编码', sortable: true },
                 
 { field: 'Remark', title: '备注', sortable: true },
                 
 { field: 'Depth', title: '深度', sortable: true },
                 
 { field: 'Icon', title: '', sortable: true },
                 
o.InstrumentStandardId,                 
o.Pid,                 
o.ProductCode,                 
o.ProductName,                 
o.UnitName,                 
o.Abbreviation,                 
o.EduCode,                 
o.Remark,                 
o.Depth,                 
o.Icon,                 
        
        $('#InstrumentStandardId').val(d.data.rows.InstrumentStandardId);          
        $('#Pid').val(d.data.rows.Pid);          
        $('#ProductCode').val(d.data.rows.ProductCode);          
        $('#ProductName').val(d.data.rows.ProductName);          
        $('#UnitName').val(d.data.rows.UnitName);          
        $('#Abbreviation').val(d.data.rows.Abbreviation);          
        $('#EduCode').val(d.data.rows.EduCode);          
        $('#Remark').val(d.data.rows.Remark);          
        $('#Depth').val(d.data.rows.Depth);          
        $('#Icon').val(d.data.rows.Icon);          

 $('#th_InstrumentStandardId').html(' 国家库Id');               
 $('#th_Pid').html(' 父级Id');               
 $('#th_ProductCode').html(' 产品代码');               
 $('#th_ProductName').html(' 产品名称');               
 $('#th_UnitName').html(' 单位');               
 $('#th_Abbreviation').html(' 缩写');               
 $('#th_EduCode').html(' 教育部编码');               
 $('#th_Remark').html(' 备注');               
 $('#th_Depth').html(' 深度');               
 $('#th_Icon').html(' ');               
 
 $('#tr_InstrumentStandardId').hide();               
 $('#tr_Pid').hide();               
 $('#tr_ProductCode').hide();               
 $('#tr_ProductName').hide();               
 $('#tr_UnitName').hide();               
 $('#tr_Abbreviation').hide();               
 $('#tr_EduCode').hide();               
 $('#tr_Remark').hide();               
 $('#tr_Depth').hide();               
 $('#tr_Icon').hide();               

 , "InstrumentStandardId" : instrumentStandardId
 , "Pid" : pid
 , "ProductCode" : productCode
 , "ProductName" : productName
 , "UnitName" : unitName
 , "Abbreviation" : abbreviation
 , "EduCode" : eduCode
 , "Remark" : remark
 , "Depth" : depth
 , "Icon" : icon

 var instrumentStandardId = $('#o_InstrumentStandardId').val();
 var pid = $('#o_Pid').val();
 var productCode = $('#o_ProductCode').val();
 var productName = $('#o_ProductName').val();
 var unitName = $('#o_UnitName').val();
 var abbreviation = $('#o_Abbreviation').val();
 var eduCode = $('#o_EduCode').val();
 var remark = $('#o_Remark').val();
 var depth = $('#o_Depth').val();
 var icon = $('#o_Icon').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '国家库Id' : '产品名称', d.data.rows.InstrumentStandardId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父级Id' : '产品名称', d.data.rows.Pid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品代码' : '产品名称', d.data.rows.ProductCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品名称' : '产品名称', d.data.rows.ProductName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位' : '产品名称', d.data.rows.UnitName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '缩写' : '产品名称', d.data.rows.Abbreviation);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '教育部编码' : '产品名称', d.data.rows.EduCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Remark);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '深度' : '产品名称', d.data.rows.Depth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '' : '产品名称', d.data.rows.Icon);



