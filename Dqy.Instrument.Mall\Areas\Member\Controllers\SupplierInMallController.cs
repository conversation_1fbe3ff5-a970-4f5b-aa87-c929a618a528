using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Dqy.TrainManage.Base.Util;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

/// <summary>
/// lss供应商会员中西
/// </summary>
namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class SupplierInMallController : ControllerMember
    {

        #region lss 商城列表、商城申请入驻
        /// <summary>
        /// 供应商-商城管理列表。
        /// </summary>
        /// <param name="args">列表搜索条件</param>
        /// <returns>商城列表</returns>
        public async Task<ActionResult> Mall(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }

            if (args.Status == null)
            {
                args.Status = 0;
            }

            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            string url = "supplier/searchmall";

            var ret = await WebApiHelper.SendAsync<QueryResult<SupplierInMallViewModel>>(url, args);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }

                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }
            ViewBag.Args = args;
            return View(ret);
        }

        /// <summary>
        /// 供应商申请入驻
        /// </summary>
        /// <param name="mallId"></param>
        /// <returns></returns>
        public async Task<ActionResult> ApplyFor(int mallId = 0, string url = "")
        {
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            if (mallId <= 0)
            {
                //return ReturnHome();
            }

            //根据商城Id ，和单位id查询是否存在已申请过的。未审批通过的。
            string q_url = "supplier/applyfor" + "?mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<SupplierApplyForViewModel>>(q_url, null, CommonTypes.CommonJsonSendType.GET);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }

            SupplierInMallInputModel model = new SupplierInMallInputModel();
            model.MallId = mallId;
            model.BaseUnitId = Operater.UnitId;
            if (ret.Entity != null)
            {
                var entity = ret.Entity;
                //SupplierApplyForViewModel
                model.MallId = entity.MallId;
                model.BaseUnitId = entity.UnitId;
                model.Name = entity.Name;
                model.SocialCreditCode = entity.SocialCreditCode;
                model.Address = entity.Address;
                model.LinkMan = entity.LinkMan;
                model.Mobile = entity.Mobile;
                model.QQ = entity.QQ;
                model.Tel = entity.Tel;
                model.Brief = entity.Brief;
                model.BusLicense = entity.BusLicense;
                model.Reason = entity.Reason;
                model.SupplierInMallStatuz = entity.SupplierInMallStatuz;
                model.ProductionLicense = entity.ProductionLicense;
                if (entity.AgentLicenseList != null && entity.AgentLicenseList.Count > 0)
                {
                    model.AgentLicense = (from f in entity.AgentLicenseList
                                          select new AttachmentImgInputModel
                                          {
                                              AttachmentId = f.AttachmentId,
                                              IsDefault = f.IsDefault,
                                              Path = f.Path,
                                              Remark = f.Remark,
                                              Title = f.Title
                                          }).ToList();
                }
                else
                {
                    model.AgentLicense = null;
                }

                if (entity.OtherList != null && entity.OtherList.Count > 0)
                {
                    model.Other = (from f in entity.OtherList
                                   select new AttachmentImgInputModel
                                   {
                                       AttachmentId = f.AttachmentId,
                                       IsDefault = f.IsDefault,
                                       Path = f.Path,
                                       Remark = f.Remark,
                                       Title = f.Title
                                   }).ToList();
                }
                else
                {
                    model.Other = null;
                }
                //省市区转换。 
                if (entity.ProvinceList != null && entity.ProvinceList.Count > 0)
                {
                    SelectList selectProvinceList = new SelectList(entity.ProvinceList, "AreaId", "Name", model.ProvinceId);
                    model.ProvinceList = selectProvinceList;
                }

                if (entity.CityList != null && entity.CityList.Count > 0)
                {
                    SelectList cityList = new SelectList(entity.CityList, "AreaId", "Name", model.CityId);
                    model.CityList = cityList;
                }

                if (entity.CountyList != null && entity.CountyList.Count > 0)
                {
                    SelectList countyList = new SelectList(entity.CountyList, "AreaId", "Name", model.CountyId);
                    model.CountyList = countyList;
                }

                model.Statuz = entity.Statuz;
                model.CertificationStatuz = entity.CertificationStatuz;
                model.BaseUserId = entity.UserId;
                model.RegTime = entity.RegTime;
            }
            else
            {
                //非法请求。 

            }

            ViewBag.Url = url;
            model.Token = Operater.Token;
            return View(model);
        }

        [System.Web.Mvc.HttpPost]
        [ValidateInput(false)]
        public async Task<JsonResult> SaveApplyFor(SupplierInMallInputModel model)
        {
            ReturnResult ret = new ReturnResult();

            if (model.MallId <= 0)
            {
                ret.flag = 0;
                ret.msg = "非法请求";
                return Json(ret, JsonRequestBehavior.AllowGet);
            }

            if (ComLib.NoHTML(model.Brief).Length > 5000)
            {
                ret.flag = 0;
                ret.msg = "产品详情字数长度请控制在5000以内";
                return Json(ret, JsonRequestBehavior.AllowGet);
            }


            if (!ModelState.IsValid)
            {
                foreach (var item in ModelState.Values)
                {
                    // err.Add(item.Errors);
                    if (item.Errors.Count > 0)
                    {
                        ret.msg = item.Errors[0].ErrorMessage;
                        break;
                    }
                }
            }
            else
            {

                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                string url = "supplier/saveapplyfor";
                ret = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "申请入驻平台",
                        Action = "supplier/saveproduct",
                        Param = model,
                        Result = ret
                    }, ret.flag);

            }
            return Json(ret, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// 修改已入住商城，联系人信息。（获取要修改的实体）
        /// </summary>
        /// <param name="supplierInMallId">入驻平台Id</param>
        /// <returns></returns>
        public async Task<ActionResult> InMallLink(int id = 0, string url = "")
        {
            //API地址 
            if (id <= 0)
            {
                return Content("数据异常");
            }

            //根据商城Id ，和单位id查询是否存在已申请过的。未审批通过的。
            string q_url = "supplier/getinmalllink?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<InMallLinkInputModel>>(q_url, null, CommonTypes.CommonJsonSendType.GET);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }
            ViewBag.Url = url;
            return View(ret.Entity);
        }

        public async Task<JsonResult> SaveInMallLink(InMallLinkInputModel model)
        {
            ReturnResult ret = new ReturnResult();

            if (!ModelState.IsValid)
            {
                foreach (var item in ModelState.Values)
                {
                    // err.Add(item.Errors);
                    if (item.Errors.Count > 0)
                    {
                        ret.msg = item.Errors[0].ErrorMessage;
                        break;
                    }
                }
            }
            else
            {
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                string url = "supplier/saveinmalllink";
                ret = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "供应商入驻平台联系人添加修改",
                        Action = "supplier/saveinmalllink",
                        Param = model,
                        Result = ret
                    }, ret.flag);
            }
            return Json(ret, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region  lss商城退回产品列表，商城退回产品查看列表。 退回产品再上架、退回产品删除

        /// <summary>
        /// 商城下架产品管理
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> Backout(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/searchsuppliermall";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<SupplierInMallViewModel>>(url, args);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Args = args;
            return View(ret);
        }

        /// <summary>
        /// 商城下架产品列表查看
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> BackoutProduct(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/searchbackoutproduct";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            if (args.MallId <= 0 && ret.Entity != null)
            {
                args.MallId = ret.Entity.MallId;
            }

            ViewBag.Args = args;
            return View(ret);
        }

        /// <summary>
        /// 上架被退回产品-删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<JsonResult> BackoutProductDel(string ids, int mallId)
        {
            ReturnResult result = new ReturnResult();
            if (string.IsNullOrEmpty(ids))
            {
                result.flag = 0;
                result.msg = "请选择要删除的产品。";

                return Json(result, JsonRequestBehavior.AllowGet);
            }

            if (mallId <= 0)
            {
                result.flag = 0;
                result.msg = "非法操作， 数据异常。";

                return Json(result, JsonRequestBehavior.AllowGet);
            }
            string url = "supplier/backoutproductdel" + "?ids=" + ids + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            result = await WebApiHelper.SendAsync<ReturnResult>(url, null, CommonTypes.CommonJsonSendType.GET);
            Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "已下架，已退回，待审核撤销产品删除",
                        Action = "supplier/backoutproductdel",
                        Param = "ids=" + ids + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token,
                        Result = result
                    }, result.flag);

            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region 商城统计列表。
        /// <summary>
        /// lss商城统计 (已下架、已上架)
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns> 
        public async Task<ActionResult> Statistic(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/searchsupplierstatistic";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<SupplierStatisticViewModel>>(url, args);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Args = args;
            return View(ret);
        }

        #endregion

        #region 待审核产品列表管理

        /// <summary>
        /// 待审核产品列表管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns> 
        public async Task<ActionResult> ProductWaitAudit(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/auditproduct";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }
            if (args.MallId <= 0 && ret.Entity != null)
            {
                args.MallId = ret.Entity.MallId;
            }

            ViewBag.Args = args;
            return View(ret);
        }

        #endregion

        #region 销售区域申请和审核

        /// <summary>
        /// 销售区域管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> MarketArea(int mallid)
        {
            if (Operater == null)
            {
                return ApiTimeOut();
            }
            QueryResult<SupplierMarketAreaViewModel> result = new QueryResult<SupplierMarketAreaViewModel>();
            if (mallid > 0)
            {
                //编辑页面
                string url = "supplier/getmarketarea?mallid=" + mallid + "&token=" + Operater.Token;
                result = await WebApiHelper.SendAsync<QueryResult<SupplierMarketAreaViewModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
                if (result.flag == -1)
                {
                    return ApiTimeOut();
                }
                else if (result.flag == -2)
                {
                    return ReturnHome();
                }
            }
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId;
            return View("_MarketArea", result);
        }

        public async Task<JsonResult> ApplyMarketArea(SupplierMarketAreaInputModel model)
        {
            ReturnResult r = new ReturnResult();
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            string url = "supplier/applymarketarea";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            Log.Supplier(
                   new
                   {
                       User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                       Memo = "申请销售区域",
                       Action = "supplier/applymarketarea",
                       Param = model,
                       Result = r
                   }, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 销售区域管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> MarketAreaAuditList(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.Cid = 2;
            string url = "supplier/getmarketareaaudit";
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierMarketAreaViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            ViewBag.Args = args;
            return View(result);
        }

        public async Task<JsonResult> AuditMarketArea(SupplierMarketAreaInputModel model)
        {
            ReturnResult r = new ReturnResult();
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            model.MallId = Operater.MallId;
            string url = "supplier/auditmarketarea";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            Log.Supplier(
                   new
                   {
                       User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                       Memo = "申请销售区域",
                       Action = "supplier/applymarketarea",
                       Param = model,
                       Result = r
                   }, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 销售区域管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> AuditMarketAreaModel(int id)
        {
            if (Operater == null)
            {
                return ApiTimeOut();
            }
            //编辑页面
            string url = "supplier/getmarketareamodel?id=" + id + "&token=" + Operater.Token;
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierMarketAreaInputModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
            if (result.flag == -1)
            {
                return ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return ReturnHome();
            }
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            return View("_MarketAreaAudit", result.Entity);
        }
        #endregion

        #region 销售区管理 版本2 添加模式

        /// <summary>
        /// 添加销售区域，加载所有区域
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> MarketAreaAdd(SearchArgumentsInputModel args)
        {
            if (Operater == null)
            {
                return ApiTimeOut();
            }
            QueryResult<EduAreaViewModel> result = new QueryResult<EduAreaViewModel>();
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            string url = "supplier/getareaall";

            var ret = await WebApiHelper.SendAsync<QueryResult<EduAreaViewModel>>(url, args);
            if (result.flag == -1)
            {
                return ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return ReturnHome();
            }
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.args = args;
            return View("_MarketAreaAdd", ret);
        }

        public async Task<JsonResult> SaveMarketArea(SupplierMarketAreaInputModel model)
        {
            ReturnResult r = new ReturnResult();
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            string url = "supplier/addmarketarea";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            Log.Supplier(
                   new
                   {
                       User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                       Memo = "添加销售区域",
                       Action = "supplier/addmarketarea",
                       Param = model,
                       Result = r
                   }, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 销售区域管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> MarketAreaList(SearchArgumentsInputModel args)
        {
            QueryResult<SupplierMarketAreaViewModel> r = new QueryResult<SupplierMarketAreaViewModel>();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 10;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = "supplier/getmarketareaaudit";
            r = await WebApiHelper.SendAsync<QueryResult<SupplierMarketAreaViewModel>>(url, args);
            if (r.flag == -1)
            {
                return this.ApiTimeOut();
            }
            ViewBag.Args = args;
            return View(r);
        }


        public async Task<JsonResult> CancelMarketAreaApply(SupplierMarketAreaInputModel model)
        {
            ReturnResult r = new ReturnResult();
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.UnitId = Operater.UnitId;
            model.Token = Operater.Token;
            string url = "supplier/cancelapply";
            r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            Log.Supplier(
                   new
                   {
                       User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                       Memo = "撤销销售区域申请",
                       Action = "supplier/cancelapply",
                       Param = model,
                       Result = r
                   }, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region lss 已下架产品管理 下架产品列表 、产品删除、产品再上架

        /// <summary>
        /// 已下架产品管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns> 
        public async Task<ActionResult> OffShelfProduct(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/searchoffshelfproduct";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }
            if (args.MallId <= 0 && ret.Entity != null)
            {
                args.MallId = ret.Entity.MallId;
            }

            ViewBag.Args = args;
            return View(ret);
        }

        #endregion

        #region lss已上架产品管理 上架产品列表、下架、修改（修改的上架信息（单价、质保期、邮费，说明））。

        /// <summary>
        /// lss
        /// 已上架产品管理-列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns> 
        public async Task<ActionResult> ShelfProduct(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/searchshelfproduct";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            if (args.MallId <= 0 && ret.Entity != null)
            {
                args.MallId = ret.Entity.MallId;
            }

            ViewBag.Args = args;
            return View(ret);
        }

        /// <summary>
        /// lss 上架产品--下架
        /// </summary>
        /// <param name="id">产品上架Id</param>
        /// <param name="mallId">商城Id</param>
        /// <returns></returns>
        public async Task<JsonResult> ShelfProductOff(string ids, int mallId)
        {
            ReturnResult result = new ReturnResult();
            if (string.IsNullOrEmpty(ids))
            {
                //
                result.flag = 0;
                result.msg = "请选择下架的产品。";

                return Json(result, JsonRequestBehavior.AllowGet);
            }

            if (mallId <= 0)
            {
                result.flag = 0;
                result.msg = "非法操作， 数据异常。";

                return Json(result, JsonRequestBehavior.AllowGet);
            }
            string url = "supplier/shelfproductoff" + "?ids=" + ids + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            result = await WebApiHelper.SendAsync<ReturnResult>(url, null, CommonTypes.CommonJsonSendType.GET);
            Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "架上产品下架",
                        Action = "supplier/shelfproductoff",
                        Param = "ids=" + ids + "&mallId=" + mallId,
                        Result = result
                    }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// lss 上架产品- 更新
        /// </summary>
        /// <param name="id">产品上架Id</param>
        /// <param name="mallId">商城Id</param>
        /// <returns></returns>
        public async Task<JsonResult> ShelfProductRenewal(int id, int mallId)
        {
            ReturnResult result = new ReturnResult();
            if (id <= 0 || mallId <= 0)
            {
                result.flag = 0;
                result.msg = "非法操作。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            string url = "supplier/shelfproductrenewal" + "?id=" + id + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            result = await WebApiHelper.SendAsync<ReturnResult>(url, null, CommonTypes.CommonJsonSendType.GET);
            Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "架上产品跟新",
                        Action = "supplier/shelfproductrenewal",
                        Param = "id=" + id + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token,
                        Result = result
                    }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        /// <summary>
        /// 修改 已上架的产品
        /// </summary>
        /// <param name="id">产品上架Id</param>
        /// <param name="mallId">商城Id</param>
        /// <returns></returns>
        public async Task<ActionResult> EditShelf(int id, int mallId, string url)
        {
            string s_url = "supplier/editshelf?id=" + id + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<EditSignleShelfInputModel>>(s_url, null, CommonTypes.CommonJsonSendType.GET);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Url = url;
            return View(ret.Entity);
        }

        /// <summary>
        /// 保存已上架产品的修改。
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveEditShelf(EditSignleShelfInputModel model)
        {
            ReturnResult r = new ReturnResult();
            if (model.MallId <= 0 || model.ProductShelfId <= 0)
            {
                r.flag = 0;
                r.msg = "请重新选择产品再修改";
            }
            else
            {
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                string url = "supplier/saveeditshelf";
                r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "修改上架",
                        Action = "supplier/saveeditshelf",
                        Param = model,
                        Result = r
                    }, r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 批量修改已上架产品。
        /// </summary>
        /// <param name="ids">产品上架Id</param>
        /// <param name="mallId">商城Id</param>
        /// <returns></returns>
        public ActionResult EditBatchShelf(string ids, int mallId, string url)
        {
            EditBatchShelfInputModel model = new EditBatchShelfInputModel();
            model.MallId = mallId;
            model.ProductShelfIds = ids;
            model.Freight = FreightType.yes;
            //string url = Constant.ApiPath + "supplier/editbatchshelf" + "?ids=" + ids + "&unitId=" + Operater.UnitId + "&mallId=" + mallId; 
            //var ret = await WebApiHelper.SendAsync<EditBatchShelfInputModel>(url, null, CommonTypes.CommonJsonSendType.GET);

            //if (ret.flag <= 0)
            //{
            //    if (ret.flag == -1)
            //    {
            //        return ApiTimeOut();

            //    }
            //    if (ret.flag == -2)
            //    {
            //        return ReturnHome();
            //    }

            //}
            ViewBag.Url = url;
            return View(model);
        }

        /// <summary>
        /// 批量保存已上架产品的修改。（只改 了，产品的邮费说明和状态）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveEditBatchShelf(EditBatchShelfInputModel model)
        {
            ReturnResult r = new ReturnResult();
            if (model.MallId <= 0 || string.IsNullOrEmpty(model.ProductShelfIds))
            {
                r.flag = 0;
                r.msg = "请重新选择产品再修改";
            }
            else
            {
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                string url = "supplier/saveeditbatchshelf";
                r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "批量修改上架",
                        Action = "supplier/saveeditbatchshelf",
                        Param = model,
                        Result = r
                    }, r.flag);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }


        #endregion

        #region 在上架  下架在上架，退回在上架 ，单个上架，批量上架
        /// <summary>
        /// 单个产品在上架，展示实体页面。
        /// </summary>
        /// <param name="id"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<ActionResult> SignleAgainShelf(int id, string url)
        {
            if (id < 0)
            {
                //异常
            }

            string surl = "supplier/signleagainshelf?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<SignleShelfInputModel>>(surl, null, CommonJsonSendType.GET);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }

            }
            ViewBag.Url = url;
            return View(ret.Entity);
        }

        public async Task<JsonResult> SaveSignleAgainShelf(SignleShelfInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (model == null)
            {
                //异常
                result.flag = 0;
                result.msg = "非法操作。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            else
            {
                if (!ModelState.IsValid)
                {
                    foreach (var item in ModelState.Values)
                    {
                        // err.Add(item.Errors);
                        if (item.Errors.Count > 0)
                        {
                            result.flag = 0;
                            result.msg = item.Errors[0].ErrorMessage;
                            break;
                        }
                    }
                }
                else
                {
                    model.BaseUnitId = Operater.UnitId;
                    model.BaseUserId = Operater.UserId;
                    model.Token = Operater.Token;
                    string url = "supplier/saveproductAginshelf";
                    result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                    Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "单个产品再上架",
                        Action = "supplier/saveproductAginshelf",
                        Param = model,
                        Result = result
                    }, result.flag);
                }
            }

            return Json(result, JsonRequestBehavior.AllowGet);
        }

        public async Task<ActionResult> BatchAgainShelf(string ids, int mallId, string url)
        {
            if (string.IsNullOrEmpty(ids))
            {
                return ReturnHome();
            }
            string surl = "supplier/getbatchagainshelf?ids=" + ids + "&mallId=" + mallId + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<BatchShelfInputModel>>(surl, null, CommonJsonSendType.GET);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }

            ViewBag.Url = url;
            return View(ret.Entity);
        }

        public async Task<JsonResult> SaveBatchAgainShelf(BatchShelfInputModel model)
        {
            ReturnResult r = new ReturnResult();
            if (model.MallId <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作。";
                return Json(r, JsonRequestBehavior.AllowGet);
            }

            if (model.Ps == null || model.Ps.Count <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作。";
                return Json(r, JsonRequestBehavior.AllowGet);
            }

            try
            {
                var tempList = model.Ps.Where(m => m.id > 0).ToList();
                if (tempList != null && tempList.Count > 0)
                {
                    foreach (var item in tempList)
                    {
                        if (item.P < 0 || item.P > 10000000 || item.WM.ToInt() < 0 || item.WM.ToInt() > 100)
                        {
                            r.flag = -3;
                            r.msg = "你输入的价格或者质保期不符合要求。";
                            return Json(r, JsonRequestBehavior.AllowGet);
                        }

                    }
                    string url = "supplier/savebatchAgainshelf";
                    model.BaseUnitId = Operater.UnitId;
                    model.BaseUserId = Operater.UserId;
                    model.Token = Operater.Token;
                    r = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                    Log.Supplier(
                            new
                            {
                                User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                                Memo = "批量产品再上架",
                                Action = "supplier/savebatchAgainshelf",
                                Param = model,
                                Result = r
                            }, r.flag);
                }
                else
                {
                    r.flag = -3;
                    r.msg = "未提交要上架的产品。";
                    return Json(r, JsonRequestBehavior.AllowGet);
                }

            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("批量上架:" + e.Message);
                r.flag = -3;
                r.msg = "批量上架异常。";
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }


        #endregion

        #region
        /// <summary>
        /// lss商城统计 (已下架、已上架)
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns> 
        public async Task<ActionResult> CheckInClause(int id)
        {
            string url = "mallconfigure/checkinclause?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<CheckInClauseViewModel>>(url, null, CommonJsonSendType.GET);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }
            return View(ret.Entity);
        }
        #endregion  

        #region 周跃峰(商城管理->资质审核)

        /// <summary>
        /// 获取待审核列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> AuditList(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.Status = 0;
            string url = "supplier/postsearchauditlist";
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierAuditViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 审核页面
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        public async Task<ActionResult> Audit(int Id, int UnitId)
        {
            //API服务器地址
            ViewBag.ApiPath = Constant.ApiPath.Replace("api/", "");
            ViewBag.AreaInfo = Constant.ApiPath + "area/getarealist";
            //根据单位Id获取单位信息
            string url = $"supplier/getsupplierinmallinputmodel?token={Operater.Token}&userId={Operater.UserId}&SupplierInMallId={Id}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);

            if (r.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (r.flag == -2 || r.flag == 0)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            SupplierAuditInputModel sModel = ComLib.JSON2Object<SupplierAuditInputModel>(r.obj.ToString());
            if (sModel.UnitId != 0)
            {
                //根据单位Id,附件类型获取附件代理许可证信息
                string attachAgentLicense = WebApiHelper.GetRequest("attachment/getattachmentslist", "token=" + Operater.Token + "&objectId=" + UnitId + "&FileCategory=1&UnitId=" + Operater.UnitId + "");
                List<AttachmentInputModel> listAttachAgentLicense = ComLib.JSON2Object<List<AttachmentInputModel>>(attachAgentLicense);
                sModel.AgentLicense = listAttachAgentLicense;

                //获取其它附件
                string attachOther = WebApiHelper.GetRequest("attachment/getattachmentslist", "token=" + Operater.Token + "&objectId=" + UnitId + "&FileCategory=2&UnitId=" + Operater.UnitId + "");
                List<AttachmentInputModel> listAttachOther = ComLib.JSON2Object<List<AttachmentInputModel>>(attachOther);
                sModel.OtherAttachment = listAttachOther;
            }
            ViewBag.Token = Operater.Token;
            return View(sModel);
        }

        /// <summary>
        /// 提交审核
        /// </summary>
        /// <param name="audit"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public async Task<JsonResult> Save(SupplierAuditInputModel auditInput)
        {
            auditInput.Token = Operater.Token;
            auditInput.UnitId = Operater.UnitId;
            auditInput.AuditorId = Operater.UserId;
            auditInput.MallId = Operater.CurrentMallId;
            auditInput.AuditTime = DateTime.Now;
            auditInput.AuditResult = (CommonTypes.AuditResult)auditInput.Pass;
            auditInput.IpAddress = IPOperate.GetIP();
            string url = "supplier/postsaveaudit";
            var result = await WebApiHelper.SendAsync(url, auditInput);
            Log.UnitInfo(auditInput, result, "(平台管理员)用户‘" + Operater.Name + "’资质审核", "MallAdmin", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 周跃峰(商城管理->产品管理)
        /// <summary>
        /// 产品管理供应商列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> ProductManagerList(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.Token = Operater.Token;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Status = AuditResult.Pass.ToInt();
            string url = "supplier/postsearchproductauditlist";
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierProductAuditModel>>(url, args);
            if (result.flag == -1)
            {
                //r.flag = 0;
                //r.msg = "登录超时，请重新登录。";
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 保存配置信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveMallConfigure(MallConfigureInputModel model)
        {
            model.MallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            model.IpAddress = IPOperate.GetIP();
            string url = "mallconfigure/postupdatebymallid";
            var result = await WebApiHelper.SendAsync(url, model);
            Log.UnitInfo(model, result, "(平台管理员)用户‘" + Operater.Name + "’保存权限控制", "MallAdmin", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 产品审核页面
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        public async Task<ActionResult> ProductAudit(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            //args.UnitId = UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Status = ProductShelfStatuz.WaitAudit.ToInt();
            args.Token = Operater.Token;
            string url = "supplier/postsearchwaitproductaudit";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductAuditViewModel>>(url, args);
            if (result.flag == -1)
            {
                //r.msg = "登录超时，请重新登录。";
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.msg = "您无权操作此功能。";
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        public async Task<ActionResult> AuditPreview(long id, int unitId = 0, int type = 0)
        {
            if (type == 1)
            {
                ReturnResult r = new ReturnResult();
                SearchArgumentsInputModel args = new SearchArgumentsInputModel();
                args.Limit = 1;
                //args.UnitId = UnitId;
                args.MallId = Operater.CurrentMallId;
                args.Status = ProductShelfStatuz.WaitAudit.ToInt();
                args.Token = Operater.Token;
                args.UnitId = unitId;
                string url1 = "supplier/postsearchwaitproductaudit";
                var result = await WebApiHelper.SendAsync<QueryResult<ProductAuditViewModel>>(url1, args);
                if (result != null && result.Data != null && result.Data.Count > 0)
                {
                    id = result.Data[0].ProductShelfId;
                }
            }
            string url = "product/getproductdetail?productShelfId=" + id + "&type=2";

            var model = await WebApiHelper.SendAsync<ProductDetailViewModel>(url, null, CommonTypes.CommonJsonSendType.GET);
            return View(model);
        }


        /// <summary>
        /// 产品审核
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveProductAudit(ProductAuditInputModel productAudit)
        {
            productAudit.MallId = Operater.CurrentMallId;
            productAudit.Token = Operater.Token;
            //productAudit.CompanyId = Operater.UnitId;
            productAudit.IpAddress = IPOperate.GetIP();
            string url = "supplier/postsaveproductaudit";
            var result = await WebApiHelper.SendAsync(url, productAudit);
            if (result.flag == 0 && result.msg == "")
            {
                result.msg = "添加索引失败";
            }
            Log.UnitInfo(productAudit, result, "(平台管理员)用户‘" + Operater.Name + "’产品审核", "MallAdmin", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 周跃峰(商城管理->在售产品)
        /// <summary>
        /// 在售产品页面
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        public async Task<ActionResult> OnSaleProductList(SearchProductInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchProductInputModel();
            }
            args.Limit = 20;
            args.Token = Operater.Token;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Status = ProductShelfStatuz.AuditAdopt.ToInt();
            string url = "supplier/postsearchwaitproductaudit";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductAuditViewModel>>(url, args);
            if (result.flag == -1)
            {
                //r.msg = "登录超时，请重新登录。";
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.msg = "您无权操作此功能。";
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 暂停销售、强制下架
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveOnSaleProduct(ProductAuditInputModel productAudit)
        {
            productAudit.MallId = Operater.CurrentMallId;
            productAudit.Token = Operater.Token;
            productAudit.IpAddress = IPOperate.GetIP();
            string url = "supplier/postsaveonsaleproduce";
            var result = await WebApiHelper.SendAsync(url, productAudit);
            Log.UnitInfo(productAudit, result, "(平台管理员)用户‘" + Operater.Name + "’暂停销售、强制下架", "MallAdmin", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 周跃峰(商城管理->停售产品)
        /// <summary>
        /// 停售产品页面
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        public async Task<ActionResult> StopSaleProductList(SearchArgumentsInputModel args)
        {
            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            //args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Status = ProductShelfStatuz.StopSale.ToInt();
            args.Token = Operater.Token;
            string url = "supplier/postsearchwaitproductaudit";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductAuditViewModel>>(url, args);
            if (result.flag == -1)
            {
                //r.msg = "登录超时，请重新登录。";
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.msg = "您无权操作此功能。";
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            ViewBag.Args = args;
            return View(result);
        }

        /// <summary>
        /// 恢复销售
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveRecoverySale(ProductAuditInputModel productAudit)
        {
            productAudit.MallId = Operater.CurrentMallId;
            productAudit.Token = Operater.Token;
            productAudit.IpAddress = IPOperate.GetIP();
            string url = "supplier/postsaverecoverysale";
            var result = await WebApiHelper.SendAsync(url, productAudit);
            Log.UnitInfo(productAudit, result, "(平台管理员)用户‘" + Operater.Name + "’恢复销售", "MallAdmin", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion


        /// <summary>
        /// 供应商信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> SupplierStatistic(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = "supplier/postsearchsupplierstatistic";
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierStatistic>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            ViewBag.Args = args;
            return View(result);
        }


        public async Task<ActionResult> PowerControl()
        {
            //根据单位Id获取单位信息
            string url = $"mallconfigure/getconfigurebymallid?token={Operater.Token}&mallId={Operater.CurrentMallId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                return this.ApiTimeOut();
            }
            MallConfigureInputModel configureResult = ComLib.JSON2Object<MallConfigureInputModel>(r.obj.ToString());
            return View(configureResult);
        }


        /// <summary>
        /// 产品审核查询
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> SearchProductAuditList(SearchAuditInputModel args)
        {

            if (args == null)
            {
                args = new SearchAuditInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            args.RecordUserId = Operater.RecordUserId;
            if (string.IsNullOrEmpty(args.RegTime))
            {
                string dtBegin = DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd") + " - " + DateTime.Now.ToString("yyyy-MM-dd");
                args.RegTime = dtBegin;
            }

            string url = "supplier/postsupplierproductauditlist";
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierProductAuditViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            ViewBag.Args = args;
            return View(result);
        }

        public async Task<ActionResult> ProductRecord(SearchAuditInputModel args, string url = "")
        {
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.MallId = Operater.CurrentMallId;

            string q_url = "supplier/ProductRecord";
            var model = await WebApiHelper.SendAsync<QueryResult<PProductRecordViewModel>>(q_url, args);
            if (model.flag == -1)
            {
                return this.ApiTimeOut();
            }
            ViewBag.Args = args;
            ViewBag.Url = url;
            return View(model);
        }


        /// <summary>
        /// 申请推荐商家
        /// </summary>
        /// <param name="supplierInMallId">商家入驻表id</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> RecommendApplySupplier(int supplierInMallId)
        {
            PRecommendInputModel model = new PRecommendInputModel();
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.ObjId = supplierInMallId;
            model.ObjType = RecommendObjType.Supplier;
            model.MarketType = RecommendMarketType.Recommend;
            model.Statuz = RecommendStatuz.WaitAudit;

            string url = "recommend/apply";
            var result = await WebApiHelper.SendAsync(url, model);
            Log.Supplier(
                         new
                         {
                             User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                             Memo = "申请推荐商家",
                             Action = "recommend/apply",
                             Param = model,
                             Result = result
                         }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 申请推荐产品
        /// </summary>
        /// <param name="supplierInMallId">商家入驻表id</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> RecommendApplyProduct(List<long> productShelfIds)
        {
            PRecommendInputModel model = new PRecommendInputModel();
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            model.BaseUserId = Operater.UserId;
            model.BaseUnitId = Operater.UnitId;
            model.ObjIdList = productShelfIds;
            model.ObjType = RecommendObjType.Product;
            model.MarketType = RecommendMarketType.Recommend;
            model.Statuz = RecommendStatuz.WaitAudit;

            string url = "recommend/batchapply";
            var result = await WebApiHelper.SendAsync(url, model);
            Log.Supplier(new
            {
                User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                Memo = "申请推荐产品",
                Action = "recommend/batchapply",
                Param = model,
                Result = result
            }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 已推荐产品
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns> 
        public async Task<ActionResult> RecommendProduct(SearchArgumentsInputModel args)
        {
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            string url = "supplier/searchrecommendproduct";
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            if (args.MallId <= 0 && ret.Entity != null)
            {
                args.MallId = ret.Entity.MallId;
            }

            ViewBag.Args = args;
            return View(ret);
        }
    }
}
