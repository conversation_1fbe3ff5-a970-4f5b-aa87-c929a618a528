﻿ OrderId = entity.OrderId,
 ProductDeclareId = entity.ProductDeclareId,
 ProductId = entity.ProductId,
 ProductShelvesId = entity.ProductShelvesId,
 Quality = entity.Quality,
 Logistics = entity.Logistics,
 Attitude = entity.Attitude,
 Comment = entity.Comment,
 UserId = entity.UserId,
 RegTime = entity.RegTime,
 IsAnonymous = entity.IsAnonymous,


 OrderId = model.OrderId,
 ProductDeclareId = model.ProductDeclareId,
 ProductId = model.ProductId,
 ProductShelvesId = model.ProductShelvesId,
 Quality = model.Quality,
 Logistics = model.Logistics,
 Attitude = model.Attitude,
 Comment = model.Comment,
 UserId = model.UserId,
 RegTime = model.RegTime,
 IsAnonymous = model.IsAnonymous,


 temp.OrderId = model.OrderId,
 temp.ProductDeclareId = model.ProductDeclareId,
 temp.ProductId = model.ProductId,
 temp.ProductShelvesId = model.ProductShelvesId,
 temp.Quality = model.Quality,
 temp.Logistics = model.Logistics,
 temp.Attitude = model.Attitude,
 temp.Comment = model.Comment,
 temp.UserId = model.UserId,
 temp.RegTime = model.RegTime,
 temp.IsAnonymous = model.IsAnonymous,

 OrderEvaluationId = item.OrderEvaluationId,
 OrderId = item.OrderId,
 ProductDeclareId = item.ProductDeclareId,
 ProductId = item.ProductId,
 ProductShelvesId = item.ProductShelvesId,
 Quality = item.Quality,
 Logistics = item.Logistics,
 Attitude = item.Attitude,
 Comment = item.Comment,
 UserId = item.UserId,
 RegTime = item.RegTime,
 IsAnonymous = item.IsAnonymous,

public class OrderEvaluationInputModel
{
 [Display(Name = "订单评价Id")] 
    public long OrderEvaluationId {get; set; }
    
 [Display(Name = "订单Id")] 
    public long OrderId {get; set; }
    
 [Display(Name = "申报表Id")] 
    public long ProductDeclareId {get; set; }
    
 [Display(Name = "产品Id（作废）")] 
    public long ProductId {get; set; }
    
 [Display(Name = "产品上架Id")] 
    public long ProductShelvesId {get; set; }
    
 [Display(Name = "产品质量")] 
    public int Quality {get; set; }
    
 [Display(Name = "物流服务")] 
    public int Logistics {get; set; }
    
 [Display(Name = "服务态度")] 
    public int Attitude {get; set; }
    
 [Display(Name = "评论")] 
    public string Comment {get; set; }
    
 [Display(Name = "记录人")] 
    public long UserId {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "是否匿名评价 0：否，1：是")] 
    public bool IsAnonymous {get; set; }
    
 }
 
 public class OrderEvaluationViewModel
 {
    /// <summary>
    /// 订单评价Id
    /// </summary>
    public long OrderEvaluationId {get; set; }
    
    /// <summary>
    /// 订单Id
    /// </summary>
    public long OrderId {get; set; }
    
    /// <summary>
    /// 申报表Id
    /// </summary>
    public long ProductDeclareId {get; set; }
    
    /// <summary>
    /// 产品Id（作废）
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 产品上架Id
    /// </summary>
    public long ProductShelvesId {get; set; }
    
    /// <summary>
    /// 产品质量
    /// </summary>
    public int Quality {get; set; }
    
    /// <summary>
    /// 物流服务
    /// </summary>
    public int Logistics {get; set; }
    
    /// <summary>
    /// 服务态度
    /// </summary>
    public int Attitude {get; set; }
    
    /// <summary>
    /// 评论
    /// </summary>
    public string Comment {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 是否匿名评价 0：否，1：是
    /// </summary>
    public bool IsAnonymous {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductDeclareId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductDeclareId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入申报表Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id（作废）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductShelvesId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductShelvesId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品上架Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Quality, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Quality, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品质量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Logistics, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Logistics, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入物流服务" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Attitude, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Attitude, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入服务态度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Comment, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Comment, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入评论" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsAnonymous, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsAnonymous, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否匿名评价 0：否，1：是" } })                    
                </div>
           </div>
  




 { field: 'OrderId', title: '订单Id', sortable: true },
                 
 { field: 'ProductDeclareId', title: '申报表Id', sortable: true },
                 
 { field: 'ProductId', title: '产品Id（作废）', sortable: true },
                 
 { field: 'ProductShelvesId', title: '产品上架Id', sortable: true },
                 
 { field: 'Quality', title: '产品质量', sortable: true },
                 
 { field: 'Logistics', title: '物流服务', sortable: true },
                 
 { field: 'Attitude', title: '服务态度', sortable: true },
                 
 { field: 'Comment', title: '评论', sortable: true },
                 
 { field: 'UserId', title: '记录人', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
 { field: 'IsAnonymous', title: '是否匿名评价 0：否，1：是', sortable: true },
                 
o.OrderId,                 
o.ProductDeclareId,                 
o.ProductId,                 
o.ProductShelvesId,                 
o.Quality,                 
o.Logistics,                 
o.Attitude,                 
o.Comment,                 
o.UserId,                 
o.RegTime,                 
o.IsAnonymous,                 
        
        $('#OrderId').val(d.data.rows.OrderId);          
        $('#ProductDeclareId').val(d.data.rows.ProductDeclareId);          
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#ProductShelvesId').val(d.data.rows.ProductShelvesId);          
        $('#Quality').val(d.data.rows.Quality);          
        $('#Logistics').val(d.data.rows.Logistics);          
        $('#Attitude').val(d.data.rows.Attitude);          
        $('#Comment').val(d.data.rows.Comment);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#IsAnonymous').val(d.data.rows.IsAnonymous);          

 $('#th_OrderId').html(' 订单Id');               
 $('#th_ProductDeclareId').html(' 申报表Id');               
 $('#th_ProductId').html(' 产品Id（作废）');               
 $('#th_ProductShelvesId').html(' 产品上架Id');               
 $('#th_Quality').html(' 产品质量');               
 $('#th_Logistics').html(' 物流服务');               
 $('#th_Attitude').html(' 服务态度');               
 $('#th_Comment').html(' 评论');               
 $('#th_UserId').html(' 记录人');               
 $('#th_RegTime').html(' 记录时间');               
 $('#th_IsAnonymous').html(' 是否匿名评价 0：否，1：是');               
 
 $('#tr_OrderId').hide();               
 $('#tr_ProductDeclareId').hide();               
 $('#tr_ProductId').hide();               
 $('#tr_ProductShelvesId').hide();               
 $('#tr_Quality').hide();               
 $('#tr_Logistics').hide();               
 $('#tr_Attitude').hide();               
 $('#tr_Comment').hide();               
 $('#tr_UserId').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_IsAnonymous').hide();               

 , "OrderId" : orderId
 , "ProductDeclareId" : productDeclareId
 , "ProductId" : productId
 , "ProductShelvesId" : productShelvesId
 , "Quality" : quality
 , "Logistics" : logistics
 , "Attitude" : attitude
 , "Comment" : comment
 , "UserId" : userId
 , "RegTime" : regTime
 , "IsAnonymous" : isAnonymous

 var orderId = $('#o_OrderId').val();
 var productDeclareId = $('#o_ProductDeclareId').val();
 var productId = $('#o_ProductId').val();
 var productShelvesId = $('#o_ProductShelvesId').val();
 var quality = $('#o_Quality').val();
 var logistics = $('#o_Logistics').val();
 var attitude = $('#o_Attitude').val();
 var comment = $('#o_Comment').val();
 var userId = $('#o_UserId').val();
 var regTime = $('#o_RegTime').val();
 var isAnonymous = $('#o_IsAnonymous').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单Id' : '产品名称', d.data.rows.OrderId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '申报表Id' : '产品名称', d.data.rows.ProductDeclareId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id（作废）' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品上架Id' : '产品名称', d.data.rows.ProductShelvesId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品质量' : '产品名称', d.data.rows.Quality);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '物流服务' : '产品名称', d.data.rows.Logistics);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '服务态度' : '产品名称', d.data.rows.Attitude);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '评论' : '产品名称', d.data.rows.Comment);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否匿名评价 0：否，1：是' : '产品名称', d.data.rows.IsAnonymous);



