﻿using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    /***
     过程监管（区县端）
     add by jiangpeng 2019-1-2
     ***/
    public class SuperviseController : ControllerMember
    {
        /// <summary>
        /// 订单监管列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> Order(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = "order/superviseorder";
            var result = await WebApiHelper.SendAsync<QueryResult<AlreadyGenerateOrderViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            return View(result);
        }


        /// <summary>
        /// 送货监管列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> Receive(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = Constant.ApiPath + "order/supervisereceive";
            var result = await WebApiHelper.SendAsync<QueryResult<OrderReceiveListViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            return View(result);
        }
    }
}