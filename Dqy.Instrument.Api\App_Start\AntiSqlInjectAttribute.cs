﻿using Dqy.Instrument.Framework.Component;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http.Controllers;
using System.Web.Mvc;

namespace Dqy.Instrument.Api
{
    public class AntiSqlInjectAttribute : FilterAttribute, IActionFilter
    {
        public void OnActionExecuted(ActionExecutedContext filterContext)
        {

        }

        public void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var actionParameters = filterContext.ActionDescriptor.GetParameters();
            foreach (var p in actionParameters)
            {
                if (p.ParameterType == typeof(string))
                {
                    if (p.ParameterName != "token")
                    {
                        if (filterContext.ActionParameters[p.ParameterName] != null)
                        {
                            filterContext.ActionParameters[p.ParameterName] = StringFilter.SearchSql(filterContext.ActionParameters[p.ParameterName].ToString());
                        }
                    }
                }
            }
        }
    }
}