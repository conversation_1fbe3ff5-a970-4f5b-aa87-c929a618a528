<?xml version="1.0" encoding="utf-8"?>
<!--
此文件由 Web 项目的发布/打包过程使用。可以通过编辑此 MSBuild 文件
自定义此过程的行为。为了解与此相关的更多内容，请访问 https://go.microsoft.com/fwlink/?LinkID=208121。 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <TimeStampOfAssociatedLegacyPublishXmlFile />
    <_PublishTargetUrl>D:\Projects\instrument_api</_PublishTargetUrl>
    <History>True|2025-06-06T03:19:59.1824834Z||;True|2025-06-06T09:29:52.2893939+08:00||;True|2025-06-06T09:12:56.8420065+08:00||;True|2025-06-05T22:05:25.0879922+08:00||;True|2025-06-05T19:00:00.0597638+08:00||;True|2025-06-05T18:56:28.9900972+08:00||;True|2025-06-05T18:27:09.8844046+08:00||;True|2025-06-05T10:59:02.1968343+08:00||;True|2025-04-18T17:28:50.3084390+08:00||;True|2025-04-07T10:18:20.6564606+08:00||;False|2025-04-07T10:14:59.1151836+08:00||;True|2025-01-22T10:03:09.1311087+08:00||;True|2025-01-22T10:01:52.5864306+08:00||;True|2024-11-19T16:46:10.2543505+08:00||;True|2024-11-08T18:12:52.3507242+08:00||;False|2024-09-04T14:00:08.2306796+08:00||;False|2024-09-04T13:59:16.2870884+08:00||;False|2024-09-04T13:55:47.0592753+08:00||;True|2024-08-23T17:29:43.1854588+08:00||;True|2024-08-23T17:18:52.2349284+08:00||;True|2024-07-03T13:17:47.7164343+08:00||;True|2024-06-06T17:49:36.3427555+08:00||;True|2024-05-30T10:30:51.3603626+08:00||;True|2024-05-06T15:51:10.0588903+08:00||;True|2024-05-06T11:46:15.6088273+08:00||;True|2024-04-30T13:35:30.6111683+08:00||;True|2024-04-30T13:28:52.2264468+08:00||;True|2024-03-29T17:31:27.7000195+08:00||;True|2024-03-27T19:54:11.1158259+08:00||;True|2024-03-27T16:40:54.7053346+08:00||;True|2024-03-27T16:28:41.2770588+08:00||;True|2024-03-27T14:50:14.6703268+08:00||;True|2024-03-01T10:19:54.2228257+08:00||;True|2023-12-28T17:13:31.6082491+08:00||;True|2023-12-19T15:50:56.0914697+08:00||;True|2023-12-01T09:16:59.1024203+08:00||;True|2023-11-15T10:59:50.0910547+08:00||;True|2023-11-14T15:31:45.7585247+08:00||;True|2023-11-14T14:21:15.0264792+08:00||;True|2023-11-14T13:46:29.6249532+08:00||;True|2023-11-06T19:41:53.7741278+08:00||;True|2023-11-06T17:42:50.7155726+08:00||;True|2023-10-31T11:39:32.5935498+08:00||;True|2023-10-31T11:20:51.5535437+08:00||;True|2023-10-31T11:20:19.8638637+08:00||;True|2023-09-26T14:53:58.9424385+08:00||;True|2023-08-16T16:37:52.3624960+08:00||;True|2023-07-19T15:33:12.5159335+08:00||;True|2023-07-12T15:13:04.8714801+08:00||;True|2023-07-04T17:18:59.7495224+08:00||;True|2023-07-04T10:45:24.1694148+08:00||;True|2023-06-29T11:08:48.1339403+08:00||;True|2023-06-14T14:44:49.8795674+08:00||;True|2023-06-09T10:46:53.1076293+08:00||;True|2023-06-06T17:58:55.9565220+08:00||;True|2023-06-05T13:20:49.1810137+08:00||;True|2023-06-02T13:59:26.8833203+08:00||;True|2023-06-02T13:13:59.3992466+08:00||;True|2023-05-26T09:44:47.6334071+08:00||;True|2023-05-23T10:01:27.5420968+08:00||;True|2023-04-24T17:52:13.3052188+08:00||;True|2023-04-11T16:18:51.1934734+08:00||;True|2023-04-11T14:36:19.4389475+08:00||;True|2023-04-11T14:32:28.6279616+08:00||;True|2023-04-11T13:42:42.5833055+08:00||;True|2023-04-11T11:49:02.5639590+08:00||;True|2023-04-11T11:46:48.6191005+08:00||;True|2023-04-11T10:28:43.8368756+08:00||;True|2023-04-11T09:57:23.2260161+08:00||;True|2023-04-10T13:06:28.5292164+08:00||;True|2023-03-23T17:29:45.9832388+08:00||;True|2023-03-15T17:44:24.1690654+08:00||;True|2023-02-23T17:51:25.0813379+08:00||;True|2023-02-21T16:06:49.2639912+08:00||;True|2023-02-21T10:06:26.6135107+08:00||;True|2023-02-16T18:44:02.6159390+08:00||;True|2023-02-16T18:37:18.4868302+08:00||;True|2023-02-16T18:33:33.4781950+08:00||;True|2023-02-16T18:30:01.5020835+08:00||;True|2023-02-16T17:22:18.4349979+08:00||;True|2023-02-16T17:18:17.0124612+08:00||;True|2023-02-16T16:21:51.3814668+08:00||;True|2023-02-16T14:47:09.9192447+08:00||;True|2023-02-07T15:41:22.6847189+08:00||;True|2023-01-30T09:06:42.4474455+08:00||;True|2023-01-11T18:09:56.8122659+08:00||;True|2023-01-11T16:06:21.4752387+08:00||;True|2023-01-11T11:37:52.4545892+08:00||;True|2023-01-09T10:34:56.7594079+08:00||;True|2023-01-06T13:43:20.3289297+08:00||;True|2023-01-05T14:22:05.0483356+08:00||;True|2022-12-30T13:04:01.5965202+08:00||;True|2022-12-26T09:15:22.8531846+08:00||;True|2022-12-20T17:28:45.1223083+08:00||;True|2022-11-28T10:13:11.8368039+08:00||;True|2022-11-08T16:14:09.9051162+08:00||;True|2022-10-21T15:46:03.5644657+08:00||;True|2022-10-19T15:58:00.2147848+08:00||;True|2022-10-17T18:26:44.3920822+08:00||;True|2022-10-17T18:19:35.9983469+08:00||;</History>
    <LastFailureDetails />
  </PropertyGroup>
  <ItemGroup>
    <File Include="Areas/HelpPage/HelpPage.css">
      <publishTime>09/09/2022 17:08:52</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/Api.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/ApiGroup.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/CollectionModelDescription.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/ComplexTypeModelDescription.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/DictionaryModelDescription.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/EnumTypeModelDescription.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/HelpPageApiModel.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/ImageSample.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/InvalidSample.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/KeyValuePairModelDescription.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/ModelDescriptionLink.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/Parameters.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/Returns.cshtml">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/Samples.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/SimpleTypeModelDescription.cshtml">
      <publishTime>09/09/2022 17:08:49</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/TestGet.cshtml">
      <publishTime>01/14/2020 16:18:11</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/TestPost.cshtml">
      <publishTime>01/14/2020 16:18:11</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/TestRoute.cshtml">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/DisplayTemplates/TextSample.cshtml">
      <publishTime>09/09/2022 17:08:48</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/Index.cshtml">
      <publishTime>09/09/2022 17:08:48</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Help/ResourceModel.cshtml">
      <publishTime>09/09/2022 17:08:48</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Shared/_Layout.cshtml">
      <publishTime>09/09/2022 17:08:48</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/Web.config">
      <publishTime>09/09/2022 17:08:48</publishTime>
    </File>
    <File Include="Areas/HelpPage/Views/_ViewStart.cshtml">
      <publishTime>09/09/2022 17:08:48</publishTime>
    </File>
    <File Include="bin/Antlr3.Runtime.dll">
      <publishTime>09/10/2013 16:29:20</publishTime>
    </File>
    <File Include="bin/Antlr3.Runtime.pdb">
      <publishTime>09/10/2013 16:29:20</publishTime>
    </File>
    <File Include="bin/AutoMapper.dll">
      <publishTime>06/01/2021 11:02:54</publishTime>
    </File>
    <File Include="bin/BinaryFormatter.dll">
      <publishTime>07/21/2019 10:19:08</publishTime>
    </File>
    <File Include="bin/BouncyCastle.Crypto.dll">
      <publishTime>10/19/2021 19:53:32</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Api.dll">
      <publishTime>06/06/2025 11:19:58</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Api.pdb">
      <publishTime>06/06/2025 11:19:58</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.ApplicationServices.dll">
      <publishTime>06/06/2025 09:12:52</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.ApplicationServices.dll.config">
      <publishTime>09/09/2022 15:42:49</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.ApplicationServices.pdb">
      <publishTime>06/06/2025 09:12:52</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.CloudMarketing.dll">
      <publishTime>06/05/2025 18:27:03</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.CloudMarketing.dll.config">
      <publishTime>09/09/2022 15:42:49</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.CloudMarketing.pdb">
      <publishTime>06/05/2025 18:27:03</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.CommonTypes.dll">
      <publishTime>06/05/2025 10:58:34</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.CommonTypes.pdb">
      <publishTime>06/05/2025 10:58:34</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Configurations.dll">
      <publishTime>06/06/2025 09:12:52</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Configurations.dll.config">
      <publishTime>09/09/2022 15:42:49</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Configurations.pdb">
      <publishTime>06/06/2025 09:12:52</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.Component.dll">
      <publishTime>06/05/2025 13:50:55</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.Component.dll.config">
      <publishTime>06/05/2025 18:26:38</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.Component.pdb">
      <publishTime>06/05/2025 13:50:55</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.PetaPoco.dll">
      <publishTime>06/05/2025 10:58:54</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.PetaPoco.pdb">
      <publishTime>06/05/2025 10:58:54</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.RestLite.dll">
      <publishTime>06/05/2025 10:58:55</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.RestLite.dll.config">
      <publishTime>09/09/2022 14:05:34</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Framework.RestLite.pdb">
      <publishTime>06/05/2025 10:58:55</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Search.dll">
      <publishTime>06/05/2025 18:27:04</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Search.dll.config">
      <publishTime>09/09/2022 15:42:50</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Search.pdb">
      <publishTime>06/05/2025 18:27:04</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Services.ImplementedInterfaces.dll">
      <publishTime>06/06/2025 09:12:52</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Services.ImplementedInterfaces.dll.config">
      <publishTime>09/09/2022 15:42:49</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.Services.ImplementedInterfaces.pdb">
      <publishTime>06/06/2025 09:12:52</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.UI.InputModels.dll">
      <publishTime>06/05/2025 13:50:56</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.UI.InputModels.dll.config">
      <publishTime>09/09/2022 15:42:50</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.UI.InputModels.pdb">
      <publishTime>06/05/2025 13:50:56</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.UI.ViewModels.dll">
      <publishTime>06/05/2025 13:50:56</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.UI.ViewModels.dll.config">
      <publishTime>09/09/2022 15:42:50</publishTime>
    </File>
    <File Include="bin/Dqy.Instrument.UI.ViewModels.pdb">
      <publishTime>06/05/2025 13:50:56</publishTime>
    </File>
    <File Include="bin/EmitMapper.dll">
      <publishTime>01/14/2020 16:18:16</publishTime>
    </File>
    <File Include="bin/EntityFramework.dll">
      <publishTime>04/16/2020 20:38:42</publishTime>
    </File>
    <File Include="bin/EntityFramework.SqlServer.dll">
      <publishTime>04/16/2020 20:38:56</publishTime>
    </File>
    <File Include="bin/Enyim.Caching.dll">
      <publishTime>07/28/2011 13:38:44</publishTime>
    </File>
    <File Include="bin/Enyim.Caching.pdb">
      <publishTime>07/28/2011 13:38:44</publishTime>
    </File>
    <File Include="bin/Enyim.Caching.Web.dll">
      <publishTime>05/01/2012 15:34:58</publishTime>
    </File>
    <File Include="bin/Enyim.Caching.Web.pdb">
      <publishTime>05/01/2012 15:34:58</publishTime>
    </File>
    <File Include="bin/ICSharpCode.SharpZipLib.dll">
      <publishTime>09/19/2021 17:20:28</publishTime>
    </File>
    <File Include="bin/ICSharpCode.SharpZipLib.pdb">
      <publishTime>09/19/2021 17:20:28</publishTime>
    </File>
    <File Include="bin/Membase.dll">
      <publishTime>07/28/2011 13:38:44</publishTime>
    </File>
    <File Include="bin/Membase.pdb">
      <publishTime>07/28/2011 13:38:44</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNet.Identity.Core.dll">
      <publishTime>11/25/2019 10:57:50</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNet.Identity.EntityFramework.dll">
      <publishTime>11/25/2019 10:57:50</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNet.Identity.Owin.dll">
      <publishTime>11/25/2019 10:57:50</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNetCore.Http.Abstractions.dll">
      <publishTime>11/12/2018 17:29:00</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNetCore.Http.dll">
      <publishTime>01/24/2019 23:18:54</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNetCore.Http.Features.dll">
      <publishTime>04/16/2022 06:18:18</publishTime>
    </File>
    <File Include="bin/Microsoft.AspNetCore.WebUtilities.dll">
      <publishTime>11/12/2018 17:29:00</publishTime>
    </File>
    <File Include="bin/Microsoft.Bcl.AsyncInterfaces.dll">
      <publishTime>10/23/2021 07:42:08</publishTime>
    </File>
    <File Include="bin/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll">
      <publishTime>07/08/2020 21:52:58</publishTime>
    </File>
    <File Include="bin/Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <publishTime>10/23/2021 07:48:04</publishTime>
    </File>
    <File Include="bin/Microsoft.Extensions.ObjectPool.dll">
      <publishTime>07/14/2022 09:11:10</publishTime>
    </File>
    <File Include="bin/Microsoft.Extensions.Options.dll">
      <publishTime>10/23/2021 07:48:14</publishTime>
    </File>
    <File Include="bin/Microsoft.Extensions.Primitives.dll">
      <publishTime>10/23/2021 07:47:28</publishTime>
    </File>
    <File Include="bin/Microsoft.Net.Http.Headers.dll">
      <publishTime>10/09/2019 16:35:38</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.dll">
      <publishTime>05/11/2022 07:31:20</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Host.SystemWeb.dll">
      <publishTime>05/11/2022 07:31:40</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.Cookies.dll">
      <publishTime>05/11/2022 07:31:50</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.dll">
      <publishTime>05/11/2022 07:31:38</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.Facebook.dll">
      <publishTime>01/14/2020 16:18:22</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.Google.dll">
      <publishTime>01/14/2020 16:18:38</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.MicrosoftAccount.dll">
      <publishTime>05/11/2022 07:31:48</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.OAuth.dll">
      <publishTime>05/11/2022 07:31:50</publishTime>
    </File>
    <File Include="bin/Microsoft.Owin.Security.Twitter.dll">
      <publishTime>01/14/2020 16:18:37</publishTime>
    </File>
    <File Include="bin/Microsoft.Web.Infrastructure.dll">
      <publishTime>04/12/2022 03:09:46</publishTime>
    </File>
    <File Include="bin/Newtonsoft.Json.dll">
      <publishTime>03/17/2021 20:03:36</publishTime>
    </File>
    <File Include="bin/Ninject.dll">
      <publishTime>05/27/2022 23:01:24</publishTime>
    </File>
    <File Include="bin/Ninject.Web.Common.dll">
      <publishTime>02/08/2020 08:20:52</publishTime>
    </File>
    <File Include="bin/Ninject.Web.Common.WebHost.dll">
      <publishTime>02/08/2020 16:20:54</publishTime>
    </File>
    <File Include="bin/NPOI.dll">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.OOXML.dll">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.OOXML.pdb">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.OpenXml4Net.dll">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.OpenXml4Net.pdb">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.OpenXmlFormats.dll">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.OpenXmlFormats.pdb">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/NPOI.pdb">
      <publishTime>04/26/2022 00:23:22</publishTime>
    </File>
    <File Include="bin/Owin.dll">
      <publishTime>01/14/2020 16:18:19</publishTime>
    </File>
    <File Include="bin/PlainElastic.Net.dll">
      <publishTime>01/14/2020 16:18:14</publishTime>
    </File>
    <File Include="bin/PlainElastic.Net.pdb">
      <publishTime>01/14/2020 16:18:14</publishTime>
    </File>
    <File Include="bin/roslyn/csc.exe">
      <publishTime>05/19/2020 01:51:56</publishTime>
    </File>
    <File Include="bin/roslyn/csc.exe.config">
      <publishTime>05/19/2020 01:51:56</publishTime>
    </File>
    <File Include="bin/roslyn/csc.rsp">
      <publishTime>05/19/2020 01:40:20</publishTime>
    </File>
    <File Include="bin/roslyn/csi.exe">
      <publishTime>05/19/2020 01:52:16</publishTime>
    </File>
    <File Include="bin/roslyn/csi.exe.config">
      <publishTime>05/19/2020 01:52:16</publishTime>
    </File>
    <File Include="bin/roslyn/csi.rsp">
      <publishTime>05/19/2020 01:40:40</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Build.Tasks.CodeAnalysis.dll">
      <publishTime>05/19/2020 01:47:18</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.CSharp.dll">
      <publishTime>05/19/2020 01:51:40</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.CSharp.Scripting.dll">
      <publishTime>05/19/2020 01:51:58</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.dll">
      <publishTime>05/19/2020 01:49:16</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.Scripting.dll">
      <publishTime>05/19/2020 01:49:32</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.VisualBasic.dll">
      <publishTime>05/19/2020 01:50:04</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CSharp.Core.targets">
      <publishTime>05/19/2020 01:40:20</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.DiaSymReader.Native.amd64.dll">
      <publishTime>12/05/2017 01:36:44</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.DiaSymReader.Native.x86.dll">
      <publishTime>12/05/2017 01:36:44</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Managed.Core.targets">
      <publishTime>05/19/2020 01:40:20</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.VisualBasic.Core.targets">
      <publishTime>05/19/2020 01:40:20</publishTime>
    </File>
    <File Include="bin/roslyn/System.Buffers.dll">
      <publishTime>07/19/2017 17:01:28</publishTime>
    </File>
    <File Include="bin/roslyn/System.Collections.Immutable.dll">
      <publishTime>05/15/2018 20:29:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Memory.dll">
      <publishTime>04/17/2019 23:24:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Numerics.Vectors.dll">
      <publishTime>07/19/2017 17:01:36</publishTime>
    </File>
    <File Include="bin/roslyn/System.Reflection.Metadata.dll">
      <publishTime>05/15/2018 20:29:44</publishTime>
    </File>
    <File Include="bin/roslyn/System.Runtime.CompilerServices.Unsafe.dll">
      <publishTime>11/15/2019 16:37:58</publishTime>
    </File>
    <File Include="bin/roslyn/System.Text.Encoding.CodePages.dll">
      <publishTime>11/29/2018 23:39:18</publishTime>
    </File>
    <File Include="bin/roslyn/System.Threading.Tasks.Extensions.dll">
      <publishTime>06/18/2019 19:08:48</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.exe">
      <publishTime>05/19/2020 01:50:06</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.exe.config">
      <publishTime>05/19/2020 01:50:06</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.rsp">
      <publishTime>05/19/2020 01:40:26</publishTime>
    </File>
    <File Include="bin/roslyn/VBCSCompiler.exe">
      <publishTime>05/19/2020 01:52:02</publishTime>
    </File>
    <File Include="bin/roslyn/VBCSCompiler.exe.config">
      <publishTime>05/19/2020 01:52:02</publishTime>
    </File>
    <File Include="bin/Senparc.CO2NET.APM.dll">
      <publishTime>07/21/2022 20:15:04</publishTime>
    </File>
    <File Include="bin/Senparc.CO2NET.AspNet.dll">
      <publishTime>07/21/2022 20:14:54</publishTime>
    </File>
    <File Include="bin/Senparc.CO2NET.Cache.Memcached.dll">
      <publishTime>04/03/2022 21:42:30</publishTime>
    </File>
    <File Include="bin/Senparc.CO2NET.dll">
      <publishTime>07/21/2022 20:14:54</publishTime>
    </File>
    <File Include="bin/Senparc.NeuChar.App.dll">
      <publishTime>05/03/2022 23:44:40</publishTime>
    </File>
    <File Include="bin/Senparc.NeuChar.AspNet.dll">
      <publishTime>05/03/2022 23:44:40</publishTime>
    </File>
    <File Include="bin/Senparc.NeuChar.dll">
      <publishTime>05/03/2022 23:44:36</publishTime>
    </File>
    <File Include="bin/Senparc.WebSocket.dll">
      <publishTime>07/21/2022 20:28:02</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.AspNet.dll">
      <publishTime>07/21/2022 20:28:22</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.Cache.Memcached.dll">
      <publishTime>07/21/2022 20:28:16</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.dll">
      <publishTime>08/09/2022 00:10:28</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.dll.config">
      <publishTime>06/01/2021 09:32:54</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.MP.dll">
      <publishTime>09/11/2022 23:41:12</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.MP.MvcExtension.dll">
      <publishTime>07/21/2022 20:28:24</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.MP.Sample.CommonService.dll">
      <publishTime>06/06/2025 09:12:51</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.MP.Sample.CommonService.dll.config">
      <publishTime>09/13/2022 09:29:54</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.MP.Sample.CommonService.pdb">
      <publishTime>06/06/2025 09:12:51</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.Open.dll">
      <publishTime>09/01/2022 12:41:08</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.pdb">
      <publishTime>06/01/2021 09:44:58</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.Work.dll">
      <publishTime>02/28/2022 00:24:04</publishTime>
    </File>
    <File Include="bin/Senparc.Weixin.WxOpen.dll">
      <publishTime>08/09/2022 00:10:54</publishTime>
    </File>
    <File Include="bin/ServiceStack.Text.dll">
      <publishTime>08/30/2022 16:20:08</publishTime>
    </File>
    <File Include="bin/System.Buffers.dll">
      <publishTime>02/19/2020 10:05:18</publishTime>
    </File>
    <File Include="bin/System.ComponentModel.Annotations.dll">
      <publishTime>05/15/2018 13:29:36</publishTime>
    </File>
    <File Include="bin/System.Diagnostics.DiagnosticSource.dll">
      <publishTime>10/23/2021 07:41:28</publishTime>
    </File>
    <File Include="bin/System.IO.Pipelines.dll">
      <publishTime>04/14/2022 01:49:24</publishTime>
    </File>
    <File Include="bin/System.Json.dll">
      <publishTime>04/14/2020 22:39:26</publishTime>
    </File>
    <File Include="bin/System.Memory.dll">
      <publishTime>05/08/2022 11:31:02</publishTime>
    </File>
    <File Include="bin/System.Net.Http.Formatting.dll">
      <publishTime>05/28/2022 07:41:34</publishTime>
    </File>
    <File Include="bin/System.Numerics.Vectors.dll">
      <publishTime>02/13/2025 05:22:04</publishTime>
    </File>
    <File Include="bin/System.Runtime.CompilerServices.Unsafe.dll">
      <publishTime>10/23/2021 07:40:18</publishTime>
    </File>
    <File Include="bin/System.Text.Encodings.Web.dll">
      <publishTime>10/23/2021 07:41:14</publishTime>
    </File>
    <File Include="bin/System.Threading.Tasks.Extensions.dll">
      <publishTime>02/19/2020 10:05:18</publishTime>
    </File>
    <File Include="bin/System.Web.Cors.dll">
      <publishTime>05/28/2022 07:41:44</publishTime>
    </File>
    <File Include="bin/System.Web.Helpers.dll">
      <publishTime>05/28/2022 07:41:54</publishTime>
    </File>
    <File Include="bin/System.Web.Http.Cors.dll">
      <publishTime>05/28/2022 07:42:16</publishTime>
    </File>
    <File Include="bin/System.Web.Http.dll">
      <publishTime>05/28/2022 07:41:46</publishTime>
    </File>
    <File Include="bin/System.Web.Http.Owin.dll">
      <publishTime>05/28/2022 07:42:06</publishTime>
    </File>
    <File Include="bin/System.Web.Http.WebHost.dll">
      <publishTime>05/28/2022 07:41:54</publishTime>
    </File>
    <File Include="bin/System.Web.Mvc.dll">
      <publishTime>05/28/2022 07:42:20</publishTime>
    </File>
    <File Include="bin/System.Web.Optimization.dll">
      <publishTime>01/14/2020 16:18:38</publishTime>
    </File>
    <File Include="bin/System.Web.Razor.dll">
      <publishTime>05/28/2022 07:42:06</publishTime>
    </File>
    <File Include="bin/System.Web.WebPages.Deployment.dll">
      <publishTime>05/28/2022 07:42:46</publishTime>
    </File>
    <File Include="bin/System.Web.WebPages.dll">
      <publishTime>05/28/2022 07:42:16</publishTime>
    </File>
    <File Include="bin/System.Web.WebPages.Razor.dll">
      <publishTime>05/28/2022 07:42:50</publishTime>
    </File>
    <File Include="bin/ThoughtWorks.QRCode.dll">
      <publishTime>01/14/2020 16:18:13</publishTime>
    </File>
    <File Include="bin/WebActivatorEx.dll">
      <publishTime>01/14/2020 16:18:37</publishTime>
    </File>
    <File Include="bin/WebGrease.dll">
      <publishTime>01/23/2014 13:57:34</publishTime>
    </File>
    <File Include="bin/zh-Hans/EntityFramework.resources.dll">
      <publishTime>03/02/2015 17:32:38</publishTime>
    </File>
    <File Include="bin/zh-Hans/EntityFramework.SqlServer.resources.dll">
      <publishTime>03/02/2015 17:32:38</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.AspNet.Identity.Core.resources.dll">
      <publishTime>11/25/2019 19:00:52</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.AspNet.Identity.EntityFramework.resources.dll">
      <publishTime>04/04/2015 01:09:34</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.AspNet.Identity.Owin.resources.dll">
      <publishTime>11/25/2019 19:00:54</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.Host.SystemWeb.resources.dll">
      <publishTime>02/13/2015 21:23:48</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.resources.dll">
      <publishTime>02/13/2015 21:23:48</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.Security.Facebook.resources.dll">
      <publishTime>02/13/2015 21:23:48</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.Security.Google.resources.dll">
      <publishTime>02/13/2015 21:23:50</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.Security.MicrosoftAccount.resources.dll">
      <publishTime>02/13/2015 21:23:50</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.Security.resources.dll">
      <publishTime>02/13/2015 21:23:50</publishTime>
    </File>
    <File Include="bin/zh-Hans/Microsoft.Owin.Security.Twitter.resources.dll">
      <publishTime>02/13/2015 21:23:50</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Net.Http.Formatting.resources.dll">
      <publishTime>05/28/2022 07:34:44</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Helpers.resources.dll">
      <publishTime>05/28/2022 07:34:44</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Http.Owin.resources.dll">
      <publishTime>05/28/2022 07:34:46</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Http.resources.dll">
      <publishTime>05/28/2022 07:34:04</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Http.WebHost.resources.dll">
      <publishTime>05/28/2022 07:34:44</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Mvc.resources.dll">
      <publishTime>05/28/2022 07:34:44</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Optimization.resources.dll">
      <publishTime>02/11/2014 23:28:40</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.Razor.resources.dll">
      <publishTime>05/28/2022 07:35:16</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.WebPages.Deployment.resources.dll">
      <publishTime>05/28/2022 07:34:24</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.WebPages.Razor.resources.dll">
      <publishTime>05/28/2022 07:34:04</publishTime>
    </File>
    <File Include="bin/zh-Hans/System.Web.WebPages.resources.dll">
      <publishTime>05/28/2022 07:34:44</publishTime>
    </File>
    <File Include="Content/bootstrap-theme.css">
      <publishTime>09/13/2022 13:15:02</publishTime>
    </File>
    <File Include="Content/bootstrap-theme.min.css">
      <publishTime>09/13/2022 13:15:02</publishTime>
    </File>
    <File Include="Content/bootstrap.css">
      <publishTime>09/13/2022 13:15:02</publishTime>
    </File>
    <File Include="Content/bootstrap.min.css">
      <publishTime>09/13/2022 13:15:02</publishTime>
    </File>
    <File Include="Content/Site.css">
      <publishTime>01/14/2020 16:18:11</publishTime>
    </File>
    <File Include="crossdomain.xml">
      <publishTime>07/26/2021 17:37:05</publishTime>
    </File>
    <File Include="favicon.ico">
      <publishTime>01/14/2020 16:19:43</publishTime>
    </File>
    <File Include="fonts/glyphicons-halflings-regular.eot">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="fonts/glyphicons-halflings-regular.svg">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="fonts/glyphicons-halflings-regular.ttf">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="fonts/glyphicons-halflings-regular.woff">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Global.asax">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="packages.config">
      <publishTime>09/13/2022 13:15:03</publishTime>
    </File>
    <File Include="Scripts/bootstrap.js">
      <publishTime>09/13/2022 13:15:02</publishTime>
    </File>
    <File Include="Scripts/bootstrap.min.js">
      <publishTime>09/13/2022 13:15:02</publishTime>
    </File>
    <File Include="Scripts/jquery-1.10.2.js">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Scripts/jquery-1.10.2.min.js">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Scripts/jquery-1.10.2.min.map">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Scripts/jquery-3.6.0.js">
      <publishTime>09/09/2022 13:02:24</publishTime>
    </File>
    <File Include="Scripts/jquery-3.6.0.min.js">
      <publishTime>09/09/2022 13:02:24</publishTime>
    </File>
    <File Include="Scripts/jquery-3.6.0.min.map">
      <publishTime>09/09/2022 13:02:23</publishTime>
    </File>
    <File Include="Scripts/jquery-3.6.0.slim.js">
      <publishTime>09/09/2022 13:02:23</publishTime>
    </File>
    <File Include="Scripts/jquery-3.6.0.slim.min.js">
      <publishTime>09/09/2022 13:02:23</publishTime>
    </File>
    <File Include="Scripts/jquery-3.6.0.slim.min.map">
      <publishTime>09/09/2022 13:02:23</publishTime>
    </File>
    <File Include="Scripts/jquery.validate.js">
      <publishTime>09/09/2022 12:20:04</publishTime>
    </File>
    <File Include="Scripts/jquery.validate.min.js">
      <publishTime>09/09/2022 12:20:04</publishTime>
    </File>
    <File Include="Scripts/jquery.validate.unobtrusive.js">
      <publishTime>09/09/2022 15:01:49</publishTime>
    </File>
    <File Include="Scripts/jquery.validate.unobtrusive.min.js">
      <publishTime>09/09/2022 15:01:49</publishTime>
    </File>
    <File Include="Scripts/modernizr-2.6.2.js">
      <publishTime>01/14/2020 16:18:10</publishTime>
    </File>
    <File Include="Scripts/modernizr-2.8.3.js">
      <publishTime>09/09/2022 16:57:50</publishTime>
    </File>
    <File Include="Scripts/respond.js">
      <publishTime>09/09/2022 11:29:18</publishTime>
    </File>
    <File Include="Scripts/respond.matchmedia.addListener.js">
      <publishTime>09/09/2022 11:29:18</publishTime>
    </File>
    <File Include="Scripts/respond.matchmedia.addListener.min.js">
      <publishTime>09/09/2022 11:29:18</publishTime>
    </File>
    <File Include="Scripts/respond.min.js">
      <publishTime>09/09/2022 11:29:18</publishTime>
    </File>
    <File Include="Scripts/_references.js">
      <publishTime>09/09/2022 16:57:51</publishTime>
    </File>
    <File Include="Views/Ckfinder/Upload.cshtml">
      <publishTime>01/14/2020 16:18:11</publishTime>
    </File>
    <File Include="Views/Home/Index.cshtml">
      <publishTime>07/26/2021 11:11:33</publishTime>
    </File>
    <File Include="Views/Shared/Error.cshtml">
      <publishTime>01/14/2020 16:18:11</publishTime>
    </File>
    <File Include="Views/Shared/_Layout.cshtml">
      <publishTime>09/09/2022 17:56:47</publishTime>
    </File>
    <File Include="Views/Web.config">
      <publishTime>06/02/2021 16:27:19</publishTime>
    </File>
    <File Include="Views/_ViewStart.cshtml">
      <publishTime>01/14/2020 16:18:11</publishTime>
    </File>
    <File Include="Web.config">
      <publishTime>04/07/2025 10:18:19</publishTime>
    </File>
    <File Include="WebAppSettings.config">
      <publishTime>06/05/2025 22:17:27</publishTime>
    </File>
    <File Include="WebConnectionStrings.config">
      <publishTime>08/21/2024 20:14:25</publishTime>
    </File>
  </ItemGroup>
</Project>