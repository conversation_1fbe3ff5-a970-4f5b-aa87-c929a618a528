﻿ InstrumentLogicId = entity.InstrumentLogicId,
 InstrumentModelId = entity.InstrumentModelId,
 StudySectionId = entity.StudySectionId,
 StudySectionName = entity.StudySectionName,


 InstrumentLogicId = model.InstrumentLogicId,
 InstrumentModelId = model.InstrumentModelId,
 StudySectionId = model.StudySectionId,
 StudySectionName = model.StudySectionName,


 temp.InstrumentLogicId = model.InstrumentLogicId,
 temp.InstrumentModelId = model.InstrumentModelId,
 temp.StudySectionId = model.StudySectionId,
 temp.StudySectionName = model.StudySectionName,

 AdaptationId = item.AdaptationId,
 InstrumentLogicId = item.InstrumentLogicId,
 InstrumentModelId = item.InstrumentModelId,
 StudySectionId = item.StudySectionId,
 StudySectionName = item.StudySectionName,

public class AdaptationSectionInputModel
{
 [Display(Name = "Id")] 
    public int AdaptationId {get; set; }
    
 [Display(Name = "仪器逻辑库Id")] 
    public int InstrumentLogicId {get; set; }
    
 [Display(Name = "规格型号表Id（没有为0）")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "学段Id")] 
    public int StudySectionId {get; set; }
    
 [Display(Name = "学段名称")] 
    public string StudySectionName {get; set; }
    
 }
 
 public class AdaptationSectionViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int AdaptationId {get; set; }
    
    /// <summary>
    /// 仪器逻辑库Id
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
    /// <summary>
    /// 规格型号表Id（没有为0）
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 学段Id
    /// </summary>
    public int StudySectionId {get; set; }
    
    /// <summary>
    /// 学段名称
    /// </summary>
    public string StudySectionName {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入仪器逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentModelId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentModelId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号表Id（没有为0）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StudySectionId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StudySectionId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学段Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StudySectionName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StudySectionName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学段名称" } })                    
                </div>
           </div>
  




 { field: 'InstrumentLogicId', title: '仪器逻辑库Id', sortable: true },
                 
 { field: 'InstrumentModelId', title: '规格型号表Id（没有为0）', sortable: true },
                 
 { field: 'StudySectionId', title: '学段Id', sortable: true },
                 
 { field: 'StudySectionName', title: '学段名称', sortable: true },
                 
o.InstrumentLogicId,                 
o.InstrumentModelId,                 
o.StudySectionId,                 
o.StudySectionName,                 
        
        $('#InstrumentLogicId').val(d.data.rows.InstrumentLogicId);          
        $('#InstrumentModelId').val(d.data.rows.InstrumentModelId);          
        $('#StudySectionId').val(d.data.rows.StudySectionId);          
        $('#StudySectionName').val(d.data.rows.StudySectionName);          

 $('#th_InstrumentLogicId').html(' 仪器逻辑库Id');               
 $('#th_InstrumentModelId').html(' 规格型号表Id（没有为0）');               
 $('#th_StudySectionId').html(' 学段Id');               
 $('#th_StudySectionName').html(' 学段名称');               
 
 $('#tr_InstrumentLogicId').hide();               
 $('#tr_InstrumentModelId').hide();               
 $('#tr_StudySectionId').hide();               
 $('#tr_StudySectionName').hide();               

 , "InstrumentLogicId" : instrumentLogicId
 , "InstrumentModelId" : instrumentModelId
 , "StudySectionId" : studySectionId
 , "StudySectionName" : studySectionName

 var instrumentLogicId = $('#o_InstrumentLogicId').val();
 var instrumentModelId = $('#o_InstrumentModelId').val();
 var studySectionId = $('#o_StudySectionId').val();
 var studySectionName = $('#o_StudySectionName').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '仪器逻辑库Id' : '产品名称', d.data.rows.InstrumentLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号表Id（没有为0）' : '产品名称', d.data.rows.InstrumentModelId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学段Id' : '产品名称', d.data.rows.StudySectionId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学段名称' : '产品名称', d.data.rows.StudySectionName);



