﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Web.Http.Dependencies;
using Ninject;
using Ninject.Syntax;
using Dqy.Instrument.Configurations.Ninject;

namespace Dqy.Instrument.Api.App_Start
{
     public class MvcNinjectResolver : NinjectDependencyScope, IDependencyResolver
    {
        private IKernel kernel;
        public MvcNinjectResolver()
           : this(new StandardKernel())
        {
        }
        public MvcNinjectResolver(IKernel kernel)
            : base(kernel)
        {
            this.kernel = kernel;
            NinjectConfiguration.AddBindings(kernel);
        }       
        public IDependencyScope BeginScope()
        {
            return new NinjectDependencyScope(kernel.BeginBlock());
        }
    }
    public class NinjectDependencyScope : IDependencyScope
    {
        private IResolutionRoot resolver;

        internal NinjectDependencyScope(IResolutionRoot resolver)
        {
            Contract.Assert(resolver != null);

            this.resolver = resolver;
        }

        public void Dispose()
        {
            IDisposable disposable = resolver as IDisposable;
            if (disposable != null)
                disposable.Dispose();

            resolver = null;
        }

        public object GetService(Type serviceType)
        {
            if (resolver == null)
                throw new ObjectDisposedException("this", "This scope has already been disposed");

            return resolver.TryGet(serviceType);
        }

        public IEnumerable<object> GetServices(Type serviceType)
        {
            if (resolver == null)
                throw new ObjectDisposedException("this", "This scope has already been disposed");

            return resolver.GetAll(serviceType);
        }
    }

   

    //public class MvcNinjectResolver : NinjectDependencyScope, IDependencyResolver
    //{
    //    private readonly IKernel _kernel;

    //    public MvcNinjectResolver()
    //        : this(new StandardKernel())
    //    {
    //    }

    //    public MvcNinjectResolver(IKernel ninjectKernel)
    //    {
    //        _kernel = ninjectKernel;
    //        NinjectConfiguration.AddBindings(ninjectKernel);
    //    }

    //    public object GetService(Type serviceType)
    //    {
    //        return _kernel.TryGet(serviceType);
    //    }

    //    public T GetService<T>()
    //    {
    //        return _kernel.TryGet<T>();
    //    }

    //    public IEnumerable<object> GetServices(Type serviceType)
    //    {
    //        return _kernel.GetAll(serviceType);
    //    }

    //    public void Dispose()
    //    {
    //    }
    //}
}