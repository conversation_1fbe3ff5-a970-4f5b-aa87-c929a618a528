﻿/*
 * Copyright (C) Alibaba Cloud Computing
 * All rights reserved.
 * 
 */

namespace Aliyun.OSS.Util
{
    public static class HttpHeaders
    {
        public const string Authorization = "Authorization";

        public const string CacheControl = "Cache-Control";

        public const string ContentDisposition = "Content-Disposition";

        public const string ContentEncoding = "Content-Encoding";

        public const string ContentLength = "Content-Length";
        
        public const string ContentMd5 = "Content-MD5";

        public const string ContentType = "Content-Type";

        public const string Date = "Date";

        public const string Expires = "Expires";

        public const string ETag = "ETag";

        public const string LastModified = "Last-Modified";

        public const string Range = "Range";

        public const string CopySource = "x-oss-copy-source";

        public const string CopySourceRange = "x-oss-copy-source-range";

        public const string Location = "Location";

        public const string ServerSideEncryption = "x-oss-server-side-encryption";

        public const string SecurityToken = "x-oss-security-token";

        public const string NextAppendPosition = "x-oss-next-append-position";

        public const string HashCrc64Ecma = "x-oss-hash-crc64ecma";

        public const string ObjectType = "x-oss-object-type";

        public const string RequestId = "x-oss-request-id";

        public const string ServerElapsedTime = "x-oss-server-time";

        public const string Callback = "x-oss-callback";

        public const string CallbackVar = "x-oss-callback-var";

        public const string BucketRegion = "x-oss-bucket-region";

        public const string QuotaDeltaSize = "x-oss-quota-delta-size";
    }
}
