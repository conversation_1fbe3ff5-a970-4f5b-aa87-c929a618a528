# Windows Server 2016 + nginx代理 SSL/TLS 部署检查清单

## 🚀 部署步骤

### 1. 代码部署
- [ ] 确保所有新增的文件都已部署到服务器
  - [ ] `SslHelper.cs`
  - [ ] `ApplicationStartup.cs`
  - [ ] `NginxSslDiagnostic.cs`
  - [ ] `ApiConnectionTester.cs`
  - [ ] `SslTestController.cs`
  - [ ] `Views/SslTest/Index.cshtml`

- [ ] 确保修改的文件已更新
  - [ ] `WebApiHelper.cs`

### 2. 应用程序配置

#### 在 Global.asax.cs 中添加初始化代码：
```csharp
protected void Application_Start()
{
    // 其他初始化代码...
    
    // 初始化SSL/TLS配置 - 解决Windows Server 2016 SSL连接问题
    Dqy.Instrument.Mall.CommonLib.ApplicationStartup.Initialize();
    
    // 其他初始化代码...
}
```

#### 或者在 web.config 中配置HTTP模块：
```xml
<system.webServer>
  <modules>
    <add name="SslConfigurationModule" type="Dqy.Instrument.Mall.CommonLib.SslConfigurationModule" />
  </modules>
</system.webServer>
```

### 3. Windows Server 2016 系统配置

#### 注册表配置（以管理员身份运行）：
```reg
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client]
"DisabledByDefault"=dword:00000000
"Enabled"=dword:00000001

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Server]
"DisabledByDefault"=dword:00000000
"Enabled"=dword:00000001
```

#### PowerShell 脚本（可选）：
```powershell
# 启用TLS 1.2
New-Item 'HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client' -Force
New-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client' -Name 'DisabledByDefault' -Value 0 -PropertyType DWORD -Force
New-ItemProperty -Path 'HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client' -Name 'Enabled' -Value 1 -PropertyType DWORD -Force

# 重启服务器以使配置生效
Restart-Computer -Force
```

### 4. nginx 配置检查

#### 检查 nginx.conf 或站点配置文件：
```nginx
server {
    listen 443 ssl http2;
    server_name jzzxmall.czedu.cn;
    
    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 代理配置
    location /api/ {
        proxy_pass http://backend_api_servers;
        proxy_ssl_protocols TLSv1.2 TLSv1.3;
        proxy_ssl_ciphers HIGH:!aNULL:!MD5;
        proxy_ssl_verify off;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
    }
}
```

## 🧪 测试步骤

### 1. 基础测试
- [ ] 重启IIS应用程序池
- [ ] 访问 `/SslTest` 页面
- [ ] 点击"重新初始化SSL"按钮
- [ ] 查看SSL配置信息

### 2. API连接测试
- [ ] 使用"快速测试"功能测试 `jzzxmall.czedu.cn`
- [ ] 如果失败，使用"SSL诊断"功能
- [ ] 根据诊断结果调整配置

### 3. 生产环境测试
- [ ] 测试实际的API调用
- [ ] 检查日志文件中的错误信息
- [ ] 验证所有功能正常工作

## 🔍 故障排除

### 如果仍然出现SSL错误：

1. **检查证书**
   ```bash
   # 使用openssl检查证书
   openssl s_client -connect jzzxmall.czedu.cn:443 -servername jzzxmall.czedu.cn
   ```

2. **检查TLS协议支持**
   ```bash
   # 测试TLS 1.2支持
   openssl s_client -connect jzzxmall.czedu.cn:443 -tls1_2
   ```

3. **临时忽略证书验证**（仅用于测试）
   ```csharp
   var result = await WebApiHelper.SendAsync<YourType>(
       url: "https://jzzxmall.czedu.cn/api/api/main/index?domain=jzzxmall.czedu.cn&userid=0",
       data: yourData,
       checkValidationResult: true  // 临时忽略证书验证
   );
   ```

4. **检查防火墙和网络**
   - 确保443端口开放
   - 检查网络连接
   - 验证DNS解析

5. **查看详细日志**
   - 检查IIS日志
   - 查看应用程序日志
   - 检查nginx错误日志

## 📞 支持联系

如果按照以上步骤仍然无法解决问题，请提供以下信息：

1. 错误的完整堆栈跟踪
2. SSL诊断工具的输出结果
3. nginx配置文件
4. Windows Server版本和.NET Framework版本
5. 证书信息（不包含私钥）

## 📝 更新日志

- **2024-01-XX**: 初始版本，针对Windows Server 2016 + nginx代理环境
- **2024-01-XX**: 添加详细的诊断工具和测试页面
- **2024-01-XX**: 增强错误处理和日志记录
