﻿using Dqy.Instrument.Search;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels.SearchViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Dqy.Instrument.Api.CommonLib
{
    public class ProductSearch
    {
        /// <summary>
        /// 获取学科、品牌、供应商信息
        /// </summary>
        /// <param name="brandCount"></param>
        /// <param name="supplierCount"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static ProductSearchStats GetProductSearchStats(int brandCount,int supplierCount, MallProductSearchInputModel args)
        {
            ProductSearchStats searchStats= ElasticSearchHelper.GetProductSearchStats(brandCount, supplierCount, "ProductSearchSimpleYun", args);
            return searchStats;
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static ProductList SearchProduct(MallProductSearchInputModel args)
        {
            ProductList projectList= ElasticSearchHelper.SearchProduct<ProductSearchSimpleYun>("ProductSearchSimpleYun", args);
            return projectList;
        }


        /// <summary>
        /// 获取学科、品牌、供应商信息
        /// </summary>
        /// <param name="brandCount"></param>
        /// <param name="supplierCount"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static ProductSearchStats GetProductSearchStatsByExperi(int brandCount, int supplierCount, MallProductSearchInputModel args)
        {
            ProductSearchStats searchStats = ElasticSearchHelper.GetProductSearchStatsByExperi(brandCount, supplierCount, "ProductSearchSimpleYun", args);
            return searchStats;
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static ProductList SearchProductByExperi(MallProductSearchInputModel args)
        {
            ProductList projectList = ElasticSearchHelper.SearchProductByExperi<ProductSearchSimpleYun>("ProductSearchSimpleYun", args);
            return projectList;
        }
    }
}