﻿using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Controllers
{
    public class ArticlesController : ControllerReception
    {
        /// <summary>
        /// 获取资讯详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult> Detail(long id)
        {
            ViewBag.Title = "资讯详情";
            ViewBag.Tag = "";
            try
            {
                string urlData = "article/getDetail?id=" + id + "";
                var entity = await WebApiHelper.SendAsync<ArticleViewModel>(urlData, null, CommonTypes.CommonJsonSendType.GET);
                if (entity != null)
                {
                    ViewBag.Title = entity.Title;
                    var  model = new ArticleDetailViewModel() {
                        Title=entity.Title,
                        ShortTitle=entity.ShortTitle,
                        HasImage=entity.HasImage,
                        ImageUrl=entity.ImageUrl,
                        Attachment=entity.Attachment,
                        Remark=entity.Remark,
                        RegDate=entity.RegDate
                    };

                    return View(model);
                }
                else
                {
                    ViewBag.Tag = Constant.Request_Lose_Msg;
                }
                return View();
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("请求article/getarticleApi异常" + e.Message);
                ViewBag.Tag = Constant.Request_Ext_Msg;
            }
            return View();
        }

        /// <summary>
        /// 获取资讯列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> List(SearchArticleInputModel args)
        {
            ViewBag.Tag = "";
            ViewBag.Title = "资讯列表";
            if (args == null)
            {
                args = new SearchArticleInputModel();
            }
            args.Limit = 20;

            args.MallId = MallId;
            ViewBag.Args = args;

            try
            {
                //获取列表信息
                string urlData = "article/postarticlelist";
                var r = await WebApiHelper.SendAsync<QueryResult<AboutMallViewModel>>(urlData, args);
                if (r == null)
                {
                    ViewBag.Tag = Constant.Request_Lose_Msg;
                }
                return View(r);
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("获取资讯列表异常。" + e.Message);
                ViewBag.Tag = Constant.Request_Ext_Msg;
                return View();
            }

        }

        public async Task<ActionResult> About(string code, string codeList)
        {
            ViewBag.Tag = "";
            ViewBag.Title = "平台信息";
            if (codeList == null || codeList.Length <= 0)
            {
                codeList = "zcwj_ptjj_gywm_wztk_ptkf";
            }
            var domain = Constant.Current_Local_Domain.ToString();
            string url = Constant.ApiPath + "article/categorydetail?code=" + code + "&codeList=" + codeList + "&domain=" + domain;
            AboutMallViewModel model = new AboutMallViewModel();
            try
            {
                var r = await WebApiHelper.SendAsync<QueryResult<AboutMallViewModel>>(url, null, CommonTypes.CommonJsonSendType.GET); 
                if (r != null && r.flag > 0)
                {
                    if (r.Entity != null)
                    {
                        model = r.Entity;
                        if (r != null)
                        {
                            ViewBag.Title = model.Name;
                            return View(model);
                        } 
                    }
                }
                ViewBag.Tag = Constant.Request_Lose_Msg;
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("获取平台信息异常。" + e.Message);
                ViewBag.Tag = Constant.Request_Ext_Msg;
            }
            return View(model);
        }
    }
}
