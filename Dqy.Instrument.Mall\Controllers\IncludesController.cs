﻿using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Microsoft.AspNet.Identity;
using Microsoft.Owin.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Controllers
{
    public class IncludesController : ControllerReception
    {
    

        public PartialViewResult Head_TopUser()
        {
            ViewBag.UserName = Operater == null ? "" : Operater.UserName;
            return PartialView();
        }

        public PartialViewResult Head_Search()
        {
            //获取地区
            var domain = Constant.Current_Local_Domain;
            string result = WebApiHelper.GetRequest("main/getareanav", "domain=" + domain);
            var list = ComLib.JSON2Object<List<string>>(result);
           
            return PartialView(list);            
        }
        public PartialViewResult Head_Nav()
        {
            return PartialView();
        }

        public PartialViewResult Head_Menu()
        {
           //MallId
            //var domain = Fetch.ServerDomain; 
            var domain= Constant.Current_Local_Domain;
            //string url = Constant.ApiPath + "main/getclassifyonelist?domain=" + domain; 
            //var model = await WebApiHelper.SendAsync<HomeMenuViewModel>(url, null, CommonTypes.CommonJsonSendType.GET);

            string result = WebApiHelper.GetRequest("main/getclassifyonelist", "domain=" + domain);
            HomeMenuViewModel model = ComLib.JSON2Object<HomeMenuViewModel>(result);
            if (model==null)
            {
                model = new HomeMenuViewModel();
            }
            model.ApiPath = Constant.ApiPath ;
            return PartialView(model);
        }

        public PartialViewResult Bottom()
        {
            //string url = WebApiHelper.GetRequest("article/categorydetail", "");
            //AboutMallViewModel model = new AboutMallViewModel();
            //QueryResult<AboutMallViewModel> r = ComLib.JSON2Object<QueryResult<AboutMallViewModel>>(url);  
            //if (r != null && r.flag > 0)
            //{
            //    if (r.Entity != null)
            //    {
            //        return PartialView(r.Entity);
            //    }
            //} 
            //return PartialView(model);

            string icp = ""; //网站备案号
            try
            {
                //获取配置数据
                string url = "configset/getconfigset?moduleCode=1&typeCode=WZBAH&unitType=0&unitId=0";
                var list = WebApiHelper.Send<List<ConfigSetInputModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
                if (list != null && list.Count > 0)
                {
                    ConfigSetInputModel configSetEntity = list.LastOrDefault();

                    icp = configSetEntity.ConfigValue;
                }
                ViewBag.Tag = Constant.Request_Lose_Msg;
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("获取网站备案号信息异常。" + e.Message);
                ViewBag.Tag = Constant.Request_Ext_Msg;
            }
            ViewBag.WebsiteICP = icp;
            return PartialView();
        }

        public PartialViewResult UserLogin()
        {
            return PartialView();
        }


        private IAuthenticationManager AuthenticationManager { get { return HttpContext.GetOwinContext().Authentication; } }
       
        [AllowAnonymous]
        [HttpPost]
        public async Task<JsonResult> LoginAjax(LoginInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (model.Mobile == null || model.Pkey == null || model.Code == null)
            {
                result.flag = 0;
                result.msg = "请输入账号、密码、验证码。";
                return Json(result);
            }
            if (Session["lcode"] == null || !model.Code.ToUpper().Equals(Session["lcode"].ToString()))
            {
                result.flag = 0;
                result.msg = "验证码输入错误，请重新输入。";
                return Json(result);
            }
            if (ModelState.IsValid)
            {
                //model.Domain = Fetch.ServerDomain;
                model.Domain= Constant.Current_Local_Domain;
                string url = Constant.ApiPath + "account/login";
                result = await WebApiHelper.SendAsync(url, model);
                if (result.flag == 1)
                {
                    UserCompanyViewModel uc = ComLib.JSON2Object<UserCompanyViewModel>((string)result.data.rows);
                    if (uc != null)
                    {
                        var _identity = CreateIdentity(uc, DefaultAuthenticationTypes.ApplicationCookie);
                        AuthenticationManager.SignIn(new AuthenticationProperties() { IsPersistent = model.RememberMe }, _identity);

                    }
                }

            }
            else
            {
                result.flag = 0;
                result.msg = "请输入账号、密码、验证码。";
            }

            return Json(result);
        }

        private ClaimsIdentity CreateIdentity(UserCompanyViewModel account, string authenticationType)
        {

            ClaimsIdentity _identity = new ClaimsIdentity(DefaultAuthenticationTypes.ApplicationCookie);
            _identity.AddClaim(new Claim(ClaimTypes.Name, account.LoginName));
            _identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, account.UserId.ToString()));
            _identity.AddClaim(new Claim("http://schemas.microsoft.com/accesscontrolservice/2010/07/claims/identityprovider", "ASP.NET Identity"));

            _identity.AddClaim(new Claim("MallId", account.MallId.ToString()));
            _identity.AddClaim(new Claim("CurrentMallId", account.CurrentMallId.ToString()));
            _identity.AddClaim(new Claim("UserId", account.UserId.ToString()));
            _identity.AddClaim(new Claim("UserType", account.UserType.ToString()));
            _identity.AddClaim(new Claim("Token", account.Key));
            _identity.AddClaim(new Claim("UnitId", account.UnitId.ToString()));
            _identity.AddClaim(new Claim("UnitType", account.UnitType.ToString()));
            _identity.AddClaim(new Claim("UnitName", account.UnitName));
            _identity.AddClaim(new Claim("IsThird", "0"));

            string userName = account.RealName == "" ? account.LoginName : account.RealName;
            _identity.AddClaim(new Claim("UserName", userName));
            //
            _identity.AddClaim(new Claim("RecordUserId", account.RecordUserId.ToString()));
            //防伪标记，必须!
            _identity.AddClaim(new Claim("Dqy_Cookie_Key", "Dqy.Instrument.Mall"));

            return _identity;
        }

    }
}