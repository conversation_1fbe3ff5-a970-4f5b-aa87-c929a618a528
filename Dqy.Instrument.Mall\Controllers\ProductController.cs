﻿using Dqy.Instrument.Mall.CommonLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json;
using Dqy.Instrument.UI.ViewModels;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.Framework.Component;
using System.Threading.Tasks;
using Dqy.Instrument.Mall.Areas.Member.Controllers;
using Dqy.Instrument.UI.ViewModels.SearchViewModels;
using System.Text.RegularExpressions;
using Dqy.Instrument.CommonTypes;

namespace Dqy.Instrument.Mall.Controllers
{
    public class ProductController : ControllerReception
    {
        /// <summary>
        /// 产品详情
        /// </summary>
        /// <param name="id">上架id</param>
        /// <returns></returns>
        public async Task<ActionResult> Detail(long id = 68)
        {
            string url = "product/getproductdetail?productShelfId=" + id;
            var result = await WebApiHelper.SendAsync<ProductDetailViewModel>(url, null, CommonTypes.CommonJsonSendType.GET);
            return View(result);
        }

        /// <summary>
        /// 获取产品评价信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetEvaluation(SearchProductEvaluateInputModel args)
        {
            string url = "product/getevaluatelist";
            var result = await WebApiHelper.SendAsync<QueryEvaluationResult<OrderEvaluationViewModel>>(url, args);
            result.paging = PageStringUtilities.GetPage(args.CurrPage, args.Limit, result.TotalCount, Url.Action("GetEvaluation"), "");
            result.Data.ToList().ForEach(f =>
            {
                //处理日期格式
                f.RegTimeStr = f.RegTime.ToString("yyyy年MM月dd日");
                //处理匿名评价
                if (f.IsAnonymous)
                {
                    f.LoginName = f.LoginName.Substring(0, 1) + "***" + f.LoginName.Substring(f.LoginName.Length - 1, 1);
                }
            });
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  加入购物车
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> AddShoppingCart(ShoppingCartInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (Operater == null)
            {
                result.flag = -1;
                result.msg = "登录超时，请重新登录。";
            }
            else
            {
                Regex reg = new Regex(@"^[1-9]\d*$");
                if (!reg.IsMatch(model.Num.ToString()))
                {
                    result.flag = 0;
                    result.msg = "数量输入不合法，请重新输入。";
                }
                else
                {
                    model.JoinTime = DateTime.Now;
                    model.Sum = model.Num * model.Price;
                    model.BaseCurrentMallId = Operater.CurrentMallId; //
                    model.BaseUnitId = Operater.UnitId;//
                    model.BaseUserId = Operater.UserId;//
                    model.Token = Operater.Token;
                    string url = "product/addshoppingcart";
                    result = await WebApiHelper.SendAsync(url, model);
                    string log = string.Format("加入购物车 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, url, ComLib.Object2JSON(result));
                    Log.ShoppingCartLog(log, result.flag);
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 产品查询列表
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <param name="indexType">索引类型</param>
        /// <param name="args">MallProductSearchInputModel 参数</param>
        /// <returns></returns>
        public async Task<ActionResult> List(MallProductSearchInputModel args)
        {
            if (args == null)
            {
                args = new MallProductSearchInputModel();
            }
            args.CurrPage = 1;
            args.Limit = 20;

            args.BrandCount = 10;
            args.SupplierCount = 10;
            args.MallId = MallId;
            ViewBag.Args = args;

            //获取学科与供应商信息
            string url = "productsearch/postsearchstats";
            var result = await WebApiHelper.SendAsync<ProductSearchStats>(url, args);
            ViewBag.ProductSearchStats = result;

            //判断是否为首页分类点击进来
            if (args.IsClassfy)
            {
                result.subjects = args.Subjects.Split(',');
            }
            else
            //判断是否为学段点击进入
            if (args.IsSection)
            {
                string urlCourse = "course/getcoursebysection?sectionName=" + args.Sections + "";
                var resultCourse = await WebApiHelper.SendAsync<List<string>>(urlCourse, null, CommonTypes.CommonJsonSendType.GET);
                result.subjects = resultCourse;
            }
            else
            {
                var allSubject = ComLib.GetAppSetting<string>("Data.Subjects");
                string[] arrSubject = allSubject.Split(',');
                List<string> listSubject = new List<string>();
                var subs = result.subjects;
                if (subs != null && subs.ToList().Count > 0)
                {
                    foreach (var a in arrSubject)
                    {
                        if (subs.Contains<string>(a))
                        {
                            listSubject.Add(a);
                        }
                    }
                    result.subjects = listSubject;
                }
            }
            //获取列表信息
            string urlData = "productsearch/postsearchproduct";
            var resultData = await WebApiHelper.SendAsync<ProductList>(urlData, args);
            resultData.paging = PageStringUtilities.GetPage(args.CurrPage, args.Limit, resultData.hits, Url.Action("SearchProductList"), "");
            ViewBag.Paging = resultData.paging;
            if (Operater != null && Operater.UnitType == UnitType.School.ToInt())
            {
                url = $"school/getschoolinformation?token={Operater.Token}&UnitId={Operater.UnitId}";
                var rUnit = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
                if (rUnit.flag == 1)
                {
                    SchoolInfoInputModel sModel = ComLib.JSON2Object<SchoolInfoInputModel>(rUnit.obj.ToString());
                    ViewBag.SchoolInfo = new
                    {
                        sCounty = sModel.StrCounty,
                        SCitys = sModel.StrCity,
                        SProvinces = sModel.StrProvince
                    };
                }
            }

            return View(resultData);
        }

        /// <summary>
        /// 获取产品列表信息
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <param name="indexType">索引类型</param>
        /// <param name="args">MallProductSearchInputModel 参数</param>
        /// <returns></returns>
        public async Task<JsonResult> SearchProductList(MallProductSearchInputModel args)
        {
            if (args == null)
            {
                args = new MallProductSearchInputModel();
            }
            args.Limit = 20;
            args.MallId = MallId;
            //获取列表信息
            string urlData = "productsearch/postsearchproduct";
            var resultData = await WebApiHelper.SendAsync<ProductList>(urlData, args);
            string where = "";
            resultData.paging = PageStringUtilities.GetPage(args.CurrPage, args.Limit, resultData.hits, Url.Action("SearchProductList"), where);
            return Json(resultData, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 获取品牌供应商信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<JsonResult> SearchBrandSupplierList(MallProductSearchInputModel args)
        {
            args.BrandCount = 10;
            args.SupplierCount = 10;
            args.MallId = MallId;
            //获取学科与供应商信息
            string url = "productsearch/postsearchstats";
            var result = await WebApiHelper.SendAsync<ProductSearchStats>(url, args);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 获取购物车中的产品数量
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetShoppingCartCount()
        {
            if(Operater == null)
            {
                return 0;
            }
            string url = "shoppingcart/getcartcount?token=" + Operater.Token + "";
            var result = await WebApiHelper.SendAsync<int>(url, null, CommonTypes.CommonJsonSendType.GET);
            return result;
        }

        /// <summary>
        /// 产品查询列表
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <param name="indexType">索引类型</param>
        /// <param name="args">MallProductSearchInputModel 参数</param>
        /// <returns></returns>
        public async Task<ActionResult> ListExperi(MallProductSearchInputModel args)
        {
            if (args == null)
            {
                args = new MallProductSearchInputModel();
            }
            args.CurrPage = 1;
            args.Limit = 20;

            args.BrandCount = 10;
            args.SupplierCount = 10;
            args.MallId = MallId;
            ViewBag.Args = args;

            //获取学科与供应商信息
            string url = "productsearch/postsearchstatsbyexperi";
            var result = await WebApiHelper.SendAsync<ProductSearchStats>(url, args);
            ViewBag.ProductSearchStats = result;

            //判断是否为首页分类点击进来
            if (args.IsClassfy)
            {
                result.subjects = args.Subjects.Split(',');
            }
            else
            //判断是否为学段点击进入
            if (args.IsSection)
            {
                string urlCourse = "course/getcoursebysection?sectionName=" + args.Sections + "";
                var resultCourse = await WebApiHelper.SendAsync<List<string>>(urlCourse, null, CommonTypes.CommonJsonSendType.GET);
                result.subjects = resultCourse;
            }
            else
            {
                var allSubject = ComLib.GetAppSetting<string>("Data.Subjects");
                string[] arrSubject = allSubject.Split(',');
                List<string> listSubject = new List<string>();
                var subs = result.subjects;
                if (subs != null && subs.ToList().Count > 0)
                {
                    foreach (var a in arrSubject)
                    {
                        if (subs.Contains<string>(a))
                        {
                            listSubject.Add(a);
                        }
                    }
                    result.subjects = listSubject;
                }
            }
            //获取列表信息
            string urlData = "productsearch/postsearchproductbyexperi";
            var resultData = await WebApiHelper.SendAsync<ProductList>(urlData, args);
            resultData.paging = PageStringUtilities.GetPage(args.CurrPage, args.Limit, resultData.hits, Url.Action("SearchProductList"), "");
            ViewBag.Paging = resultData.paging;

            return View(resultData);
        }

        /// <summary>
        /// 获取产品列表信息（根据实验查询）
        /// 说明：点击学段、品牌、供应商的时候调用
        /// </summary>
        /// <param name="args">MallProductSearchInputModel 参数</param>
        /// <returns></returns>
        public async Task<JsonResult> SearchProductListExperi(MallProductSearchInputModel args)
        {
            if (args == null)
            {
                args = new MallProductSearchInputModel();
            }
            args.Limit = 20;
            args.MallId = MallId; 
            string urlData = "productsearch/postsearchproductbyexperi";
            var resultData = await WebApiHelper.SendAsync<ProductList>(urlData, args);
            resultData.paging = PageStringUtilities.GetPage(args.CurrPage, args.Limit, resultData.hits, Url.Action("SearchProductList"), "");

            return Json(resultData, JsonRequestBehavior.AllowGet);
        }
    }

}