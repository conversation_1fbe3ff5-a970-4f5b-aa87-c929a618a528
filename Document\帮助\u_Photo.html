﻿ ObjId = entity.ObjId,
 ObjType = entity.ObjType,
 Name = entity.Name,
 UnitPhotoGalleryId = entity.UnitPhotoGalleryId,
 ImagePath = entity.ImagePath,
 Statuz = entity.Statuz,
 ClassType = entity.ClassType,
 IsShow = entity.IsShow,
 Memo = entity.Memo,
 RegTime = entity.RegTime,
 CreatorId = entity.CreatorId,
 SourceType = entity.SourceType,


 ObjId = model.ObjId,
 ObjType = model.ObjType,
 Name = model.Name,
 UnitPhotoGalleryId = model.UnitPhotoGalleryId,
 ImagePath = model.ImagePath,
 Statuz = model.Statuz,
 ClassType = model.ClassType,
 IsShow = model.IsShow,
 Memo = model.Memo,
 RegTime = model.RegTime,
 CreatorId = model.CreatorId,
 SourceType = model.SourceType,


 temp.ObjId = model.ObjId,
 temp.ObjType = model.ObjType,
 temp.Name = model.Name,
 temp.UnitPhotoGalleryId = model.UnitPhotoGalleryId,
 temp.ImagePath = model.ImagePath,
 temp.Statuz = model.Statuz,
 temp.ClassType = model.ClassType,
 temp.IsShow = model.IsShow,
 temp.Memo = model.Memo,
 temp.RegTime = model.RegTime,
 temp.CreatorId = model.CreatorId,
 temp.SourceType = model.SourceType,

 PhotoId = item.PhotoId,
 ObjId = item.ObjId,
 ObjType = item.ObjType,
 Name = item.Name,
 UnitPhotoGalleryId = item.UnitPhotoGalleryId,
 ImagePath = item.ImagePath,
 Statuz = item.Statuz,
 ClassType = item.ClassType,
 IsShow = item.IsShow,
 Memo = item.Memo,
 RegTime = item.RegTime,
 CreatorId = item.CreatorId,
 SourceType = item.SourceType,

public class PhotoInputModel
{
 [Display(Name = "ID")] 
    public long PhotoId {get; set; }
    
 [Display(Name = "所属对象Id")] 
    public long ObjId {get; set; }
    
 [Display(Name = "对象类型（0：UnitId；1：UserId）")] 
    public int ObjType {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "相册Id")] 
    public int UnitPhotoGalleryId {get; set; }
    
 [Display(Name = "图片路径")] 
    public string ImagePath {get; set; }
    
 [Display(Name = "状态")] 
    public int Statuz {get; set; }
    
 [Display(Name = "类型（1：图片；2：视频）")] 
    public int ClassType {get; set; }
    
 [Display(Name = "是否显示隐藏(1显示,0隐藏)")] 
    public int IsShow {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "创建人Id")] 
    public long CreatorId {get; set; }
    
 [Display(Name = "来源（1：用户上传；2：课程活动发布管理）")] 
    public int SourceType {get; set; }
    
 }
 
 public class PhotoViewModel
 {
    /// <summary>
    /// ID
    /// </summary>
    public long PhotoId {get; set; }
    
    /// <summary>
    /// 所属对象Id
    /// </summary>
    public long ObjId {get; set; }
    
    /// <summary>
    /// 对象类型（0：UnitId；1：UserId）
    /// </summary>
    public int ObjType {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 相册Id
    /// </summary>
    public int UnitPhotoGalleryId {get; set; }
    
    /// <summary>
    /// 图片路径
    /// </summary>
    public string ImagePath {get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 类型（1：图片；2：视频）
    /// </summary>
    public int ClassType {get; set; }
    
    /// <summary>
    /// 是否显示隐藏(1显示,0隐藏)
    /// </summary>
    public int IsShow {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId {get; set; }
    
    /// <summary>
    /// 来源（1：用户上传；2：课程活动发布管理）
    /// </summary>
    public int SourceType {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入所属对象Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入对象类型（0：UnitId；1：UserId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitPhotoGalleryId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitPhotoGalleryId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入相册Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImagePath, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImagePath, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ClassType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ClassType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类型（1：图片；2：视频）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsShow, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsShow, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否显示隐藏(1显示,0隐藏)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreatorId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreatorId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SourceType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SourceType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入来源（1：用户上传；2：课程活动发布管理）" } })                    
                </div>
           </div>
  




 { field: 'ObjId', title: '所属对象Id', sortable: true },
                 
 { field: 'ObjType', title: '对象类型（0：UnitId；1：UserId）', sortable: true },
                 
 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'UnitPhotoGalleryId', title: '相册Id', sortable: true },
                 
 { field: 'ImagePath', title: '图片路径', sortable: true },
                 
 { field: 'Statuz', title: '状态', sortable: true },
                 
 { field: 'ClassType', title: '类型（1：图片；2：视频）', sortable: true },
                 
 { field: 'IsShow', title: '是否显示隐藏(1显示,0隐藏)', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
 { field: 'CreatorId', title: '创建人Id', sortable: true },
                 
 { field: 'SourceType', title: '来源（1：用户上传；2：课程活动发布管理）', sortable: true },
                 
o.ObjId,                 
o.ObjType,                 
o.Name,                 
o.UnitPhotoGalleryId,                 
o.ImagePath,                 
o.Statuz,                 
o.ClassType,                 
o.IsShow,                 
o.Memo,                 
o.RegTime,                 
o.CreatorId,                 
o.SourceType,                 
        
        $('#ObjId').val(d.data.rows.ObjId);          
        $('#ObjType').val(d.data.rows.ObjType);          
        $('#Name').val(d.data.rows.Name);          
        $('#UnitPhotoGalleryId').val(d.data.rows.UnitPhotoGalleryId);          
        $('#ImagePath').val(d.data.rows.ImagePath);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#ClassType').val(d.data.rows.ClassType);          
        $('#IsShow').val(d.data.rows.IsShow);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#CreatorId').val(d.data.rows.CreatorId);          
        $('#SourceType').val(d.data.rows.SourceType);          

 $('#th_ObjId').html(' 所属对象Id');               
 $('#th_ObjType').html(' 对象类型（0：UnitId；1：UserId）');               
 $('#th_Name').html(' 名称');               
 $('#th_UnitPhotoGalleryId').html(' 相册Id');               
 $('#th_ImagePath').html(' 图片路径');               
 $('#th_Statuz').html(' 状态');               
 $('#th_ClassType').html(' 类型（1：图片；2：视频）');               
 $('#th_IsShow').html(' 是否显示隐藏(1显示,0隐藏)');               
 $('#th_Memo').html(' 备注');               
 $('#th_RegTime').html(' 创建时间');               
 $('#th_CreatorId').html(' 创建人Id');               
 $('#th_SourceType').html(' 来源（1：用户上传；2：课程活动发布管理）');               
 
 $('#tr_ObjId').hide();               
 $('#tr_ObjType').hide();               
 $('#tr_Name').hide();               
 $('#tr_UnitPhotoGalleryId').hide();               
 $('#tr_ImagePath').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_ClassType').hide();               
 $('#tr_IsShow').hide();               
 $('#tr_Memo').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_CreatorId').hide();               
 $('#tr_SourceType').hide();               

 , "ObjId" : objId
 , "ObjType" : objType
 , "Name" : name
 , "UnitPhotoGalleryId" : unitPhotoGalleryId
 , "ImagePath" : imagePath
 , "Statuz" : statuz
 , "ClassType" : classType
 , "IsShow" : isShow
 , "Memo" : memo
 , "RegTime" : regTime
 , "CreatorId" : creatorId
 , "SourceType" : sourceType

 var objId = $('#o_ObjId').val();
 var objType = $('#o_ObjType').val();
 var name = $('#o_Name').val();
 var unitPhotoGalleryId = $('#o_UnitPhotoGalleryId').val();
 var imagePath = $('#o_ImagePath').val();
 var statuz = $('#o_Statuz').val();
 var classType = $('#o_ClassType').val();
 var isShow = $('#o_IsShow').val();
 var memo = $('#o_Memo').val();
 var regTime = $('#o_RegTime').val();
 var creatorId = $('#o_CreatorId').val();
 var sourceType = $('#o_SourceType').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '所属对象Id' : '产品名称', d.data.rows.ObjId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '对象类型（0：UnitId；1：UserId）' : '产品名称', d.data.rows.ObjType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '相册Id' : '产品名称', d.data.rows.UnitPhotoGalleryId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片路径' : '产品名称', d.data.rows.ImagePath);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类型（1：图片；2：视频）' : '产品名称', d.data.rows.ClassType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否显示隐藏(1显示,0隐藏)' : '产品名称', d.data.rows.IsShow);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建人Id' : '产品名称', d.data.rows.CreatorId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '来源（1：用户上传；2：课程活动发布管理）' : '产品名称', d.data.rows.SourceType);



