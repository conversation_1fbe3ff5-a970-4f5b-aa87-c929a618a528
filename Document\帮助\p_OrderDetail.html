﻿ OrderId = entity.OrderId,
 ProductDeclareId = entity.ProductDeclareId,
 ProductId = entity.ProductId,
 ProductShelfId = entity.ProductShelfId,
 SnapshotId = entity.SnapshotId,
 CourseId = entity.CourseId,
 Code = entity.Code,
 ProductName = entity.ProductName,
 Num = entity.Num,
 Price = entity.Price,
 Brand = entity.Brand,
 InstrumentModelId = entity.InstrumentModelId,
 ModelDescription = entity.ModelDescription,
 Sum = entity.Sum,
 UnitName = entity.UnitName,
 UnitId = entity.UnitId,
 SourceFundId = entity.SourceFundId,
 IsEvaluate = entity.IsEvaluate,


 OrderId = model.OrderId,
 ProductDeclareId = model.ProductDeclareId,
 ProductId = model.ProductId,
 ProductShelfId = model.ProductShelfId,
 SnapshotId = model.SnapshotId,
 CourseId = model.CourseId,
 Code = model.Code,
 ProductName = model.ProductName,
 Num = model.Num,
 Price = model.Price,
 Brand = model.Brand,
 InstrumentModelId = model.InstrumentModelId,
 ModelDescription = model.ModelDescription,
 Sum = model.Sum,
 UnitName = model.UnitName,
 UnitId = model.UnitId,
 SourceFundId = model.SourceFundId,
 IsEvaluate = model.IsEvaluate,


 temp.OrderId = model.OrderId,
 temp.ProductDeclareId = model.ProductDeclareId,
 temp.ProductId = model.ProductId,
 temp.ProductShelfId = model.ProductShelfId,
 temp.SnapshotId = model.SnapshotId,
 temp.CourseId = model.CourseId,
 temp.Code = model.Code,
 temp.ProductName = model.ProductName,
 temp.Num = model.Num,
 temp.Price = model.Price,
 temp.Brand = model.Brand,
 temp.InstrumentModelId = model.InstrumentModelId,
 temp.ModelDescription = model.ModelDescription,
 temp.Sum = model.Sum,
 temp.UnitName = model.UnitName,
 temp.UnitId = model.UnitId,
 temp.SourceFundId = model.SourceFundId,
 temp.IsEvaluate = model.IsEvaluate,

 OrderDetailId = item.OrderDetailId,
 OrderId = item.OrderId,
 ProductDeclareId = item.ProductDeclareId,
 ProductId = item.ProductId,
 ProductShelfId = item.ProductShelfId,
 SnapshotId = item.SnapshotId,
 CourseId = item.CourseId,
 Code = item.Code,
 ProductName = item.ProductName,
 Num = item.Num,
 Price = item.Price,
 Brand = item.Brand,
 InstrumentModelId = item.InstrumentModelId,
 ModelDescription = item.ModelDescription,
 Sum = item.Sum,
 UnitName = item.UnitName,
 UnitId = item.UnitId,
 SourceFundId = item.SourceFundId,
 IsEvaluate = item.IsEvaluate,

public class OrderDetailInputModel
{
 [Display(Name = "Id")] 
    public long OrderDetailId {get; set; }
    
 [Display(Name = "订单Id")] 
    public long OrderId {get; set; }
    
 [Display(Name = "产品申报Id")] 
    public long ProductDeclareId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "产品上架Id")] 
    public long ProductShelfId {get; set; }
    
 [Display(Name = "产品快照Id")] 
    public long SnapshotId {get; set; }
    
 [Display(Name = "学科Id")] 
    public int CourseId {get; set; }
    
 [Display(Name = "产品代码")] 
    public string Code {get; set; }
    
 [Display(Name = "产品名称")] 
    public string ProductName {get; set; }
    
 [Display(Name = "数量")] 
    public decimal Num {get; set; }
    
 [Display(Name = "单价")] 
    public decimal Price {get; set; }
    
 [Display(Name = "品牌")] 
    public string Brand {get; set; }
    
 [Display(Name = "规格Id")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "规格型号描述")] 
    public string ModelDescription {get; set; }
    
 [Display(Name = "金额")] 
    public decimal Sum {get; set; }
    
 [Display(Name = "单位")] 
    public string UnitName {get; set; }
    
 [Display(Name = "供应商Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "资金来源Id")] 
    public int SourceFundId {get; set; }
    
 [Display(Name = "是否已评价（0：否  1：是）")] 
    public bool IsEvaluate {get; set; }
    
 }
 
 public class OrderDetailViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long OrderDetailId {get; set; }
    
    /// <summary>
    /// 订单Id
    /// </summary>
    public long OrderId {get; set; }
    
    /// <summary>
    /// 产品申报Id
    /// </summary>
    public long ProductDeclareId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 产品上架Id
    /// </summary>
    public long ProductShelfId {get; set; }
    
    /// <summary>
    /// 产品快照Id
    /// </summary>
    public long SnapshotId {get; set; }
    
    /// <summary>
    /// 学科Id
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 产品代码
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName {get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal Num {get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public decimal Price {get; set; }
    
    /// <summary>
    /// 品牌
    /// </summary>
    public string Brand {get; set; }
    
    /// <summary>
    /// 规格Id
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 规格型号描述
    /// </summary>
    public string ModelDescription {get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Sum {get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName {get; set; }
    
    /// <summary>
    /// 供应商Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 资金来源Id
    /// </summary>
    public int SourceFundId {get; set; }
    
    /// <summary>
    /// 是否已评价（0：否  1：是）
    /// </summary>
    public bool IsEvaluate {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductDeclareId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductDeclareId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品申报Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductShelfId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductShelfId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品上架Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SnapshotId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SnapshotId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品快照Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Num, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Num, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Price, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Price, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Brand, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Brand, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入品牌" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentModelId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentModelId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ModelDescription, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ModelDescription, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号描述" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入金额" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SourceFundId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SourceFundId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资金来源Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsEvaluate, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsEvaluate, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否已评价（0：否  1：是）" } })                    
                </div>
           </div>
  




 { field: 'OrderId', title: '订单Id', sortable: true },
                 
 { field: 'ProductDeclareId', title: '产品申报Id', sortable: true },
                 
 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'ProductShelfId', title: '产品上架Id', sortable: true },
                 
 { field: 'SnapshotId', title: '产品快照Id', sortable: true },
                 
 { field: 'CourseId', title: '学科Id', sortable: true },
                 
 { field: 'Code', title: '产品代码', sortable: true },
                 
 { field: 'ProductName', title: '产品名称', sortable: true },
                 
 { field: 'Num', title: '数量', sortable: true },
                 
 { field: 'Price', title: '单价', sortable: true },
                 
 { field: 'Brand', title: '品牌', sortable: true },
                 
 { field: 'InstrumentModelId', title: '规格Id', sortable: true },
                 
 { field: 'ModelDescription', title: '规格型号描述', sortable: true },
                 
 { field: 'Sum', title: '金额', sortable: true },
                 
 { field: 'UnitName', title: '单位', sortable: true },
                 
 { field: 'UnitId', title: '供应商Id', sortable: true },
                 
 { field: 'SourceFundId', title: '资金来源Id', sortable: true },
                 
 { field: 'IsEvaluate', title: '是否已评价（0：否  1：是）', sortable: true },
                 
o.OrderId,                 
o.ProductDeclareId,                 
o.ProductId,                 
o.ProductShelfId,                 
o.SnapshotId,                 
o.CourseId,                 
o.Code,                 
o.ProductName,                 
o.Num,                 
o.Price,                 
o.Brand,                 
o.InstrumentModelId,                 
o.ModelDescription,                 
o.Sum,                 
o.UnitName,                 
o.UnitId,                 
o.SourceFundId,                 
o.IsEvaluate,                 
        
        $('#OrderId').val(d.data.rows.OrderId);          
        $('#ProductDeclareId').val(d.data.rows.ProductDeclareId);          
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#ProductShelfId').val(d.data.rows.ProductShelfId);          
        $('#SnapshotId').val(d.data.rows.SnapshotId);          
        $('#CourseId').val(d.data.rows.CourseId);          
        $('#Code').val(d.data.rows.Code);          
        $('#ProductName').val(d.data.rows.ProductName);          
        $('#Num').val(d.data.rows.Num);          
        $('#Price').val(d.data.rows.Price);          
        $('#Brand').val(d.data.rows.Brand);          
        $('#InstrumentModelId').val(d.data.rows.InstrumentModelId);          
        $('#ModelDescription').val(d.data.rows.ModelDescription);          
        $('#Sum').val(d.data.rows.Sum);          
        $('#UnitName').val(d.data.rows.UnitName);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#SourceFundId').val(d.data.rows.SourceFundId);          
        $('#IsEvaluate').val(d.data.rows.IsEvaluate);          

 $('#th_OrderId').html(' 订单Id');               
 $('#th_ProductDeclareId').html(' 产品申报Id');               
 $('#th_ProductId').html(' 产品Id');               
 $('#th_ProductShelfId').html(' 产品上架Id');               
 $('#th_SnapshotId').html(' 产品快照Id');               
 $('#th_CourseId').html(' 学科Id');               
 $('#th_Code').html(' 产品代码');               
 $('#th_ProductName').html(' 产品名称');               
 $('#th_Num').html(' 数量');               
 $('#th_Price').html(' 单价');               
 $('#th_Brand').html(' 品牌');               
 $('#th_InstrumentModelId').html(' 规格Id');               
 $('#th_ModelDescription').html(' 规格型号描述');               
 $('#th_Sum').html(' 金额');               
 $('#th_UnitName').html(' 单位');               
 $('#th_UnitId').html(' 供应商Id');               
 $('#th_SourceFundId').html(' 资金来源Id');               
 $('#th_IsEvaluate').html(' 是否已评价（0：否  1：是）');               
 
 $('#tr_OrderId').hide();               
 $('#tr_ProductDeclareId').hide();               
 $('#tr_ProductId').hide();               
 $('#tr_ProductShelfId').hide();               
 $('#tr_SnapshotId').hide();               
 $('#tr_CourseId').hide();               
 $('#tr_Code').hide();               
 $('#tr_ProductName').hide();               
 $('#tr_Num').hide();               
 $('#tr_Price').hide();               
 $('#tr_Brand').hide();               
 $('#tr_InstrumentModelId').hide();               
 $('#tr_ModelDescription').hide();               
 $('#tr_Sum').hide();               
 $('#tr_UnitName').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_SourceFundId').hide();               
 $('#tr_IsEvaluate').hide();               

 , "OrderId" : orderId
 , "ProductDeclareId" : productDeclareId
 , "ProductId" : productId
 , "ProductShelfId" : productShelfId
 , "SnapshotId" : snapshotId
 , "CourseId" : courseId
 , "Code" : code
 , "ProductName" : productName
 , "Num" : num
 , "Price" : price
 , "Brand" : brand
 , "InstrumentModelId" : instrumentModelId
 , "ModelDescription" : modelDescription
 , "Sum" : sum
 , "UnitName" : unitName
 , "UnitId" : unitId
 , "SourceFundId" : sourceFundId
 , "IsEvaluate" : isEvaluate

 var orderId = $('#o_OrderId').val();
 var productDeclareId = $('#o_ProductDeclareId').val();
 var productId = $('#o_ProductId').val();
 var productShelfId = $('#o_ProductShelfId').val();
 var snapshotId = $('#o_SnapshotId').val();
 var courseId = $('#o_CourseId').val();
 var code = $('#o_Code').val();
 var productName = $('#o_ProductName').val();
 var num = $('#o_Num').val();
 var price = $('#o_Price').val();
 var brand = $('#o_Brand').val();
 var instrumentModelId = $('#o_InstrumentModelId').val();
 var modelDescription = $('#o_ModelDescription').val();
 var sum = $('#o_Sum').val();
 var unitName = $('#o_UnitName').val();
 var unitId = $('#o_UnitId').val();
 var sourceFundId = $('#o_SourceFundId').val();
 var isEvaluate = $('#o_IsEvaluate').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单Id' : '产品名称', d.data.rows.OrderId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品申报Id' : '产品名称', d.data.rows.ProductDeclareId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品上架Id' : '产品名称', d.data.rows.ProductShelfId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品快照Id' : '产品名称', d.data.rows.SnapshotId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科Id' : '产品名称', d.data.rows.CourseId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品代码' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品名称' : '产品名称', d.data.rows.ProductName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '数量' : '产品名称', d.data.rows.Num);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单价' : '产品名称', d.data.rows.Price);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '品牌' : '产品名称', d.data.rows.Brand);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格Id' : '产品名称', d.data.rows.InstrumentModelId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号描述' : '产品名称', d.data.rows.ModelDescription);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '金额' : '产品名称', d.data.rows.Sum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位' : '产品名称', d.data.rows.UnitName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资金来源Id' : '产品名称', d.data.rows.SourceFundId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否已评价（0：否  1：是）' : '产品名称', d.data.rows.IsEvaluate);



