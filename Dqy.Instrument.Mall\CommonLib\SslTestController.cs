using System;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.CommonLib
{
    /// <summary>
    /// SSL测试控制器
    /// 用于在Web环境中测试SSL连接问题
    /// </summary>
    public class SslTestController : Controller
    {
        /// <summary>
        /// SSL测试页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <param name="apiUrl">API地址</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> TestConnection(string apiUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(apiUrl))
                {
                    apiUrl = "https://jzzxmall.czedu.cn/api/api/main/index?domain=jzzxmall.czedu.cn&userid=0";
                }

                var result = await ApiConnectionTester.TestApiConnection(apiUrl);
                
                return Json(new { 
                    success = true, 
                    message = "测试完成", 
                    result = result 
                });
            }
            catch (Exception ex)
            {
                return Json(new { 
                    success = false, 
                    message = ex.Message,
                    result = ApiConnectionTester.GetSolutionRecommendations(ex.Message)
                });
            }
        }

        /// <summary>
        /// 快速测试指定域名的API
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> QuickTest(string domain = "jzzxmall.czedu.cn", int userId = 0)
        {
            try
            {
                var result = await ApiConnectionTester.QuickTestApi(domain, userId);
                
                return Json(new { 
                    success = true, 
                    message = "快速测试完成", 
                    result = result 
                });
            }
            catch (Exception ex)
            {
                return Json(new { 
                    success = false, 
                    message = ex.Message,
                    result = ApiConnectionTester.GetSolutionRecommendations(ex.Message)
                });
            }
        }

        /// <summary>
        /// 诊断SSL连接
        /// </summary>
        /// <param name="url">要诊断的URL</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> DiagnoseSSL(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    url = "https://jzzxmall.czedu.cn";
                }

                var result = await NginxSslDiagnostic.DiagnoseUrl(url);
                
                return Json(new { 
                    success = true, 
                    message = "SSL诊断完成", 
                    result = result 
                });
            }
            catch (Exception ex)
            {
                return Json(new { 
                    success = false, 
                    message = ex.Message,
                    result = $"SSL诊断失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取当前SSL配置信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult GetSslInfo()
        {
            try
            {
                var info = new
                {
                    IsInitialized = ApplicationStartup.IsInitialized(),
                    SupportedProtocols = SslHelper.GetSupportedProtocols(),
                    Recommendations = NginxSslDiagnostic.GetNginxProxyRecommendations(),
                    CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                return Json(info, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { 
                    success = false, 
                    message = ex.Message 
                }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 重新初始化SSL配置
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult ReinitializeSSL()
        {
            try
            {
                ApplicationStartup.Reset();
                ApplicationStartup.Initialize();
                
                return Json(new { 
                    success = true, 
                    message = "SSL配置重新初始化完成",
                    protocols = SslHelper.GetSupportedProtocols()
                });
            }
            catch (Exception ex)
            {
                return Json(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }
    }
}
