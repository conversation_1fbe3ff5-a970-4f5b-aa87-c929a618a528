﻿ MallId = entity.MallId,
 SchoolNum = entity.SchoolNum,
 SupplierNum = entity.SupplierNum,
 SellGoodsNum = entity.SellGoodsNum,
 Amount = entity.Amount,


 MallId = model.MallId,
 SchoolNum = model.SchoolNum,
 SupplierNum = model.SupplierNum,
 SellGoodsNum = model.SellGoodsNum,
 Amount = model.Amount,


 temp.MallId = model.MallId,
 temp.SchoolNum = model.SchoolNum,
 temp.SupplierNum = model.SupplierNum,
 temp.SellGoodsNum = model.SellGoodsNum,
 temp.Amount = model.Amount,

 MallStatisticsId = item.MallStatisticsId,
 MallId = item.MallId,
 SchoolNum = item.SchoolNum,
 SupplierNum = item.SupplierNum,
 SellGoodsNum = item.SellGoodsNum,
 Amount = item.Amount,

public class MallStatisticInputModel
{
 [Display(Name = "id")] 
    public int MallStatisticsId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "学校数量")] 
    public int SchoolNum {get; set; }
    
 [Display(Name = "供应商数量")] 
    public int SupplierNum {get; set; }
    
 [Display(Name = "销售产品数量")] 
    public int SellGoodsNum {get; set; }
    
 [Display(Name = "已销售金额")] 
    public decimal Amount {get; set; }
    
 }
 
 public class MallStatisticViewModel
 {
    /// <summary>
    /// id
    /// </summary>
    public int MallStatisticsId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 学校数量
    /// </summary>
    public int SchoolNum {get; set; }
    
    /// <summary>
    /// 供应商数量
    /// </summary>
    public int SupplierNum {get; set; }
    
    /// <summary>
    /// 销售产品数量
    /// </summary>
    public int SellGoodsNum {get; set; }
    
    /// <summary>
    /// 已销售金额
    /// </summary>
    public decimal Amount {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SellGoodsNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SellGoodsNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入销售产品数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Amount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Amount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入已销售金额" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'SchoolNum', title: '学校数量', sortable: true },
                 
 { field: 'SupplierNum', title: '供应商数量', sortable: true },
                 
 { field: 'SellGoodsNum', title: '销售产品数量', sortable: true },
                 
 { field: 'Amount', title: '已销售金额', sortable: true },
                 
o.MallId,                 
o.SchoolNum,                 
o.SupplierNum,                 
o.SellGoodsNum,                 
o.Amount,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#SchoolNum').val(d.data.rows.SchoolNum);          
        $('#SupplierNum').val(d.data.rows.SupplierNum);          
        $('#SellGoodsNum').val(d.data.rows.SellGoodsNum);          
        $('#Amount').val(d.data.rows.Amount);          

 $('#th_MallId').html(' 商城Id');               
 $('#th_SchoolNum').html(' 学校数量');               
 $('#th_SupplierNum').html(' 供应商数量');               
 $('#th_SellGoodsNum').html(' 销售产品数量');               
 $('#th_Amount').html(' 已销售金额');               
 
 $('#tr_MallId').hide();               
 $('#tr_SchoolNum').hide();               
 $('#tr_SupplierNum').hide();               
 $('#tr_SellGoodsNum').hide();               
 $('#tr_Amount').hide();               

 , "MallId" : mallId
 , "SchoolNum" : schoolNum
 , "SupplierNum" : supplierNum
 , "SellGoodsNum" : sellGoodsNum
 , "Amount" : amount

 var mallId = $('#o_MallId').val();
 var schoolNum = $('#o_SchoolNum').val();
 var supplierNum = $('#o_SupplierNum').val();
 var sellGoodsNum = $('#o_SellGoodsNum').val();
 var amount = $('#o_Amount').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校数量' : '产品名称', d.data.rows.SchoolNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商数量' : '产品名称', d.data.rows.SupplierNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '销售产品数量' : '产品名称', d.data.rows.SellGoodsNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '已销售金额' : '产品名称', d.data.rows.Amount);



