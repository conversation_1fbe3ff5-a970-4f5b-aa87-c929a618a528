﻿ PID = entity.PID,
 Name = entity.Name,
 Depth = entity.Depth,
 Path = entity.Path,
 Sort = entity.Sort,


 PID = model.PID,
 Name = model.Name,
 Depth = model.Depth,
 Path = model.Path,
 Sort = model.Sort,


 temp.PID = model.PID,
 temp.Name = model.Name,
 temp.Depth = model.Depth,
 temp.Path = model.Path,
 temp.Sort = model.Sort,

 ID = item.ID,
 PID = item.PID,
 Name = item.Name,
 Depth = item.Depth,
 Path = item.Path,
 Sort = item.Sort,

public class ArticleSubjectInputModel
{
 [Display(Name = "分类编号")] 
    public int ID {get; set; }
    
 [Display(Name = "分类父ID")] 
    public int PID {get; set; }
    
 [Display(Name = "分类名称")] 
    public string Name {get; set; }
    
 [Display(Name = "分类深度")] 
    public int Depth {get; set; }
    
 [Display(Name = "路径(排序)")] 
    public string Path {get; set; }
    
 [Display(Name = "用户定义排序")] 
    public int Sort {get; set; }
    
 }
 
 public class ArticleSubjectViewModel
 {
    /// <summary>
    /// 分类编号
    /// </summary>
    public int ID {get; set; }
    
    /// <summary>
    /// 分类父ID
    /// </summary>
    public int PID {get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 分类深度
    /// </summary>
    public int Depth {get; set; }
    
    /// <summary>
    /// 路径(排序)
    /// </summary>
    public string Path {get; set; }
    
    /// <summary>
    /// 用户定义排序
    /// </summary>
    public int Sort {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.PID, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PID, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入分类父ID" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入分类名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入分类深度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Path, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Path, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入路径(排序)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sort, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sort, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户定义排序" } })                    
                </div>
           </div>
  




 { field: 'PID', title: '分类父ID', sortable: true },
                 
 { field: 'Name', title: '分类名称', sortable: true },
                 
 { field: 'Depth', title: '分类深度', sortable: true },
                 
 { field: 'Path', title: '路径(排序)', sortable: true },
                 
 { field: 'Sort', title: '用户定义排序', sortable: true },
                 
o.PID,                 
o.Name,                 
o.Depth,                 
o.Path,                 
o.Sort,                 
        
        $('#PID').val(d.data.rows.PID);          
        $('#Name').val(d.data.rows.Name);          
        $('#Depth').val(d.data.rows.Depth);          
        $('#Path').val(d.data.rows.Path);          
        $('#Sort').val(d.data.rows.Sort);          

 $('#th_PID').html(' 分类父ID');               
 $('#th_Name').html(' 分类名称');               
 $('#th_Depth').html(' 分类深度');               
 $('#th_Path').html(' 路径(排序)');               
 $('#th_Sort').html(' 用户定义排序');               
 
 $('#tr_PID').hide();               
 $('#tr_Name').hide();               
 $('#tr_Depth').hide();               
 $('#tr_Path').hide();               
 $('#tr_Sort').hide();               

 , "PID" : pID
 , "Name" : name
 , "Depth" : depth
 , "Path" : path
 , "Sort" : sort

 var pID = $('#o_PID').val();
 var name = $('#o_Name').val();
 var depth = $('#o_Depth').val();
 var path = $('#o_Path').val();
 var sort = $('#o_Sort').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '分类父ID' : '产品名称', d.data.rows.PID);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '分类名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '分类深度' : '产品名称', d.data.rows.Depth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '路径(排序)' : '产品名称', d.data.rows.Path);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户定义排序' : '产品名称', d.data.rows.Sort);



