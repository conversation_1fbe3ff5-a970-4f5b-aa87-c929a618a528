﻿ ReportUnitId = entity.ReportUnitId,
 FundTypes = entity.FundTypes,
 FundTypeName = entity.FundTypeName,
 UnitId = entity.UnitId,
 IsQuota = entity.IsQuota,
 Year = entity.Year,
 Quota = entity.Quota,
 UserId = entity.UserId,
 RegTime = entity.RegTime,
 IsNeed = entity.IsNeed,
 ValidDate = entity.ValidDate,


 ReportUnitId = model.ReportUnitId,
 FundTypes = model.FundTypes,
 FundTypeName = model.FundTypeName,
 UnitId = model.UnitId,
 IsQuota = model.IsQuota,
 Year = model.Year,
 Quota = model.Quota,
 UserId = model.UserId,
 RegTime = model.RegTime,
 IsNeed = model.IsNeed,
 ValidDate = model.ValidDate,


 temp.ReportUnitId = model.ReportUnitId,
 temp.FundTypes = model.FundTypes,
 temp.FundTypeName = model.FundTypeName,
 temp.UnitId = model.UnitId,
 temp.IsQuota = model.IsQuota,
 temp.Year = model.Year,
 temp.Quota = model.Quota,
 temp.UserId = model.UserId,
 temp.RegTime = model.RegTime,
 temp.IsNeed = model.IsNeed,
 temp.ValidDate = model.ValidDate,

 CapitalConfigureId = item.CapitalConfigureId,
 ReportUnitId = item.ReportUnitId,
 FundTypes = item.FundTypes,
 FundTypeName = item.FundTypeName,
 UnitId = item.UnitId,
 IsQuota = item.IsQuota,
 Year = item.Year,
 Quota = item.Quota,
 UserId = item.UserId,
 RegTime = item.RegTime,
 IsNeed = item.IsNeed,
 ValidDate = item.ValidDate,

public class CapitalConfigureInputModel
{
 [Display(Name = "资金配置Id")] 
    public int CapitalConfigureId {get; set; }
    
 [Display(Name = "学校Id(UnitId)")] 
    public int ReportUnitId {get; set; }
    
 [Display(Name = "上报资金类型(存储资金类型表示以（，）分隔)")] 
    public string FundTypes {get; set; }
    
 [Display(Name = "上报资金类型名称以（，）分隔")] 
    public string FundTypeName {get; set; }
    
 [Display(Name = "记录单位")] 
    public int UnitId {get; set; }
    
 [Display(Name = "是否限额")] 
    public bool IsQuota {get; set; }
    
 [Display(Name = "年度")] 
    public string Year {get; set; }
    
 [Display(Name = "采购限额值")] 
    public decimal Quota {get; set; }
    
 [Display(Name = "记录人")] 
    public long UserId {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "是否需要上报")] 
    public bool IsNeed {get; set; }
    
 [Display(Name = "有效期")] 
    public DateTime ValidDate {get; set; }
    
 }
 
 public class CapitalConfigureViewModel
 {
    /// <summary>
    /// 资金配置Id
    /// </summary>
    public int CapitalConfigureId {get; set; }
    
    /// <summary>
    /// 学校Id(UnitId)
    /// </summary>
    public int ReportUnitId {get; set; }
    
    /// <summary>
    /// 上报资金类型(存储资金类型表示以（，）分隔)
    /// </summary>
    public string FundTypes {get; set; }
    
    /// <summary>
    /// 上报资金类型名称以（，）分隔
    /// </summary>
    public string FundTypeName {get; set; }
    
    /// <summary>
    /// 记录单位
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 是否限额
    /// </summary>
    public bool IsQuota {get; set; }
    
    /// <summary>
    /// 年度
    /// </summary>
    public string Year {get; set; }
    
    /// <summary>
    /// 采购限额值
    /// </summary>
    public decimal? Quota {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 是否需要上报
    /// </summary>
    public bool IsNeed {get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ValidDate {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ReportUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ReportUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校Id(UnitId)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FundTypes, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FundTypes, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上报资金类型(存储资金类型表示以（，）分隔)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FundTypeName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FundTypeName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上报资金类型名称以（，）分隔" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsQuota, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsQuota, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否限额" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Year, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Year, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入年度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Quota, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Quota, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入采购限额值" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsNeed, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsNeed, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否需要上报" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ValidDate, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ValidDate, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入有效期" } })                    
                </div>
           </div>
  




 { field: 'ReportUnitId', title: '学校Id(UnitId)', sortable: true },
                 
 { field: 'FundTypes', title: '上报资金类型(存储资金类型表示以（，）分隔)', sortable: true },
                 
 { field: 'FundTypeName', title: '上报资金类型名称以（，）分隔', sortable: true },
                 
 { field: 'UnitId', title: '记录单位', sortable: true },
                 
 { field: 'IsQuota', title: '是否限额', sortable: true },
                 
 { field: 'Year', title: '年度', sortable: true },
                 
 { field: 'Quota', title: '采购限额值', sortable: true },
                 
 { field: 'UserId', title: '记录人', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
 { field: 'IsNeed', title: '是否需要上报', sortable: true },
                 
 { field: 'ValidDate', title: '有效期', sortable: true },
                 
o.ReportUnitId,                 
o.FundTypes,                 
o.FundTypeName,                 
o.UnitId,                 
o.IsQuota,                 
o.Year,                 
o.Quota,                 
o.UserId,                 
o.RegTime,                 
o.IsNeed,                 
o.ValidDate,                 
        
        $('#ReportUnitId').val(d.data.rows.ReportUnitId);          
        $('#FundTypes').val(d.data.rows.FundTypes);          
        $('#FundTypeName').val(d.data.rows.FundTypeName);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#IsQuota').val(d.data.rows.IsQuota);          
        $('#Year').val(d.data.rows.Year);          
        $('#Quota').val(d.data.rows.Quota);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#IsNeed').val(d.data.rows.IsNeed);          
        $('#ValidDate').val(d.data.rows.ValidDate);          

 $('#th_ReportUnitId').html(' 学校Id(UnitId)');               
 $('#th_FundTypes').html(' 上报资金类型(存储资金类型表示以（，）分隔)');               
 $('#th_FundTypeName').html(' 上报资金类型名称以（，）分隔');               
 $('#th_UnitId').html(' 记录单位');               
 $('#th_IsQuota').html(' 是否限额');               
 $('#th_Year').html(' 年度');               
 $('#th_Quota').html(' 采购限额值');               
 $('#th_UserId').html(' 记录人');               
 $('#th_RegTime').html(' 记录时间');               
 $('#th_IsNeed').html(' 是否需要上报');               
 $('#th_ValidDate').html(' 有效期');               
 
 $('#tr_ReportUnitId').hide();               
 $('#tr_FundTypes').hide();               
 $('#tr_FundTypeName').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_IsQuota').hide();               
 $('#tr_Year').hide();               
 $('#tr_Quota').hide();               
 $('#tr_UserId').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_IsNeed').hide();               
 $('#tr_ValidDate').hide();               

 , "ReportUnitId" : reportUnitId
 , "FundTypes" : fundTypes
 , "FundTypeName" : fundTypeName
 , "UnitId" : unitId
 , "IsQuota" : isQuota
 , "Year" : year
 , "Quota" : quota
 , "UserId" : userId
 , "RegTime" : regTime
 , "IsNeed" : isNeed
 , "ValidDate" : validDate

 var reportUnitId = $('#o_ReportUnitId').val();
 var fundTypes = $('#o_FundTypes').val();
 var fundTypeName = $('#o_FundTypeName').val();
 var unitId = $('#o_UnitId').val();
 var isQuota = $('#o_IsQuota').val();
 var year = $('#o_Year').val();
 var quota = $('#o_Quota').val();
 var userId = $('#o_UserId').val();
 var regTime = $('#o_RegTime').val();
 var isNeed = $('#o_IsNeed').val();
 var validDate = $('#o_ValidDate').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校Id(UnitId)' : '产品名称', d.data.rows.ReportUnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上报资金类型(存储资金类型表示以（，）分隔)' : '产品名称', d.data.rows.FundTypes);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上报资金类型名称以（，）分隔' : '产品名称', d.data.rows.FundTypeName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录单位' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否限额' : '产品名称', d.data.rows.IsQuota);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '年度' : '产品名称', d.data.rows.Year);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '采购限额值' : '产品名称', d.data.rows.Quota);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否需要上报' : '产品名称', d.data.rows.IsNeed);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '有效期' : '产品名称', d.data.rows.ValidDate);



