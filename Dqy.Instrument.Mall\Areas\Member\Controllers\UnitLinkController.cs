﻿using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    /// <summary>
    /// 单位联系人信息
    /// </summary>
    public class UnitLinkController : ControllerMember
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> List(SearchArgumentsInputModel args)
        {

            ReturnResult r = new ReturnResult();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }
            args.Limit = 20;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            string url = "unitlink/postunitlink";
            var result = await WebApiHelper.SendAsync<QueryResult<UnitLinkInputModel>>(url, args);
            if (result.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //r.flag = 0;
                //r.msg = "登录超时，请重新登录。";
                //return View("RegSucc", r);
                return this.ApiTimeOut();
            }
            if (result.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                r.flag = 0;
                r.msg = "您无权操作此功能。";
                return View("RegSucc", r);
            }
            ViewBag.Args = args;
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            return View(result);
        }

        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="txtAccount"></param>
        /// <param name="txtOldPswd"></param>
        /// <param name="txtNewPswd"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveUnitLink(UnitLinkInputModel u)
        {
            ReturnResult r = new ReturnResult();
            u.Token = Operater.Token;
            u.UnitId = Operater.UnitId;
            if (u.UnitLinkId == 0)
            {
                string url = Constant.ApiPath + "unitlink/postaddunitlink";
                r = await WebApiHelper.SendAsync(url, u);
            }
            else
            {
                string url = Constant.ApiPath + "unitlink/postmodifyunitlink";
                r = await WebApiHelper.SendAsync(url, u);
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 获取修改单位用户信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetUnitLink(int unitLinkId)
        {
            string url = Constant.ApiPath + $"unitlink/getunitlink?token={Operater.Token}&unitId={Operater.UnitId}&unitLinkId={unitLinkId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            r.obj = ComLib.Object2JSON(r.obj);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 删除单位联系人信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetDeleteUnitLink(int unitLinkId)
        {
            ReturnResult r = new ReturnResult();
            string url = Constant.ApiPath + "unitlink/getdel?token=" + Operater.Token + "&Id=" + unitLinkId + "&unitId=" + Operater.UnitId + "";
            r = await WebApiHelper.SendAsync(url,null,CommonTypes.CommonJsonSendType.GET);
            return Json(r, JsonRequestBehavior.AllowGet);
        }
    }
}