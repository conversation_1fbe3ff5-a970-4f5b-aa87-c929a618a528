﻿using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Dqy.Instrument.Api.Areas.HelpPage.Controllers
{
    [RoutePrefix("api/area")]
    public class AreaController : ApiController
    {
        IBAreaApplicationService _bareaApplicationService;
        IBEduAreaApplicationService _beduAreaApplicationService;

        public AreaController(IBAreaApplicationService bareaApplicationService,IBEduAreaApplicationService beduAreaApplicationService)
        {
            _bareaApplicationService = bareaApplicationService;
            _beduAreaApplicationService = beduAreaApplicationService;
        }

        /// <summary>
        /// 获取行政区域信息
        /// </summary>
        /// <param name="pid"></param>
        /// <param name="depth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getarealist")]
        public List<AreaViewModel> GetAreaList(int pid, int depth)
        {
            return _bareaApplicationService.GetList(pid, depth);
        }

        /// <summary>
        /// 获取教育区域信息
        /// </summary>
        /// <param name="pid"></param>
        /// <param name="depth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("geteduarealist")]
        public List<EduAreaViewModel> GetEduAreaList(int pid, int depth)
        {
            return _beduAreaApplicationService.GetList(pid, depth);
        }

        /// <summary>
        /// 获取教育区域信息
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("geteduarealist")]
        public async Task<ReturnResult> GetEduAreaList(int pid)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                r = _beduAreaApplicationService.GetEduAreaList(pid);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 获取行政区域信息
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getarealist")]
        public async Task<ReturnResult> GetAreaList(int pid)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                r = _bareaApplicationService.GetAreaList(pid);
                return r;
            });
            return result;
        }
    }
}
