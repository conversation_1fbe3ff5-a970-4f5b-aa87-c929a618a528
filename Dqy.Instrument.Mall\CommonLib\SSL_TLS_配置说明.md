# SSL/TLS 配置解决方案

## 问题描述
在Windows Server 2016服务器上调用`WebApiHelper.SendAsync<T>`方法访问通过nginx代理的HTTPS API时，出现"请求被中止: 未能创建 SSL/TLS 安全通道"的错误。

特别是访问类似 `https://jzzxmall.czedu.cn/api/api/main/index?domain=jzzxmall.czedu.cn&userid=0` 这样的API地址。

## 解决方案

### 1. 新增文件
- `SslHelper.cs` - SSL/TLS配置帮助类（增强版，支持nginx代理）
- `ApplicationStartup.cs` - 应用程序启动配置类
- `NginxSslDiagnostic.cs` - nginx代理环境SSL诊断工具
- `ApiConnectionTester.cs` - API连接测试工具
- `SslTestController.cs` - Web测试控制器
- `Views/SslTest/Index.cshtml` - 测试页面
- `SSL_TLS_配置说明.md` - 本说明文档

### 2. 修改文件
- `WebApiHelper.cs` - 更新SSL/TLS配置逻辑，增强错误处理

## 使用方法

### 方法一：在Application_Start中初始化（推荐）

在`Global.asax.cs`的`Application_Start`方法中添加：

```csharp
protected void Application_Start()
{
    // 其他初始化代码...
    
    // 初始化SSL/TLS配置
    Dqy.Instrument.Mall.CommonLib.ApplicationStartup.Initialize();
    
    // 其他初始化代码...
}
```

### 方法二：在web.config中配置HTTP模块

在`web.config`的`<system.web>`节点中添加：

```xml
<httpModules>
  <add name="SslConfigurationModule" type="Dqy.Instrument.Mall.CommonLib.SslConfigurationModule" />
</httpModules>
```

或在`<system.webServer>`节点中添加（IIS 7+）：

```xml
<modules>
  <add name="SslConfigurationModule" type="Dqy.Instrument.Mall.CommonLib.SslConfigurationModule" />
</modules>
```

### 方法三：手动调用

如果需要在特定时机配置SSL/TLS，可以手动调用：

```csharp
// 配置全局SSL/TLS协议
Dqy.Instrument.Mall.CommonLib.SslHelper.ConfigureSslProtocols();

// 或者为特定URL配置（包含证书验证选项）
Dqy.Instrument.Mall.CommonLib.SslHelper.ConfigureForUrl("https://api.example.com", ignoreCertificateErrors: false);
```

## 配置详情

### 支持的协议版本
- TLS 1.2 (推荐)
- TLS 1.1
- TLS 1.0
- TLS 1.3 (如果.NET Framework版本支持)

### 安全设置
- 禁用证书吊销检查以提高性能
- 可选择性忽略证书验证错误（仅用于开发/测试环境）
- 设置合理的连接限制

## 注意事项

### 生产环境
1. **不要**在生产环境中设置`ignoreCertificateErrors = true`
2. 确保服务器证书有效且未过期
3. 建议使用TLS 1.2或更高版本

### 开发/测试环境
1. 可以设置`ignoreCertificateErrors = true`来绕过证书验证
2. 注意这会降低安全性，仅用于开发调试

### Windows Server 2016特殊配置
1. 确保已安装最新的Windows更新
2. 可能需要在注册表中启用TLS 1.2支持：
   ```
   [HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client]
   "DisabledByDefault"=dword:00000000
   "Enabled"=dword:00000001
   ```

## 针对nginx代理的特殊配置

### nginx服务器配置建议
```nginx
server {
    listen 443 ssl http2;
    server_name jzzxmall.czedu.cn;

    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 代理配置
    location /api/ {
        proxy_pass http://backend_servers;
        proxy_ssl_protocols TLSv1.2 TLSv1.3;
        proxy_ssl_ciphers HIGH:!aNULL:!MD5;
        proxy_ssl_verify off;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 快速测试方法

### 1. 使用Web测试工具
访问 `/SslTest` 页面进行可视化测试：
- 快速测试指定域名的API
- 诊断SSL连接问题
- 查看详细的错误信息和解决建议

### 2. 使用代码测试
```csharp
// 快速测试API连接
var result = await ApiConnectionTester.QuickTestApi("jzzxmall.czedu.cn", 0);
Console.WriteLine(result);

// 诊断SSL连接
var diagnosis = await NginxSslDiagnostic.DiagnoseUrl("https://jzzxmall.czedu.cn");
Console.WriteLine(diagnosis);
```

## 故障排除

### 常见错误及解决方法

1. **"请求被中止: 未能创建 SSL/TLS 安全通道"**
   - 确保已调用`ApplicationStartup.Initialize()`
   - 检查目标服务器是否支持TLS 1.2
   - 验证服务器证书是否有效
   - 检查nginx的SSL配置
   - 临时测试时可设置`checkValidationResult: true`

2. **"基础连接已经关闭: 发送时发生错误"**
   - 检查网络连接
   - 确认防火墙设置
   - 验证nginx代理配置
   - 检查后端服务器状态

3. **证书验证失败**
   - 检查系统时间是否正确
   - 验证证书链是否完整
   - 检查证书是否过期
   - 确认nginx证书配置正确

4. **连接超时**
   - 增加请求超时时间
   - 检查nginx超时配置
   - 验证网络延迟

### 调试信息
可以通过以下方法获取当前SSL配置信息：

```csharp
// 获取SSL协议信息
string protocolInfo = SslHelper.GetSupportedProtocols();

// 检查初始化状态
bool isInitialized = ApplicationStartup.IsInitialized();

// 获取nginx代理建议
string recommendations = NginxSslDiagnostic.GetNginxProxyRecommendations();
```

## 版本兼容性
- .NET Framework 4.0+
- Windows Server 2012+
- IIS 7.0+

## 更新日志
- 2024-01-XX: 初始版本，解决Windows Server 2016 SSL/TLS连接问题
