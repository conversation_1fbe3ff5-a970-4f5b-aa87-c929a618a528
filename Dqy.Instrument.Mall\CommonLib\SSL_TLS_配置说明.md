# SSL/TLS 配置解决方案

## 问题描述
在Windows Server 2016服务器上调用`WebApiHelper.SendAsync<T>`方法时，出现"请求被中止: 未能创建 SSL/TLS 安全通道"的错误。

## 解决方案

### 1. 新增文件
- `SslHelper.cs` - SSL/TLS配置帮助类
- `ApplicationStartup.cs` - 应用程序启动配置类
- `SSL_TLS_配置说明.md` - 本说明文档

### 2. 修改文件
- `WebApiHelper.cs` - 更新SSL/TLS配置逻辑

## 使用方法

### 方法一：在Application_Start中初始化（推荐）

在`Global.asax.cs`的`Application_Start`方法中添加：

```csharp
protected void Application_Start()
{
    // 其他初始化代码...
    
    // 初始化SSL/TLS配置
    Dqy.Instrument.Mall.CommonLib.ApplicationStartup.Initialize();
    
    // 其他初始化代码...
}
```

### 方法二：在web.config中配置HTTP模块

在`web.config`的`<system.web>`节点中添加：

```xml
<httpModules>
  <add name="SslConfigurationModule" type="Dqy.Instrument.Mall.CommonLib.SslConfigurationModule" />
</httpModules>
```

或在`<system.webServer>`节点中添加（IIS 7+）：

```xml
<modules>
  <add name="SslConfigurationModule" type="Dqy.Instrument.Mall.CommonLib.SslConfigurationModule" />
</modules>
```

### 方法三：手动调用

如果需要在特定时机配置SSL/TLS，可以手动调用：

```csharp
// 配置全局SSL/TLS协议
Dqy.Instrument.Mall.CommonLib.SslHelper.ConfigureSslProtocols();

// 或者为特定URL配置（包含证书验证选项）
Dqy.Instrument.Mall.CommonLib.SslHelper.ConfigureForUrl("https://api.example.com", ignoreCertificateErrors: false);
```

## 配置详情

### 支持的协议版本
- TLS 1.2 (推荐)
- TLS 1.1
- TLS 1.0
- TLS 1.3 (如果.NET Framework版本支持)

### 安全设置
- 禁用证书吊销检查以提高性能
- 可选择性忽略证书验证错误（仅用于开发/测试环境）
- 设置合理的连接限制

## 注意事项

### 生产环境
1. **不要**在生产环境中设置`ignoreCertificateErrors = true`
2. 确保服务器证书有效且未过期
3. 建议使用TLS 1.2或更高版本

### 开发/测试环境
1. 可以设置`ignoreCertificateErrors = true`来绕过证书验证
2. 注意这会降低安全性，仅用于开发调试

### Windows Server 2016特殊配置
1. 确保已安装最新的Windows更新
2. 可能需要在注册表中启用TLS 1.2支持：
   ```
   [HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client]
   "DisabledByDefault"=dword:00000000
   "Enabled"=dword:00000001
   ```

## 故障排除

### 常见错误及解决方法

1. **"请求被中止: 未能创建 SSL/TLS 安全通道"**
   - 确保已调用`ApplicationStartup.Initialize()`
   - 检查目标服务器是否支持TLS 1.2
   - 验证服务器证书是否有效

2. **"基础连接已经关闭: 发送时发生错误"**
   - 检查网络连接
   - 确认防火墙设置
   - 验证代理配置

3. **证书验证失败**
   - 检查系统时间是否正确
   - 验证证书链是否完整
   - 考虑在测试环境中临时忽略证书错误

### 调试信息
可以通过以下方法获取当前SSL配置信息：

```csharp
string protocolInfo = Dqy.Instrument.Mall.CommonLib.SslHelper.GetSupportedProtocols();
System.Diagnostics.Debug.WriteLine(protocolInfo);
```

## 版本兼容性
- .NET Framework 4.0+
- Windows Server 2012+
- IIS 7.0+

## 更新日志
- 2024-01-XX: 初始版本，解决Windows Server 2016 SSL/TLS连接问题
