using System;
using System.Threading.Tasks;
using System.Net;
using System.Text;

namespace Dqy.Instrument.Mall.CommonLib
{
    /// <summary>
    /// API连接测试工具
    /// 专门用于测试通过nginx代理的API连接
    /// </summary>
    public static class ApiConnectionTester
    {
        /// <summary>
        /// 测试指定的API连接
        /// </summary>
        /// <param name="apiUrl">API地址，如：https://jzzxmall.czedu.cn/api/api/main/index?domain=jzzxmall.czedu.cn&userid=0</param>
        /// <returns>测试结果</returns>
        public static async Task<string> TestApiConnection(string apiUrl)
        {
            var result = new StringBuilder();
            result.AppendLine($"=== API连接测试 ===");
            result.AppendLine($"测试URL: {apiUrl}");
            result.AppendLine($"测试时间: {DateTime.Now}");
            result.AppendLine();

            try
            {
                // 1. 初始化SSL配置
                result.AppendLine("1. 初始化SSL配置...");
                ApplicationStartup.Initialize();
                result.AppendLine("   ✓ SSL配置初始化完成");
                result.AppendLine();

                // 2. 配置针对nginx代理的SSL设置
                result.AppendLine("2. 配置nginx代理SSL设置...");
                SslHelper.ConfigureForNginxProxy(apiUrl, false);
                result.AppendLine("   ✓ nginx代理SSL配置完成");
                result.AppendLine();

                // 3. 测试基本连接
                result.AppendLine("3. 测试基本连接...");
                await TestBasicConnection(apiUrl, result);
                result.AppendLine();

                // 4. 测试WebApiHelper.SendAsync方法
                result.AppendLine("4. 测试WebApiHelper.SendAsync方法...");
                await TestWebApiHelper(apiUrl, result);
                result.AppendLine();

                // 5. 测试不同的SSL配置
                result.AppendLine("5. 测试忽略证书验证的配置...");
                await TestWithIgnoreCertificate(apiUrl, result);
                result.AppendLine();

            }
            catch (Exception ex)
            {
                result.AppendLine($"测试过程中发生错误: {ex.Message}");
                result.AppendLine($"堆栈跟踪: {ex.StackTrace}");
            }

            return result.ToString();
        }

        /// <summary>
        /// 测试基本HTTP连接
        /// </summary>
        private static async Task TestBasicConnection(string apiUrl, StringBuilder result)
        {
            try
            {
                var request = WebRequest.Create(apiUrl) as HttpWebRequest;
                request.Method = "GET";
                request.Timeout = 30000; // 30秒超时
                request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
                request.Accept = "application/json, text/plain, */*";
                
                using (var response = await request.GetResponseAsync() as HttpWebResponse)
                {
                    result.AppendLine($"   ✓ 基本连接成功");
                    result.AppendLine($"   状态码: {response.StatusCode}");
                    result.AppendLine($"   服务器: {response.Server}");
                    result.AppendLine($"   内容类型: {response.ContentType}");
                    
                    // 读取响应内容的前200个字符
                    using (var stream = response.GetResponseStream())
                    using (var reader = new System.IO.StreamReader(stream))
                    {
                        var content = await reader.ReadToEndAsync();
                        var preview = content.Length > 200 ? content.Substring(0, 200) + "..." : content;
                        result.AppendLine($"   响应预览: {preview}");
                    }
                }
            }
            catch (WebException webEx)
            {
                result.AppendLine($"   ✗ 基本连接失败: {webEx.Message}");
                if (webEx.Response is HttpWebResponse errorResponse)
                {
                    result.AppendLine($"   错误状态码: {errorResponse.StatusCode}");
                }
                
                // 检查是否是SSL错误
                if (webEx.Message.Contains("SSL") || webEx.Message.Contains("TLS") || webEx.Message.Contains("安全通道"))
                {
                    result.AppendLine($"   ⚠️ 这是SSL/TLS相关错误，建议检查证书配置");
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"   ✗ 基本连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试WebApiHelper.SendAsync方法
        /// </summary>
        private static async Task TestWebApiHelper(string apiUrl, StringBuilder result)
        {
            try
            {
                // 构造测试数据
                var testData = new
                {
                    test = true,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                // 使用GET方法测试
                var getResult = await WebApiHelper.SendAsync<dynamic>(
                    url: apiUrl,
                    data: null,
                    sendType: CommonJsonSendType.GET,
                    timeOut: 30000,
                    checkValidationResult: false
                );

                result.AppendLine($"   ✓ WebApiHelper GET请求成功");
                result.AppendLine($"   响应类型: {getResult?.GetType().Name}");
                
            }
            catch (Exception ex)
            {
                result.AppendLine($"   ✗ WebApiHelper请求失败: {ex.Message}");
                
                // 提供具体的错误分析
                if (ex.Message.Contains("SSL") || ex.Message.Contains("TLS") || ex.Message.Contains("安全通道"))
                {
                    result.AppendLine($"   ⚠️ SSL/TLS错误，建议尝试忽略证书验证的配置");
                }
                else if (ex.Message.Contains("超时") || ex.Message.Contains("timeout"))
                {
                    result.AppendLine($"   ⚠️ 请求超时，建议增加超时时间或检查网络连接");
                }
                else if (ex.Message.Contains("404") || ex.Message.Contains("Not Found"))
                {
                    result.AppendLine($"   ⚠️ API地址不存在，请检查URL是否正确");
                }
            }
        }

        /// <summary>
        /// 测试忽略证书验证的配置
        /// </summary>
        private static async Task TestWithIgnoreCertificate(string apiUrl, StringBuilder result)
        {
            try
            {
                // 配置忽略证书验证
                SslHelper.ConfigureForNginxProxy(apiUrl, true);
                
                var getResult = await WebApiHelper.SendAsync<dynamic>(
                    url: apiUrl,
                    data: null,
                    sendType: CommonJsonSendType.GET,
                    timeOut: 30000,
                    checkValidationResult: true // 忽略证书验证
                );

                result.AppendLine($"   ✓ 忽略证书验证的请求成功");
                result.AppendLine($"   ⚠️ 注意：此配置仅用于测试，生产环境请使用正确的证书");
                
            }
            catch (Exception ex)
            {
                result.AppendLine($"   ✗ 忽略证书验证的请求仍然失败: {ex.Message}");
                result.AppendLine($"   这可能不是证书问题，建议检查网络连接和nginx配置");
            }
        }

        /// <summary>
        /// 快速测试指定的API地址
        /// </summary>
        /// <param name="domain">域名，如：jzzxmall.czedu.cn</param>
        /// <param name="userId">用户ID，默认为0</param>
        /// <returns>测试结果</returns>
        public static async Task<string> QuickTestApi(string domain, int userId = 0)
        {
            var apiUrl = $"https://{domain}/api/api/main/index?domain={domain}&userid={userId}";
            return await TestApiConnection(apiUrl);
        }

        /// <summary>
        /// 生成针对当前错误的解决方案建议
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>解决方案建议</returns>
        public static string GetSolutionRecommendations(string errorMessage)
        {
            var recommendations = new StringBuilder();
            recommendations.AppendLine("=== 解决方案建议 ===");
            recommendations.AppendLine();

            if (errorMessage.Contains("SSL") || errorMessage.Contains("TLS") || errorMessage.Contains("安全通道"))
            {
                recommendations.AppendLine("SSL/TLS错误解决方案:");
                recommendations.AppendLine("1. 确保已在Application_Start中调用 ApplicationStartup.Initialize()");
                recommendations.AppendLine("2. 检查服务器是否支持TLS 1.2协议");
                recommendations.AppendLine("3. 临时测试时可以设置 checkValidationResult: true");
                recommendations.AppendLine("4. 检查Windows Server 2016的TLS注册表配置");
                recommendations.AppendLine("5. 确认nginx的SSL配置正确");
            }
            else if (errorMessage.Contains("超时") || errorMessage.Contains("timeout"))
            {
                recommendations.AppendLine("超时错误解决方案:");
                recommendations.AppendLine("1. 增加请求超时时间（如：60000毫秒）");
                recommendations.AppendLine("2. 检查网络连接和防火墙设置");
                recommendations.AppendLine("3. 检查nginx的超时配置");
                recommendations.AppendLine("4. 确认API服务器响应正常");
            }
            else if (errorMessage.Contains("404") || errorMessage.Contains("Not Found"))
            {
                recommendations.AppendLine("404错误解决方案:");
                recommendations.AppendLine("1. 检查API URL是否正确");
                recommendations.AppendLine("2. 确认nginx的路由配置");
                recommendations.AppendLine("3. 检查后端API服务是否正常运行");
            }
            else
            {
                recommendations.AppendLine("通用解决方案:");
                recommendations.AppendLine("1. 检查网络连接");
                recommendations.AppendLine("2. 确认API服务器状态");
                recommendations.AppendLine("3. 查看详细的错误日志");
                recommendations.AppendLine("4. 联系系统管理员检查服务器配置");
            }

            return recommendations.ToString();
        }
    }
}
