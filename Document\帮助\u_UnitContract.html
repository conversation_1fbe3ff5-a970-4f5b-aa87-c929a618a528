﻿ UnitId = entity.UnitId,
 ContractMainName = entity.ContractMainName,
 SocialCreditCode = entity.SocialCreditCode,
 UserId = entity.UserId,
 UserName = entity.UserName,
 Mobile = entity.Mobile,
 Tel = entity.Tel,
 OpeningBank = entity.OpeningBank,
 BankAccount = entity.BankAccount,
 Statuz = entity.Statuz,
 CreateId = entity.CreateId,
 RegTime = entity.RegTime,


 UnitId = model.UnitId,
 ContractMainName = model.ContractMainName,
 SocialCreditCode = model.SocialCreditCode,
 UserId = model.UserId,
 UserName = model.UserName,
 Mobile = model.Mobile,
 Tel = model.Tel,
 OpeningBank = model.OpeningBank,
 BankAccount = model.BankAccount,
 Statuz = model.Statuz,
 CreateId = model.CreateId,
 RegTime = model.RegTime,


 temp.UnitId = model.UnitId,
 temp.ContractMainName = model.ContractMainName,
 temp.SocialCreditCode = model.SocialCreditCode,
 temp.UserId = model.UserId,
 temp.UserName = model.UserName,
 temp.Mobile = model.Mobile,
 temp.Tel = model.Tel,
 temp.OpeningBank = model.OpeningBank,
 temp.BankAccount = model.BankAccount,
 temp.Statuz = model.Statuz,
 temp.CreateId = model.CreateId,
 temp.RegTime = model.RegTime,

 UnitContractId = item.UnitContractId,
 UnitId = item.UnitId,
 ContractMainName = item.ContractMainName,
 SocialCreditCode = item.SocialCreditCode,
 UserId = item.UserId,
 UserName = item.UserName,
 Mobile = item.Mobile,
 Tel = item.Tel,
 OpeningBank = item.OpeningBank,
 BankAccount = item.BankAccount,
 Statuz = item.Statuz,
 CreateId = item.CreateId,
 RegTime = item.RegTime,

public class UnitContractInputModel
{
 [Display(Name = "Id")] 
    public long UnitContractId {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "合同主体名称")] 
    public string ContractMainName {get; set; }
    
 [Display(Name = "统一社会信用代码")] 
    public string SocialCreditCode {get; set; }
    
 [Display(Name = "联系人")] 
    public long UserId {get; set; }
    
 [Display(Name = "联系人名称")] 
    public string UserName {get; set; }
    
 [Display(Name = "手机号码")] 
    public string Mobile {get; set; }
    
 [Display(Name = "电话号码")] 
    public string Tel {get; set; }
    
 [Display(Name = "单位开户行")] 
    public string OpeningBank {get; set; }
    
 [Display(Name = "银行账号")] 
    public string BankAccount {get; set; }
    
 [Display(Name = "启用和禁用（1：启用  0：禁用)")] 
    public int Statuz {get; set; }
    
 [Display(Name = "记录人")] 
    public long CreateId {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class UnitContractViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long UnitContractId {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 合同主体名称
    /// </summary>
    public string ContractMainName {get; set; }
    
    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string SocialCreditCode {get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 联系人名称
    /// </summary>
    public string UserName {get; set; }
    
    /// <summary>
    /// 手机号码
    /// </summary>
    public string Mobile {get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    public string Tel {get; set; }
    
    /// <summary>
    /// 单位开户行
    /// </summary>
    public string OpeningBank {get; set; }
    
    /// <summary>
    /// 银行账号
    /// </summary>
    public string BankAccount {get; set; }
    
    /// <summary>
    /// 启用和禁用（1：启用  0：禁用)
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long CreateId {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractMainName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractMainName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同主体名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SocialCreditCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SocialCreditCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入统一社会信用代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系人名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Mobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Mobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Tel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Tel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入电话号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OpeningBank, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OpeningBank, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位开户行" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BankAccount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BankAccount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入银行账号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入启用和禁用（1：启用  0：禁用)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreateId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreateId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'ContractMainName', title: '合同主体名称', sortable: true },
                 
 { field: 'SocialCreditCode', title: '统一社会信用代码', sortable: true },
                 
 { field: 'UserId', title: '联系人', sortable: true },
                 
 { field: 'UserName', title: '联系人名称', sortable: true },
                 
 { field: 'Mobile', title: '手机号码', sortable: true },
                 
 { field: 'Tel', title: '电话号码', sortable: true },
                 
 { field: 'OpeningBank', title: '单位开户行', sortable: true },
                 
 { field: 'BankAccount', title: '银行账号', sortable: true },
                 
 { field: 'Statuz', title: '启用和禁用（1：启用  0：禁用)', sortable: true },
                 
 { field: 'CreateId', title: '记录人', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
o.UnitId,                 
o.ContractMainName,                 
o.SocialCreditCode,                 
o.UserId,                 
o.UserName,                 
o.Mobile,                 
o.Tel,                 
o.OpeningBank,                 
o.BankAccount,                 
o.Statuz,                 
o.CreateId,                 
o.RegTime,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#ContractMainName').val(d.data.rows.ContractMainName);          
        $('#SocialCreditCode').val(d.data.rows.SocialCreditCode);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#UserName').val(d.data.rows.UserName);          
        $('#Mobile').val(d.data.rows.Mobile);          
        $('#Tel').val(d.data.rows.Tel);          
        $('#OpeningBank').val(d.data.rows.OpeningBank);          
        $('#BankAccount').val(d.data.rows.BankAccount);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#CreateId').val(d.data.rows.CreateId);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_UnitId').html(' 单位Id');               
 $('#th_ContractMainName').html(' 合同主体名称');               
 $('#th_SocialCreditCode').html(' 统一社会信用代码');               
 $('#th_UserId').html(' 联系人');               
 $('#th_UserName').html(' 联系人名称');               
 $('#th_Mobile').html(' 手机号码');               
 $('#th_Tel').html(' 电话号码');               
 $('#th_OpeningBank').html(' 单位开户行');               
 $('#th_BankAccount').html(' 银行账号');               
 $('#th_Statuz').html(' 启用和禁用（1：启用  0：禁用)');               
 $('#th_CreateId').html(' 记录人');               
 $('#th_RegTime').html(' 记录时间');               
 
 $('#tr_UnitId').hide();               
 $('#tr_ContractMainName').hide();               
 $('#tr_SocialCreditCode').hide();               
 $('#tr_UserId').hide();               
 $('#tr_UserName').hide();               
 $('#tr_Mobile').hide();               
 $('#tr_Tel').hide();               
 $('#tr_OpeningBank').hide();               
 $('#tr_BankAccount').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_CreateId').hide();               
 $('#tr_RegTime').hide();               

 , "UnitId" : unitId
 , "ContractMainName" : contractMainName
 , "SocialCreditCode" : socialCreditCode
 , "UserId" : userId
 , "UserName" : userName
 , "Mobile" : mobile
 , "Tel" : tel
 , "OpeningBank" : openingBank
 , "BankAccount" : bankAccount
 , "Statuz" : statuz
 , "CreateId" : createId
 , "RegTime" : regTime

 var unitId = $('#o_UnitId').val();
 var contractMainName = $('#o_ContractMainName').val();
 var socialCreditCode = $('#o_SocialCreditCode').val();
 var userId = $('#o_UserId').val();
 var userName = $('#o_UserName').val();
 var mobile = $('#o_Mobile').val();
 var tel = $('#o_Tel').val();
 var openingBank = $('#o_OpeningBank').val();
 var bankAccount = $('#o_BankAccount').val();
 var statuz = $('#o_Statuz').val();
 var createId = $('#o_CreateId').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同主体名称' : '产品名称', d.data.rows.ContractMainName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '统一社会信用代码' : '产品名称', d.data.rows.SocialCreditCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系人名称' : '产品名称', d.data.rows.UserName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机号码' : '产品名称', d.data.rows.Mobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '电话号码' : '产品名称', d.data.rows.Tel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位开户行' : '产品名称', d.data.rows.OpeningBank);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '银行账号' : '产品名称', d.data.rows.BankAccount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '启用和禁用（1：启用  0：禁用)' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.CreateId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);



