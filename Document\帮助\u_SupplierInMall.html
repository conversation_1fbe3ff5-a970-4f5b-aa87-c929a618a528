﻿ MallId = entity.MallId,
 UnitId = entity.UnitId,
 Name = entity.Name,
 SocialCreditCode = entity.SocialCreditCode,
 ProvinceId = entity.ProvinceId,
 CityId = entity.CityId,
 CountyId = entity.CountyId,
 Address = entity.Address,
 LinkMan = entity.LinkMan,
 Mobile = entity.Mobile,
 Tel = entity.Tel,
 Brief = entity.Brief,
 BusLicense = entity.BusLicense,
 ProductionLicense = entity.ProductionLicense,
 AgentLicense = entity.AgentLicense,
 Other = entity.Other,
 Statuz = entity.Statuz,
 AuditTime = entity.AuditTime,
 AuditorId = entity.AuditorId,
 Reason = entity.Reason,
 Memo = entity.Memo,
 BackoutNum = entity.BackoutNum,
 UserId = entity.UserId,
 RegTime = entity.RegTime,


 MallId = model.MallId,
 UnitId = model.UnitId,
 Name = model.Name,
 SocialCreditCode = model.SocialCreditCode,
 ProvinceId = model.ProvinceId,
 CityId = model.CityId,
 CountyId = model.CountyId,
 Address = model.Address,
 LinkMan = model.LinkMan,
 Mobile = model.Mobile,
 Tel = model.Tel,
 Brief = model.Brief,
 BusLicense = model.BusLicense,
 ProductionLicense = model.ProductionLicense,
 AgentLicense = model.AgentLicense,
 Other = model.Other,
 Statuz = model.Statuz,
 AuditTime = model.AuditTime,
 AuditorId = model.AuditorId,
 Reason = model.Reason,
 Memo = model.Memo,
 BackoutNum = model.BackoutNum,
 UserId = model.UserId,
 RegTime = model.RegTime,


 temp.MallId = model.MallId,
 temp.UnitId = model.UnitId,
 temp.Name = model.Name,
 temp.SocialCreditCode = model.SocialCreditCode,
 temp.ProvinceId = model.ProvinceId,
 temp.CityId = model.CityId,
 temp.CountyId = model.CountyId,
 temp.Address = model.Address,
 temp.LinkMan = model.LinkMan,
 temp.Mobile = model.Mobile,
 temp.Tel = model.Tel,
 temp.Brief = model.Brief,
 temp.BusLicense = model.BusLicense,
 temp.ProductionLicense = model.ProductionLicense,
 temp.AgentLicense = model.AgentLicense,
 temp.Other = model.Other,
 temp.Statuz = model.Statuz,
 temp.AuditTime = model.AuditTime,
 temp.AuditorId = model.AuditorId,
 temp.Reason = model.Reason,
 temp.Memo = model.Memo,
 temp.BackoutNum = model.BackoutNum,
 temp.UserId = model.UserId,
 temp.RegTime = model.RegTime,

 SupplierInMallId = item.SupplierInMallId,
 MallId = item.MallId,
 UnitId = item.UnitId,
 Name = item.Name,
 SocialCreditCode = item.SocialCreditCode,
 ProvinceId = item.ProvinceId,
 CityId = item.CityId,
 CountyId = item.CountyId,
 Address = item.Address,
 LinkMan = item.LinkMan,
 Mobile = item.Mobile,
 Tel = item.Tel,
 Brief = item.Brief,
 BusLicense = item.BusLicense,
 ProductionLicense = item.ProductionLicense,
 AgentLicense = item.AgentLicense,
 Other = item.Other,
 Statuz = item.Statuz,
 AuditTime = item.AuditTime,
 AuditorId = item.AuditorId,
 Reason = item.Reason,
 Memo = item.Memo,
 BackoutNum = item.BackoutNum,
 UserId = item.UserId,
 RegTime = item.RegTime,

public class SupplierInMallInputModel
{
 [Display(Name = "Id")] 
    public int SupplierInMallId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "供应商Id（UnitId）")] 
    public int UnitId {get; set; }
    
 [Display(Name = "企业全称")] 
    public string Name {get; set; }
    
 [Display(Name = "社会统一社会信用代码")] 
    public string SocialCreditCode {get; set; }
    
 [Display(Name = "省Id")] 
    public int ProvinceId {get; set; }
    
 [Display(Name = "市Id")] 
    public int CityId {get; set; }
    
 [Display(Name = "区Id")] 
    public int CountyId {get; set; }
    
 [Display(Name = "详细地址")] 
    public string Address {get; set; }
    
 [Display(Name = "联系人")] 
    public string LinkMan {get; set; }
    
 [Display(Name = "手机号码")] 
    public string Mobile {get; set; }
    
 [Display(Name = "电话号码")] 
    public string Tel {get; set; }
    
 [Display(Name = "公司简介")] 
    public string Brief {get; set; }
    
 [Display(Name = "营业执照")] 
    public string BusLicense {get; set; }
    
 [Display(Name = "生产许可证")] 
    public string ProductionLicense {get; set; }
    
 [Display(Name = "代理许可证(是否存在 1：是   0：否  )")] 
    public bool AgentLicense {get; set; }
    
 [Display(Name = "其它(是否存在 1：是   0：否  )")] 
    public bool Other {get; set; }
    
 [Display(Name = "状态（0：待审核；1：审核通过；2：审核未通过）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "审核时间")] 
    public DateTime AuditTime {get; set; }
    
 [Display(Name = "审核人Id")] 
    public long AuditorId {get; set; }
    
 [Display(Name = "审批原因")] 
    public string Reason {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 [Display(Name = "产品退回数量")] 
    public int BackoutNum {get; set; }
    
 [Display(Name = "记录人")] 
    public long UserId {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class SupplierInMallViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int SupplierInMallId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 供应商Id（UnitId）
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 企业全称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 社会统一社会信用代码
    /// </summary>
    public string SocialCreditCode {get; set; }
    
    /// <summary>
    /// 省Id
    /// </summary>
    public int ProvinceId {get; set; }
    
    /// <summary>
    /// 市Id
    /// </summary>
    public int CityId {get; set; }
    
    /// <summary>
    /// 区Id
    /// </summary>
    public int CountyId {get; set; }
    
    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address {get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string LinkMan {get; set; }
    
    /// <summary>
    /// 手机号码
    /// </summary>
    public string Mobile {get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    public string Tel {get; set; }
    
    /// <summary>
    /// 公司简介
    /// </summary>
    public string Brief {get; set; }
    
    /// <summary>
    /// 营业执照
    /// </summary>
    public string BusLicense {get; set; }
    
    /// <summary>
    /// 生产许可证
    /// </summary>
    public string ProductionLicense {get; set; }
    
    /// <summary>
    /// 代理许可证(是否存在 1：是   0：否  )
    /// </summary>
    public bool AgentLicense {get; set; }
    
    /// <summary>
    /// 其它(是否存在 1：是   0：否  )
    /// </summary>
    public bool Other {get; set; }
    
    /// <summary>
    /// 状态（0：待审核；1：审核通过；2：审核未通过）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? AuditTime {get; set; }
    
    /// <summary>
    /// 审核人Id
    /// </summary>
    public long? AuditorId {get; set; }
    
    /// <summary>
    /// 审批原因
    /// </summary>
    public string Reason {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 产品退回数量
    /// </summary>
    public int BackoutNum {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商Id（UnitId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入企业全称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SocialCreditCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SocialCreditCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入社会统一社会信用代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProvinceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProvinceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入省Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CityId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CityId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountyId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountyId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Address, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Address, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入详细地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LinkMan, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LinkMan, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Mobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Mobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Tel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Tel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入电话号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Brief, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Brief, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入公司简介" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BusLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BusLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入营业执照" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductionLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductionLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入生产许可证" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AgentLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AgentLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入代理许可证(是否存在 1：是   0：否  )" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Other, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Other, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入其它(是否存在 1：是   0：否  )" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（0：待审核；1：审核通过；2：审核未通过）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditorId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditorId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Reason, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Reason, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批原因" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BackoutNum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BackoutNum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品退回数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'UnitId', title: '供应商Id（UnitId）', sortable: true },
                 
 { field: 'Name', title: '企业全称', sortable: true },
                 
 { field: 'SocialCreditCode', title: '社会统一社会信用代码', sortable: true },
                 
 { field: 'ProvinceId', title: '省Id', sortable: true },
                 
 { field: 'CityId', title: '市Id', sortable: true },
                 
 { field: 'CountyId', title: '区Id', sortable: true },
                 
 { field: 'Address', title: '详细地址', sortable: true },
                 
 { field: 'LinkMan', title: '联系人', sortable: true },
                 
 { field: 'Mobile', title: '手机号码', sortable: true },
                 
 { field: 'Tel', title: '电话号码', sortable: true },
                 
 { field: 'Brief', title: '公司简介', sortable: true },
                 
 { field: 'BusLicense', title: '营业执照', sortable: true },
                 
 { field: 'ProductionLicense', title: '生产许可证', sortable: true },
                 
 { field: 'AgentLicense', title: '代理许可证(是否存在 1：是   0：否  )', sortable: true },
                 
 { field: 'Other', title: '其它(是否存在 1：是   0：否  )', sortable: true },
                 
 { field: 'Statuz', title: '状态（0：待审核；1：审核通过；2：审核未通过）', sortable: true },
                 
 { field: 'AuditTime', title: '审核时间', sortable: true },
                 
 { field: 'AuditorId', title: '审核人Id', sortable: true },
                 
 { field: 'Reason', title: '审批原因', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
 { field: 'BackoutNum', title: '产品退回数量', sortable: true },
                 
 { field: 'UserId', title: '记录人', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
o.MallId,                 
o.UnitId,                 
o.Name,                 
o.SocialCreditCode,                 
o.ProvinceId,                 
o.CityId,                 
o.CountyId,                 
o.Address,                 
o.LinkMan,                 
o.Mobile,                 
o.Tel,                 
o.Brief,                 
o.BusLicense,                 
o.ProductionLicense,                 
o.AgentLicense,                 
o.Other,                 
o.Statuz,                 
o.AuditTime,                 
o.AuditorId,                 
o.Reason,                 
o.Memo,                 
o.BackoutNum,                 
o.UserId,                 
o.RegTime,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#Name').val(d.data.rows.Name);          
        $('#SocialCreditCode').val(d.data.rows.SocialCreditCode);          
        $('#ProvinceId').val(d.data.rows.ProvinceId);          
        $('#CityId').val(d.data.rows.CityId);          
        $('#CountyId').val(d.data.rows.CountyId);          
        $('#Address').val(d.data.rows.Address);          
        $('#LinkMan').val(d.data.rows.LinkMan);          
        $('#Mobile').val(d.data.rows.Mobile);          
        $('#Tel').val(d.data.rows.Tel);          
        $('#Brief').val(d.data.rows.Brief);          
        $('#BusLicense').val(d.data.rows.BusLicense);          
        $('#ProductionLicense').val(d.data.rows.ProductionLicense);          
        $('#AgentLicense').val(d.data.rows.AgentLicense);          
        $('#Other').val(d.data.rows.Other);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#AuditTime').val(d.data.rows.AuditTime);          
        $('#AuditorId').val(d.data.rows.AuditorId);          
        $('#Reason').val(d.data.rows.Reason);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#BackoutNum').val(d.data.rows.BackoutNum);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_MallId').html(' 商城Id');               
 $('#th_UnitId').html(' 供应商Id（UnitId）');               
 $('#th_Name').html(' 企业全称');               
 $('#th_SocialCreditCode').html(' 社会统一社会信用代码');               
 $('#th_ProvinceId').html(' 省Id');               
 $('#th_CityId').html(' 市Id');               
 $('#th_CountyId').html(' 区Id');               
 $('#th_Address').html(' 详细地址');               
 $('#th_LinkMan').html(' 联系人');               
 $('#th_Mobile').html(' 手机号码');               
 $('#th_Tel').html(' 电话号码');               
 $('#th_Brief').html(' 公司简介');               
 $('#th_BusLicense').html(' 营业执照');               
 $('#th_ProductionLicense').html(' 生产许可证');               
 $('#th_AgentLicense').html(' 代理许可证(是否存在 1：是   0：否  )');               
 $('#th_Other').html(' 其它(是否存在 1：是   0：否  )');               
 $('#th_Statuz').html(' 状态（0：待审核；1：审核通过；2：审核未通过）');               
 $('#th_AuditTime').html(' 审核时间');               
 $('#th_AuditorId').html(' 审核人Id');               
 $('#th_Reason').html(' 审批原因');               
 $('#th_Memo').html(' 备注');               
 $('#th_BackoutNum').html(' 产品退回数量');               
 $('#th_UserId').html(' 记录人');               
 $('#th_RegTime').html(' 记录时间');               
 
 $('#tr_MallId').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_Name').hide();               
 $('#tr_SocialCreditCode').hide();               
 $('#tr_ProvinceId').hide();               
 $('#tr_CityId').hide();               
 $('#tr_CountyId').hide();               
 $('#tr_Address').hide();               
 $('#tr_LinkMan').hide();               
 $('#tr_Mobile').hide();               
 $('#tr_Tel').hide();               
 $('#tr_Brief').hide();               
 $('#tr_BusLicense').hide();               
 $('#tr_ProductionLicense').hide();               
 $('#tr_AgentLicense').hide();               
 $('#tr_Other').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_AuditTime').hide();               
 $('#tr_AuditorId').hide();               
 $('#tr_Reason').hide();               
 $('#tr_Memo').hide();               
 $('#tr_BackoutNum').hide();               
 $('#tr_UserId').hide();               
 $('#tr_RegTime').hide();               

 , "MallId" : mallId
 , "UnitId" : unitId
 , "Name" : name
 , "SocialCreditCode" : socialCreditCode
 , "ProvinceId" : provinceId
 , "CityId" : cityId
 , "CountyId" : countyId
 , "Address" : address
 , "LinkMan" : linkMan
 , "Mobile" : mobile
 , "Tel" : tel
 , "Brief" : brief
 , "BusLicense" : busLicense
 , "ProductionLicense" : productionLicense
 , "AgentLicense" : agentLicense
 , "Other" : other
 , "Statuz" : statuz
 , "AuditTime" : auditTime
 , "AuditorId" : auditorId
 , "Reason" : reason
 , "Memo" : memo
 , "BackoutNum" : backoutNum
 , "UserId" : userId
 , "RegTime" : regTime

 var mallId = $('#o_MallId').val();
 var unitId = $('#o_UnitId').val();
 var name = $('#o_Name').val();
 var socialCreditCode = $('#o_SocialCreditCode').val();
 var provinceId = $('#o_ProvinceId').val();
 var cityId = $('#o_CityId').val();
 var countyId = $('#o_CountyId').val();
 var address = $('#o_Address').val();
 var linkMan = $('#o_LinkMan').val();
 var mobile = $('#o_Mobile').val();
 var tel = $('#o_Tel').val();
 var brief = $('#o_Brief').val();
 var busLicense = $('#o_BusLicense').val();
 var productionLicense = $('#o_ProductionLicense').val();
 var agentLicense = $('#o_AgentLicense').val();
 var other = $('#o_Other').val();
 var statuz = $('#o_Statuz').val();
 var auditTime = $('#o_AuditTime').val();
 var auditorId = $('#o_AuditorId').val();
 var reason = $('#o_Reason').val();
 var memo = $('#o_Memo').val();
 var backoutNum = $('#o_BackoutNum').val();
 var userId = $('#o_UserId').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商Id（UnitId）' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '企业全称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '社会统一社会信用代码' : '产品名称', d.data.rows.SocialCreditCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '省Id' : '产品名称', d.data.rows.ProvinceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市Id' : '产品名称', d.data.rows.CityId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区Id' : '产品名称', d.data.rows.CountyId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '详细地址' : '产品名称', d.data.rows.Address);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系人' : '产品名称', d.data.rows.LinkMan);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机号码' : '产品名称', d.data.rows.Mobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '电话号码' : '产品名称', d.data.rows.Tel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '公司简介' : '产品名称', d.data.rows.Brief);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '营业执照' : '产品名称', d.data.rows.BusLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '生产许可证' : '产品名称', d.data.rows.ProductionLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '代理许可证(是否存在 1：是   0：否  )' : '产品名称', d.data.rows.AgentLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '其它(是否存在 1：是   0：否  )' : '产品名称', d.data.rows.Other);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（0：待审核；1：审核通过；2：审核未通过）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核时间' : '产品名称', d.data.rows.AuditTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核人Id' : '产品名称', d.data.rows.AuditorId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批原因' : '产品名称', d.data.rows.Reason);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品退回数量' : '产品名称', d.data.rows.BackoutNum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);



