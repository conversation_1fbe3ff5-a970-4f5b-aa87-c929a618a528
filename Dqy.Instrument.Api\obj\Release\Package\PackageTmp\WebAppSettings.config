﻿<?xml version="1.0"?>
<appSettings>
	<add key="webpages:Version" value="*******" />
	<add key="webpages:Enabled" value="false" />
	<add key="PreserveLoginUrl" value="true" />
	<add key="ClientValidationEnabled" value="true" />
	<add key="UnobtrusiveJavaScriptEnabled" value="true" />
	<!-- 微信公众号URL对接信息 -->
	<add key="WeixinToken" value="应用服务器（开发者服务器）URL对应的Token" />
	<add key="WeixinEncodingAESKey" value="应用服务器（开发者服务器）URL对应的消息加解密密钥" />
	<!-- 高级接口信息 -->
	<add key="WeixinAppId" value="微信AppId" />
	<add key="WeixinAppSecret" value="微信AppSecret" />
	<!-- SDK提供的代理功能设置 -->
	<add key="WeixinAgentUrl" value="外部代理Url" />
	<add key="WeixinAgentToken" value="外部代理Token" />
	<add key="WeixinAgentWeiweihiKey" value="外部代理WeiWeiHiKey" />
	<!-- 微信支付相关参数 -->
	<!-- 微信支付V2 -->
	<add key="WeixinPay_Tenpay" value="WeixinPay_Tenpay" />
	<add key="WeixinPay_PartnerId" value="WeixinPay_PartnerId" />
	<add key="WeixinPay_Key" value="WeixinPay_Key" />
	<add key="WeixinPay_AppId" value="WeixinPay_AppId" />
	<add key="WeixinPay_AppKey" value="WeixinPay_AppKey" />
	<add key="WeixinPay_TenpayNotify" value="WeixinPay_TenpayNotify" />
	<!-- 微信支付V3 -->
	<add key="TenPayV3_MchId" value="TenPayV3_MchId" />
	<add key="TenPayV3_Key" value="TenPayV3_Key" />
	<add key="TenPayV3_AppId" value="TenPayV3_AppId" />
	<add key="TenPayV3_AppSecret" value="TenPayV3_AppSecret" />
	<add key="TenPayV3_TenpayNotify" value="http://YourDomainName/TenpayV3/PayNotifyUrl" />
	<!-- 开放平台 -->
	<add key="Component_Appid" value="Component_Appid" />
	<add key="Component_Secret" value="Component_Secret" />
	<add key="Component_Token" value="Component_Token" />
	<add key="Component_EncodingAESKey" value="Component_EncodingAESKey" />
	<!-- 微信企业号 -->
	<add key="WeixinCorpId" value="WeixinCorpId" />
	<add key="WeixinCorpSecret" value="WeixinCorpSecret" />

	<!-- 小程序 -->
	<!-- 小程序消息URL对接信息 -->
	<add key="WxOpenToken" value="wxfbe7419e7daf2ec1" />
	<add key="WxOpenEncodingAESKey" value="c5a1f6c6ae386e8f9e0e52b3d81d4d0f" />
	<!-- 小程序秘钥信息 -->
	<add key="WxOpenAppId" value="wxfbe7419e7daf2ec1" />
	<add key="WxOpenAppSecret" value="c5a1f6c6ae386e8f9e0e52b3d81d4d0f" />

	<!-- Cache.Redis连接配置 -->
	<add key="Cache_Redis_Configuration" value="Redis配置" />
	<!--<add key="Cache_Redis_Configuration" value="localhost:6379" />-->
	
	<!--限制手机当天最多使用次数-->
	<add key="SendMessage.TimesPhone" value="3" />
	<!--限制IP地址当天最多使用次数-->
	<add key="SendMessage.TimesIP" value="20" />
	<!--验证码等待时间(秒)-->
	<add key="SendMessage.WaitSecond" value="120" />
	<!--找回密码当天限制次数-->
	<add key="SendMessage.TimesDayFindPwd" value="100" />

	<!--上传文件类型控制-->
	<add key="File.Upload.Ext.Allow" value=".jpg.jpeg.png.gif.bmp.doc.pdf.ppt.pptx.txt.xls.xlsx.docx.dwg.wps." />
	<add key="File.Upload.ExtCode.Allow" value=".255216.255216.13780.7173.6677.60116.3780.208207.8075.102100.208207.8075.8075.6567." />
	<add key="Api.Path" value="/api" />
	<add key="Manage.Server.Domain" value="" />
	<add key="Search.Port" value="9200" />
	<add key="Search.Host" value="localhost" />
	<add key="Log.IsRecord" value="true" />
	<add key="Log.Path" value="D:\\hyun\\Log_instrument_api\\" />


	<!--密码验证方式（0：默认6位密码；1：禁止不符合规则的密码登录；2：允许弱密码登录一次，并提强制用户修改密码后才能使用系统）-->
	<add key="Pwd.Verification.Way" value="1" />
	<!--密码验证规则-->
	<add key="Pwd.Verification.RegExp" value="^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{6,30}$" />
	<!--阻止弱密码登录提示信息-->
	<add key="Prohibit.WeakPwdLogin.Msg" value="弱密码，禁止登录，请联系管理员处理" />
	<!--密码解密私钥-->
	<add key="Encrypt.Pwd.PrivateKey" value="MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAMSyhKVpaT6ni6ydvNgBp1EFb8CGXnPDzEv6U95mnBzcHvPM7QwnNQK1Ow2J/YMJqjA8JaD+mNXojZVZ7yTvUS7o+CfHZRlywT13AYdmkWNmuu/iLcgzNqllKQXGvQEs0+vBdAZaoqI0GHkQ58K7OifIH9/C9PJd1sbwvXnz18KLAgEDAoGAIMhrcObm38aXR2+feVWb4tY9SsEPvfX3Yf8N+mZvWiSv00zSLLEzgHOJ15b/ldbxsrSw8CpuzlFs7jmn2304MjEaibopWEgmsnp8bvLQ4R/64pV+SED5uIayugBqSJnu7/+iveNWJn3pqEKqXm1E1m7Bnj3D6Kh2f6jh50tfMI8CQQDz2K7IvzMmadZknWRXr5GQNjWnhrSZurcNOW6wQQ+RgKVAqcj6sY5wyPWJMI6jxaO9npaWzSZQRCx/4GGU9liVAkEAzoA+oa3Um284M3mJXfyLFqNqubFiE6GfL787E/49/7mmgvnKC+wsz9WR97j9h9d9cfeTuZBYGsuce5GsmqZGnwJBAKKQdIXUzMRGju2+QuUftmAkI8UEeGZ8egjQ9HWAtQurGNXGhfx2XvXbTlt1tG0ubSkUZGSIxDWCyFVAQQ35kGMCQQCJqtRryThnn3rM+7DpUwdkbPHRIOwNFmof1NINVClVJm8B+9wH8siKjmFP0KkFOlOhT7fRCuVnMmhSYR28btm/AkB2EL8x7LiJ7i7TWi7Cwd4II23t2yaFT1Kqn8Tg7sKwqrZXCvg+enkv6hytADENzFp6au5WfT4bygUO0bmJTLiE" />


	<!--产品、供应商、实验教学索引库，服务器部署一个平台默认值即可，多个需修改配置-->
	<add key="db_product_yun" value="db_product_yun" />
	<add key="db_dealer_yun" value="db_dealer_yun" />
	<add key="db_experiment_yun" value="db_experiment_yun" />
</appSettings>