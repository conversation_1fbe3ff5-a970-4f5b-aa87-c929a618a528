﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;

/// <summary>
/// lss
/// </summary>
namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class SupplierController : ControllerMember
    {

        public ActionResult Index()
        {
            return View();
        }

        #region 周跃峰 供应商保存、申请认证
        /// <summary>
        /// 单位信息
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> UnitInfo()
        {
            string attachAgentLicense = "";
            string attachOther = "";

            //API Server服务器地址
            ViewBag.ServicePath = Constant.ApiPath.Replace("api/", "");
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId;
            //根据单位Id获取单位信息
            string url = $"supplier/getsupplierinformation?token={Operater.Token}&UnitId={Operater.UnitId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (r.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            SupplierInfoInputModel sModel = r.obj == null ? null : ComLib.JSON2Object<SupplierInfoInputModel>(r.obj.ToString());
            if (sModel != null)
            {
                //根据单位Id,附件类型获取附件代理许可证信息
                attachAgentLicense = WebApiHelper.GetRequest("attachment/getattachmentslist", "token=" + Operater.Token + "&objectId=" + Operater.UnitId + "&FileCategory=1&UnitId=" + Operater.UnitId + "");
                List<AttachmentInputModel> listAttachAgentLicense = ComLib.JSON2Object<List<AttachmentInputModel>>(attachAgentLicense);
                sModel.AgentLicense = listAttachAgentLicense;

                //获取其它附件
                attachOther = WebApiHelper.GetRequest("attachment/getattachmentslist", "token=" + Operater.Token + "&objectId=" + Operater.UnitId + "&FileCategory=2&UnitId=" + Operater.UnitId + "");
                List<AttachmentInputModel> listAttachOther = ComLib.JSON2Object<List<AttachmentInputModel>>(attachOther);
                sModel.OtherAttachment = listAttachOther;
            }
            ViewBag.listAgenLicense = attachAgentLicense;
            ViewBag.listOther = attachOther;
            return View(sModel);
        }

        /// <summary>
        /// 保存、申请认证
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public async Task<JsonResult> SaveSupplier(SupplierInfoInputModel supplierInfo)
        {
            if (supplierInfo.StrsAgenLicense != "[]")
            {
                supplierInfo.AgentLicense = ComLib.JSON2Object<List<AttachmentInputModel>>(supplierInfo.StrsAgenLicense);
            }
            if (supplierInfo.StrsOther != "[]")
            {
                supplierInfo.OtherAttachment = ComLib.JSON2Object<List<AttachmentInputModel>>(supplierInfo.StrsOther);
            }
            supplierInfo.UserId = Operater.UserId;
            supplierInfo.UnitId = Operater.UnitId;
            supplierInfo.Token = Operater.Token;
            supplierInfo.MallId = Operater.CurrentMallId;
            if (supplierInfo.ButtonType.Equals(1))
            {
                string url = Constant.ApiPath + "supplier/postsavesupplierinformation";
                var result = await WebApiHelper.SendAsync(url, supplierInfo);
                Log.UnitInfo(supplierInfo, result, "(企业)用户‘" + Operater.Name + "’保存单位信息", "UnitInfo",result.flag);
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            else
            {
                string url = Constant.ApiPath + "supplier/postapplysupplierinfo";
                var result = await WebApiHelper.SendAsync(url, supplierInfo);
                Log.UnitInfo(supplierInfo, result, "(企业)用户‘" + Operater.Name + "’申请认证", "UnitInfo",result.flag);
                return Json(result, JsonRequestBehavior.AllowGet);
            }

        }

        /// <summary>
        /// 重新申请页面
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> Apply()
        {
            //API Server服务器地址
            ViewBag.ServicePath = Constant.ApiPath.Replace("api/", "");
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Token = Operater.Token;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId;
            //根据单位Id获取单位信息
            string url = $"supplier/getsupplierbyunitid?token={Operater.Token}&UnitId={Operater.UnitId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (r.flag == -2 || r.flag==0)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            //根据单位Id获取单位信息
            SupplierInfoInputModel sModel = ComLib.JSON2Object<SupplierInfoInputModel>(r.obj.ToString());
            return View(sModel);
        }

        /// <summary>
        /// 重新申请认证
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveApply(SupplierInfoInputModel supplierInfo)
        {
            supplierInfo.Token = Operater.Token;
            supplierInfo.UnitId = Operater.UnitId;
            supplierInfo.MallId = Operater.CurrentMallId;
            string url = "supplier/postsaveapply";
            var result = await WebApiHelper.SendAsync(url, supplierInfo);
            Log.UnitInfo(supplierInfo, result, "(企业)用户‘" + Operater.Name + "’重新申请认证", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);

        }
        #endregion

    }
}