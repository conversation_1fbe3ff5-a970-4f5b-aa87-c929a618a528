﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Show.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Dqy.TrainManage.Base.Util;
using Microsoft.AspNet.Identity;
using Microsoft.Owin.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Show.Controllers
{

    public class AccountController : Controller
    {
        private static readonly string PwdVerificationMsg = ComLib.GetAppSetting<string>("Pwd.Verification.Msg");
        private static readonly string PwdVerificationWay = ComLib.GetAppSetting<string>("Pwd.Verification.Way") == "" ? "0" : ComLib.GetAppSetting<string>("Pwd.Verification.Way");
        private static readonly string PwdVerificationRegExp = ComLib.GetAppSetting<string>("Pwd.Verification.RegExp");
        private static readonly string ProhibitWeakPwdLoginMsg = ComLib.GetAppSetting<string>("Prohibit.WeakPwdLogin.Msg");
        // GET: Account
        public ActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 注册页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Reg()
        {
            return View(new UserRegisterInputModel());
        }

        /// <summary>
        /// 找回密码
        /// </summary>
        /// <returns></returns>
        public ActionResult ResetPwd()
        {
            return View();
        }

        /// <summary>
        /// 注册成功页面
        /// </summary>
        /// <returns></returns>
        public ActionResult RegSucc(ReturnResult r)
        {
            return View(r);
        }

        /// <summary>
        /// 获取检验码
        /// </summary>
        /// <param name="CodeNum">验证码</param>
        /// <param name="phoneNumber">手机号码</param>
        /// <returns></returns>
        public JsonResult SendSecurityCode(string CodeNum, string phoneNumber)
        {
            ReturnResult r = new ReturnResult();
            if (!TypeHelper.IsMobile(phoneNumber))
            {
                r.flag = 0;
                r.msg = "手机号码格式不正确";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            string result = "";
            if (Session["vcode"] == null)
            {
                r.flag = 0;
                r.msg = "验证码已失效,请重新获取新的验证码";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            if (string.IsNullOrEmpty(CodeNum))
            {
                r.flag = 0;
                r.msg = "验证码不能为空";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
        
            //string strPhone = WebApiHelper.GetRequest("register/checkexistsmobile", "mobile=" + phoneNumber);
            //bool bResult = false;
            //bool.TryParse(strPhone, out bResult);
            //if (bResult)
            //{
            //    r.flag = 3;
            //    r.msg = "该手机号已被注册!";
            //    return Json(r, JsonRequestBehavior.AllowGet);
            //}
            if (!CodeNum.ToUpper().Equals(Session["vcode"].ToString()))
            {
                r.flag = 2;
                r.msg = "验证码输入有误";
                return Json(r, JsonRequestBehavior.AllowGet);
            }           
            Session.Remove("vcode");//验证码使用一次，即失效，防止恶意多次调用发送短信接口
            try
            {
                int timesPhone = Constant.SendMessage_TimesPhone;
                int timesIP = Constant.SendMessage_TimesIP;
                int waitSecond = Constant.SendMessage_WaitSecond;
                string ipAddress = IPOperate.GetIP();
                result = WebApiHelper.GetRequest("register/issendvalidatecode", "mobile=" + phoneNumber + "&nMaxSendCount=" + timesPhone + "&nMaxIpSendCount=" + timesIP + "&ipAddress=" + ipAddress + "");
                r = ComLib.JSON2Object<ReturnResult>(result);
                //判断是否可以发送校验码
                if (r.flag == 1)
                {
                    //产生随便6位数
                    System.Random ran = new Random();
                    string RanKey = ran.Next(100000, 999999).ToString();

                    //存入Session
                    Session["RegisterMobile"] = phoneNumber;
                    Session["RegisterVerCode"] = RanKey;
                    Session.Timeout = 5;
                    string strSendMessage = Constant.SendMessage_Event_RegisterCode;
                    string strMessage = string.Format(strSendMessage, RanKey);
                    //发送短信
                    SendMessage.SendToMobile(phoneNumber, strMessage, false);
                    return Json(r, JsonRequestBehavior.AllowGet);

                }
                else
                {
                    return Json(r, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                FileLog.SendCustomLog("用户‘" + phoneNumber + "’发送验证码失败。result:"+ result + "\r\n 原因：" + ex.Message+"。",0);
                r.flag = 0;
                r.msg = ex.Message;
                return Json(r, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 通过手机号码找回密码
        /// </summary>
        /// <param name="CodeNum">验证码</param>
        /// <param name="phoneNumber">手机号码</param>
        /// <returns></returns>
        public JsonResult SendSecurityCodeByPhone(string CodeNum, string phoneNumber)
        {
            ReturnResult r = new ReturnResult();
            if (!TypeHelper.IsMobile(phoneNumber))
            {
                r.flag = 0;
                r.msg = "手机号码格式不正确";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            if (string.IsNullOrEmpty(CodeNum))
            {
                r.flag = 0;
                r.msg = "验证码不能为空";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
           
            string strPhone = WebApiHelper.GetRequest("register/checkexistsmobile", "mobile=" + phoneNumber);
            bool bResult = false;
            bool.TryParse(strPhone, out bResult);
            if (!bResult)
            {
                r.flag = 3;
                r.msg = "输入的手机号码不存在!";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            if (!CodeNum.ToUpper().Equals(Session["rcode"].ToString()))
            {
                r.flag = 2;
                r.msg = "验证码输入有误";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            int timesPhone = Constant.SendMessage_TimesPhone;
            int timesIP = Constant.SendMessage_TimesIP;
            int waitSecond = Constant.SendMessage_WaitSecond;
            string ipAddress = IPOperate.GetIP();
            string result = WebApiHelper.GetRequest("register/issendcodebyphone", "mobile=" + phoneNumber + "&nMaxSendCount=" + timesPhone + "&nMaxIpSendCount=" + timesIP + "&ipAddress=" + ipAddress + "");
            r = ComLib.JSON2Object<ReturnResult>(result);

            //判断是否可以发送校验码
            if (r.flag == 1)
            {
                Log.Register(phoneNumber, "用户‘" + phoneNumber + "’通过手机号码找回密码成功");

                //产生随便6位数
                System.Random ran = new Random();
                string RanKey = ran.Next(100000, 999999).ToString();
                

                //存入Session
                Session["RMobile"] = phoneNumber;
                Session["RVerCode"] = RanKey;
                Session.Timeout = 5;
                string strSendMessage = Constant.SendMessage_Event_RCode;
                string strMessage = string.Format(strSendMessage, RanKey);
                //发送短信
                SendMessage.SendToMobile(phoneNumber, strMessage, false);
                return Json(r, JsonRequestBehavior.AllowGet);

            }
            else
            {
                FileLog.SendCustomLog("用户‘" + phoneNumber + "’通过手机号码找回密码失败。\r\n 原因：" + r.msg, r.flag);
                return Json(r, JsonRequestBehavior.AllowGet);
            }

        }

        /// <summary>
        /// 用户找回密码
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> GetPswd(ResetPwdInputModel model)
        {
            ReturnResult r = new ReturnResult();
            int errNum = 0;
            string resetPwdErrNumKey = string.Format("ResetPwd{0}", model.Mobile);
            string resetPwdErrNumDateKey = string.Format("ResetPwdDate{0}", model.Mobile);
            var resetPwdErrNum = Session[resetPwdErrNumKey];
            if (resetPwdErrNum != null && int.TryParse(resetPwdErrNum.ToString(), out errNum))
            {
                var resetPwdErrNumDate = Session[resetPwdErrNumDateKey];
                DateTime errDate = DateTime.Now.AddDays(-2);
                if (errNum > Constant.SendMessage_TimesPhone)
                {
                    if (resetPwdErrNumDate != null && DateTime.TryParse(resetPwdErrNumDate.ToString(), out errDate))
                    {
                        if (errDate.Date.AddDays(1) < DateTime.Now)
                        {
                            errNum = 0;
                        }
                    }
                    if (errNum != 0)
                    {
                        var blance = (errDate.Date.AddDays(1) - DateTime.Now).Hours;
                        string msg = string.Format("非法操作,24小时内多次提交找回密码错误，请{0}小时后再试。", blance);
                        ModelState.AddModelError("Error", msg);
                        return View("ResetPwd");
                    }
                }
                else
                {

                    if (resetPwdErrNumDate != null && DateTime.TryParse(resetPwdErrNumDate.ToString(), out errDate))
                    {
                        if (errDate.Date != DateTime.Now.Date)
                        {
                            errNum = 0;
                        }
                    }
                }
            }
            errNum++;
            Session[resetPwdErrNumKey] = errNum;
            Session[resetPwdErrNumDateKey] = DateTime.Now;
            //此处缺少校验码、验证码判断
            if (Session["rcode"] == null)
            {
                ModelState.AddModelError("VerificationCode", "验证码不能为空");
                return View("ResetPwd");
            }
            if (!Session["rcode"].ToString().Equals(model.VerificationCode.ToUpper()))
            {
                ModelState.AddModelError("VerificationCode", "验证码输入有误");
                return View("ResetPwd");
            }
            Session["rcode"] = null;//每次必须刷新后的验证码
            if (Session["RMobile"] == null || Session["RVerCode"] == null)
            {
                ModelState.AddModelError("CheckCode", "请先获取校验码");
                return View("ResetPwd");
            }

            if (!Session["RMobile"].ToString().Equals(model.Mobile) || !Session["RVerCode"].ToString().Equals(model.CheckCode))
            {
                ModelState.AddModelError("CheckCode", "手机号码或校验码输入有误");
                return View("ResetPwd");
            }

            if (ModelState.IsValid)
            {
                model.Salt = ComLib.GetGuid();
                model.Password = SecurityHelper.MD5(model.Password + model.Salt);
                model.ConfirmPassword= model.Password;
                string url = Constant.ApiPath + "account/changereturnpswd";
                r = await WebApiHelper.SendAsync(url, model);
                if (r.flag == 1)
                {
                    Session[resetPwdErrNumKey] = 0;
                    Log.Login(model, "用户‘" + model.Mobile + "’找回密码成功");
                    return RedirectToAction("Login");
                }
                else
                {
                    FileLog.SendCustomLog("用户‘" + model.Mobile + "’找回密码失败。\r\n 原因：" + r.msg, r.flag);
                    ModelState.AddModelError("Error", "找回密码失败，\r\n 原因："+r.msg);
                    return View("ResetPwd");
                }
            }
            else
            {
                FileLog.SendCustomLog("用户‘" + model.Mobile + "’校验失败， 找回密码信息中存在不符合要求的信息。", r.flag);
                ModelState.AddModelError("Error", "校验失败， 找回密码信息中存在不符合要求的信息。");
                return View("ResetPwd");
            }
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Reg(UserRegisterInputModel model)
        {
            ReturnResult r = new ReturnResult();
            //if (Session["vcode"] == null)
            //{
            //    ModelState.AddModelError("CheckCode", "请先获取校验码");
            //    //model.Password
            //    return View("Reg");
            //}
            //if (!Session["vcode"].ToString().Equals(model.VerificationCode.ToUpper()))
            //{
            //    ModelState.AddModelError("VerificationCode", "验证码输入有误");
            //    return View("Reg");
            //}
            if (Session["RegisterMobile"] == null || Session["RegisterVerCode"] == null)
            {
                ModelState.AddModelError("CheckCode", "请先获取校验码");
                return View("Reg");
            }

            if (!Session["RegisterMobile"].ToString().Equals(model.Mobile) || !Session["RegisterVerCode"].ToString().Equals(model.CheckCode))
            {
                ModelState.AddModelError("CheckCode", "手机号码或校验码输入有误");
                return View("Reg");
            }
            if (ModelState.IsValid)
            {
                if(model.UnitType == 0)
                {
                    model.UnitType = UnitType.AgentFirm;
                }
                model.Mobile = model.Mobile.Trim();
                LoginInputModel lp = new LoginInputModel();
                lp.EncryptType = "md5";
                lp.Mobile = model.Mobile;
                lp.Pkey = model.Password;
                //lp.Domain = Fetch.ServerDomain;
                lp.Domain = Constant.Current_Local_Domain;
                model.Salt = ComLib.GetGuid();
                //model.Password = SecurityHelper.MD5(SecurityHelper.MD5(model.Password) + model.Salt);
                model.Password = SecurityHelper.MD5(model.Password + model.Salt);
                //model.Domain = Fetch.ServerDomain;
                model.Domain = Constant.Current_Local_Domain;
                model.ConfirmPassword = model.Password;
                string url = Constant.ApiPath + "account/reg";
                r = await WebApiHelper.SendAsync(url, model);
                if (r.flag == 1)
                {
                    Log.Register(model, "用户‘" + model.Mobile + "’注册成功");
                    string loginUrl = Constant.ApiPath + "account/login";
                    r = await WebApiHelper.SendAsync(loginUrl, lp);
                    if (r.flag == 1)
                    {
                        UserCompanyViewModel uc = ComLib.JSON2Object<UserCompanyViewModel>((string)r.data.rows);
                        if (uc != null)
                        {
                            var _identity = CreateIdentity(uc, DefaultAuthenticationTypes.ApplicationCookie);
                            AuthenticationManager.SignIn(new AuthenticationProperties() { IsPersistent = lp.RememberMe }, _identity);
                            return Redirect("~/");
                        }
                        else
                        {
                            return RedirectToAction("Login");
                        }
                    }
                    else
                    {
                        return RedirectToAction("Login");
                    }
                }
                else
                {
                    FileLog.SendCustomLog("用户‘" + model.Mobile + "’注册失败。\r\n 原因：由于注册人数比较多，注册失败，请稍后再试。", r.flag);
                    ModelState.AddModelError("Error", "由于注册人数比较多，注册失败，请稍后再试。");
                    return View(model);
                }
            }
            else
            {
                FileLog.SendCustomLog("用户‘" + model.Mobile + "’注册失败。\r\n 原因：校验失败，注册信息中存在不符合要求的信息。", r.flag);
                ModelState.AddModelError("Error", "校验失败，注册信息中存在不符合要求的信息。");
                return View(model);
            }
        }

        /// <summary>
        /// 生成验证码图片
        /// </summary>
        /// <returns></returns>
        [OutputCache(Duration = 0)]
        public ActionResult VCode()
        {
            string code = CodeGenerate.CreateRandomCode(4);
            Session["vcode"] = code;
            return File(CodeGenerate.GetVerifyCode(code), @"image/jpeg");
        }

        /// <summary>
        /// 生成登录验证码图片
        /// </summary>
        /// <returns></returns>
        [OutputCache(Duration = 0)]
        public ActionResult LCode()
        {
            string code = CodeGenerate.CreateRandomCode(4);
            Session["lcode"] = code;
            return File(CodeGenerate.GetVerifyCode(code), @"image/jpeg");
        }

        /// <summary>
        /// 生成找回密码验证码图片
        /// </summary>
        /// <returns></returns>
        [OutputCache(Duration = 0)]
        public ActionResult RCode()
        {
            string code = CodeGenerate.CreateRandomCode(4);
            Session["rcode"] = code;
            return File(CodeGenerate.GetVerifyCode(code), @"image/jpeg");
        }

        /// <summary>
        /// mobile是否可以注册
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public JsonResult IsMobileCanBeRegistered(string Mobile)//参数名称必须与字段名相同
        {
            string result = WebApiHelper.GetRequest("register/checkexistsmobile", "mobile=" + Mobile);
            bool bResult = false;
            bool.TryParse(result, out bResult);
            return Json(!bResult, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// mobile是否存在
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public JsonResult IsMobileCanBeReturn(string Mobile)//参数名称必须与字段名相同
        {
            string result = WebApiHelper.GetRequest("register/checkexistsverifiedmobile", "mobile=" + Mobile);
            bool bResult = false;
            bool.TryParse(result, out bResult);
            return Json(bResult, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 单位名称是否可以注册
        /// </summary>
        /// <param name="UnitName"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public JsonResult IsUnitNameRegistered(string UnitName)
        {
            string result = WebApiHelper.GetRequest("supplier/checkexistsunitname", "UnitName=" + UnitName);
            bool bResult = false;
            bool.TryParse(result, out bResult);
            return Json(!bResult, JsonRequestBehavior.AllowGet);
        }


        [AllowAnonymous]
        [HttpGet]
        public ActionResult Login(string returnUrl)
        {
            var thirdData = AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"IsThird");
            if (thirdData != null)
            {
                var IsThird = Convert.ToInt32(thirdData.Value);
                if (IsThird == 1)
                {
                    return RedirectToAction("Index", "Auth");
                }
            }
            ViewBag.PwdVerificationWay = PwdVerificationWay;
            ViewBag.ReturnUrl = returnUrl;
            var isOpenSSO = 0;
            try
            {
                int.TryParse(System.Configuration.ConfigurationManager.AppSettings["dysoftauth.isOpen"], out isOpenSSO);
            }
            catch { }
            ViewBag.IsOpenSSO = isOpenSSO;
            return View();
        }

        private IAuthenticationManager AuthenticationManager { get { return HttpContext.GetOwinContext().Authentication; } }

        [AllowAnonymous]
        [HttpPost]
        public async Task<ActionResult> Login(LoginInputModel model, string returnUrl)
        {
            ViewBag.PwdVerificationWay = PwdVerificationWay;
            if (model.Mobile == null || model.Pkey == null)
            {
                return View();
            }
            //首先判断，是否需要验证：验证码
            if (GetIsCodeVerify() == 1)
            {
                if (model.Code == null || Session["lcode"] == null || !model.Code.ToUpper().Equals(Session["lcode"].ToString()))
                {
                    ModelState.AddModelError("Code", "验证码输入有误");
                    return View();
                }
            }
            if (ModelState.IsValid)
            {
                model.Mobile = model.Mobile.Trim();
                //model.Domain = Fetch.ServerDomain;
                model.Domain = Constant.Current_Local_Domain;
                string url = Constant.ApiPath + "account/login";
                var result = await WebApiHelper.SendAsync(url, model);
                if (result.flag == 1)
                {
                    ClearLoginErrorNum();
                    UserCompanyViewModel uc = ComLib.JSON2Object<UserCompanyViewModel>((string)result.data.rows);
                    if (uc != null)
                    {
                        Log.Login(uc,"用户‘"+uc.LoginName+"’登录成功。");
                        var _identity = CreateIdentity(uc, DefaultAuthenticationTypes.ApplicationCookie);
                        AuthenticationManager.SignIn(new AuthenticationProperties() { IsPersistent = model.RememberMe }, _identity);
                        
                        if (string.IsNullOrEmpty(returnUrl))
                        {
                            return Redirect("~/");
                        }
                        else 
                        {
                            var currentPath = Request.Url.Scheme + "://" + Request.Url.Authority;

                            returnUrl = returnUrl.ToLower();
                            if (returnUrl.StartsWith("http") && !returnUrl.StartsWith(currentPath))
                            {
                                return Redirect("~/");
                            }
                            else
                                return Redirect(returnUrl);
                        }
                    }
                }
                else
                {
                    FileLog.SendCustomLog("用户‘" + model.Mobile + "’登录失败。\r\n 原因："+result.msg, result.flag);
                    if (result.flag == 0)
                    {
                        ModelState.AddModelError("Pkey", result.msg);
                        return View();
                    }
                    if (result.flag == 2)
                    {
                        ModelState.AddModelError("Mobile", result.msg);
                        return View();
                    }
                    if (result.flag == 3)
                    {
                        AddLoginErrorNum();
                        ModelState.AddModelError("Pkey", result.msg);
                        return View();
                    }
                    if (result.flag == 4)
                    {
                        AddLoginErrorNum();
                        ModelState.AddModelError("Mobile", result.msg);
                        return View();
                    }
                }
            }
            return View(model);
        }

        #region 验证码是否需要验证方法

        /// <summary>
        /// 添加记录登录错误次数
        /// </summary>
        private void AddLoginErrorNum()
        {
            if (Session["pwderrornum"]!=null)
            {
                int num = 0;
                if (int.TryParse(Session["pwderrornum"].ToString(),out num))
                {
                    num++;
                    Session["pwderrornum"] = num;
                }
            }
            else
            {
                Session["pwderrornum"] = 1;
            }
        }

        private void ClearLoginErrorNum()
        {
            if (Session["pwderrornum"] != null)
            {
                Session.Remove("pwderrornum");
            } 
        }

        /// <summary>
        /// 获取验证码是否需要验证：1：验证  0：不验证不输入
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public int GetIsCodeVerify()
        {
            int success = 0;
            if (Session["pwderrornum"] != null)
            {
                int num = 0;
                if (int.TryParse(Session["pwderrornum"].ToString(), out num))
                {
                    if (num >= 3)
                    {
                        success = 1;
                    }
                }
            }
            return success;
        }
        #endregion

        [AllowAnonymous]
        [HttpPost]
        public async Task<JsonResult> LoginAjax(LoginInputModel model)
        {
            ReturnResult result = new ReturnResult();
            try
            {
                if (model.Mobile == null || model.Pkey == null)
                {
                    result.flag = 0;
                    result.msg = "请输入账号、密码。";
                    return Json(result);
                }
                //首先判断，是否需要验证：验证码
                if (GetIsCodeVerify() == 1)
                {
                    if (model.Code == null || Session["lcode"] == null || !model.Code.ToUpper().Equals(Session["lcode"].ToString()))
                    {
                        result.flag = 0;
                        result.msg = "验证码输入错误，请重新输入。";
                        return Json(result);
                    }
                }

                if (ModelState.IsValid)
                {
                    //model.Domain = Fetch.ServerDomain;
                    model.Domain = Constant.Current_Local_Domain;
                    string url = Constant.ApiPath + "account/login";
                    result = await WebApiHelper.SendAsync(url, model);
                    if (result.flag == 1)
                    {
                        UserCompanyViewModel uc = ComLib.JSON2Object<UserCompanyViewModel>((string)result.data.rows);
                        if (uc != null)
                        {
                            ClearLoginErrorNum();
                            var _identity = CreateIdentity(uc, DefaultAuthenticationTypes.ApplicationCookie);
                            AuthenticationManager.SignIn(new AuthenticationProperties() { IsPersistent = model.RememberMe }, _identity);

                        }
                        else
                        {
                            result.flag = 0;
                            result.msg = "登录失败,你输入账号、密码不正确。";
                            AddLoginErrorNum();
                        }
                    }
                    else
                    {
                        result.flag = 0;
                        result.msg = "登录失败,你输入账号、密码不正确。";
                        AddLoginErrorNum();
                    }

                }
                else
                {
                    result.flag = 0;
                    result.msg = "请输入账号、密码。";
                    AddLoginErrorNum();
                }
            }
            catch
            {
                result.flag = 0;
                result.msg = "你输入账号、密码不正确。";
                AddLoginErrorNum();
            }
            return Json(result);
        }


        /// <summary>
        /// 登出
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> Logout()
        {
            var isThird = 0;
            if (Request.IsAuthenticated && AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"IsThird") != null)
            {
                isThird = Convert.ToInt32(AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"IsThird").Value);
            }

            if (Request.IsAuthenticated && AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"Token") != null)
            {
                var sessionId = AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"Token").Value;
                string url = Constant.ApiPath + "account/logout?sessionId=" + sessionId;
                var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            }
            AuthenticationManager.SignOut(DefaultAuthenticationTypes.ApplicationCookie);

            if(isThird == 1)
            {
                var logout = System.Configuration.ConfigurationManager.AppSettings["dysoftauth.Logout"];
                return Redirect(logout);
            }
            else
            {
               return Redirect(Url.Content("~/")); 
            }            
        }

        private ClaimsIdentity CreateIdentity(UserCompanyViewModel account, string authenticationType)
        {

            ClaimsIdentity _identity = new ClaimsIdentity(DefaultAuthenticationTypes.ApplicationCookie);
            _identity.AddClaim(new Claim(ClaimTypes.Name, account.LoginName));
            _identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, account.UserId.ToString()));
            _identity.AddClaim(new Claim("http://schemas.microsoft.com/accesscontrolservice/2010/07/claims/identityprovider", "ASP.NET Identity"));

            _identity.AddClaim(new Claim("MallId", account.MallId.ToString()));
            _identity.AddClaim(new Claim("CurrentMallId", account.CurrentMallId.ToString()));
            _identity.AddClaim(new Claim("UserId", account.UserId.ToString()));
            _identity.AddClaim(new Claim("UserType", account.UserType.ToString()));
            _identity.AddClaim(new Claim("Token", account.Key));
            _identity.AddClaim(new Claim("UnitId", account.UnitId.ToString()));
            _identity.AddClaim(new Claim("UnitType", account.UnitType.ToString()));
            _identity.AddClaim(new Claim("UnitName", account.UnitName));
            _identity.AddClaim(new Claim("IsThird", "0"));

            string userName = account.RealName == "" ? account.LoginName : account.RealName;
            _identity.AddClaim(new Claim("UserName", userName));
            //
            _identity.AddClaim(new Claim("RecordUserId", account.RecordUserId.ToString()));
            //防伪标记，必须!
            _identity.AddClaim(new Claim("Dqy_Cookie_Key", "Dqy.Instrument.Show"));

            return _identity;
        }

        private ActionResult RedirectToLocal(string returnUrl)
        {
            if (Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }
            return RedirectToAction("Index", "Home");
        }


        /// <summary>
        /// mobile是否可以注册
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public JsonResult GetPwdVerification()//参数名称必须与字段名相同
        {
            var data = new {
                PwdVerificationMsg = PwdVerificationMsg,
                PwdVerificationWay = PwdVerificationWay,
                PwdVerificationRegExp = PwdVerificationRegExp,
                ProhibitWeakPwdLoginMsg = ProhibitWeakPwdLoginMsg
            };
            return Json(data, JsonRequestBehavior.AllowGet);
        }
    }
}