﻿ MallId = entity.MallId,
 DomainName = entity.DomainName,
 Ip = entity.Ip,
 AuthorizationCode = entity.AuthorizationCode,
 AuthorizationTime = entity.AuthorizationTime,
 AuthorizationEndTime = entity.AuthorizationEndTime,


 MallId = model.MallId,
 DomainName = model.DomainName,
 Ip = model.Ip,
 AuthorizationCode = model.AuthorizationCode,
 AuthorizationTime = model.AuthorizationTime,
 AuthorizationEndTime = model.AuthorizationEndTime,


 temp.MallId = model.MallId,
 temp.DomainName = model.DomainName,
 temp.Ip = model.Ip,
 temp.AuthorizationCode = model.AuthorizationCode,
 temp.AuthorizationTime = model.AuthorizationTime,
 temp.AuthorizationEndTime = model.AuthorizationEndTime,

 MallConsoleId = item.MallConsoleId,
 MallId = item.MallId,
 DomainName = item.DomainName,
 Ip = item.Ip,
 AuthorizationCode = item.AuthorizationCode,
 AuthorizationTime = item.AuthorizationTime,
 AuthorizationEndTime = item.AuthorizationEndTime,

public class MallConsoleInputModel
{
 [Display(Name = "Id")] 
    public long MallConsoleId {get; set; }
    
 [Display(Name = "商城管理Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "商城域名")] 
    public string DomainName {get; set; }
    
 [Display(Name = "商城对应Ip地址")] 
    public string Ip {get; set; }
    
 [Display(Name = "授权码")] 
    public string AuthorizationCode {get; set; }
    
 [Display(Name = "授权时间")] 
    public DateTime AuthorizationTime {get; set; }
    
 [Display(Name = "授权到期时间")] 
    public DateTime AuthorizationEndTime {get; set; }
    
 }
 
 public class MallConsoleViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long MallConsoleId {get; set; }
    
    /// <summary>
    /// 商城管理Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 商城域名
    /// </summary>
    public string DomainName {get; set; }
    
    /// <summary>
    /// 商城对应Ip地址
    /// </summary>
    public string Ip {get; set; }
    
    /// <summary>
    /// 授权码
    /// </summary>
    public string AuthorizationCode {get; set; }
    
    /// <summary>
    /// 授权时间
    /// </summary>
    public DateTime AuthorizationTime {get; set; }
    
    /// <summary>
    /// 授权到期时间
    /// </summary>
    public DateTime AuthorizationEndTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城管理Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DomainName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DomainName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城域名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Ip, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Ip, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城对应Ip地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuthorizationCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuthorizationCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入授权码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuthorizationTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuthorizationTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入授权时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuthorizationEndTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuthorizationEndTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入授权到期时间" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城管理Id', sortable: true },
                 
 { field: 'DomainName', title: '商城域名', sortable: true },
                 
 { field: 'Ip', title: '商城对应Ip地址', sortable: true },
                 
 { field: 'AuthorizationCode', title: '授权码', sortable: true },
                 
 { field: 'AuthorizationTime', title: '授权时间', sortable: true },
                 
 { field: 'AuthorizationEndTime', title: '授权到期时间', sortable: true },
                 
o.MallId,                 
o.DomainName,                 
o.Ip,                 
o.AuthorizationCode,                 
o.AuthorizationTime,                 
o.AuthorizationEndTime,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#DomainName').val(d.data.rows.DomainName);          
        $('#Ip').val(d.data.rows.Ip);          
        $('#AuthorizationCode').val(d.data.rows.AuthorizationCode);          
        $('#AuthorizationTime').val(d.data.rows.AuthorizationTime);          
        $('#AuthorizationEndTime').val(d.data.rows.AuthorizationEndTime);          

 $('#th_MallId').html(' 商城管理Id');               
 $('#th_DomainName').html(' 商城域名');               
 $('#th_Ip').html(' 商城对应Ip地址');               
 $('#th_AuthorizationCode').html(' 授权码');               
 $('#th_AuthorizationTime').html(' 授权时间');               
 $('#th_AuthorizationEndTime').html(' 授权到期时间');               
 
 $('#tr_MallId').hide();               
 $('#tr_DomainName').hide();               
 $('#tr_Ip').hide();               
 $('#tr_AuthorizationCode').hide();               
 $('#tr_AuthorizationTime').hide();               
 $('#tr_AuthorizationEndTime').hide();               

 , "MallId" : mallId
 , "DomainName" : domainName
 , "Ip" : ip
 , "AuthorizationCode" : authorizationCode
 , "AuthorizationTime" : authorizationTime
 , "AuthorizationEndTime" : authorizationEndTime

 var mallId = $('#o_MallId').val();
 var domainName = $('#o_DomainName').val();
 var ip = $('#o_Ip').val();
 var authorizationCode = $('#o_AuthorizationCode').val();
 var authorizationTime = $('#o_AuthorizationTime').val();
 var authorizationEndTime = $('#o_AuthorizationEndTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城管理Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城域名' : '产品名称', d.data.rows.DomainName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城对应Ip地址' : '产品名称', d.data.rows.Ip);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '授权码' : '产品名称', d.data.rows.AuthorizationCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '授权时间' : '产品名称', d.data.rows.AuthorizationTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '授权到期时间' : '产品名称', d.data.rows.AuthorizationEndTime);



