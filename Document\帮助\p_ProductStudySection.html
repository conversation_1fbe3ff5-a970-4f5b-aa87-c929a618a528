﻿ ProductNormPriceId = entity.ProductNormPriceId,
 ProductId = entity.ProductId,
 StudySectionId = entity.StudySectionId,


 ProductNormPriceId = model.ProductNormPriceId,
 ProductId = model.ProductId,
 StudySectionId = model.StudySectionId,


 temp.ProductNormPriceId = model.ProductNormPriceId,
 temp.ProductId = model.ProductId,
 temp.StudySectionId = model.StudySectionId,

 ProductStudySectionId = item.ProductStudySectionId,
 ProductNormPriceId = item.ProductNormPriceId,
 ProductId = item.ProductId,
 StudySectionId = item.StudySectionId,

public class ProductStudySectionInputModel
{
 [Display(Name = "Id")] 
    public long ProductStudySectionId {get; set; }
    
 [Display(Name = "产品规格价格Id")] 
    public long ProductNormPriceId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "学段Id")] 
    public int StudySectionId {get; set; }
    
 }
 
 public class ProductStudySectionViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ProductStudySectionId {get; set; }
    
    /// <summary>
    /// 产品规格价格Id
    /// </summary>
    public long ProductNormPriceId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 学段Id
    /// </summary>
    public int StudySectionId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductNormPriceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductNormPriceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品规格价格Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StudySectionId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StudySectionId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学段Id" } })                    
                </div>
           </div>
  




 { field: 'ProductNormPriceId', title: '产品规格价格Id', sortable: true },
                 
 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'StudySectionId', title: '学段Id', sortable: true },
                 
o.ProductNormPriceId,                 
o.ProductId,                 
o.StudySectionId,                 
        
        $('#ProductNormPriceId').val(d.data.rows.ProductNormPriceId);          
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#StudySectionId').val(d.data.rows.StudySectionId);          

 $('#th_ProductNormPriceId').html(' 产品规格价格Id');               
 $('#th_ProductId').html(' 产品Id');               
 $('#th_StudySectionId').html(' 学段Id');               
 
 $('#tr_ProductNormPriceId').hide();               
 $('#tr_ProductId').hide();               
 $('#tr_StudySectionId').hide();               

 , "ProductNormPriceId" : productNormPriceId
 , "ProductId" : productId
 , "StudySectionId" : studySectionId

 var productNormPriceId = $('#o_ProductNormPriceId').val();
 var productId = $('#o_ProductId').val();
 var studySectionId = $('#o_StudySectionId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品规格价格Id' : '产品名称', d.data.rows.ProductNormPriceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学段Id' : '产品名称', d.data.rows.StudySectionId);



