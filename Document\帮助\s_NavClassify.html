﻿ MallId = entity.MallId,
 Name = entity.Name,
 ParentId = entity.ParentId,
 Depth = entity.Depth,


 MallId = model.MallId,
 Name = model.Name,
 ParentId = model.ParentId,
 Depth = model.Depth,


 temp.MallId = model.MallId,
 temp.Name = model.Name,
 temp.ParentId = model.ParentId,
 temp.Depth = model.Depth,

 NavClassifyId = item.NavClassifyId,
 MallId = item.MallId,
 Name = item.Name,
 ParentId = item.ParentId,
 Depth = item.Depth,

public class NavClassifyInputModel
{
 [Display(Name = "id")] 
    public int NavClassifyId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "父级")] 
    public int ParentId {get; set; }
    
 [Display(Name = "深度")] 
    public int Depth {get; set; }
    
 }
 
 public class NavClassifyViewModel
 {
    /// <summary>
    /// id
    /// </summary>
    public int NavClassifyId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 父级
    /// </summary>
    public int ParentId {get; set; }
    
    /// <summary>
    /// 深度
    /// </summary>
    public int Depth {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ParentId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ParentId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父级" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入深度" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'ParentId', title: '父级', sortable: true },
                 
 { field: 'Depth', title: '深度', sortable: true },
                 
o.MallId,                 
o.Name,                 
o.ParentId,                 
o.Depth,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#Name').val(d.data.rows.Name);          
        $('#ParentId').val(d.data.rows.ParentId);          
        $('#Depth').val(d.data.rows.Depth);          

 $('#th_MallId').html(' 商城Id');               
 $('#th_Name').html(' 名称');               
 $('#th_ParentId').html(' 父级');               
 $('#th_Depth').html(' 深度');               
 
 $('#tr_MallId').hide();               
 $('#tr_Name').hide();               
 $('#tr_ParentId').hide();               
 $('#tr_Depth').hide();               

 , "MallId" : mallId
 , "Name" : name
 , "ParentId" : parentId
 , "Depth" : depth

 var mallId = $('#o_MallId').val();
 var name = $('#o_Name').val();
 var parentId = $('#o_ParentId').val();
 var depth = $('#o_Depth').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父级' : '产品名称', d.data.rows.ParentId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '深度' : '产品名称', d.data.rows.Depth);



