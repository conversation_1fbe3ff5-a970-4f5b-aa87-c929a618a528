﻿ UnitId = entity.UnitId,
 Name = entity.Name,
 SocialCreditCode = entity.SocialCreditCode,
 BusLicense = entity.BusLicense,
 ProvinceId = entity.ProvinceId,
 CityId = entity.CityId,
 CountyId = entity.CountyId,
 Address = entity.Address,
 Nature = entity.Nature,
 SchoolStage = entity.SchoolStage,
 BeLongUnit = entity.BeLongUnit,
 AuthStatuz = entity.AuthStatuz,
 Reason = entity.Reason,
 IsCurrent = entity.IsCurrent,
 MallId = entity.MallId,


 UnitId = model.UnitId,
 Name = model.Name,
 SocialCreditCode = model.SocialCreditCode,
 BusLicense = model.BusLicense,
 ProvinceId = model.ProvinceId,
 CityId = model.CityId,
 CountyId = model.CountyId,
 Address = model.Address,
 Nature = model.Nature,
 SchoolStage = model.SchoolStage,
 BeLongUnit = model.BeLongUnit,
 AuthStatuz = model.AuthStatuz,
 Reason = model.Reason,
 IsCurrent = model.IsCurrent,
 MallId = model.MallId,


 temp.UnitId = model.UnitId,
 temp.Name = model.Name,
 temp.SocialCreditCode = model.SocialCreditCode,
 temp.BusLicense = model.BusLicense,
 temp.ProvinceId = model.ProvinceId,
 temp.CityId = model.CityId,
 temp.CountyId = model.CountyId,
 temp.Address = model.Address,
 temp.Nature = model.Nature,
 temp.SchoolStage = model.SchoolStage,
 temp.BeLongUnit = model.BeLongUnit,
 temp.AuthStatuz = model.AuthStatuz,
 temp.Reason = model.Reason,
 temp.IsCurrent = model.IsCurrent,
 temp.MallId = model.MallId,

 SupplierAuditId = item.SupplierAuditId,
 UnitId = item.UnitId,
 Name = item.Name,
 SocialCreditCode = item.SocialCreditCode,
 BusLicense = item.BusLicense,
 ProvinceId = item.ProvinceId,
 CityId = item.CityId,
 CountyId = item.CountyId,
 Address = item.Address,
 Nature = item.Nature,
 SchoolStage = item.SchoolStage,
 BeLongUnit = item.BeLongUnit,
 AuthStatuz = item.AuthStatuz,
 Reason = item.Reason,
 IsCurrent = item.IsCurrent,
 MallId = item.MallId,

public class SupplierSchoolAuditInputModel
{
 [Display(Name = "Id")] 
    public int SupplierAuditId {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "企业名称")] 
    public string Name {get; set; }
    
 [Display(Name = "统一社会信用代码")] 
    public string SocialCreditCode {get; set; }
    
 [Display(Name = "营业执照")] 
    public string BusLicense {get; set; }
    
 [Display(Name = "省Id")] 
    public int ProvinceId {get; set; }
    
 [Display(Name = "市Id")] 
    public int CityId {get; set; }
    
 [Display(Name = "区Id")] 
    public int CountyId {get; set; }
    
 [Display(Name = "地址")] 
    public string Address {get; set; }
    
 [Display(Name = "学校性质(1：公办  2：民办)")] 
    public int Nature {get; set; }
    
 [Display(Name = "学段")] 
    public int SchoolStage {get; set; }
    
 [Display(Name = "是否市直属(1：是，0:否)")] 
    public bool BeLongUnit {get; set; }
    
 [Display(Name = "认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)")] 
    public int AuthStatuz {get; set; }
    
 [Display(Name = "不通过原因")] 
    public string Reason {get; set; }
    
 [Display(Name = "是否当前 0：否，1：是")] 
    public bool IsCurrent {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 }
 
 public class SupplierSchoolAuditViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int SupplierAuditId {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 企业名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string SocialCreditCode {get; set; }
    
    /// <summary>
    /// 营业执照
    /// </summary>
    public string BusLicense {get; set; }
    
    /// <summary>
    /// 省Id
    /// </summary>
    public int ProvinceId {get; set; }
    
    /// <summary>
    /// 市Id
    /// </summary>
    public int CityId {get; set; }
    
    /// <summary>
    /// 区Id
    /// </summary>
    public int CountyId {get; set; }
    
    /// <summary>
    /// 地址
    /// </summary>
    public string Address {get; set; }
    
    /// <summary>
    /// 学校性质(1：公办  2：民办)
    /// </summary>
    public int Nature {get; set; }
    
    /// <summary>
    /// 学段
    /// </summary>
    public int SchoolStage {get; set; }
    
    /// <summary>
    /// 是否市直属(1：是，0:否)
    /// </summary>
    public bool BeLongUnit {get; set; }
    
    /// <summary>
    /// 认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)
    /// </summary>
    public int AuthStatuz {get; set; }
    
    /// <summary>
    /// 不通过原因
    /// </summary>
    public string Reason {get; set; }
    
    /// <summary>
    /// 是否当前 0：否，1：是
    /// </summary>
    public bool IsCurrent {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int? MallId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入企业名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SocialCreditCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SocialCreditCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入统一社会信用代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BusLicense, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BusLicense, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入营业执照" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProvinceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProvinceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入省Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CityId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CityId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountyId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountyId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Address, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Address, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Nature, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Nature, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校性质(1：公办  2：民办)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolStage, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolStage, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学段" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BeLongUnit, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BeLongUnit, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否市直属(1：是，0:否)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuthStatuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuthStatuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Reason, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Reason, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入不通过原因" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsCurrent, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsCurrent, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否当前 0：否，1：是" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'Name', title: '企业名称', sortable: true },
                 
 { field: 'SocialCreditCode', title: '统一社会信用代码', sortable: true },
                 
 { field: 'BusLicense', title: '营业执照', sortable: true },
                 
 { field: 'ProvinceId', title: '省Id', sortable: true },
                 
 { field: 'CityId', title: '市Id', sortable: true },
                 
 { field: 'CountyId', title: '区Id', sortable: true },
                 
 { field: 'Address', title: '地址', sortable: true },
                 
 { field: 'Nature', title: '学校性质(1：公办  2：民办)', sortable: true },
                 
 { field: 'SchoolStage', title: '学段', sortable: true },
                 
 { field: 'BeLongUnit', title: '是否市直属(1：是，0:否)', sortable: true },
                 
 { field: 'AuthStatuz', title: '认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)', sortable: true },
                 
 { field: 'Reason', title: '不通过原因', sortable: true },
                 
 { field: 'IsCurrent', title: '是否当前 0：否，1：是', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
o.UnitId,                 
o.Name,                 
o.SocialCreditCode,                 
o.BusLicense,                 
o.ProvinceId,                 
o.CityId,                 
o.CountyId,                 
o.Address,                 
o.Nature,                 
o.SchoolStage,                 
o.BeLongUnit,                 
o.AuthStatuz,                 
o.Reason,                 
o.IsCurrent,                 
o.MallId,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#Name').val(d.data.rows.Name);          
        $('#SocialCreditCode').val(d.data.rows.SocialCreditCode);          
        $('#BusLicense').val(d.data.rows.BusLicense);          
        $('#ProvinceId').val(d.data.rows.ProvinceId);          
        $('#CityId').val(d.data.rows.CityId);          
        $('#CountyId').val(d.data.rows.CountyId);          
        $('#Address').val(d.data.rows.Address);          
        $('#Nature').val(d.data.rows.Nature);          
        $('#SchoolStage').val(d.data.rows.SchoolStage);          
        $('#BeLongUnit').val(d.data.rows.BeLongUnit);          
        $('#AuthStatuz').val(d.data.rows.AuthStatuz);          
        $('#Reason').val(d.data.rows.Reason);          
        $('#IsCurrent').val(d.data.rows.IsCurrent);          
        $('#MallId').val(d.data.rows.MallId);          

 $('#th_UnitId').html(' 单位Id');               
 $('#th_Name').html(' 企业名称');               
 $('#th_SocialCreditCode').html(' 统一社会信用代码');               
 $('#th_BusLicense').html(' 营业执照');               
 $('#th_ProvinceId').html(' 省Id');               
 $('#th_CityId').html(' 市Id');               
 $('#th_CountyId').html(' 区Id');               
 $('#th_Address').html(' 地址');               
 $('#th_Nature').html(' 学校性质(1：公办  2：民办)');               
 $('#th_SchoolStage').html(' 学段');               
 $('#th_BeLongUnit').html(' 是否市直属(1：是，0:否)');               
 $('#th_AuthStatuz').html(' 认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)');               
 $('#th_Reason').html(' 不通过原因');               
 $('#th_IsCurrent').html(' 是否当前 0：否，1：是');               
 $('#th_MallId').html(' 商城Id');               
 
 $('#tr_UnitId').hide();               
 $('#tr_Name').hide();               
 $('#tr_SocialCreditCode').hide();               
 $('#tr_BusLicense').hide();               
 $('#tr_ProvinceId').hide();               
 $('#tr_CityId').hide();               
 $('#tr_CountyId').hide();               
 $('#tr_Address').hide();               
 $('#tr_Nature').hide();               
 $('#tr_SchoolStage').hide();               
 $('#tr_BeLongUnit').hide();               
 $('#tr_AuthStatuz').hide();               
 $('#tr_Reason').hide();               
 $('#tr_IsCurrent').hide();               
 $('#tr_MallId').hide();               

 , "UnitId" : unitId
 , "Name" : name
 , "SocialCreditCode" : socialCreditCode
 , "BusLicense" : busLicense
 , "ProvinceId" : provinceId
 , "CityId" : cityId
 , "CountyId" : countyId
 , "Address" : address
 , "Nature" : nature
 , "SchoolStage" : schoolStage
 , "BeLongUnit" : beLongUnit
 , "AuthStatuz" : authStatuz
 , "Reason" : reason
 , "IsCurrent" : isCurrent
 , "MallId" : mallId

 var unitId = $('#o_UnitId').val();
 var name = $('#o_Name').val();
 var socialCreditCode = $('#o_SocialCreditCode').val();
 var busLicense = $('#o_BusLicense').val();
 var provinceId = $('#o_ProvinceId').val();
 var cityId = $('#o_CityId').val();
 var countyId = $('#o_CountyId').val();
 var address = $('#o_Address').val();
 var nature = $('#o_Nature').val();
 var schoolStage = $('#o_SchoolStage').val();
 var beLongUnit = $('#o_BeLongUnit').val();
 var authStatuz = $('#o_AuthStatuz').val();
 var reason = $('#o_Reason').val();
 var isCurrent = $('#o_IsCurrent').val();
 var mallId = $('#o_MallId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '企业名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '统一社会信用代码' : '产品名称', d.data.rows.SocialCreditCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '营业执照' : '产品名称', d.data.rows.BusLicense);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '省Id' : '产品名称', d.data.rows.ProvinceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市Id' : '产品名称', d.data.rows.CityId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区Id' : '产品名称', d.data.rows.CountyId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '地址' : '产品名称', d.data.rows.Address);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校性质(1：公办  2：民办)' : '产品名称', d.data.rows.Nature);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学段' : '产品名称', d.data.rows.SchoolStage);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否市直属(1：是，0:否)' : '产品名称', d.data.rows.BeLongUnit);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '认证状态(-1：未申请：0待认证 2：认证不通过  1：认证通过)' : '产品名称', d.data.rows.AuthStatuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '不通过原因' : '产品名称', d.data.rows.Reason);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否当前 0：否，1：是' : '产品名称', d.data.rows.IsCurrent);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);



