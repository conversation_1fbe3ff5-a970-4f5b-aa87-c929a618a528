﻿using System;
using System.Collections.Generic;
using Ninject;
using System.Web.Http.Dependencies;
using Dqy.Instrument.Configurations.Ninject;

namespace Dqy.Instrument.Api.App_Start
{
    public class ApiNinjectResolver : IDependencyResolver
    {
        private readonly IKernel _kernel;

        public ApiNinjectResolver()
            : this(new StandardKernel())
        {
        }

        public static IKernel CreateKernel()
        {
            return new ApiNinjectResolver()._kernel;
        }

        public ApiNinjectResolver(IKernel ninjectKernel)
        {
            _kernel = ninjectKernel;
            NinjectConfiguration.AddBindings(_kernel);

            //_kernel.Bind<HttpConfiguration>().ToConstant(GlobalConfiguration.Configuration).InCustomRequestScope();
            //_kernel.Bind<IControllerIdentificationDetector>().To<DefaultControllerIdentificationDetector>().InCustomRequestScope();
            //_kernel.Bind<IRequestControllerIdentificationDetector>().To<DefaultRequestControllerIdentificationDetector>().InCustomRequestScope();
        }

        public IDependencyScope BeginScope()
        {
            return this;
        }

        public object GetServiceOrDie(Type serviceType)
        {
            return _kernel.Get(serviceType);
        }

        public object GetService(Type serviceType)
        {
            return _kernel.TryGet(serviceType);
        }

        public T GetService<T>()
        {
            return _kernel.TryGet<T>();
        }

        public IEnumerable<object> GetServices(Type serviceType)
        {
            return _kernel.GetAll(serviceType);
        }

        public void Dispose()
        {
        }
    }
}
