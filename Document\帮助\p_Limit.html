﻿ CourseId = entity.CourseId,
 Year = entity.Year,
 Quota = entity.Quota,
 SchoolId = entity.SchoolId,
 UserId = entity.UserId,
 RegTime = entity.RegTime,


 CourseId = model.CourseId,
 Year = model.Year,
 Quota = model.Quota,
 SchoolId = model.SchoolId,
 UserId = model.UserId,
 RegTime = model.RegTime,


 temp.CourseId = model.CourseId,
 temp.Year = model.Year,
 temp.Quota = model.Quota,
 temp.SchoolId = model.SchoolId,
 temp.UserId = model.UserId,
 temp.RegTime = model.RegTime,

 LimitId = item.LimitId,
 CourseId = item.CourseId,
 Year = item.Year,
 Quota = item.Quota,
 SchoolId = item.SchoolId,
 UserId = item.UserId,
 RegTime = item.RegTime,

public class LimitInputModel
{
 [Display(Name = "配额Id")] 
    public int LimitId {get; set; }
    
 [Display(Name = "学科")] 
    public int CourseId {get; set; }
    
 [Display(Name = "年度")] 
    public string Year {get; set; }
    
 [Display(Name = "配额")] 
    public decimal Quota {get; set; }
    
 [Display(Name = "学校Id（UnitId）")] 
    public int SchoolId {get; set; }
    
 [Display(Name = "记录人")] 
    public long UserId {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class LimitViewModel
 {
    /// <summary>
    /// 配额Id
    /// </summary>
    public int LimitId {get; set; }
    
    /// <summary>
    /// 学科
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 年度
    /// </summary>
    public string Year {get; set; }
    
    /// <summary>
    /// 配额
    /// </summary>
    public decimal Quota {get; set; }
    
    /// <summary>
    /// 学校Id（UnitId）
    /// </summary>
    public int SchoolId {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Year, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Year, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入年度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Quota, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Quota, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入配额" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校Id（UnitId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
  




 { field: 'CourseId', title: '学科', sortable: true },
                 
 { field: 'Year', title: '年度', sortable: true },
                 
 { field: 'Quota', title: '配额', sortable: true },
                 
 { field: 'SchoolId', title: '学校Id（UnitId）', sortable: true },
                 
 { field: 'UserId', title: '记录人', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
o.CourseId,                 
o.Year,                 
o.Quota,                 
o.SchoolId,                 
o.UserId,                 
o.RegTime,                 
        
        $('#CourseId').val(d.data.rows.CourseId);          
        $('#Year').val(d.data.rows.Year);          
        $('#Quota').val(d.data.rows.Quota);          
        $('#SchoolId').val(d.data.rows.SchoolId);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_CourseId').html(' 学科');               
 $('#th_Year').html(' 年度');               
 $('#th_Quota').html(' 配额');               
 $('#th_SchoolId').html(' 学校Id（UnitId）');               
 $('#th_UserId').html(' 记录人');               
 $('#th_RegTime').html(' 记录时间');               
 
 $('#tr_CourseId').hide();               
 $('#tr_Year').hide();               
 $('#tr_Quota').hide();               
 $('#tr_SchoolId').hide();               
 $('#tr_UserId').hide();               
 $('#tr_RegTime').hide();               

 , "CourseId" : courseId
 , "Year" : year
 , "Quota" : quota
 , "SchoolId" : schoolId
 , "UserId" : userId
 , "RegTime" : regTime

 var courseId = $('#o_CourseId').val();
 var year = $('#o_Year').val();
 var quota = $('#o_Quota').val();
 var schoolId = $('#o_SchoolId').val();
 var userId = $('#o_UserId').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科' : '产品名称', d.data.rows.CourseId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '年度' : '产品名称', d.data.rows.Year);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '配额' : '产品名称', d.data.rows.Quota);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校Id（UnitId）' : '产品名称', d.data.rows.SchoolId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);



