﻿using Microsoft.AspNetCore.Http;
using System.Web.Http;
using Senparc.CO2NET.Cache;
using Senparc.CO2NET.Extensions;
using Senparc.Weixin.MP.MvcExtension;
using Senparc.Weixin.MP.Sample.CommonService.WxOpenMessageHandler;
using Senparc.Weixin.WxOpen.AdvancedAPIs.Sns;
using Senparc.Weixin.WxOpen.Containers;
using Senparc.Weixin.WxOpen.Entities;
using Senparc.Weixin.WxOpen.Entities.Request;
using Senparc.Weixin.WxOpen.Helpers;
using System;
using System.IO;
using Senparc.Weixin;
using Senparc.Weixin.MP.Sample.CommonService;
using Senparc.CO2NET.Utilities;
using System.Threading.Tasks;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp;
using Senparc.Weixin.MP;
using Senparc.Weixin.Entities.TemplateMessage;
using Senparc.CO2NET.AspNet.HttpUtility;
using System.Collections;
using Senparc.Weixin.Exceptions;
using Dqy.Instrument.UI.ViewModels;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.Api.Containers;
using SessionContainer = Dqy.Instrument.Api.Containers.SessionContainer;
using EncryptHelper = Dqy.Instrument.Api.Containers.EncryptHelpers;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.Api.Models;


namespace Dqy.Instrument.Api.Controllers.WxOpen
{
    /// <summary>
    /// 微信小程序Controller
    /// </summary>
    [RoutePrefix("api/wxopen")]
    public class WxOpenController : ApiController
    {
        public static readonly string Token = Config.SenparcWeixinSetting.WxOpenToken;//与微信小程序后台的Token设置保持一致，区分大小写。
        public static readonly string EncodingAESKey = Config.SenparcWeixinSetting.WxOpenEncodingAESKey;//与微信小程序后台的EncodingAESKey设置保持一致，区分大小写。
        public static readonly string WxOpenAppId = Config.SenparcWeixinSetting.WxOpenAppId;//与微信小程序后台的AppId设置保持一致，区分大小写。
        public static readonly string WxOpenAppSecret = Config.SenparcWeixinSetting.WxOpenAppSecret;//与微信小程序账号后台的AppId设置保持一致，区分大小写。
     
        readonly Func<string> _getRandomFileName = () => SystemTime.Now.ToString("yyyyMMdd-HHmmss") + Guid.NewGuid().ToString("n").Substring(0, 6);
        private IWxUserBindOpenidApplicationService _userBindOpenidApplicationService;
        private readonly IWxBindFromPcApplicationService _wxBindFromPcApplicationService;

        private IUUserApplicationService _userService;
        private ISMallApplicationService _mallService;
        public WxOpenController(IWxUserBindOpenidApplicationService iWxUserBindOpenid, IWxBindFromPcApplicationService wxBindFromPcApplicationService, IUUserApplicationService userService, ISMallApplicationService mallService)
        {
            _userBindOpenidApplicationService = iWxUserBindOpenid;
            _wxBindFromPcApplicationService = wxBindFromPcApplicationService;
            _userService = userService;
            _mallService = mallService;
        }
        /// <summary>
        /// 通过code的判断是否绑定账号，如果微信曾经绑定过账号，自动登录，并返回sessionId
        /// </summary>
        /// <param name="code">小程序code</param>
        /// <returns>验证结果，如果登录成功，返回用户信息，及sessionId（存放在msg中）</returns>
        [HttpGet]
        [Route("loginbycode")]
        public async Task<ReturnResult> GetLoginByCode([FromUri] string code)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var jsonResult = SnsApi.JsCode2Json(WxOpenAppId, WxOpenAppSecret, code);
                if (jsonResult.errcode == ReturnCode.请求成功)
                {
                    string where = string.Format(" IsDefaultLogin = 1 AND Statuz = 1 AND OpenId = '{0}'", jsonResult.openid);
                    var ubList = _userBindOpenidApplicationService.GetUserBindOpenidInfo(where);
                    if (ubList.Count > 0)
                    { 
                        long userId = 0;
                        userId = ubList[0].UserId;
                        int unitId = 0;
                        int unitType = 0;
                        int currentMallId = 1;
                        UserDetailViewModel u = _userService.GetUserById(userId);
                        UserCompanyViewModel uc = _userService.GetUserCompanyByUserId(userId);
                        if (uc != null)
                        {
                            unitId = uc.UnitId;
                            unitType = uc.UnitType;
                            uc.CurrentMallId = currentMallId;
                        }
                        r.flag = 1;
                        r.msg = "微信登录成功！";

                        var sessionBag = SessionContainer.UpdateSession(null, jsonResult.session_key, jsonResult.openid, u.UserId, u.UserType, u.LoginName, u.MallId, currentMallId, unitId, unitType);

                        uc.Key = sessionBag.Key;
                        r.data.rows = ComLib.Object2JSON(uc);
                        //登录日志
                        _userService.AddUserActionLog(new UserAcitonLogInputModel
                        {
                            UserId = u.UserId,
                            AccountName = u.LoginName,
                            SessionKey = sessionBag.Key,
                            Pwd = u.Pwd,
                            //UserIp = u.UserIp,
                            Type = 1,
                            DataFormat = DateTime.Now.ToString("yyyyMMdd"),
                            RegTime = DateTime.Now,
                        });
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "自动登录失败，未找到账号！";
                    }

                }
                else
                {
                    r.flag = 0;
                    r.msg = "code错误";
                }
                return r;
            });
            return result;

            
        }

        /// <summary>
        /// 微信小程序登并录绑定账号
        /// </summary>        
        /// <param name="loginInfo">微信登录信息</param>
        /// <returns>登录结果，如果登录成功，返回用户信息，及sessionId（存放在msg中）</returns>
        [HttpPost]
        [Route("loginbyaccount")]
        [AntiSqlInject]
        public async Task<ReturnResult> PostLoginByAccount([FromBody] WxLoginInfo loginInfo)
        {
            var result = await Task.Run(() =>
            {
                FileLog.SendLogin("loginbyaccount_begin:" + ComLib.Object2JSON(loginInfo));

                string loginName, pwd, code, sessionId = "";
                loginName = loginInfo.LoginName;
                pwd = loginInfo.Pwd;
                code = loginInfo.Code;
                sessionId = loginInfo.SessionId;

                ReturnResult r = new ReturnResult();
                r.flag = 0;
                r.msg = "绑定账号失败！";
                sessionId = sessionId == null ? "" : sessionId;
                var sessionBag = SessionContainer.GetSession(sessionId);
                sessionId = sessionBag == null ? "" : sessionId;

                try
                {
                    var jsonResult = SnsApi.JsCode2Json(WxOpenAppId, WxOpenAppSecret, code);
                    if (jsonResult.errcode == ReturnCode.请求成功)
                    {
                        FileLog.SendLogin("请求成功！" + ComLib.Object2JSON(jsonResult));                       
                        loginName = StringFilter.SearchSql(loginName);
                        UserDetailViewModel u = _userService.GetUserByLoginName(loginName);
                        if (u == null)
                        {
                            r.flag = 2;
                            r.msg = "账号或密码错误，登录失败";
                            return r;
                        }

                        string strUserPwd = SecurityHelper.MD5(pwd + u.Salt);
                        if (strUserPwd != u.Pwd)
                        {
                            r.flag = 3;
                            r.msg = "账号或密码错误，登录失败";
                            return r;
                        }

                        if (u.Statuz == 2 || u.Statuz == 0)
                        {
                            r.flag = 4;
                            r.msg = "您的账号被禁用，请联系管理员";
                            return r;
                        }
                        else if (u.Statuz == 3)
                        {
                            r.flag = 4;
                            r.msg = "您已经离职，再未加入新的单位之前，您将无法使用平台，请联系新单位管理员邀请您加入。";
                            return r;
                        }
                        if (DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd")) > u.ValidDate)
                        {
                            r.flag = 4;
                            r.msg = "您的使用期限已到，请联系管理员";
                            return r;
                        }

                        int unitId = 0;
                        int unitType = 0;
                        int currentMallId = 1;
                        UserCompanyViewModel uc = _userService.GetUserCompanyByUserId(u.UserId);
                        if (uc != null)
                        {
                            unitId = uc.UnitId;
                            unitType = uc.UnitType;
                            uc.CurrentMallId = currentMallId;
                        }

                        r = _userBindOpenidApplicationService.SaveUserBindOpenidInfo(new UserBindOpenidInputModel()
                        {
                            OpenId = jsonResult.openid,
                            UnionId = jsonResult.unionid,
                            UserId = u.UserId,
                            UnitId = unitId,
                        });
                        //r.flag=0绑定微信信息失败，即使失败，不影响用户登录使用，故此时无需处理r.flag=0

                        if (sessionId == "")
                            sessionBag = SessionContainer.UpdateSession(null, jsonResult.session_key, jsonResult.openid, u.UserId, u.UserType, u.LoginName, u.MallId, currentMallId, unitId, unitType);
                        else
                            sessionBag = SessionContainer.UpdateSession(sessionId, jsonResult.session_key, jsonResult.openid, u.UserId, u.UserType, u.LoginName, u.MallId, currentMallId, unitId, unitType);
                        uc.Key = sessionBag.Key;
                        r.data.rows = ComLib.Object2JSON(uc);
                        long id = Convert.ToInt64(r.data.id);
                        var wxbo = _userBindOpenidApplicationService.GetUserBindInfoById(id);
                       
                        if (wxbo != null && string.IsNullOrEmpty(wxbo.WxAccount))
                        {
                            r.msg = "WxAccount";
                        }
                        r.flag = 1;
                        //登录日志
                        _userService.AddUserActionLog(new UserAcitonLogInputModel
                        {
                            UserId = u.UserId,
                            AccountName = loginName,
                            SessionKey = sessionBag.Key,
                            Pwd = u.Pwd,
                            Type = 1,
                            DataFormat = DateTime.Now.ToString("yyyyMMdd"),
                            RegTime = DateTime.Now,
                        });


                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "code错误";
                    }                  
                }
                catch (Exception exp)
                {
                    r.flag = 0;
                    r.msg = exp.Message;
                    FileLog.SendLogin("loginbyaccount_error:" + exp.Message);
                }

                return r;

            });
            return result;
        }


        /// <summary>
        /// 小程序上传信息给第三方平台
        /// </summary>
        /// <param name="ecpt"> 小程序上传信息给第三方平台参数</param>      
        /// <returns></returns>
        [HttpPost]
        [Route("uploaduserinfo")]
        public async Task<ReturnResult> PostUserInfoData(WxDecodeEncryptedData ecpt)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                string type = ecpt.Type;
                string sessionId = ecpt.SessionId;
                string encryptedData = ecpt.EncryptedData;
                string iv = ecpt.Iv;

                r.flag = 0;
                r.msg = "";
                if (sessionId == null || sessionId.Length <= 0)//表示当前没有账号登录
                {
                    r.flag = -1;
                    r.msg = "登录超时。";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(sessionId);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时。";
                    return r;
                }
                try
                {
                    DecodedUserInfo decodedEntity = null;
                    switch (type.ToUpper())
                    {
                        case "USERINFO"://wx.getUserInfo()
                            decodedEntity = EncryptHelper.DecodeUserInfoBySessionId(
                                sessionId,
                                encryptedData, iv);
                            break;
                        default:
                            break;
                    }
                    //检验水印
                    var checkWartmark = false;
                    if (decodedEntity != null)
                    {
                        checkWartmark = decodedEntity.CheckWatermark_dqy(WxOpenAppId);
                    }
                    if (checkWartmark)
                    {
                        FileLog.SendLogin(ComLib.Object2JSON(decodedEntity));
                       _userBindOpenidApplicationService.UpdateWxAccount(sessionBag.OpenId, decodedEntity.nickName);
                        r.flag = 1; // 即使失败也不影响用户其他操作，未来登录时，可以再次绑定，所以将 r.flag强制为1.
                        r.msg = "解析成功。";
                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "解析失败。";
                    }
                }
                catch (Exception exp)
                {
                    r.flag = 0;
                    r.msg = exp.Message;
                    FileLog.SendLogin("uploaduserinfo_error:" + exp.Message);
                }
                return r;

            });
            return result;
        }

        /// <summary>
        /// 判断是否授权，1已授权；0：未授权
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getuserauth")]
        public async Task<int> GetAuthStatus([FromUri] string code)
        {
            var result = await Task.Run(() =>
            {
                int isAuth = 0;
                var jsonResult = SnsApi.JsCode2Json(WxOpenAppId, WxOpenAppSecret, code);
                if (jsonResult.errcode == ReturnCode.请求成功)
                {
                    string where = string.Format(" WxAccount is not null and len(WxAccount) > 0 and OpenId = '{0}'", jsonResult.openid);
                    var ubList = _userBindOpenidApplicationService.GetUserBindOpenidInfo(where);
                    if (ubList.Count > 0)
                    {
                        isAuth = 1;
                    }  
                }
               
                return isAuth;
            });
            return result;
        }

    
        /// <summary>
        /// 通过pc端扫码登录
        /// </summary>
        /// <param name="lqi"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("loginbyqrcode")]
        public async Task<ReturnResult> PostLoginByQrCode([FromBody] WxLoginQrcodeInfo loginInfo)
        {
            var result = await Task.Run(() =>
            {
                FileLog.SendLogin("loginbyqrcode_begin:" + ComLib.Object2JSON(loginInfo));

                string token, code, sessionId = "";
              
                code = loginInfo.Code;
                sessionId = loginInfo.SessionId;
                token = loginInfo.token;
                ReturnResult r = new ReturnResult();
                r.flag = 0;
                r.msg = "扫码登录失败！";
                sessionId = sessionId == null ? "" : sessionId;
                var sessionBag = SessionContainer.GetSession(sessionId);
                sessionId = sessionBag == null ? "" : sessionId;

                try
                {
                    var jsonResult = SnsApi.JsCode2Json(WxOpenAppId, WxOpenAppSecret, code);
                    if (jsonResult.errcode == ReturnCode.请求成功)
                    {
                        FileLog.SendLogin("code换取openid，请求成功！");
                        token = StringFilter.SearchSql(token);

                        var bfp = _wxBindFromPcApplicationService.GetByUserToken(token, loginInfo.UserId);
                        if(bfp == null)
                        {
                            r.flag = 2;
                            r.msg = "绑定失败，请刷新二维码后，重新扫码。";
                            return r;
                        }
                       
                        if(bfp.RegTime.AddMinutes(5) < DateTime.Now || bfp.Statuz != 0)
                        {
                            _wxBindFromPcApplicationService.Update(bfp.Id, "", 2);
                            r.flag = 2;
                            r.msg = "二维码已经失效，请刷新二维码后，重新扫码。";
                            return r;
                        }
                        var u = _userService.GetUserById(bfp.UserId);
                        if (u == null)
                        {
                            r.flag = 2;
                            r.msg = "账号不存在，绑定失败。";
                            return r;
                        }
                        _wxBindFromPcApplicationService.Update(bfp.Id, jsonResult.openid, 1);
                        int unitId = 0;
                        int unitType = 0;
                        int currentMallId = 1;
                        UserCompanyViewModel uc = _userService.GetUserCompanyByUserId(bfp.UserId);
                        if (uc != null)
                        {
                            unitId = uc.UnitId;
                            unitType = uc.UnitType;
                            uc.CurrentMallId = currentMallId;
                        }                       
                        if (sessionId == "")
                            sessionBag = SessionContainer.UpdateSession(null, jsonResult.session_key, jsonResult.openid, u.UserId, u.UserType, u.LoginName, u.MallId, currentMallId, unitId, unitType);
                        else
                            sessionBag = SessionContainer.UpdateSession(sessionId, jsonResult.session_key, jsonResult.openid, u.UserId, u.UserType, u.LoginName, u.MallId, currentMallId, unitId, unitType);
                        string nickName = "";
                        if (loginInfo.IsAuth == 0)
                        {
                            nickName = GetWxNickName(loginInfo.Type, sessionBag.Key, loginInfo.EncryptedData, loginInfo.Iv);
                        }                        
                        r = _userBindOpenidApplicationService.SaveUserBindOpenidInfo(new UserBindOpenidInputModel()
                        {
                            OpenId = jsonResult.openid,
                            UnionId = jsonResult.unionid,
                            UserId = bfp.UserId,
                            UnitId = unitId,
                            WxAccount = nickName
                        });
                        uc.Key = sessionBag.Key;
                        r.data.rows = ComLib.Object2JSON(uc);
                        long id = Convert.ToInt64(r.data.id);
                        var wxbo = _userBindOpenidApplicationService.GetUserBindInfoById(id);

                        if (wxbo != null && string.IsNullOrEmpty(wxbo.WxAccount))
                        {
                            r.msg = "WxAccount";
                        }
                        r.flag = 1;
                        //登录日志
                        _userService.AddUserActionLog(new UserAcitonLogInputModel
                        {
                            UserId = bfp.UserId,
                            AccountName = u.LoginName,
                            SessionKey = sessionBag.Key,                            
                            Type = 1,
                            DataFormat = DateTime.Now.ToString("yyyyMMdd"),
                            RegTime = DateTime.Now,
                        });

                    }
                    else
                    {
                        r.flag = 0;
                        r.msg = "code错误";
                    }
                }
                catch (Exception exp)
                {
                    r.flag = 0;
                    r.msg = exp.Message;
                    FileLog.SendLogin("loginbyaccount_error:" + exp.Message);
                }

                return r;

            });
            return result;
        }
              
        /// <summary>
        /// 获取微信昵称
        /// </summary>
        /// <param name="type"></param>
        /// <param name="sessionId"></param>
        /// <param name="encryptedData"></param>
        /// <param name="iv"></param>
        /// <returns></returns>
        private string GetWxNickName(string type,string sessionId, string encryptedData,  string iv )
        {
            string nickName = "";
            try
            {
                DecodedUserInfo decodedEntity = null;
                switch (type.ToUpper())
                {
                    case "USERINFO"://wx.getUserInfo()
                        decodedEntity = EncryptHelper.DecodeUserInfoBySessionId(
                            sessionId,
                            encryptedData, iv);
                        break;
                    default:
                        break;
                }
                //检验水印
                var checkWartmark = false;
                if (decodedEntity != null)
                {
                    checkWartmark = decodedEntity.CheckWatermark_dqy(WxOpenAppId);
                }
                if (checkWartmark)
                {
                    FileLog.SendLogin(ComLib.Object2JSON(decodedEntity));
                    nickName = decodedEntity.nickName;                    
                }               
            }
            catch (Exception exp)
            {
                FileLog.SendLogin(exp.Message);
            }
            return nickName;
        }

        /// <summary>
        /// 退出登录
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("quitlogin")]
        public async Task<ReturnResult> QuitLogin(string token)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                r = _userBindOpenidApplicationService.QuitLogin(sessionBag.UserId, sessionBag.OpenId);
                //记录日志
                string strLog = string.Format("openid:{0},userid:{1}", sessionBag.OpenId, sessionBag.UserId);
                FileLog.SendApiLog(string.Format("账号解绑 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}-{4}】", sessionBag.UnitId, sessionBag.UserId, strLog, r.flag, r.msg));
                return r;
            });
            return result;
        }
    }
}