﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.InputModels.SearchInputModel;
using Dqy.Instrument.UI.ViewModels;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class StatisticsController : ControllerMember
    {
        /// <summary>
        /// 学校学科采购额统计报表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> SchoolCourseStatistics(SerarchStatisticsByCourseSchoolInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (!string.IsNullOrEmpty(args.TimeSlot))
            {
                //拆分时间段
                string[] timeSlotAry = args.TimeSlot.Split(new string[] { " - " }, StringSplitOptions.RemoveEmptyEntries);
                if (timeSlotAry.Length > 1)
                {
                    timeSlotAry[1] = timeSlotAry[1] + " 23:59:59";
                    DateTime beginTime;
                    DateTime endTime;
                    DateTime.TryParse(timeSlotAry[0], out beginTime);
                    DateTime.TryParse(timeSlotAry[1], out endTime);
                    args.BeginTime = beginTime;
                    args.EndTime = endTime;
                }
            }
            else
            {
                string currentYearFirst = DateTime.Now.Year + "-1-1" + " 0:00:00";
                string nowDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59");
                args.BeginTime = DateTime.Parse(currentYearFirst);
                args.EndTime = DateTime.Parse(nowDate);
                args.TimeSlot = args.BeginTime.Value.ToString("yyyy-MM-dd") + " - " + args.EndTime.Value.ToString("yyyy-MM-dd");
            }
            string url = Constant.ApiPath + "statistics/getschoolcourse";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.StudySectionId = args.StudySectionId;
            ViewBag.UnitType = Operater.UnitType;
            return View(result);
        }

        /// <summary>
        /// 区县学科采购额统计报表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> CountyCourseStatistics(SerarchStatisticsByCourseSchoolInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (!string.IsNullOrEmpty(args.TimeSlot))
            {
                //拆分时间段
                string[] timeSlotAry = args.TimeSlot.Split(new string[] { " - " }, StringSplitOptions.RemoveEmptyEntries);
                if (timeSlotAry.Length > 1)
                {
                    timeSlotAry[1] = timeSlotAry[1] + " 23:59:59";
                    DateTime beginTime;
                    DateTime endTime;
                    DateTime.TryParse(timeSlotAry[0], out beginTime);
                    DateTime.TryParse(timeSlotAry[1], out endTime);
                    args.BeginTime = beginTime;
                    args.EndTime = endTime;
                }
            }
            else
            {
                string currentYearFirst = DateTime.Now.Year + "-1-1" + " 0:00:00";
                string nowDate = DateTime.Now.ToString("yyyy-MM-dd 23:59:59");
                args.BeginTime = DateTime.Parse(currentYearFirst);
                args.EndTime = DateTime.Parse(nowDate);
                args.TimeSlot = args.BeginTime.Value.ToString("yyyy-MM-dd") + " - " + args.EndTime.Value.ToString("yyyy-MM-dd");
            }
            string url = "statistics/getcountycourse";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.StudySectionId = args.StudySectionId;
            return View(result);
        }

        /// <summary>
        /// 按学科统计
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> SchoolSubjectStatistics(SearchStatisticsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            //默认显示近三年
            if (string.IsNullOrEmpty(args.Years) && !args.IsSearch)
            {
                List<int> listYears = new List<int>();
                int currentYear = DateTime.Now.Year;
                for (int i = 0; i < 3; i++)
                {
                    int sYear = currentYear - i;
                    if (sYear < 2018)
                    {
                        break;
                    }
                    listYears.Add(sYear);
                }
                args.Years = string.Join(",", listYears);
            }
            string url = Constant.ApiPath + "statistics/getschoolstatisticsbyyear";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            ViewBag.Year = args.Years;
            ViewBag.IsNotShowCourse = args.IsNotShowCourse;
            return View(result);
        }

        public async Task<ActionResult> SchoolByCounty(SearchStatisticsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (args.Years == null || args.Years == "")
            {
                //默认近三年
                var nowYear = DateTime.Now.Year;
                if (nowYear > 2018)
                {
                    if ((nowYear - 2018) == 1)
                    {
                        args.Years = "2018,2019";
                    }
                    else
                    {
                        args.Years = string.Format("{0},{1},{2}", nowYear, nowYear - 1, nowYear - 2);
                    }
                }
                else
                {
                    args.Years = "2018";
                }
            }
            string url = Constant.ApiPath + "statistics/getschoolbycounty";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);

            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Args = args;
            ViewBag.SchoolStage = args.SchoolStage;
            ViewBag.CourseId = args.CourseId;
            ViewBag.Year = args.Years;
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }

            return View(result);
        }

        public async Task<ActionResult> SupplierByCounty(SearchStatisticsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (args.Years == null || args.Years == "")
            {
                //默认近三年
                var nowYear = DateTime.Now.Year;
                if (nowYear > 2018)
                {
                    if ((nowYear - 2018) == 1)
                    {
                        args.Years = "2018,2019";
                    }
                    else
                    {
                        args.Years = string.Format("{0},{1},{2}", nowYear, nowYear - 1, nowYear - 2);
                    }
                }
                else
                {
                    args.Years = "2018";
                }
            }
            string url = Constant.ApiPath + "statistics/getsupplierbycounty";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Args = args;
            ViewBag.SchoolStage = args.SchoolStage;
            ViewBag.CourseId = args.CourseId;
            ViewBag.Year = args.Years;

            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }

            return View(result);
        }

        public async Task<ActionResult> SchoolByCity(SearchStatisticsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (args.Years == null || args.Years == "")
            {
                //默认近三年
                var nowYear = DateTime.Now.Year;
                if (nowYear > 2018)
                {
                    if ((nowYear - 2018) == 1)
                    {
                        args.Years = "2018,2019";
                    }
                    else
                    {
                        args.Years = string.Format("{0},{1},{2}", nowYear, nowYear - 1, nowYear - 2);
                    }
                }
                else
                {
                    args.Years = "2018";
                }
            }

            string url = Constant.ApiPath + "statistics/getschoolbycity";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Args = args;
            ViewBag.Year = args.Years;
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }

            return View(result);
        }

        public async Task<ActionResult> SupplierByCity(SearchStatisticsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (args.Years == null || args.Years == "")
            {
                //默认近三年
                var nowYear = DateTime.Now.Year;
                if (nowYear > 2018)
                {
                    if ((nowYear - 2018) == 1)
                    {
                        args.Years = "2018,2019";
                    }
                    else
                    {
                        args.Years = string.Format("{0},{1},{2}", nowYear, nowYear - 1, nowYear - 2);
                    }
                }
                else
                {
                    args.Years = "2018";
                }
            }
            string url = Constant.ApiPath + "statistics/getsupplierbycity";
            var result = await WebApiHelper.SendAsync<QueryResultStatistic>(url, args);
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.Args = args;
            ViewBag.Year = args.Years;

            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }

            return View(result);
        }

        /// <summary>
        /// 按明细查看
        /// add by jiangpeng 2018-12-4
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> SchoolCourseDetailStatistics(SearchStatisticsInputModel args)
        {
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (string.IsNullOrEmpty(args.RegTime))
            {
                string dtBegin = DateTime.Now.Year + "-" + DateTime.Now.Month.ToString().PadLeft(2,'0') + "-01" + " - " + DateTime.Now.ToString("yyyy-MM-dd");
                args.RegTime = dtBegin;
            }
            string url = Constant.ApiPath + "statistics/schoolcoursedetail";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductPurchasedViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            ViewBag.UnitType = Operater.UnitType;
            ViewBag.ApiPath = Constant.ApiPath;
            return View(result);
        }

        /// <summary>
        /// 导出按明细查看
        /// add by jiangpeng 2018-12-3 
        /// </summary>
        /// <returns></returns>
        public async Task<FileResult> ExportSchoolCourseDetailStatistics(SearchStatisticsInputModel args)
        {
            //先获取数据
            args.UserId = Operater.UserId;
            args.UnitId = Operater.UnitId;
            args.MallId = Operater.CurrentMallId;
            args.Token = Operater.Token;
            if (string.IsNullOrEmpty(args.RegTime))
            {

                string dtBegin = DateTime.Now.Year + "-" + DateTime.Now.Month + "-01" + " - " + DateTime.Now.ToString("yyyy-MM-dd");
                args.RegTime = dtBegin;
            }
            string url = Constant.ApiPath + "statistics/exportschoolcoursedetail";
            var result = await WebApiHelper.SendAsync<QueryResult<ProductPurchasedViewModel>>(url, args);
            if (result.flag > -1)
            {
                ICell cell = null;
                HSSFWorkbook book = new HSSFWorkbook();
                ISheet sheet = book.CreateSheet("Sheet1");

                IFont font = book.CreateFont();
                IFont font1 = book.CreateFont();
                font.IsBold = true;
                font1.FontHeightInPoints = 9;
                ICellStyle cellstyle = book.CreateCellStyle();
                cellstyle.BorderTop = BorderStyle.Thin;
                cellstyle.BorderLeft = BorderStyle.Thin;
                cellstyle.BorderRight = BorderStyle.Thin;
                cellstyle.BorderBottom = BorderStyle.Thin;
                cellstyle.Alignment = HorizontalAlignment.Center;
                cellstyle.VerticalAlignment = VerticalAlignment.Center;
                cellstyle.SetFont(font);

                //居中显示
                ICellStyle cellstyle2 = book.CreateCellStyle();
                cellstyle2.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle2.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                cellstyle2.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle2.SetFont(font1);

                //居左显示
                ICellStyle cellstyle3 = book.CreateCellStyle();
                cellstyle3.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellstyle3.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle3.SetFont(font1);

                //居右显示
                ICellStyle cellstyle1 = book.CreateCellStyle();
                cellstyle1.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellstyle1.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Right;
                cellstyle1.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellstyle1.SetFont(font1);

                //添加标题
                List<ExeclFeildHeadViewModel> listHead = new List<ExeclFeildHeadViewModel>();
                List<ExeclFeildHeadViewModel> listGet = ExeclHead.GetHeadSchoolCourseDetailStatistics();

                IRow row1 = sheet.CreateRow(1);
                row1.HeightInPoints = 25;

                listHead.AddRange(listGet);

                for (int i = 0; i < listHead.Count; i++)
                {
                    cell = row1.CreateCell(i + 1);
                    cell.CellStyle = cellstyle;
                    sheet.SetColumnWidth(i + 1, listHead[i].Width * 256);
                    cell.SetCellValue(listHead[i].Head);
                }

                int k = 0;
                IList<ProductPurchasedViewModel> list = result.Data;
                for (int i = 0; i < list.Count; i++)
                {
                    k = 0;
                    IRow rowTemp = sheet.CreateRow(i + 2);
                    rowTemp.HeightInPoints = 20;
                    k++;

                    //学校
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].SchoolName);
                    k++;

                    //学科
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle2;
                    cell.SetCellValue(list[i].CourseName);
                    k++;

                    //产品名称
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].ProductName);
                    k++;

                    //品牌
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].Brand);
                    k++;

                    //规格型号
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle3;
                    cell.SetCellValue(list[i].ModelDescription);
                    k++;

                    //单位
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle2;
                    cell.SetCellValue(list[i].UnitName);
                    k++;

                    //单价
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle1;
                    cell.SetCellValue(list[i].Price.ToString("f2"));
                    k++;

                    //数量
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle1;
                    cell.SetCellValue(list[i].Num.ToString());
                    k++;

                    //金额
                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle1;
                    cell.SetCellValue(list[i].Sum.ToString("f2"));
                    k++;

                    cell = rowTemp.CreateCell(k);
                    cell.CellStyle = cellstyle2;
                    //采购方式
                    if (list[i].MainPartType == UnitType.School.ToInt() && list[i].LastApprovalType == UnitType.School.ToInt())
                    {
                        cell.SetCellValue("学校自行采购");
                    }
                    else if (list[i].MainPartType == UnitType.School.ToInt() && list[i].LastApprovalType == UnitType.County.ToInt())
                    {
                        cell.SetCellValue("区县授权采购");
                    }
                    else if (list[i].MainPartType == UnitType.County.ToInt())
                    {
                        cell.SetCellValue("区县集中采购");
                    }
                    else
                    {
                        cell.SetCellValue("其他");
                    }
                    k++;
                }
                //写入客户端
                MemoryStream ms = new MemoryStream();
                book.Write(ms);
                ms.Seek(0, SeekOrigin.Begin);
                return File(ms, "application/vnd.ms-excel", "学校学科采购明细.xls");
            }
            else
            {
                return File("", "", "已采购产品.xls");
            }
        }
    }
}