﻿ Name = entity.Name,
 IsEnable = entity.IsEnable,
 Category = entity.Category,


 Name = model.Name,
 IsEnable = model.IsEnable,
 Category = model.Category,


 temp.Name = model.Name,
 temp.IsEnable = model.IsEnable,
 temp.Category = model.Category,

 CourseId = item.CourseId,
 Name = item.Name,
 IsEnable = item.IsEnable,
 Category = item.Category,

public class CourseInputModel
{
 [Display(Name = "Id")] 
    public int CourseId {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "是否启用(0：否 1：是)")] 
    public int IsEnable {get; set; }
    
 [Display(Name = "学科类别（1：学科仪器   2：低值易耗）")] 
    public int Category {get; set; }
    
 }
 
 public class CourseViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 是否启用(0：否 1：是)
    /// </summary>
    public int IsEnable {get; set; }
    
    /// <summary>
    /// 学科类别（1：学科仪器   2：低值易耗）
    /// </summary>
    public int Category {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsEnable, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsEnable, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否启用(0：否 1：是)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Category, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Category, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科类别（1：学科仪器   2：低值易耗）" } })                    
                </div>
           </div>
  




 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'IsEnable', title: '是否启用(0：否 1：是)', sortable: true },
                 
 { field: 'Category', title: '学科类别（1：学科仪器   2：低值易耗）', sortable: true },
                 
o.Name,                 
o.IsEnable,                 
o.Category,                 
        
        $('#Name').val(d.data.rows.Name);          
        $('#IsEnable').val(d.data.rows.IsEnable);          
        $('#Category').val(d.data.rows.Category);          

 $('#th_Name').html(' 名称');               
 $('#th_IsEnable').html(' 是否启用(0：否 1：是)');               
 $('#th_Category').html(' 学科类别（1：学科仪器   2：低值易耗）');               
 
 $('#tr_Name').hide();               
 $('#tr_IsEnable').hide();               
 $('#tr_Category').hide();               

 , "Name" : name
 , "IsEnable" : isEnable
 , "Category" : category

 var name = $('#o_Name').val();
 var isEnable = $('#o_IsEnable').val();
 var category = $('#o_Category').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否启用(0：否 1：是)' : '产品名称', d.data.rows.IsEnable);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科类别（1：学科仪器   2：低值易耗）' : '产品名称', d.data.rows.Category);



