﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Services.ImplementedInterfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Dqy.Instrument.Api.CommonLib
{
    public class ProductRecordInsert
    {
        private readonly IPProductRecordApplicationService _recordService;
        public ProductRecordInsert(IPProductRecordApplicationService pras)
        {
            _recordService = pras;
        }
        public void Insert(long productId, ProductAction action, Default statuz, long userId = 0, long auditId = 0, long shelf = 0, string reason = "")
        {
            _recordService.Insert(productId, action, statuz, userId, auditId, shelf, reason);
        }

        public void InsertBatch(string productIds, ProductAction action, Default statuz, long userId = 0, long auditId = 0, string shelfs = "", string reason = "")
        {
            if (productIds != null && productIds.Length > 0)
            {
                var ids = productIds.Split(',');
                foreach (var item in ids)
                {
                    long id = 0;
                    if (long.TryParse(item,out id))
                    {
                        _recordService.Insert(id, action, statuz, userId, auditId, 0, reason);
                    } 
                }
            }
            else if (shelfs != null && shelfs.Length > 0)
            {
                var ids = shelfs.Split(',');
                foreach (var item in ids)
                {
                    long id = 0;
                    if (long.TryParse(item, out id))
                    {
                        _recordService.Insert(0, action, statuz, userId, auditId, id, reason);
                    }
                }
            }
        }

    }



    public static class ProductRecord
    {
        private static ProductQueue product = new ProductQueue();

        public static void Inser(object obj)
        {
            product.Inser(obj);
        } 
    }
    public class ProductQueue
    {
        private Queue<object> queue = new Queue<object>();

        public void Inser(object obj)
        {
            if (queue == null)
            {
                queue = new Queue<object>();
            }
            queue.Enqueue(obj);
        }



   

         
        public void GetDequeue()
        {
            if (queue.Count > 0)
            {
                queue.Dequeue();
            }
        }

    }

}