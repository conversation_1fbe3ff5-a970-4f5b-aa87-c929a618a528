﻿using Dqy.Instrument.Api.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Net.Http;
using System.Threading.Tasks;
using System.IO;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Api.Containers;

namespace Dqy.Instrument.Api.Controllers
{
    public class CkfinderController : Controller
    {
        private static readonly string ApiPath = ComLib.GetAppSetting<string>("Api.Path");
        /// <summary>
        /// 可上传文件类型
        /// </summary>
        public static readonly string FILE_UPLOAD_EXT_ALLOW = ComLib.GetAppSetting<string>("File.Upload.Ext.Allow.Pic");

        /// <summary>
        /// 可上传文件类型编码
        /// </summary>
        public static readonly string FILE_UPLOAD_EXTCODE_ALLOW = ComLib.GetAppSetting<string>("File.Upload.ExtCode.Allow.Pic");
        // GET: Ckfinder
        public  ActionResult  Upload()
        {
            HttpFileCollectionBase files = Request.Files;
            string returnUrl = Request.QueryString["p"];
            string token = Request.QueryString["t"];
            //非运营管理平台请求，需验证权限
            if(Request.UrlReferrer.Host != ComLib.GetAppSetting<string>("Manage.Server.Domain"))
            {
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    this.Response.Clear();
                    this.Response.BufferOutput = true;
                    this.Response.Redirect(returnUrl + "?e=未经授权，非法访问.");
                    return View();
                }
            }
            if (files.Count > 0)
            {             
                HttpPostedFileBase file = files[0]; 
                
                string uploadPath = "/UploadFile/" + DateTime.Now.Year + "/" + DateTime.Now.Month + "/";
                string orfilename = file.FileName;
                string fileExt = orfilename.Substring(orfilename.LastIndexOf('.'));

                if (!FILE_UPLOAD_EXT_ALLOW.Contains(fileExt.ToLower() + "."))
                {
                    this.Response.Clear();
                    this.Response.BufferOutput = true;
                    this.Response.Redirect(returnUrl + "?e=非法文件，上传失败！");
                    return View();
                }

                string fileclass = "";
                try
                {
                    fileclass = file.InputStream.ReadByte().ToString();
                    fileclass += file.InputStream.ReadByte().ToString();
                }
                catch
                {

                }
                if (!FILE_UPLOAD_EXTCODE_ALLOW.Contains("." + fileclass + "."))
                {
                    this.Response.Clear();
                    this.Response.BufferOutput = true;
                    this.Response.Redirect(returnUrl + "?e=非法文件，上传失败！");
                    return View();
                }
                    string strSaveName = DateTime.Now.ToString("yyyyMMddHHmmss") + Guid.NewGuid().ToString("n").Substring(0, 6);
                string strNewFileName = strSaveName + fileExt;
                string strMapPath = Server.MapPath("~" + uploadPath);
                if (!Directory.Exists(strMapPath))
                {
                    Directory.CreateDirectory(strMapPath);
                }

                file.SaveAs(strMapPath + strNewFileName);

                string filePath = uploadPath + strNewFileName;
                this.Response.Clear();
                this.Response.BufferOutput = true;
                this.Response.Redirect(returnUrl + "?p=" + ApiPath + filePath);
            }
            else
            {
                this.Response.Clear();
                this.Response.BufferOutput = true;
                this.Response.Redirect(returnUrl + "?e=上传失败.");
            }

            return View();
        }
    }
}