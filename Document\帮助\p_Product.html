﻿ InstrumentLogicId = entity.InstrumentLogicId,
 ThreeGradeLogicId = entity.ThreeGradeLogicId,
 Code = entity.Code,
 Name = entity.Name,
 WarrantyMonth = entity.WarrantyMonth,
 Manufacturer = entity.Manufacturer,
 Brand = entity.Brand,
 UnitName = entity.UnitName,
 Detail = entity.Detail,
 UnitId = entity.UnitId,
 IsCore = entity.IsCore,
 EntryTime = entity.EntryTime,
 UserId = entity.UserId,
 ModifyBatch = entity.ModifyBatch,
 Statuz = entity.Statuz,
 SourceProductId = entity.SourceProductId,
 CourseName = entity.CourseName,


 InstrumentLogicId = model.InstrumentLogicId,
 ThreeGradeLogicId = model.ThreeGradeLogicId,
 Code = model.Code,
 Name = model.Name,
 WarrantyMonth = model.WarrantyMonth,
 Manufacturer = model.Manufacturer,
 Brand = model.Brand,
 UnitName = model.UnitName,
 Detail = model.Detail,
 UnitId = model.UnitId,
 IsCore = model.IsCore,
 EntryTime = model.EntryTime,
 UserId = model.UserId,
 ModifyBatch = model.ModifyBatch,
 Statuz = model.Statuz,
 SourceProductId = model.SourceProductId,
 CourseName = model.CourseName,


 temp.InstrumentLogicId = model.InstrumentLogicId,
 temp.ThreeGradeLogicId = model.ThreeGradeLogicId,
 temp.Code = model.Code,
 temp.Name = model.Name,
 temp.WarrantyMonth = model.WarrantyMonth,
 temp.Manufacturer = model.Manufacturer,
 temp.Brand = model.Brand,
 temp.UnitName = model.UnitName,
 temp.Detail = model.Detail,
 temp.UnitId = model.UnitId,
 temp.IsCore = model.IsCore,
 temp.EntryTime = model.EntryTime,
 temp.UserId = model.UserId,
 temp.ModifyBatch = model.ModifyBatch,
 temp.Statuz = model.Statuz,
 temp.SourceProductId = model.SourceProductId,
 temp.CourseName = model.CourseName,

 ProductId = item.ProductId,
 InstrumentLogicId = item.InstrumentLogicId,
 ThreeGradeLogicId = item.ThreeGradeLogicId,
 Code = item.Code,
 Name = item.Name,
 WarrantyMonth = item.WarrantyMonth,
 Manufacturer = item.Manufacturer,
 Brand = item.Brand,
 UnitName = item.UnitName,
 Detail = item.Detail,
 UnitId = item.UnitId,
 IsCore = item.IsCore,
 EntryTime = item.EntryTime,
 UserId = item.UserId,
 ModifyBatch = item.ModifyBatch,
 Statuz = item.Statuz,
 SourceProductId = item.SourceProductId,
 CourseName = item.CourseName,

public class ProductInputModel
{
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "当前分类逻辑库Id")] 
    public int InstrumentLogicId {get; set; }
    
 [Display(Name = "三级分类逻辑库Id")] 
    public int ThreeGradeLogicId {get; set; }
    
 [Display(Name = "产品代码")] 
    public string Code {get; set; }
    
 [Display(Name = "产品名称")] 
    public string Name {get; set; }
    
 [Display(Name = "质保(月)")] 
    public int WarrantyMonth {get; set; }
    
 [Display(Name = "制造商全称")] 
    public string Manufacturer {get; set; }
    
 [Display(Name = "品牌")] 
    public string Brand {get; set; }
    
 [Display(Name = "单位")] 
    public string UnitName {get; set; }
    
 [Display(Name = "产品详情")] 
    public string Detail {get; set; }
    
 [Display(Name = "所属单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "是否为核心产品(0：否 1：是)")] 
    public int IsCore {get; set; }
    
 [Display(Name = "录入时间")] 
    public DateTime EntryTime {get; set; }
    
 [Display(Name = "录入人Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "产品修改批次")] 
    public int ModifyBatch {get; set; }
    
 [Display(Name = "产品状态（0：临时保存 1：已入库  2：已删除 ）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "源产品Id")] 
    public long SourceProductId {get; set; }
    
 [Display(Name = "学科名称（多个学科以逗号分隔）")] 
    public string CourseName {get; set; }
    
 }
 
 public class ProductViewModel
 {
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 当前分类逻辑库Id
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
    /// <summary>
    /// 三级分类逻辑库Id
    /// </summary>
    public int ThreeGradeLogicId {get; set; }
    
    /// <summary>
    /// 产品代码
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 质保(月)
    /// </summary>
    public int WarrantyMonth {get; set; }
    
    /// <summary>
    /// 制造商全称
    /// </summary>
    public string Manufacturer {get; set; }
    
    /// <summary>
    /// 品牌
    /// </summary>
    public string Brand {get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName {get; set; }
    
    /// <summary>
    /// 产品详情
    /// </summary>
    public string Detail {get; set; }
    
    /// <summary>
    /// 所属单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 是否为核心产品(0：否 1：是)
    /// </summary>
    public int IsCore {get; set; }
    
    /// <summary>
    /// 录入时间
    /// </summary>
    public DateTime EntryTime {get; set; }
    
    /// <summary>
    /// 录入人Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 产品修改批次
    /// </summary>
    public int ModifyBatch {get; set; }
    
    /// <summary>
    /// 产品状态（0：临时保存 1：已入库  2：已删除 ）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 源产品Id
    /// </summary>
    public long SourceProductId {get; set; }
    
    /// <summary>
    /// 学科名称（多个学科以逗号分隔）
    /// </summary>
    public string CourseName {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入当前分类逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ThreeGradeLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ThreeGradeLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入三级分类逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.WarrantyMonth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.WarrantyMonth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入质保(月)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Manufacturer, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Manufacturer, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入制造商全称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Brand, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Brand, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入品牌" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Detail, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Detail, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品详情" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入所属单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsCore, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsCore, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否为核心产品(0：否 1：是)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EntryTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EntryTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入录入时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入录入人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ModifyBatch, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ModifyBatch, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品修改批次" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品状态（0：临时保存 1：已入库  2：已删除 ）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SourceProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SourceProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入源产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科名称（多个学科以逗号分隔）" } })                    
                </div>
           </div>
  




 { field: 'InstrumentLogicId', title: '当前分类逻辑库Id', sortable: true },
                 
 { field: 'ThreeGradeLogicId', title: '三级分类逻辑库Id', sortable: true },
                 
 { field: 'Code', title: '产品代码', sortable: true },
                 
 { field: 'Name', title: '产品名称', sortable: true },
                 
 { field: 'WarrantyMonth', title: '质保(月)', sortable: true },
                 
 { field: 'Manufacturer', title: '制造商全称', sortable: true },
                 
 { field: 'Brand', title: '品牌', sortable: true },
                 
 { field: 'UnitName', title: '单位', sortable: true },
                 
 { field: 'Detail', title: '产品详情', sortable: true },
                 
 { field: 'UnitId', title: '所属单位Id', sortable: true },
                 
 { field: 'IsCore', title: '是否为核心产品(0：否 1：是)', sortable: true },
                 
 { field: 'EntryTime', title: '录入时间', sortable: true },
                 
 { field: 'UserId', title: '录入人Id', sortable: true },
                 
 { field: 'ModifyBatch', title: '产品修改批次', sortable: true },
                 
 { field: 'Statuz', title: '产品状态（0：临时保存 1：已入库  2：已删除 ）', sortable: true },
                 
 { field: 'SourceProductId', title: '源产品Id', sortable: true },
                 
 { field: 'CourseName', title: '学科名称（多个学科以逗号分隔）', sortable: true },
                 
o.InstrumentLogicId,                 
o.ThreeGradeLogicId,                 
o.Code,                 
o.Name,                 
o.WarrantyMonth,                 
o.Manufacturer,                 
o.Brand,                 
o.UnitName,                 
o.Detail,                 
o.UnitId,                 
o.IsCore,                 
o.EntryTime,                 
o.UserId,                 
o.ModifyBatch,                 
o.Statuz,                 
o.SourceProductId,                 
o.CourseName,                 
        
        $('#InstrumentLogicId').val(d.data.rows.InstrumentLogicId);          
        $('#ThreeGradeLogicId').val(d.data.rows.ThreeGradeLogicId);          
        $('#Code').val(d.data.rows.Code);          
        $('#Name').val(d.data.rows.Name);          
        $('#WarrantyMonth').val(d.data.rows.WarrantyMonth);          
        $('#Manufacturer').val(d.data.rows.Manufacturer);          
        $('#Brand').val(d.data.rows.Brand);          
        $('#UnitName').val(d.data.rows.UnitName);          
        $('#Detail').val(d.data.rows.Detail);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#IsCore').val(d.data.rows.IsCore);          
        $('#EntryTime').val(d.data.rows.EntryTime);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#ModifyBatch').val(d.data.rows.ModifyBatch);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#SourceProductId').val(d.data.rows.SourceProductId);          
        $('#CourseName').val(d.data.rows.CourseName);          

 $('#th_InstrumentLogicId').html(' 当前分类逻辑库Id');               
 $('#th_ThreeGradeLogicId').html(' 三级分类逻辑库Id');               
 $('#th_Code').html(' 产品代码');               
 $('#th_Name').html(' 产品名称');               
 $('#th_WarrantyMonth').html(' 质保(月)');               
 $('#th_Manufacturer').html(' 制造商全称');               
 $('#th_Brand').html(' 品牌');               
 $('#th_UnitName').html(' 单位');               
 $('#th_Detail').html(' 产品详情');               
 $('#th_UnitId').html(' 所属单位Id');               
 $('#th_IsCore').html(' 是否为核心产品(0：否 1：是)');               
 $('#th_EntryTime').html(' 录入时间');               
 $('#th_UserId').html(' 录入人Id');               
 $('#th_ModifyBatch').html(' 产品修改批次');               
 $('#th_Statuz').html(' 产品状态（0：临时保存 1：已入库  2：已删除 ）');               
 $('#th_SourceProductId').html(' 源产品Id');               
 $('#th_CourseName').html(' 学科名称（多个学科以逗号分隔）');               
 
 $('#tr_InstrumentLogicId').hide();               
 $('#tr_ThreeGradeLogicId').hide();               
 $('#tr_Code').hide();               
 $('#tr_Name').hide();               
 $('#tr_WarrantyMonth').hide();               
 $('#tr_Manufacturer').hide();               
 $('#tr_Brand').hide();               
 $('#tr_UnitName').hide();               
 $('#tr_Detail').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_IsCore').hide();               
 $('#tr_EntryTime').hide();               
 $('#tr_UserId').hide();               
 $('#tr_ModifyBatch').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_SourceProductId').hide();               
 $('#tr_CourseName').hide();               

 , "InstrumentLogicId" : instrumentLogicId
 , "ThreeGradeLogicId" : threeGradeLogicId
 , "Code" : code
 , "Name" : name
 , "WarrantyMonth" : warrantyMonth
 , "Manufacturer" : manufacturer
 , "Brand" : brand
 , "UnitName" : unitName
 , "Detail" : detail
 , "UnitId" : unitId
 , "IsCore" : isCore
 , "EntryTime" : entryTime
 , "UserId" : userId
 , "ModifyBatch" : modifyBatch
 , "Statuz" : statuz
 , "SourceProductId" : sourceProductId
 , "CourseName" : courseName

 var instrumentLogicId = $('#o_InstrumentLogicId').val();
 var threeGradeLogicId = $('#o_ThreeGradeLogicId').val();
 var code = $('#o_Code').val();
 var name = $('#o_Name').val();
 var warrantyMonth = $('#o_WarrantyMonth').val();
 var manufacturer = $('#o_Manufacturer').val();
 var brand = $('#o_Brand').val();
 var unitName = $('#o_UnitName').val();
 var detail = $('#o_Detail').val();
 var unitId = $('#o_UnitId').val();
 var isCore = $('#o_IsCore').val();
 var entryTime = $('#o_EntryTime').val();
 var userId = $('#o_UserId').val();
 var modifyBatch = $('#o_ModifyBatch').val();
 var statuz = $('#o_Statuz').val();
 var sourceProductId = $('#o_SourceProductId').val();
 var courseName = $('#o_CourseName').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '当前分类逻辑库Id' : '产品名称', d.data.rows.InstrumentLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '三级分类逻辑库Id' : '产品名称', d.data.rows.ThreeGradeLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品代码' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '质保(月)' : '产品名称', d.data.rows.WarrantyMonth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '制造商全称' : '产品名称', d.data.rows.Manufacturer);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '品牌' : '产品名称', d.data.rows.Brand);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位' : '产品名称', d.data.rows.UnitName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品详情' : '产品名称', d.data.rows.Detail);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '所属单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否为核心产品(0：否 1：是)' : '产品名称', d.data.rows.IsCore);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '录入时间' : '产品名称', d.data.rows.EntryTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '录入人Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品修改批次' : '产品名称', d.data.rows.ModifyBatch);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品状态（0：临时保存 1：已入库  2：已删除 ）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '源产品Id' : '产品名称', d.data.rows.SourceProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科名称（多个学科以逗号分隔）' : '产品名称', d.data.rows.CourseName);



