<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PlainElastic.Net</name>
    </assembly>
    <members>
        <member name="T:PlainElastic.Net.BulkBuilder">
            <summary>
            Allows to generate JSON to perform many index/delete operations in a single API call.
            This can greatly increase the indexing speed.
            see http://www.elasticsearch.org/guide/reference/api/bulk.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.BulkBuilder.Index(System.Object,System.String,System.String,System.String,PlainElastic.Net.BulkOperationOptions,System.String)">
            <summary>
            Builds a Bulk JSON that allows to add or update custom Json document in specified Index.
            If no index and type parameters specified, they will be taken from BulkCommand parameters.
            </summary>
            <param name="data">The document to add or update in ES index.</param>
            <param name="index">The ES index name, if not specified will be taken from BulkCommand endpoint address.</param>
            <param name="type">The ES type name, if not specified will be taken from BulkCommand endpoint address.</param>
            <param name="id">The document id, if not specified will be generated automatically by ES (This will add new document).</param>
            <param name="options">The bulk operation options.</param>
            <param name="customOptions">The custom JSON options for create operation.
             Options should be comma separated string of values:
            <c>"_index": "test", "_type": "first", "_id": 1, "_version": 100</c>
            </param>
        </member>
        <member name="M:PlainElastic.Net.BulkBuilder.Create(System.Object,System.String,System.String,System.String,PlainElastic.Net.BulkOperationOptions,System.String)">
            <summary>
            Builds a Bulk JSON that allows to add custom Json document in specified Index.
            If no index and type parameters specified, they will be taken from BulkCommand parameters.
            </summary>
            <param name="data">The document to add to ES index.</param>
            <param name="index">The ES index name, if not specified will be taken from BulkCommand endpoint address.</param>
            <param name="type">The ES type name, if not specified will be taken from BulkCommand endpoint address.</param>
            <param name="id">The document id, if not specified will be generated automatically by ES.</param>
            <param name="options">The bulk operation options.</param>
            <param name="customOptions">The custom JSON options for create operation.
             Options should be comma separated string of values:
            <c>"_index": "test", "_type": "first", "_id": 1, "_version": 100</c>
            </param>
        </member>
        <member name="M:PlainElastic.Net.BulkBuilder.Delete(System.String,System.String,System.String,PlainElastic.Net.BulkOperationOptions,System.String)">
            <summary>
            Builds a Bulk JSON that allows to remove custom Json document from specified Index.
            If no index and type parameters specified, they will be taken from BulkCommand parameters.
            </summary>
            <param name="index">The ES index name, if not specified will be taken from BulkCommand endpoint address.</param>
            <param name="type">The ES type name, if not specified will be taken from BulkCommand endpoint address.</param>
            <param name="id">The Id of document to remove from ES index.</param>
            <param name="options">The bulk operation options.</param>
            <param name="customOptions">The custom JSON options for create operation.
             Options should be comma separated string of values:
            <c>"_index": "test", "_type": "first", "_id": 1, "_version": 100</c>
            </param>
        </member>
        <member name="M:PlainElastic.Net.BulkBuilder.BuildCollection``1(System.Collections.Generic.IEnumerable{``0},System.Func{PlainElastic.Net.BulkBuilder,``0,System.String})">
            <summary>
            Returns complete Bulk JSON with all generated bulk operations.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.BulkBuilder.PipelineCollection``1(System.Collections.Generic.IEnumerable{``0},System.Func{PlainElastic.Net.BulkBuilder,``0,System.String})">
            <summary>
            Returns deferred Bulk JSONs IEnumerable with one bulk operation in each element,
            this will allow to process input elements on-the-fly - not generating all bulk JSON at once.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.VersionType">
            <summary>
            Allows to enable external versioning.
            By default internal versioning used, to supplemented version number with an external value
            (for example, if maintained in a database), version_type should be set to external
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.Version">
            <summary>
            Allows to specify document version that will be used to 
            provide optimistic concurrency control.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.Routing">
            <summary>
            Allows explicit control over the value fed into the hash function used by the router.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.Percolate">
            <summary>
            Allows to filter percolator queries that will be executed.
            Use the query string syntax to the percolate parameter.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.Parent">
            <summary>
            Allows to specify parent document ID.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.Timestamp">
            <summary>
            Allows to specify a timestamp associated with document.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.BulkOperationOptions.Ttl">
            <summary>
            Allows to specify ttl (time to live) associated with document.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.CloseCommand">
            <summary>
            Builds a command that allows to close an index. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-open-close.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.BulkCommand">
            <summary>
            Builds a command that allows to perform many index/delete operations in a single API call.
            This can greatly increase the indexing speed. 
            http://www.elasticsearch.org/guide/reference/api/bulk.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.BulkCommand.Consistency(PlainElastic.Net.WriteConsistency)">
            <summary>
            Requires a minimum number of active shards in the partition.
            It defaults to the node level setting of action.write_consistency, which in turn defaults to quorum.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.BulkCommand.Replication(PlainElastic.Net.DocumentReplication)">
            <summary>
            Allows to control Index operation replication.
            By default, the index operation only returns after all shards within the replication group have indexed the document (sync replication).
            To enable asynchronous replication, causing the replication process to take place in the background, 
            set the replication parameter to async. 
            When asynchronous replication is used, the index operation will return as soon as the operation succeeds on the primary shard.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.BulkCommand.Refresh(System.Boolean)">
            <summary>
            Allows to refresh the relevant shards immediately after the bulk operation has occurred and make it searchable,
            instead of waiting for the normal refresh interval to expire.
            Setting it to true can trigger additional load, and may slow down indexing.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.DeleteByQueryCommand">
            <summary>
            Builds a command that allows to delete documents from one or more indices and one or more types based on a query.
            http://www.elasticsearch.org/guide/reference/api/delete-by-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Q(System.String)">
            <summary>
            The query string (maps to the query_string query).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Analyzer(System.String)">
            <summary>
            The analyzer name to be used when analyzing the query string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer name to be used when analyzing the query string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Df(System.String)">
            <summary>
            The default field to use when no field prefix is defined within the query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.DefaultOperator(PlainElastic.Net.Operator)">
            <summary>
            The default operator to be used, can be AND or OR. Defaults to OR.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Timeout(System.String)">
            <summary>
             A timeout to wait if the index operation can't be performed immediately. Defaults to <tt>1m</tt>.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Refresh(System.Boolean)">
            <summary>
            Allows to refresh the relevant shard after the delete operation has occurred and make it searchable. 
            Setting it to true should be done after careful thought and verification
            that this does not cause a heavy load on the system (and slows down indexing).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Replication(PlainElastic.Net.DocumentReplication)">
            <summary>
            Allows to define the sync or async replication of the operation 
            When async the operation will return once it has be executed on the primary shard.
            The replication parameter can be set to async (defaults to sync) in order to enable it.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Consistency(PlainElastic.Net.WriteConsistency)">
            <summary>
            Requires a minimum number of active shards in the partition.
            It defaults to the node level setting of action.write_consistency, which in turn defaults to quorum.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteByQueryCommand.Routing(System.String)">
            <summary>
            The routing value (a comma separated list of the routing values) 
            can be specified to control which shards the delete by query request will be executed on.        
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Builders.Commands.IndexAliasCommand">
            <summary>
            Supports post 0.90.1 index alias api for single alias management
            http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/indices-aliases.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Builders.Commands.IndexAliasesCommand">
            <summary>
            Supports post 0.90.1 index alias api for alias retrieval
            http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/indices-aliases.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBase`1.Custom(System.String,System.String[])">
            <summary>
            Adds a custom part to Query or Filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBase`1.ForceJsonBuild">
            <summary>
            Determines whether JSON should be build despite empty content.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBase`1.HasRequiredParts">
            <summary>
            Determines whether this query/filter should generate any JSON.
            e.g. in case of "Term" query, if Value parameter not defined or empty -
            Term query will not be generated to JSON, 
            and empty string will be returned from ToJson/ToString call.
            Note: This check is not performed if there are no JSON parts registered.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.AggregationBase`2.AggregationName(System.String)">
            <summary>
            The name of the aggregation used to identify aggregation results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.AggregationBase`2.Aggregations(System.Func{PlainElastic.Net.Queries.Aggregations{`1},PlainElastic.Net.Queries.Aggregations{`1}})">
            <summary>
            Allows to collect aggregated data based on a search query. 
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.ValueAggregationBase`2">
            <summary>
            A base class for all multi- and single-value aggregations
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.Field(System.String)">
            <summary>
            The field to execute aggregation against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.Field(System.Linq.Expressions.Expression{System.Func{`1,System.Object}})">
            <summary>
            The field to execute aggregation against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to execute aggregation against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.Script(System.String)">
            <summary>
            Allow to define a script to evaluate, with its value used to compute the aggregation.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used groovy language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used groovy language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ValueAggregationBase`2.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.DateHistogramAggregation`1">
            <summary>
            The histogram aggregation works with numeric data by building a histogram across intervals of the field values.
            Each value is "rounded" into an interval (or placed in a bucket),
            and statistics are provided per interval/bucket (count and total)
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-histogram-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramAggregationBase`2.MinDocCount(System.Int64)">
            <summary>
            It is possible to only return buckets that have a document count that is greater than or equal to a configured limit through the min_doc_count option.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramAggregationBase`2.ExtendedBounds(System.Int64,System.Int64)">
            <summary>
            With extended_bounds setting, you now can "force" the histogram aggregation to start building buckets on a specific min values 
            and also keep on building buckets up to a max value (even if there are no documents anymore). 
            Using extended_bounds only makes sense when min_doc_count is 0 (the empty buckets will never be returned if min_doc_count is greater than 0).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramAggregationBase`2.Order(System.String,PlainElastic.Net.Queries.OrderDirection)">
            <summary>
            By default the returned buckets are sorted by their key ascending, though the order behaviour can be controled using the order setting.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.Interval(System.String)">
            <summary>
             The interval used to control the bucket "size" where each key value of a hit will fall into. Check
             the docs for all available values.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.PreZoneAdjustLargeInterval(System.Boolean)">
            <summary>
            Should pre zone be adjusted for large (day and above) intervals. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.PreZone(System.String)">
            <summary>
             Sets the pre time zone to use when bucketing the values. This timezone will be applied before 
             rounding off the result.
             Can either be in the form of "-10:00" or
             one of the values listed here: http://joda-time.sourceforge.net/timezones.html.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.TimeZone(System.String)">
            <summary>
            The time_zone parameter simply sets the pre_zone parameter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.PostZone(System.String)">
            <summary>
            Sets the post time zone to use when bucketing the values. This timezone will be applied after
            rounding off the result.
            Can either be in the form of "-10:00" or
            one of the values listed here: http://joda-time.sourceforge.net/timezones.html.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.PreOffset(System.String)">
            <summary>
            Sets a pre offset that will be applied before rounding the results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.PostOffset(System.String)">
            <summary>
            Sets a post offset that will be applied after rounding the results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramAggregation`1.Format(System.String)">
            <summary>
            Defines a date format, which will result in returning the dates as formatted strings next to the numeric key values.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.Aggregations`1">
            <summary>
            An aggregation can be seen as a unit-of-work that builds analytic information over a set of documents.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Terms(System.Func{PlainElastic.Net.Queries.TermsAggregation{`0},PlainElastic.Net.Queries.TermsAggregation{`0}})">
            <summary>
            Allows to specify field aggregations that return the N most frequent terms
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-terms-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Filter(System.Func{PlainElastic.Net.Queries.FilterAggregation{`0},PlainElastic.Net.Queries.FilterAggregation{`0}})">
            <summary>
            Defines a single bucket of all the documents in the current document set context that match a specified filter. Often this will be used to narrow down the current aggregation context to a specific set of documents.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-filter-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Range(System.Func{PlainElastic.Net.Queries.RangeAggregation{`0},PlainElastic.Net.Queries.RangeAggregation{`0}})">
            <summary>
            A multi-bucket value source based aggregation that enables the user to define a set of ranges - each representing a bucket. During the aggregation process, the values extracted from each document will be checked against each bucket range and "bucket" the relevant/matching document. Note that this aggregration includes the from value and excludes the to value for each range.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-range-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Stats(System.Func{PlainElastic.Net.Queries.StatsAggregation{`0},PlainElastic.Net.Queries.StatsAggregation{`0}})">
            <summary>
            A multi-value metrics aggregation that computes stats over numeric values extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            The stats that are returned consist of: min, max, sum, count and avg.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-stats-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.ExtendedStats(System.Func{PlainElastic.Net.Queries.ExtendedStatsAggregation{`0},PlainElastic.Net.Queries.ExtendedStatsAggregation{`0}})">
            <summary>
            A multi-value metrics aggregation that computes stats over numeric values extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            The extended_stats aggregations is an extended version of the stats aggregation, where additional metrics are added such as sum_of_squares, variance and std_deviation.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-extendedstats-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Min(System.Func{PlainElastic.Net.Queries.MinAggregation{`0},PlainElastic.Net.Queries.MinAggregation{`0}})">
            <summary>
            A single-value metrics aggregation that keeps track and returns the minimum value among numeric values extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-min-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Max(System.Func{PlainElastic.Net.Queries.MaxAggregation{`0},PlainElastic.Net.Queries.MaxAggregation{`0}})">
            <summary>
            A single-value metrics aggregation that keeps track and returns the maximum value among the numeric values extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-max-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Sum(System.Func{PlainElastic.Net.Queries.SumAggregation{`0},PlainElastic.Net.Queries.SumAggregation{`0}})">
            <summary>
            A single-value metrics aggregation that sums up numeric values that are extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-sum-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Avg(System.Func{PlainElastic.Net.Queries.AvgAggregation{`0},PlainElastic.Net.Queries.AvgAggregation{`0}})">
            <summary>
            A single-value metrics aggregation that computes the average of numeric values that are extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-avg-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.ValueCount(System.Func{PlainElastic.Net.Queries.ValueCountAggregation{`0},PlainElastic.Net.Queries.ValueCountAggregation{`0}})">
            <summary>
            A single-value metrics aggregation that counts the number of values that are extracted from the aggregated documents. These values can be extracted either from specific fields in the documents, or be generated by a provided script. Typically, this aggregator will be used in conjunction with other single-value aggregations. For example, when computing the avg one might be interested in the number of values the average is computed over.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-valuecount-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Percentiles(System.Func{PlainElastic.Net.Queries.PercentilesAggregation{`0},PlainElastic.Net.Queries.PercentilesAggregation{`0}})">
            <summary>
            A multi-value metrics aggregation that calculates one or more percentiles over numeric values extracted from the aggregated documents. These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-percentile-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Cardinality(System.Func{PlainElastic.Net.Queries.CardinalityAggregation{`0},PlainElastic.Net.Queries.CardinalityAggregation{`0}})">
            <summary>
            A single-value metrics aggregation that calculates an approximate count of distinct values. Values can be extracted either from specific fields in the document or generated by a script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-cardinality-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Global(System.Func{PlainElastic.Net.Queries.GlobalAggregation{`0},PlainElastic.Net.Queries.GlobalAggregation{`0}})">
            <summary>
            Defines a single bucket of all the documents within the search execution context. This context is defined by the indices and the document types you’re searching on, but is not influenced by the search query itself.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-global-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Missing(System.Func{PlainElastic.Net.Queries.MissingAggregation{`0},PlainElastic.Net.Queries.MissingAggregation{`0}})">
            <summary>
            A field data based single bucket aggregation, that creates a bucket of all documents in the current document set context that are missing a field value (effectively, missing a field or having the configured NULL value set). This aggregator will often be used in conjunction with other field data bucket aggregators (such as ranges) to return information for all the documents that could not be placed in any of the other buckets due to missing field data values.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-missing-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Nested(System.Func{PlainElastic.Net.Queries.NestedAggregation{`0},PlainElastic.Net.Queries.NestedAggregation{`0}})">
            <summary>
            A special single bucket aggregation that enables aggregating nested documents.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-nested-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.Histogram(System.Func{PlainElastic.Net.Queries.HistogramAggregation{`0},PlainElastic.Net.Queries.HistogramAggregation{`0}})">
            <summary>
            A multi-bucket values source based aggregation that can be applied on numeric values extracted from the documents. It dynamically builds fixed size (a.k.a. interval) buckets over the values. For example, if the documents have a field that holds a price (numeric), we can configure this aggregation to dynamically build buckets with interval 5 (in case of price it may represent $5). When the aggregation executes, the price field of every document will be evaluated and will be rounded down to its closest bucket - for example, if the price is 32 and the bucket size is 5 then the rounding will yield 30 and thus the document will "fall" into the bucket that is associated withe the key 30.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-histogram-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Aggregations`1.DateHistogram(System.Func{PlainElastic.Net.Queries.DateHistogramAggregation{`0},PlainElastic.Net.Queries.DateHistogramAggregation{`0}})">
            <summary>
            A multi-bucket aggregation similar to the histogram except it can only be applied on date values. Since dates are represented in elasticsearch internally as long values, it is possible to use the normal histogram on dates as well, though accuracy will be compromised. The reason for this is in the fact that time based intervals are not fixed (think of leap years and on the number of days in a month). For this reason, we need a special support for time based data. From a functionality perspective, this histogram supports the same features as the normal histogram. The main difference is that the interval can be specified by date/time expressions.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-datehistogram-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FilterAggregation`1">
            <summary>
            Defines a single bucket of all the documents in the current document set context that match a specified filter.
            Often this will be used to narrow down the current aggregation context to a specific set of documents.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-filter-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FilterAggregation`1.Filter(System.Func{PlainElastic.Net.Queries.Filter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            Filter that will be used to filter aggregation matches.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HistogramAggregation`1">
            <summary>
            The histogram aggregation works with numeric data by building a histogram across intervals of the field values.
            Each value is "rounded" into an interval (or placed in a bucket),
            and statistics are provided per interval/bucket (count and total)
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-histogram-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramAggregation`1.Interval(System.Int64)">
            <summary>
             The interval used to control the bucket "size" where each key value of a hit will fall into.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramAggregation`1.Keyed(System.Boolean)">
            <summary>
            Formats the response as a hash instead keyed by the buckets keys.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.AvgAggregation`1">
            <summary>
            A single-value metrics aggregation that computes the average of numeric values that are extracted from the aggregated documents.
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-avg-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.PercentilesAggregation`1">
            <summary>
            A multi-value metrics aggregation that calculates one or more percentiles over numeric values extracted from the aggregated documents. 
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-percentile-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PercentilesAggregation`1.Compression(System.Int32)">
            <summary>
            Compression controls memory usage and approximation error.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PercentilesAggregation`1.Percents(System.Single[])">
            <summary>
            Percents controls particular percentiles to calculate
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.CardinalityAggregation`1">
            <summary>
            A single-value metrics aggregation that calculates an approximate count of distinct values.
            Values can be extracted either from specific fields in the document or generated by a script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-cardinality-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CardinalityAggregation`1.PrecisionThreshold(System.Int32)">
            <summary>
            The precision_threshold options allows to trade memory for accuracy, and defines a unique count below which counts are expected to be close to accurate.
            Above this value, counts might become a bit more fuzzy. The maximum supported value is 40000, thresholds above this number will have the same effect as a threshold of 40000.
            Default value depends on the number of parent aggregations that multiple create buckets (such as terms or histograms).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CardinalityAggregation`1.Rehash(System.Boolean)">
            <summary>
            If you computed a hash on client-side, stored it into your documents and want Elasticsearch to use them to compute counts using this hash function without rehashing values,
            it is possible to specify rehash: false. Default value is true. Please note that the hash must be indexed as a long when rehash is false.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.GlobalAggregation`1">
            <summary>
            Defines a single bucket of all the documents within the search execution context.
            This context is defined by the indices and the document types you’re searching on, but is not influenced by the search query itself.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-global-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MissingAggregation`1">
            <summary>
            A field data based single bucket aggregation, that creates a bucket of all documents in the current document set context that are missing a field value 
            (effectively, missing a field or having the configured NULL value set).
            This aggregator will often be used in conjunction with other field data bucket aggregators (such as ranges)
            to return information for all the documents that could not be placed in any of the other buckets due to missing field data values.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-missing-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NestedAggregation`1">
            <summary>
            A special single bucket aggregation that enables aggregating nested documents.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-nested-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedAggregation`1.Path(System.String)">
            <summary>
            The path of the nested documents within the top level documents.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.ValueCountAggregation`1">
            <summary>
            A single-value metrics aggregation that counts the number of values that are extracted from the aggregated documents.
            These values can be extracted either from specific fields in the documents, or be generated by a provided script.
            Typically, this aggregator will be used in conjunction with other single-value aggregations.
            For example, when computing the avg one might be interested in the number of values the average is computed over.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-valuecount-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.SumAggregation`1">
            <summary>
            A single-value metrics aggregation that sums up numeric values that are extracted from the aggregated documents.
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-sum-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MaxAggregation`1">
            <summary>
            A single-value metrics aggregation that keeps track and returns the maximum value among the numeric values extracted from the aggregated documents.
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-max-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RangeAggregation`1">
            <summary>
            A multi-bucket value source based aggregation that enables the user to define a set of ranges - each representing a bucket.
            During the aggregation process, the values extracted from each document will be checked against each bucket range and "bucket" the relevant/matching document.
            Note that this aggregration includes the from value and excludes the to value for each range.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-range-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeAggregation`1.Keyed(System.Boolean)">
            <summary>
            Setting the key flag to true will associate a unique string key with each bucket and return the ranges as a hash rather than an array
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeAggregation`1.Ranges(System.Func{PlainElastic.Net.Queries.RangeAggregationFromTo,PlainElastic.Net.Queries.RangeAggregationFromTo})">
            <summary>
            The ranges of values to aggregate against, in format (from, to)
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.ExtendedStatsAggregation`1">
            <summary>
            A multi-value metrics aggregation that computes stats over numeric values extracted from the aggregated documents.
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            The extended_stats aggregations is an extended version of the stats aggregation, where additional metrics are added such as sum_of_squares, variance and std_deviation.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-extendedstats-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MinAggregation`1">
            <summary>
            A single-value metrics aggregation that keeps track and returns the minimum value among numeric values extracted from the aggregated documents.
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-min-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.StatsAggregation`1">
            <summary>
            A multi-value metrics aggregation that computes stats over numeric values extracted from the aggregated documents. 
            These values can be extracted either from specific numeric fields in the documents, or be generated by a provided script.
            The stats that are returned consist of: min, max, sum, count and avg.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-metrics-stats-aggregation.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermsAggregation`1">
            <summary>
            Allows to specify field aggregations that return the N most frequent terms
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations-bucket-terms-aggregation.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.Order(System.String,PlainElastic.Net.Queries.OrderDirection)">
            <summary>
            Allows to control the ordering of the terms aggregations
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.Size(System.Int32)">
            <summary>
            Sets the size - indicating how many term buckets should be returned (defaults to 10)
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.ShardSize(System.Int32)">
            <summary>
            Sets the shard_size - indicating the number of term buckets each shard will return to the coordinating node (the
            node that coordinates the search execution). The higher the shard size is, the more accurate the results are.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.MinDocCount(System.Int64)">
            <summary>
            Set the minimum document count terms should have in order to appear in the response.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.ShardMinDocCount(System.Int64)">
            <summary>
            Set the minimum document count terms should have on the shard in order to appear in the response.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.ShowTermDocCountError(System.Boolean)">
            <summary>
            Return document count errors per term in the response.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.CollectMode(PlainElastic.Net.Queries.CollectMode)">
            <summary>
            Defines collection mode
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.ValueType(PlainElastic.Net.Queries.TermsValueType)">
            <summary>
            When using scripts, the value type indicates the types of the values the script is generating.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.Include(System.String)">
            <summary>
            Allows to specify a term that should be included from the terms aggregation request result.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.Include(System.String,PlainElastic.Net.RegexFlags)">
            <summary>
            Allows to specify a term that should be included from the terms aggregation request result.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.Exclude(System.String)">
            <summary>
            Allows to specify a term that should be excluded from the terms aggregation request result.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.Exclude(System.String,PlainElastic.Net.RegexFlags)">
            <summary>
            Allows to specify a term that should be excluded from the terms aggregation request result.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsAggregation`1.ExecutionHint(System.String)">
            <summary>
            There are three mechanisms by which terms aggregations can be executed: either by using field values directly in order to aggregate data per-bucket (map), by using ordinals of the field values instead of the values themselves (ordinals) or by using global ordinals of the field (global_ordinals). The latter is faster, especially for fields with many unique values. However it can be slower if only a few documents match, when for example a terms aggregator is nested in another aggregator, this applies for both ordinals and global_ordinals execution modes. 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RegExpFilter`1">
            <summary>
            The regexp filter is similar to the regexp query, except that it is cacheable and can speedup performance in case you are reusing this filter in your queries.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-regexp-filter.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQueryBase`2.Field(System.String)">
            <summary>
            Allows to specify field name to execute query/filter against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQueryBase`2.Field(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            Allows to specify field of object to execute query/filter against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQueryBase`2.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            Allows to specify field of object from the collection of such objects to execute query/filter against.
            </summary>
            <param name="collectionField">The collection type field.</param>
            <param name="field">The field of object inside collection.</param>
        </member>
        <member name="P:PlainElastic.Net.Queries.FieldQueryBase`2.RegisteredField">
            <summary>
            Quoted field name for this query.
            </summary>
        </member>
        <member name="P:PlainElastic.Net.Queries.FieldQueryBase`2.RawFieldName">
            <summary>
            Raw/unquoted field name for this query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RegExpFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.SearchScrollCommand">
            <summary>
             Allows to scroll search request's hits.
             http://www.elasticsearch.org/guide/reference/api/search/scroll
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchScrollCommand.Scroll(System.String)">
            <summary>
            The scroll parameter is a time value parameter (for example: scroll=5m), 
            indicating for how long the nodes that participate in the search will maintain relevant resources in order to continue and support it.
            see http://www.elasticsearch.org/guide/reference/api/search/scroll.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.MoreLikeThisCommand">
            <summary>
            The more like this (mlt) API allows to get documents that are “like” a specified document.
            http://www.elasticsearch.org/guide/reference/api/more-like-this.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MltFields(System.String)">
            <summary>
            A list of the fields to run the more like this query against. Defaults to the _all field
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MltFields``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            A list of the fields to run the more like this query against. Defaults to the _all field
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.PercentTermsToMatch(System.Double)">
            <summary>
            The percentage of terms to match on (float value). Defaults to 0.3 (30 percent).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MinTermFreq(System.Int32)">
            <summary>
            The frequency below which terms will be ignored in the source doc. The default frequency is 2.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MaxQueryTerms(System.Int32)">
            <summary>
            The maximum number of query terms that will be included in any generated query.
            Defaults to 25.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.StopWords(System.String[])">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MinDocFreq(System.Int32)">
            <summary>
            The frequency at which words will be ignored which do not occur in at least this many docs.
            Defaults to 5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MaxDocFreq(System.Int32)">
            <summary>
            The maximum frequency in which words may still appear. Words that appear in more than this many docs will be ignored.
            Defaults to unbounded.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MinWordLen(System.Int32)">
            <summary>
            The minimum word length below which words will be ignored. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.MaxWordLen(System.Int32)">
            <summary>
            The maximum word length above which words will be ignored.
            Defaults to unbounded (0).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.BoostTerms(System.Int32)">
            <summary>
            Sets the boost factor to use when boosting terms. Defaults to 1.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.SearchType(PlainElastic.Net.SearchType)">
            <summary>
            The type of the search operation to perform.
            Defaults to query_then_fetch.
            see http://www.elasticsearch.org/guide/reference/api/search/search-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.SearchIndices(System.String[])">
            <summary>
            Indices to search within.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.SearchTypes(System.String[])">
            <summary>
            Types to search within.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.SearchFrom(System.Int32)">
            <summary>
            The starting from index of the hits to return. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.SearchSize(System.Int32)">
            <summary>
            The number of hits to return. Defaults to 10.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.MoreLikeThisCommand.SearchScroll(System.String)">
            <summary>
            The scroll parameter is a time value parameter (for example: scroll=5m), 
            indicating for how long the nodes that participate in the search will maintain relevant resources in order to continue and support it.
            see http://www.elasticsearch.org/guide/reference/api/search/scroll.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.CreateIndexCommand">
            <summary>
            Builds a command that allows  to instantiate an index. 
            ElasticSearch provides support for multiple indices, 
            including executing operations across several indices.
            Each index created can have specific settings associated with it.
            see http://www.elasticsearch.org/guide/reference/api/admin-indices-create-index.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.OptimizeCommand">
            <summary>
            Builds a command that allows to allows to optimize one or more indices through an API. 
            The optimize process basically optimizes the index for faster search operations 
            (and relates to the number of segments a lucene index within each shard).
            The optimize operation allows to optimize the number of segments to optimize to.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-optimize.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.OptimizeCommand.MaxNumSegments(System.Int32)">
            <summary>
            The number of segments to optimize to.
            To fully optimize the index, set it to 1.
            Defaults to simply checking if a merge needs to execute, and if so, executes it.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.OptimizeCommand.OnlyExpungeDeletes(System.Boolean)">
            <summary>
            Should the optimize process only expunge segments with deletes in it.
             In Lucene, a document is not deleted from a segment, just marked as deleted.
             During a merge process of segments, a new segment is created that does have those deletes.
             This flag allow to only merge segments that have deletes. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.OptimizeCommand.Refresh(System.Boolean)">
            <summary>
            Should a refresh be performed after the optimize. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.OptimizeCommand.Flush(System.Boolean)">
            <summary>
            Should a flush be performed after the optimize. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.OptimizeCommand.WaitForMerge(System.Boolean)">
            <summary>
            Should the request wait for the merge to end. Defaults to true.
            Note, a merge can potentially be a very heavy operation,
            so it might make sense to run it set to false.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.CountCommand">
            <summary>
            Builds a command that allows to easily execute a query and get the number of matches for that query. 
            It can be executed across one or more indices and across one or more types. 
            The query can either be provided using a simple query string as a parameter, 
            or using the Query DSL defined within the request body
            http://www.elasticsearch.org/guide/reference/api/count.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.CountCommand.Analyzer(System.String)">
            <summary>
            The analyzer name to be used when analyzing the query string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.CountCommand.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer name to be used when analyzing the query string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.CountCommand.Df(System.String)">
            <summary>
            The default field to use when no field prefix is defined within the query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.CountCommand.DefaultOperator(PlainElastic.Net.Operator)">
            <summary>
            The default operator to be used, can be AND or OR. Defaults to OR
            </summary>
        </member>
        <member name="M:PlainElastic.Net.CountCommand.Q(System.String)">
            <summary>
            The query string (maps to the query_string query).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.CountCommand.Routing(System.String)">
            <summary>
            A comma separated list of the routing values to control which shards the count request will be executed on.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.RefreshCommand">
            <summary>
             Allows to explicitly refresh one or more index, making all operations performed since the last refresh available for search.
             The (near) real-time capabilities depends on the index engine used. 
             For example, the robin one requires refresh to be called, but by default a refresh is scheduled periodically.
             http://www.elasticsearch.org/guide/reference/api/admin-indices-refresh.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.StatusCommand">
            <summary>
            Builds a command that allows to get a comprehensive status information of one or more indices.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-status.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.StatusCommand.Recovery(System.Boolean)">
            <summary>
            Used to control whether the recovery status of shard should be included to status results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.StatusCommand.Snapshot(System.Boolean)">
            <summary>
            Used to control whether the snapshot status of shard should be included to status results.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.UpdateSettingsCommand">
            <summary>
            Builds a command that allows to change specific index level settings in real time.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-update-settings.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.OpenCommand">
            <summary>
            Builds a command that allows to open an index. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-open-close.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexExistsCommand">
            <summary>
            Builds a command that allows check if the index (indices) exists or not. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-indices-exists.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.FlushCommand">
            <summary>
            Builds a command that allows to flush one or more indices through an API. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-flush.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.GetMappingCommand">
            <summary>
            Builds a command that allows to retrieve mapping definition of index or index/type.
            To get mappings for all indices you can use _all for "index"
            http://www.elasticsearch.org/guide/reference/api/admin-indices-get-mapping.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.PutMappingCommand">
            <summary>
            Builds a command that allows to register specific mapping definition for a specific type.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-put-mapping.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.PutMappingCommand.IgnoreConflicts(System.Boolean)">
            <summary>
            Used to control if conflicts should be ignored or not, by default, it is set to false which means conflicts are not ignored.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.DeleteMappingCommand">
            <summary>
            Builds a command that allows to delete a mapping (type) along with its data.
            Note, most times, it make more sense to reindex the data into a fresh index compared to delete large chunks of it.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-delete-mapping.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.DeleteCommand">
            <summary>
            Builds a command that allows to delete custom Json document in Index or delete whole Index.
            http://www.elasticsearch.org/guide/reference/api/delete.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteCommand.Consistency(PlainElastic.Net.WriteConsistency)">
            <summary>
            Requires a minimum number of active shards in the partition.
            It defaults to the node level setting of action.write_consistency, which in turn defaults to quorum.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteCommand.Parent(System.String)">
            <summary>
            The parent parameter can be set, which will basically be the same as setting the routing parameter.
            Note that deleting a parent document does not automatically delete its children.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteCommand.Refresh(System.Boolean)">
            <summary>
            Allows to refresh the relevant shard after the delete operation has occurred and make it searchable. 
            Setting it to true should be done after careful thought and verification
            that this does not cause a heavy load on the system (and slows down indexing).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteCommand.Replication(PlainElastic.Net.DocumentReplication)">
            <summary>
            Allows to define the sync or async replication of the operation 
            When async the operation will return once it has be executed on the primary shard.
            The replication parameter can be set to async (defaults to sync) in order to enable it.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteCommand.Routing(System.String)">
            <summary>
            Allows to explicitly control, how the value fed into the hash function
            used by the router can be directly specified on a per-operation basis.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.DeleteCommand.Version(System.Int64)">
            <summary>
            Allows to specify a version of document to make sure the relevant document 
            we are trying to delete is actually being deleted and it has not changed in the meantime.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexCommand">
            <summary>
            Builds a command that allows to create Index and add or update custom Json document in that Index.
            http://www.elasticsearch.org/guide/reference/api/index_.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexCommand.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates Index and adds or updates custom Json document in index.
            </summary>
            <param name="index">The name of the index.</param>
            <param name="type">The document type name. This parameter could be optional.</param>
            <param name="id">The id. If this parameter missing ID will automatically assigned. 
            Note to use POST request in this case.</param>
        </member>
        <member name="M:PlainElastic.Net.IndexCommand.VersionType(PlainElastic.Net.VersionType)">
            <summary>
            Allows to enable external versioning.
            By default internal versioning used,
            to supplemented version number with an external value (for example, if maintained in a database),
            version_type should be set to external
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexCommand.Version(System.Int64)">
            <summary>
            Allows to specify document version that will be used to 
            provide optimistic concurrency control.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.GetCommand">
            <summary>
            Builds a command that allows to get a typed JSON document from the index based on its id.
            http://www.elasticsearch.org/guide/reference/api/get.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Commands">
            <summary>
            Provides shortcuts to Elastic Search command builders.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Bulk(System.String,System.String)">
            <summary>
            Builds a command that allows to perform many index/delete operations in a single API call.
            This can greatly increase the indexing speed. 
            http://www.elasticsearch.org/guide/reference/api/bulk.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Close(System.String)">
            <summary>
            Builds a command that allows to close an index. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-open-close.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Count(System.String,System.String)">
            <summary>
            Builds a command that allows to easily execute a query and get the number of matches for that query. 
            It can be executed across one or more indices and across one or more types. 
            The query can either be provided using a simple query string as a parameter, 
            or using the Query DSL defined within the request body
            http://www.elasticsearch.org/guide/reference/api/count.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Count(System.String[],System.String[])">
            <summary>
            Builds a command that allows to easily execute a query and get the number of matches for that query. 
            It can be executed across one or more indices and across one or more types. 
            The query can either be provided using a simple query string as a parameter, 
            or using the Query DSL defined within the request body
            http://www.elasticsearch.org/guide/reference/api/count.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.CreateIndex(System.String)">
            <summary>
            Builds a command that allows  to instantiate an index. 
            ElasticSearch provides support for multiple indices, 
            including executing operations across several indices.
            Each index created can have specific settings associated with it.
            see http://www.elasticsearch.org/guide/reference/api/admin-indices-create-index.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Delete(System.String,System.String,System.String)">
            <summary>
            Builds a command that allows to delete custom Json document in Index or delete whole Index.
            http://www.elasticsearch.org/guide/reference/api/delete.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.DeleteByQuery(System.String,System.String)">
            <summary>
            Builds a command that allows to delete documents from one or more indices and one or more types based on a query.
            http://www.elasticsearch.org/guide/reference/api/delete-by-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.DeleteByQuery(System.String[],System.String[])">
            <summary>
            Builds a command that allows to delete documents from one or more indices and one or more types based on a query.
            http://www.elasticsearch.org/guide/reference/api/delete-by-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.DeleteMapping(System.String,System.String)">
            <summary>
            Builds a command that allows to delete a mapping (type) along with its data.
            Note, most times, it make more sense to reindex the data into a fresh index compared to delete large chunks of it.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-delete-mapping.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Flush(System.String,System.String)">
            <summary>
            Builds a command that allows to flush one or more indices through an API. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-flush.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Flush(System.String[],System.String[])">
            <summary>
            Builds a command that allows to flush one or more indices through an API. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-flush.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Get(System.String,System.String,System.String)">
            <summary>
            Builds a command that allows to get a typed JSON document from the index based on its id.
            http://www.elasticsearch.org/guide/reference/api/get.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.GetMapping(System.String,System.String)">
            <summary>
            Builds a command that allows to retrieve mapping definition of index or index/type.
            To get mappings for all indices you can use _all for "index"
            http://www.elasticsearch.org/guide/reference/api/admin-indices-get-mapping.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.GetMapping(System.String[],System.String[])">
            <summary>
            Builds a command that allows to retrieve mapping definition of index or index/type.
            To get mappings for all indices you can use _all for "index"
            http://www.elasticsearch.org/guide/reference/api/admin-indices-get-mapping.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Index(System.String,System.String,System.String)">
            <summary>
            Builds a command that allows to create Index and add or update custom Json document in that Index.
            http://www.elasticsearch.org/guide/reference/api/index_.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.IndexExists(System.String)">
            <summary>
            Builds a command that allows check if the index (indices) exists or not. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-indices-exists.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.MoreLikeThis(System.String,System.String,System.String)">
            <summary>
            Builds a command that finds more like a specific item.
            http://www.elasticsearch.org/guide/reference/api/more-like-this.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Open(System.String)">
            <summary>
            Builds a command that allows to open an index. 
            http://www.elasticsearch.org/guide/reference/api/admin-indices-open-close.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Optimize(System.String,System.String)">
            <summary>
            Builds a command that allows to allows to optimize one or more indices through an API. 
            The optimize process basically optimizes the index for faster search operations 
            (and relates to the number of segments a lucene index within each shard).
            The optimize operation allows to optimize the number of segments to optimize to.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-optimize.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.PutMapping(System.String,System.String)">
            <summary>
            Builds a command that allows to register specific mapping definition for a specific type.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-put-mapping.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.PutMapping(System.String[],System.String[])">
            <summary>
            Builds a command that allows to register specific mapping definition for a specific type.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-put-mapping.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Refresh(System.String)">
            <summary>
             Allows to explicitly refresh one or more index, making all operations performed since the last refresh available for search.
             The (near) real-time capabilities depends on the index engine used. 
             For example, the robin one requires refresh to be called, but by default a refresh is scheduled periodically.
             http://www.elasticsearch.org/guide/reference/api/admin-indices-refresh.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Refresh(System.String[])">
            <summary>
             Allows to explicitly refresh one or more index, making all operations performed since the last refresh available for search.
             The (near) real-time capabilities depends on the index engine used. 
             For example, the robin one requires refresh to be called, but by default a refresh is scheduled periodically.
             http://www.elasticsearch.org/guide/reference/api/admin-indices-refresh.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Search(System.String,System.String)">
            <summary>
            Builds a command that allows to execute a search query and get back search hits that match the query.
            http://www.elasticsearch.org/guide/reference/api/search/uri-request.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Search(System.String[],System.String[])">
            <summary>
            Builds a command that allows to execute a search query and get back search hits that match the query.
            http://www.elasticsearch.org/guide/reference/api/search/uri-request.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.SearchScroll(System.String)">
            <summary>
             Allows to scroll search request's hits.
             http://www.elasticsearch.org/guide/reference/api/search/scroll
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Status(System.String)">
            <summary>
            Builds a command that allows to get a comprehensive status information of one or more indices.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-status.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.Status(System.String[])">
            <summary>
            Builds a command that allows to get a comprehensive status information of one or more indices.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-status.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.UpdateSettings(System.String)">
            <summary>
            Builds a command that allows to change specific index level settings in real time.
            http://www.elasticsearch.org/guide/reference/api/admin-indices-update-settings.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.IndexAlias(System.String,System.String)">
            <summary>
            Supports post 0.90.1 index alias api for single alias management
            http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/indices-aliases.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Commands.IndexAliases(System.String,System.String)">
            <summary>
            Supports post 0.90.1 index alias api for alias retrieval
            http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/indices-aliases.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.SearchCommand">
            <summary>
            Builds a command that allows to execute a search query and get back search hits that match the query.
            http://www.elasticsearch.org/guide/reference/api/search/uri-request.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Analyzer(System.String)">
            <summary>
            The analyzer name to be used when analyzing the query string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer name to be used when analyzing the query string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.AnalyzeWildcard(System.Boolean)">
            <summary>
            Should wildcard and prefix queries be analyzed or not. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Df(System.String)">
            <summary>
            The default field to use when no field prefix is defined within the query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.DefaultOperator(PlainElastic.Net.Operator)">
            <summary>
            The default operator to be used, can be AND or OR. Defaults to OR.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Explain">
            <summary>
            Includes explanation of how scoring of the hits was computed for each hit.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Fields(System.String)">
            <summary>
            The selective fields of the document to return for each hit.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Fields``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            The selective fields of the document to return for each hit.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.From(System.Int32)">
            <summary>
            The starting from index of the hits to return. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.LowercaseExpandedTerms(System.Boolean)">
            <summary>
            Determines whether terms should be automatically lowercased or not. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Q(System.String)">
            <summary>
            The query string (maps to the query_string query).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Routing(System.String)">
            <summary>
            A comma separated list of the routing values to control which shards the count request will be executed on.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Scroll(System.String)">
            <summary>
            The scroll parameter is a time value parameter (for example: scroll=5m), 
            indicating for how long the nodes that participate in the search will maintain relevant resources in order to continue and support it.
            see http://www.elasticsearch.org/guide/reference/api/search/scroll.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.SearchType(PlainElastic.Net.SearchType)">
            <summary>
            The type of the search operation to perform.
            Defaults to query_then_fetch.
            see http://www.elasticsearch.org/guide/reference/api/search/search-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Size(System.Int32)">
            <summary>
            The number of hits to return. Defaults to 10.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Sort(System.String,PlainElastic.Net.SortDirection)">
            <summary>
            Sorting to perform. There can be several Sort parameters (order is important).
            Use "_score" to sort by query score.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.SearchCommand.Sort``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}},PlainElastic.Net.SortDirection)">
            <summary>
            Sorting to perform. There can be several Sort parameters (order is important).
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Operator.OR">
            <summary>
            When OR operator used, the query 'capital of Hungary' is translated to 'capital OR of OR Hungary'
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Operator.AND">
            <summary>
            When AND operator used, the query 'capital of Hungary' is translated to 'capital AND of AND Hungary'
            </summary>
        </member>
        <member name="T:PlainElastic.Net.DefaultAnalyzers">
            <summary>
            Analyzers that can be used in order to both break indexed (analyzed) fields when a document is indexed and process query strings.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.RegexFlags">
            <summary>
            Pattern analyzer regular expression flags.
            see http://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html#field_summary
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.CANON_EQ">
            <summary>
            Enables canonical equivalence.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.CASE_INSENSITIVE">
            <summary>
            Enables case-insensitive matching.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.COMMENTS">
            <summary>
            Permits whitespace and comments in pattern.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.DOTALL">
            <summary>
            Enables dotall mode.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.LITERAL">
            <summary>
            Enables literal parsing of the pattern.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.MULTILINE">
            <summary>
            Enables multiline mode.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.UNICODE_CASE">
            <summary>
            Enables Unicode-aware case folding.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.RegexFlags.UNIX_LINES">
            <summary>
            Enables Unix lines mode.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.Analysis">
            <summary>
            The index analysis module acts as a configurable registry of Analyzers
            that can be used in order to both break indexed (analyzed) fields when a document is indexed and process query strings.
            It maps to the Lucene Analyzer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SettingsBase`1.CustomPart(System.String,System.String[])">
            <summary>
            Adds a custom part to analysis config.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.Analysis.Analyzer(System.Func{PlainElastic.Net.IndexSettings.AnalyzerSettings,PlainElastic.Net.IndexSettings.AnalyzerSettings})">
            <summary>
            Allows to configure standard/custom analyzers to be used in mapping API.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.Analysis.Tokenizer(System.Func{PlainElastic.Net.IndexSettings.TokenizerSettings,PlainElastic.Net.IndexSettings.TokenizerSettings})">
            <summary>
            Allows to configure tokenizers to be used in custom analyzers.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.Analysis.Filter(System.Func{PlainElastic.Net.IndexSettings.TokenFilterSettings,PlainElastic.Net.IndexSettings.TokenFilterSettings})">
            <summary>
            Allows to configure token filters to be used in custom analyzers.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.Analysis.CharFilter(System.Func{PlainElastic.Net.IndexSettings.CharFilterSettings,PlainElastic.Net.IndexSettings.CharFilterSettings})">
            <summary>
            Allows to configure char filters to be used in custom analyzers.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.CharFilterSettings">
            <summary>
            Allows to configure char filters to be used in custom analyzers.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CharFilterSettings.HtmlStrip(System.String,System.Func{PlainElastic.Net.IndexSettings.HtmlStripCharFilter,PlainElastic.Net.IndexSettings.HtmlStripCharFilter})">
            <summary>
            A char filter of type html_strip stripping out HTML elements from an analyzed text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/htmlstrip-charfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CharFilterSettings.HtmlStrip(System.Func{PlainElastic.Net.IndexSettings.HtmlStripCharFilter,PlainElastic.Net.IndexSettings.HtmlStripCharFilter})">
            <summary>
            A char filter of type html_strip stripping out HTML elements from an analyzed text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/htmlstrip-charfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CharFilterSettings.Mapping(System.String,System.Func{PlainElastic.Net.IndexSettings.MappingCharFilter,PlainElastic.Net.IndexSettings.MappingCharFilter})">
            <summary>
            A char filter of type mapping replacing characters of an analyzed text with given mapping.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/mapping-charfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CharFilterSettings.Mapping(System.Func{PlainElastic.Net.IndexSettings.MappingCharFilter,PlainElastic.Net.IndexSettings.MappingCharFilter})">
            <summary>
            A char filter of type mapping replacing characters of an analyzed text with given mapping.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/mapping-charfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.HtmlStripCharFilter">
            <summary>
            A char filter of type html_strip stripping out HTML elements from an analyzed text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/htmlstrip-charfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.NamedComponentBase`1">
            <summary>
            Base class for Analyzers, Tokenizers and Filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.NamedComponentBase`1.Version(System.String)">
            <summary>
            Controls which Lucene version behavior this component should use.
            The highest version number is the default option.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.MappingCharFilter">
            <summary>
            A char filter of type mapping replacing characters of an analyzed text with given mapping.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/mapping-charfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.MappingCharFilter.Mappings(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of character mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.MappingCharFilter.Mappings(System.String[])">
            <summary>
            Sets a list of character mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.MappingCharFilter.MappingsPath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a mappings file.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.EdgeNGramComponentBase`1">
            <summary>
            Base class for edgeNGram Tokenizers and Filters.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.NGramComponentBase`1">
            <summary>
            Base class for nGram Tokenizers and Filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.NGramComponentBase`1.MinGram(System.Int32)">
            <summary>
            Sets the minimal number of characters in N-grams built.
            Defaults to 1.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.NGramComponentBase`1.MaxGram(System.Int32)">
            <summary>
            Sets the maximal number of characters in N-grams built.
            Defaults to 2.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.EdgeNGramComponentBase`1.Side(PlainElastic.Net.IndexSettings.EdgeNGramSide)">
            <summary>
            Sets the text side from which N-grams are built.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.WhitespaceTokenizer">
            <summary>
            A tokenizer of type whitespace that divides text at whitespace.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LowercaseTokenizer">
            <summary>
            A tokenizer of type lowercase that performs the function of Letter Tokenizer and Lower Case Token Filter together.
            It divides text at non-letters and converts them to lower case.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LetterTokenizer">
            <summary>
            A tokenizer of type letter that divides text at non-letters. That's to say, it defines tokens as maximal strings of adjacent letters.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/letter-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.AsciifoldingTokenFilter">
            <summary>
            A token filter of type asciifolding that converts alphabetic, numeric, and symbolic Unicode characters
            which are not in the first 127 ASCII characters (the "Basic Latin" Unicode block) into their ASCII equivalents, if one exists.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/asciifolding-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter">
            <summary>
            A token filter of type dictionary_decompounder that allows to decompose compound words.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/compound-word-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter.WordList(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of words to use.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter.WordList(System.String[])">
            <summary>
            Sets a list of words to use.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter.WordListPath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a list of words file.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.EdgeNGramTokenFilter">
            <summary>
            A token filter of type edgeNGram that builds N-characters substrings from text. Substrings are built from one side of a text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/edgengram-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.ElisionTokenFilter">
            <summary>
            A token filter which removes elisions. For example, "l'avion" (the plane) will be tokenized as "avion" (plane).
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/elision-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.ElisionTokenFilter.Articles(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of stop words articles.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.ElisionTokenFilter.Articles(System.String[])">
            <summary>
            Sets a list of stop words articles.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.HyphenationDecompounderTokenFilter">
            <summary>
            A token filter of type hyphenation_decompounder that allows to decompose compound words.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/compound-word-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.KstemTokenFilter">
            <summary>
            The kstem token filter is a high performance filter for english.
            All terms must already be lowercased (use lowercase filter) for this filter to work correctly.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/kstem-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LengthTokenFilter">
            <summary>
            A token filter of type length that removes words that are too long or too short for the stream.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/length-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LengthTokenFilter.Min(System.Int32)">
            <summary>
            Sets the minimum length.
            Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LengthTokenFilter.Max(System.Int32)">
            <summary>
            Sets the maximum length.
            Defaults to int.MaxValue.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LowercaseTokenFilter">
            <summary>
            A token filter of type lowercase that normalizes token text to lower case.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LowercaseTokenFilter.Language(System.String)">
            <summary>
            Sets non-Latin lowercase filter language.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LowercaseTokenFilter.Language(PlainElastic.Net.IndexSettings.LowercaseTokenFilterLanguages)">
            <summary>
            Sets non-Latin lowercase filter language.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.NGramTokenFilter">
            <summary>
            A token filter of type nGram that builds N-characters substrings from text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/ngram-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter">
            <summary>
            The pattern_replace token filter allows to easily handle string replacements based on a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern_replace-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter.Pattern(System.String)">
            <summary>
            Sets the regular expression pattern.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter.Replacement(System.String)">
            <summary>
            Sets the replacement string.
            Supports referencing the original text, see http://docs.oracle.com/javase/6/docs/api/java/util/regex/Matcher.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.PhoneticTokenFilter">
            <summary>
            A phonetic analysis token filter plugin.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/phonetic-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PhoneticTokenFilter.Encoder(System.String)">
            <summary>
            Sets a phonetic encoder.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PhoneticTokenFilter.Encoder(PlainElastic.Net.IndexSettings.PhoneticTokenFilterEncoders)">
            <summary>
            Sets a phonetic encoder.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PhoneticTokenFilter.Replace(System.Boolean)">
            <summary>
            Sets flag controlling if the token processed should be replaced with the encoded one (true), or added (false).
            Defaults to true.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.PorterStemTokenFilter">
            <summary>
            A token filter of type porterStem that transforms the token stream as per the Porter stemming algorithm.
            Note, the input to the stemming filter must already be in lower case, so you will need to use Lower Case Token Filter
            or Lower Case Tokenizer farther down the Tokenizer chain in order for this to work properly!
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/porterstem-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.ReverseTokenFilter">
            <summary>
            A token filter of type reverse that simply reverses the tokens.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/reverse-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.ShingleTokenFilter">
            <summary>
            A token filter of type shingle that constructs shingles (token n-grams) from a token stream.
            In other words, it creates combinations of tokens as a single token.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/shingle-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.ShingleTokenFilter.MaxShingleSize(System.Int32)">
            <summary>
            Sets the maximal shingle size.
            Defaults to 2.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.ShingleTokenFilter.OutputUnigrams(System.Boolean)">
            <summary>
            Sets flag indicating whether unigrams should be sent to output.
            Defaults to true.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.SnowballTokenFilter">
            <summary>
            A token filter that stems words using a Snowball-generated stemmer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SnowballTokenFilter.Language(System.String)">
            <summary>
            Sets the Snowball-generated stemmer language.
            Defaults to "English".
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SnowballTokenFilter.Language(PlainElastic.Net.IndexSettings.SnowballLanguages)">
            <summary>
            Sets the Snowball-generated stemmer language.
            Defaults to English.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StandardTokenFilter">
            <summary>
            A token filter of type standard that normalizes tokens extracted with the Standard Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StemmerTokenFilter">
            <summary>
            A token filter that stems words (similar to snowball, but with more options).
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stemmer-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StemmerTokenFilter.Language(System.String)">
            <summary>
            Sets the stemmer language.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StemmerTokenFilter.Language(PlainElastic.Net.IndexSettings.StemmerTokenFilterLanguages)">
            <summary>
            Sets the stemmer language.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StopTokenFilter">
            <summary>
            A token filter of type stop that removes stop words from token streams.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopTokenFilter.Stopwords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of stopwords to initialize the filter with.
            Defaults to English stop words.
            stopwords allow for custom language specific expansion of default stopwords. It follows the _lang_ notation.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopTokenFilter.Stopwords(System.String[])">
            <summary>
            Sets a list of stopwords to initialize the filter with.
            Defaults to English stop words.
            stopwords allow for custom language specific expansion of default stopwords. It follows the _lang_ notation.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopTokenFilter.StopwordsPath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a stopwords file configuration.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopTokenFilter.EnablePositionIncrements(System.Boolean)">
            <summary>
            Sets flag indicating that token positions should record the removed stop words.
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopTokenFilter.IgnoreCase(System.Boolean)">
            <summary>
            Sets flag indicating that filter should lower case all words first.
            Defaults to false.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.SynonymTokenFilter">
            <summary>
            The synonym token filter allows to easily handle synonyms during the analysis process.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/synonym-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.Synonyms(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets the synonyms configuration in the specified format.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.Synonyms(System.String[])">
            <summary>
            Sets the synonyms configuration in the specified format.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.SynonymsPath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a synonyms file configuration.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.Format(PlainElastic.Net.IndexSettings.SynonymTokenFilterFormats)">
            <summary>
            Sets the synonyms configuration format.
            Defaults to Solr.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.IgnoreCase(System.Boolean)">
            <summary>
            Sets flag indicating that tokens case should be ignored.
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.Expand(System.Boolean)">
            <summary>
            Sets flag which enables synonyms further tokenization.
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.Tokenizer(System.String)">
            <summary>
            Sets the logical / registered name of the tokenizer to use for synonyms tokenization.
            Defaults to "whitespace".
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SynonymTokenFilter.Tokenizer(PlainElastic.Net.IndexSettings.DefaultTokenizers)">
            <summary>
            Sets the logical / registered name of the tokenizer to use for synonyms tokenization.
            Defaults to whitespace.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.TokenFilterSettings">
            <summary>
            Allows to configure token filters to be used in custom analyzers.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Asciifolding(System.String,System.Func{PlainElastic.Net.IndexSettings.AsciifoldingTokenFilter,PlainElastic.Net.IndexSettings.AsciifoldingTokenFilter})">
            <summary>
            A token filter of type asciifolding that converts alphabetic, numeric, and symbolic Unicode characters
            which are not in the first 127 ASCII characters (the "Basic Latin" Unicode block) into their ASCII equivalents, if one exists.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/asciifolding-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Asciifolding(System.Func{PlainElastic.Net.IndexSettings.AsciifoldingTokenFilter,PlainElastic.Net.IndexSettings.AsciifoldingTokenFilter})">
            <summary>
            A token filter of type asciifolding that converts alphabetic, numeric, and symbolic Unicode characters
            which are not in the first 127 ASCII characters (the "Basic Latin" Unicode block) into their ASCII equivalents, if one exists.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/asciifolding-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.DictionaryDecompounder(System.String,System.Func{PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter,PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter})">
            <summary>
            A token filter of type dictionary_decompounder that allows to decompose compound words.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/compound-word-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.DictionaryDecompounder(System.Func{PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter,PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter})">
            <summary>
            A token filter of type dictionary_decompounder that allows to decompose compound words.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/compound-word-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.EdgeNGram(System.String,System.Func{PlainElastic.Net.IndexSettings.EdgeNGramTokenFilter,PlainElastic.Net.IndexSettings.EdgeNGramTokenFilter})">
            <summary>
            A token filter of type edgeNGram that builds N-characters substrings from text. Substrings are built from one side of a text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/edgengram-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.EdgeNGram(System.Func{PlainElastic.Net.IndexSettings.EdgeNGramTokenFilter,PlainElastic.Net.IndexSettings.EdgeNGramTokenFilter})">
            <summary>
            A token filter of type edgeNGram that builds N-characters substrings from text. Substrings are built from one side of a text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/edgengram-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Elision(System.String,System.Func{PlainElastic.Net.IndexSettings.ElisionTokenFilter,PlainElastic.Net.IndexSettings.ElisionTokenFilter})">
            <summary>
            A token filter which removes elisions. For example, "l'avion" (the plane) will be tokenized as "avion" (plane).
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/elision-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Elision(System.Func{PlainElastic.Net.IndexSettings.ElisionTokenFilter,PlainElastic.Net.IndexSettings.ElisionTokenFilter})">
            <summary>
            A token filter which removes elisions. For example, "l'avion" (the plane) will be tokenized as "avion" (plane).
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/elision-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.HyphenationDecompounder(System.String,System.Func{PlainElastic.Net.IndexSettings.HyphenationDecompounderTokenFilter,PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter})">
            <summary>
            A token filter of type hyphenation_decompounder that allows to decompose compound words.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/compound-word-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.HyphenationDecompounder(System.Func{PlainElastic.Net.IndexSettings.HyphenationDecompounderTokenFilter,PlainElastic.Net.IndexSettings.DictionaryDecompounderTokenFilter})">
            <summary>
            A token filter of type hyphenation_decompounder that allows to decompose compound words.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/compound-word-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Kstem(System.String,System.Func{PlainElastic.Net.IndexSettings.KstemTokenFilter,PlainElastic.Net.IndexSettings.KstemTokenFilter})">
            <summary>
            The kstem token filter is a high performance filter for english.
            All terms must already be lowercased (use lowercase filter) for this filter to work correctly.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/kstem-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Kstem(System.Func{PlainElastic.Net.IndexSettings.KstemTokenFilter,PlainElastic.Net.IndexSettings.KstemTokenFilter})">
            <summary>
            The kstem token filter is a high performance filter for english.
            All terms must already be lowercased (use lowercase filter) for this filter to work correctly.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/kstem-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Length(System.String,System.Func{PlainElastic.Net.IndexSettings.LengthTokenFilter,PlainElastic.Net.IndexSettings.LengthTokenFilter})">
            <summary>
            A token filter of type length that removes words that are too long or too short for the stream.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/length-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Length(System.Func{PlainElastic.Net.IndexSettings.LengthTokenFilter,PlainElastic.Net.IndexSettings.LengthTokenFilter})">
            <summary>
            A token filter of type length that removes words that are too long or too short for the stream.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/length-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Lowercase(System.String,System.Func{PlainElastic.Net.IndexSettings.LowercaseTokenFilter,PlainElastic.Net.IndexSettings.LowercaseTokenFilter})">
            <summary>
            A token filter of type lowercase that normalizes token text to lower case.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Lowercase(System.Func{PlainElastic.Net.IndexSettings.LowercaseTokenFilter,PlainElastic.Net.IndexSettings.LowercaseTokenFilter})">
            <summary>
            A token filter of type lowercase that normalizes token text to lower case.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.NGram(System.String,System.Func{PlainElastic.Net.IndexSettings.NGramTokenFilter,PlainElastic.Net.IndexSettings.NGramTokenFilter})">
            <summary>
            A token filter of type nGram that builds N-characters substrings from text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/ngram-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.NGram(System.Func{PlainElastic.Net.IndexSettings.NGramTokenFilter,PlainElastic.Net.IndexSettings.NGramTokenFilter})">
            <summary>
            A token filter of type nGram that builds N-characters substrings from text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/ngram-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.PatternReplace(System.String,System.Func{PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter,PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter})">
            <summary>
            The pattern_replace token filter allows to easily handle string replacements based on a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern_replace-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.PatternReplace(System.Func{PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter,PlainElastic.Net.IndexSettings.PatternReplaceTokenFilter})">
            <summary>
            The pattern_replace token filter allows to easily handle string replacements based on a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern_replace-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Phonetic(System.String,System.Func{PlainElastic.Net.IndexSettings.PhoneticTokenFilter,PlainElastic.Net.IndexSettings.PhoneticTokenFilter})">
            <summary>
            A phonetic analysis token filter plugin.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/phonetic-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Phonetic(System.Func{PlainElastic.Net.IndexSettings.PhoneticTokenFilter,PlainElastic.Net.IndexSettings.PhoneticTokenFilter})">
            <summary>
            A phonetic analysis token filter plugin.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/phonetic-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.PorterStem(System.String,System.Func{PlainElastic.Net.IndexSettings.PorterStemTokenFilter,PlainElastic.Net.IndexSettings.PorterStemTokenFilter})">
            <summary>
            A token filter of type porterStem that transforms the token stream as per the Porter stemming algorithm.
            Note, the input to the stemming filter must already be in lower case, so you will need to use Lower Case Token Filter
            or Lower Case Tokenizer farther down the Tokenizer chain in order for this to work properly!
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/porterstem-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.PorterStem(System.Func{PlainElastic.Net.IndexSettings.PorterStemTokenFilter,PlainElastic.Net.IndexSettings.PorterStemTokenFilter})">
            <summary>
            A token filter of type porterStem that transforms the token stream as per the Porter stemming algorithm.
            Note, the input to the stemming filter must already be in lower case, so you will need to use Lower Case Token Filter
            or Lower Case Tokenizer farther down the Tokenizer chain in order for this to work properly!
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/porterstem-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Reverse(System.String,System.Func{PlainElastic.Net.IndexSettings.ReverseTokenFilter,PlainElastic.Net.IndexSettings.ReverseTokenFilter})">
            <summary>
            A token filter of type reverse that simply reverses the tokens.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/reverse-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Reverse(System.Func{PlainElastic.Net.IndexSettings.ReverseTokenFilter,PlainElastic.Net.IndexSettings.ReverseTokenFilter})">
            <summary>
            A token filter of type reverse that simply reverses the tokens.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/reverse-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Shingle(System.String,System.Func{PlainElastic.Net.IndexSettings.ShingleTokenFilter,PlainElastic.Net.IndexSettings.ShingleTokenFilter})">
            <summary>
            A token filter of type shingle that constructs shingles (token n-grams) from a token stream.
            In other words, it creates combinations of tokens as a single token.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/shingle-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Shingle(System.Func{PlainElastic.Net.IndexSettings.ShingleTokenFilter,PlainElastic.Net.IndexSettings.ShingleTokenFilter})">
            <summary>
            A token filter of type shingle that constructs shingles (token n-grams) from a token stream.
            In other words, it creates combinations of tokens as a single token.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/shingle-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Snowball(System.String,System.Func{PlainElastic.Net.IndexSettings.SnowballTokenFilter,PlainElastic.Net.IndexSettings.SnowballTokenFilter})">
            <summary>
            A token filter that stems words using a Snowball-generated stemmer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Snowball(System.Func{PlainElastic.Net.IndexSettings.SnowballTokenFilter,PlainElastic.Net.IndexSettings.SnowballTokenFilter})">
            <summary>
            A token filter that stems words using a Snowball-generated stemmer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Standard(System.String,System.Func{PlainElastic.Net.IndexSettings.StandardTokenFilter,PlainElastic.Net.IndexSettings.StandardTokenFilter})">
            <summary>
            A token filter of type standard that normalizes tokens extracted with the Standard Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Standard(System.Func{PlainElastic.Net.IndexSettings.StandardTokenFilter,PlainElastic.Net.IndexSettings.StandardTokenFilter})">
            <summary>
            A token filter of type standard that normalizes tokens extracted with the Standard Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Stemmer(System.String,System.Func{PlainElastic.Net.IndexSettings.StemmerTokenFilter,PlainElastic.Net.IndexSettings.StemmerTokenFilter})">
            <summary>
            A token filter that stems words (similar to snowball, but with more options).
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stemmer-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Stemmer(System.Func{PlainElastic.Net.IndexSettings.StemmerTokenFilter,PlainElastic.Net.IndexSettings.StemmerTokenFilter})">
            <summary>
            A token filter that stems words (similar to snowball, but with more options).
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stemmer-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Stop(System.String,System.Func{PlainElastic.Net.IndexSettings.StopTokenFilter,PlainElastic.Net.IndexSettings.StopTokenFilter})">
            <summary>
            A token filter of type stop that removes stop words from token streams.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Stop(System.Func{PlainElastic.Net.IndexSettings.StopTokenFilter,PlainElastic.Net.IndexSettings.StopTokenFilter})">
            <summary>
            A token filter of type stop that removes stop words from token streams.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Synonym(System.String,System.Func{PlainElastic.Net.IndexSettings.SynonymTokenFilter,PlainElastic.Net.IndexSettings.SynonymTokenFilter})">
            <summary>
            The synonym token filter allows to easily handle synonyms during the analysis process.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/synonym-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Synonym(System.Func{PlainElastic.Net.IndexSettings.SynonymTokenFilter,PlainElastic.Net.IndexSettings.SynonymTokenFilter})">
            <summary>
            The synonym token filter allows to easily handle synonyms during the analysis process.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/synonym-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Trim(System.String,System.Func{PlainElastic.Net.IndexSettings.TrimTokenFilter,PlainElastic.Net.IndexSettings.TrimTokenFilter})">
            <summary>
            The trim token filter trims surrounding whitespaces around a token.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/trim-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Trim(System.Func{PlainElastic.Net.IndexSettings.TrimTokenFilter,PlainElastic.Net.IndexSettings.TrimTokenFilter})">
            <summary>
            The trim token filter trims surrounding whitespaces around a token.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/trim-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Truncate(System.String,System.Func{PlainElastic.Net.IndexSettings.TruncateTokenFilter,PlainElastic.Net.IndexSettings.TruncateTokenFilter})">
            <summary>
            The truncate token filter can be used to truncate tokens into a specific length.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/truncate-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Truncate(System.Func{PlainElastic.Net.IndexSettings.TruncateTokenFilter,PlainElastic.Net.IndexSettings.TruncateTokenFilter})">
            <summary>
            The truncate token filter can be used to truncate tokens into a specific length.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/truncate-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Unique(System.String,System.Func{PlainElastic.Net.IndexSettings.UniqueTokenFilter,PlainElastic.Net.IndexSettings.UniqueTokenFilter})">
            <summary>
            The unique token filter can be used to only index unique tokens during analysis.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/unique-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.Unique(System.Func{PlainElastic.Net.IndexSettings.UniqueTokenFilter,PlainElastic.Net.IndexSettings.UniqueTokenFilter})">
            <summary>
            The unique token filter can be used to only index unique tokens during analysis.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/unique-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.WordDelimiter(System.String,System.Func{PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter,PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter})">
            <summary>
            Named word_delimiter, it splits words into subwords and performs optional transformations on subword groups.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/word-delimiter-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenFilterSettings.WordDelimiter(System.Func{PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter,PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter})">
            <summary>
            Named word_delimiter, it splits words into subwords and performs optional transformations on subword groups.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/word-delimiter-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.TrimTokenFilter">
            <summary>
            The trim token filter trims surrounding whitespaces around a token.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/trim-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.TruncateTokenFilter">
            <summary>
            The truncate token filter can be used to truncate tokens into a specific length.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/truncate-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TruncateTokenFilter.Length(System.Int32)">
            <summary>
            Sets the number of characters to truncate to.
            Defaults to 10.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.UniqueTokenFilter">
            <summary>
            The unique token filter can be used to only index unique tokens during analysis.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/unique-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.UniqueTokenFilter.OnlyOnSamePosition(System.Boolean)">
            <summary>
            Sets flag indicating that only duplicate tokens on the same position should be removed.
            Defaults to false.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter">
            <summary>
            Named word_delimiter, it splits words into subwords and performs optional transformations on subword groups.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/word-delimiter-tokenfilter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.GenerateWordParts(System.Boolean)">
            <summary>
            Sets flag causing parts of words to be generated: "PowerShot" => "Power" "Shot".
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.GenerateNumberParts(System.Boolean)">
            <summary>
            Sets flag causing number subwords to be generated: "500-42" => "500" "42".
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.CatenateWords(System.Boolean)">
            <summary>
            Sets flag causing maximum runs of word parts to be catenated: "wi-fi" => "wifi".
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.CatenateNumbers(System.Boolean)">
            <summary>
            Sets flag causing maximum runs of number parts to be catenated: "500-42" => "50042".
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.CatenateAll(System.Boolean)">
            <summary>
            Sets flag causing all subword parts to be catenated: "wi-fi-4000" => "wifi4000".
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.SplitOnCaseChange(System.Boolean)">
            <summary>
            Sets flag causing "PowerShot" to be two tokens; ("Power-Shot" remains two parts regards).
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.PreserveOriginal(System.Boolean)">
            <summary>
            Sets flag controlling inclusion of original words in subwords: "500-42" => "500" "42" "500-42".
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.SplitOnNumerics(System.Boolean)">
            <summary>
            Sets flag causing "j2se" to be three tokens; "j" "2" "se".
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.StemEnglishPossessive(System.Boolean)">
            <summary>
            Sets flag causing trailing "'s" to be removed for each subword: "O'Neil's" => "O", "Neil".
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.ProtectedWords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of words protected from being delimited.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.ProtectedWords(System.String[])">
            <summary>
            Sets a list of words protected from being delimited.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.ProtectedWordsPath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a protected words file configuration.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.TypeTable(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a custom type mapping table.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.TypeTable(System.String[])">
            <summary>
            Sets a custom type mapping table.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.WordDelimiterTokenFilter.TypeTablePath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a custom type mapping file configuration.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.PathHierarchyTokenizer">
            <summary>
            The path_hierarchy tokenizer takes something like "/something/something/else"
            and produces tokens "/something", "/something/something", "/something/something/else".
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pathhierarchy-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PathHierarchyTokenizer.Delimiter(System.Char)">
            <summary>
            Sets the character delimiter to use.
            Defaults to '/'.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PathHierarchyTokenizer.Replacement(System.Char)">
            <summary>
            Sets an optional replacement character to use.
            Defaults to the delimiter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PathHierarchyTokenizer.BufferSize(System.Int32)">
            <summary>
            Sets the buffer size to use.
            Defaults to 1024.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PathHierarchyTokenizer.Reverse(System.Boolean)">
            <summary>
            Sets flag controlling tokens generation in reverse order.
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PathHierarchyTokenizer.Skip(System.Int32)">
            <summary>
            Sets number of initial tokens to skip.
            Defaults to 0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.UaxUrlEmailTokenizer">
            <summary>
            A tokenizer of type uax_url_email which works exactly like the standard tokenizer, but tokenizes emails and urls as single tokens.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/uaxurlemail-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StandardTokenizer">
            <summary>
            A tokenizer of type standard providing grammar based tokenizer that is a good tokenizer for most European language documents.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StandardTokenizer.MaxTokenLength(System.Int32)">
            <summary>
            Sets the maximum token length. If a token is seen that exceeds this length then it is discarded.
            Defaults to 255.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.NGramTokenizer">
            <summary>
            A tokenizer of type nGram that builds N-characters substrings from text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/ngram-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.KeywordTokenizer">
            <summary>
            A tokenizer of type keyword that emits the entire input as a single input.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.KeywordTokenizer.BufferSize(System.Int32)">
            <summary>
            Sets the term buffer size.
            Defaults to 256.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.EdgeNGramTokenizer">
            <summary>
            A tokenizer of type edgeNGram that builds N-characters substrings from text. Substrings are built from one side of a text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/edgengram-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.PatternTokenizer">
            <summary>
            A tokenizer of type pattern that can flexibly separate text into terms via a regular expression. 
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternTokenizer.Pattern(System.String)">
            <summary>
            Sets the regular expression pattern.
            Defaults to \W+.
            The regular expression should match the token separators, not the tokens themselves.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternTokenizer.Flags(PlainElastic.Net.RegexFlags)">
            <summary>
            Sets the regular expression flags.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternTokenizer.Group(System.Int32)">
            <summary>
            Sets which group to extract into tokens.
            Defaults to -1 (split).
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.TokenizerSettings">
            <summary>
            Allows to configure tokenizers to be used in custom analyzers.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.EdgeNGram(System.String,System.Func{PlainElastic.Net.IndexSettings.EdgeNGramTokenizer,PlainElastic.Net.IndexSettings.EdgeNGramTokenizer})">
            <summary>
            A tokenizer of type edgeNGram.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/edgengram-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.EdgeNGram(System.Func{PlainElastic.Net.IndexSettings.EdgeNGramTokenizer,PlainElastic.Net.IndexSettings.EdgeNGramTokenizer})">
            <summary>
            A tokenizer of type edgeNGram.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/edgengram-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Keyword(System.String,System.Func{PlainElastic.Net.IndexSettings.KeywordTokenizer,PlainElastic.Net.IndexSettings.KeywordTokenizer})">
            <summary>
            A tokenizer of type keyword that emits the entire input as a single input.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Keyword(System.Func{PlainElastic.Net.IndexSettings.KeywordTokenizer,PlainElastic.Net.IndexSettings.KeywordTokenizer})">
            <summary>
            A tokenizer of type keyword that emits the entire input as a single input.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Letter(System.String,System.Func{PlainElastic.Net.IndexSettings.LetterTokenizer,PlainElastic.Net.IndexSettings.LetterTokenizer})">
            <summary>
            A tokenizer of type letter that divides text at non-letters. That's to say, it defines tokens as maximal strings of adjacent letters.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/letter-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Letter(System.Func{PlainElastic.Net.IndexSettings.LetterTokenizer,PlainElastic.Net.IndexSettings.LetterTokenizer})">
            <summary>
            A tokenizer of type letter that divides text at non-letters. That's to say, it defines tokens as maximal strings of adjacent letters.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/letter-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Lowercase(System.String,System.Func{PlainElastic.Net.IndexSettings.LowercaseTokenizer,PlainElastic.Net.IndexSettings.LowercaseTokenizer})">
            <summary>
            A tokenizer of type lowercase that performs the function of Letter Tokenizer and Lower Case Token Filter together.
            It divides text at non-letters and converts them to lower case.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Lowercase(System.Func{PlainElastic.Net.IndexSettings.LowercaseTokenizer,PlainElastic.Net.IndexSettings.LowercaseTokenizer})">
            <summary>
            A tokenizer of type lowercase that performs the function of Letter Tokenizer and Lower Case Token Filter together.
            It divides text at non-letters and converts them to lower case.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.NGram(System.String,System.Func{PlainElastic.Net.IndexSettings.NGramTokenizer,PlainElastic.Net.IndexSettings.NGramTokenizer})">
            <summary>
            A tokenizer of type nGram that builds N-characters substrings from text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/ngram-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.NGram(System.Func{PlainElastic.Net.IndexSettings.NGramTokenizer,PlainElastic.Net.IndexSettings.NGramTokenizer})">
            <summary>
            A tokenizer of type nGram that builds N-characters substrings from text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/ngram-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Standard(System.String,System.Func{PlainElastic.Net.IndexSettings.StandardTokenizer,PlainElastic.Net.IndexSettings.StandardTokenizer})">
            <summary>
            A tokenizer of type standard providing grammar based tokenizer that is a good tokenizer for most European language documents.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Standard(System.Func{PlainElastic.Net.IndexSettings.StandardTokenizer,PlainElastic.Net.IndexSettings.StandardTokenizer})">
            <summary>
            A tokenizer of type standard providing grammar based tokenizer that is a good tokenizer for most European language documents.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Whitespace(System.String,System.Func{PlainElastic.Net.IndexSettings.WhitespaceTokenizer,PlainElastic.Net.IndexSettings.WhitespaceTokenizer})">
            <summary>
            A tokenizer of type whitespace that divides text at whitespace.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Whitespace(System.Func{PlainElastic.Net.IndexSettings.WhitespaceTokenizer,PlainElastic.Net.IndexSettings.WhitespaceTokenizer})">
            <summary>
            A tokenizer of type whitespace that divides text at whitespace.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Pattern(System.String,System.Func{PlainElastic.Net.IndexSettings.PatternTokenizer,PlainElastic.Net.IndexSettings.PatternTokenizer})">
            <summary>
            A tokenizer of type pattern that can flexibly separate text into terms via a regular expression. 
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.Pattern(System.Func{PlainElastic.Net.IndexSettings.PatternTokenizer,PlainElastic.Net.IndexSettings.PatternTokenizer})">
            <summary>
            A tokenizer of type pattern that can flexibly separate text into terms via a regular expression. 
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.UaxUrlEmail(System.String,System.Func{PlainElastic.Net.IndexSettings.UaxUrlEmailTokenizer,PlainElastic.Net.IndexSettings.StandardTokenizer})">
            <summary>
            A tokenizer of type uax_url_email which works exactly like the standard tokenizer, but tokenizes emails and urls as single tokens.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/uaxurlemail-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.UaxUrlEmail(System.Func{PlainElastic.Net.IndexSettings.UaxUrlEmailTokenizer,PlainElastic.Net.IndexSettings.StandardTokenizer})">
            <summary>
            A tokenizer of type uax_url_email which works exactly like the standard tokenizer, but tokenizes emails and urls as single tokens.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/uaxurlemail-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.PathHierarchy(System.String,System.Func{PlainElastic.Net.IndexSettings.PathHierarchyTokenizer,PlainElastic.Net.IndexSettings.PathHierarchyTokenizer})">
            <summary>
            The path_hierarchy tokenizer takes something like "/something/something/else"
            and produces tokens "/something", "/something/something", "/something/something/else".
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pathhierarchy-tokenizer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.TokenizerSettings.PathHierarchy(System.Func{PlainElastic.Net.IndexSettings.PathHierarchyTokenizer,PlainElastic.Net.IndexSettings.PathHierarchyTokenizer})">
            <summary>
            The path_hierarchy tokenizer takes something like "/something/something/else"
            and produces tokens "/something", "/something/something", "/something/something/else".
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pathhierarchy-tokenizer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.AnalyzerSettings">
            <summary>
            Allows to configure standard/custom analyzers to be used in mapping API.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Standard(System.String,System.Func{PlainElastic.Net.IndexSettings.StandardAnalyzer,PlainElastic.Net.IndexSettings.StandardAnalyzer})">
            <summary>
            An analyzer of type standard that is built of using
            Standard Tokenizer, with Standard Token Filter, Lower Case Token Filter, and Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Standard(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.StandardAnalyzer,PlainElastic.Net.IndexSettings.StandardAnalyzer})">
            <summary>
            An analyzer of type standard that is built of using
            Standard Tokenizer, with Standard Token Filter, Lower Case Token Filter, and Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Standard(System.Func{PlainElastic.Net.IndexSettings.StandardAnalyzer,PlainElastic.Net.IndexSettings.StandardAnalyzer})">
            <summary>
            An analyzer of type standard that is built of using
            Standard Tokenizer, with Standard Token Filter, Lower Case Token Filter, and Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Simple(System.String,System.Func{PlainElastic.Net.IndexSettings.SimpleAnalyzer,PlainElastic.Net.IndexSettings.SimpleAnalyzer})">
            <summary>
            An analyzer of type simple that is built using a Lower Case Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/simple-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Simple(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.SimpleAnalyzer,PlainElastic.Net.IndexSettings.SimpleAnalyzer})">
            <summary>
            An analyzer of type simple that is built using a Lower Case Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/simple-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Simple(System.Func{PlainElastic.Net.IndexSettings.SimpleAnalyzer,PlainElastic.Net.IndexSettings.SimpleAnalyzer})">
            <summary>
            An analyzer of type simple that is built using a Lower Case Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/simple-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Whitespace(System.String,System.Func{PlainElastic.Net.IndexSettings.WhitespaceAnalyzer,PlainElastic.Net.IndexSettings.WhitespaceAnalyzer})">
            <summary>
            An analyzer of type whitespace that is built using a Whitespace Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Whitespace(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.WhitespaceAnalyzer,PlainElastic.Net.IndexSettings.WhitespaceAnalyzer})">
            <summary>
            An analyzer of type whitespace that is built using a Whitespace Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Whitespace(System.Func{PlainElastic.Net.IndexSettings.WhitespaceAnalyzer,PlainElastic.Net.IndexSettings.WhitespaceAnalyzer})">
            <summary>
            An analyzer of type whitespace that is built using a Whitespace Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Stop(System.String,System.Func{PlainElastic.Net.IndexSettings.StopAnalyzer,PlainElastic.Net.IndexSettings.StopAnalyzer})">
            <summary>
            An analyzer of type stop that is built using a Lower Case Tokenizer, with Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Stop(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.StopAnalyzer,PlainElastic.Net.IndexSettings.StopAnalyzer})">
            <summary>
            An analyzer of type stop that is built using a Lower Case Tokenizer, with Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Stop(System.Func{PlainElastic.Net.IndexSettings.StopAnalyzer,PlainElastic.Net.IndexSettings.StopAnalyzer})">
            <summary>
            An analyzer of type stop that is built using a Lower Case Tokenizer, with Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Keyword(System.String,System.Func{PlainElastic.Net.IndexSettings.KeywordAnalyzer,PlainElastic.Net.IndexSettings.KeywordAnalyzer})">
            <summary>
            An analyzer of type keyword that “tokenizes” an entire stream as a single token.
            This is useful for data like zip codes, ids and so on.
            Note, when using mapping definitions, it make more sense to simply mark the field as not_analyzed.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Keyword(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.KeywordAnalyzer,PlainElastic.Net.IndexSettings.KeywordAnalyzer})">
            <summary>
            An analyzer of type keyword that “tokenizes” an entire stream as a single token.
            This is useful for data like zip codes, ids and so on.
            Note, when using mapping definitions, it make more sense to simply mark the field as not_analyzed.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Keyword(System.Func{PlainElastic.Net.IndexSettings.KeywordAnalyzer,PlainElastic.Net.IndexSettings.KeywordAnalyzer})">
            <summary>
            An analyzer of type keyword that “tokenizes” an entire stream as a single token.
            This is useful for data like zip codes, ids and so on.
            Note, when using mapping definitions, it make more sense to simply mark the field as not_analyzed.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Pattern(System.String,System.Func{PlainElastic.Net.IndexSettings.PatternAnalyzer,PlainElastic.Net.IndexSettings.PatternAnalyzer})">
            <summary>
            An analyzer of type pattern that can flexibly separate text into terms via a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Pattern(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.PatternAnalyzer,PlainElastic.Net.IndexSettings.PatternAnalyzer})">
            <summary>
            An analyzer of type pattern that can flexibly separate text into terms via a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Pattern(System.Func{PlainElastic.Net.IndexSettings.PatternAnalyzer,PlainElastic.Net.IndexSettings.PatternAnalyzer})">
            <summary>
            An analyzer of type pattern that can flexibly separate text into terms via a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Language(System.String,System.Func{PlainElastic.Net.IndexSettings.LanguageAnalyzer,PlainElastic.Net.IndexSettings.LanguageAnalyzer})">
            <summary>
            A set of analyzers aimed at analyzing specific language text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lang-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Language(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.LanguageAnalyzer,PlainElastic.Net.IndexSettings.LanguageAnalyzer})">
            <summary>
            A set of analyzers aimed at analyzing specific language text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lang-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Language(System.Func{PlainElastic.Net.IndexSettings.LanguageAnalyzer,PlainElastic.Net.IndexSettings.LanguageAnalyzer})">
            <summary>
            A set of analyzers aimed at analyzing specific language text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lang-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Snowball(System.String,System.Func{PlainElastic.Net.IndexSettings.SnowballAnalyzer,PlainElastic.Net.IndexSettings.SnowballAnalyzer})">
            <summary>
            An analyzer of type snowball that uses the standard tokenizer, with standard filter, lowercase filter, stop filter, and snowball filter.
            The Snowball Analyzer is a stemming analyzer from Lucene that is originally based on the snowball project from snowball.tartarus.org.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Snowball(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.SnowballAnalyzer,PlainElastic.Net.IndexSettings.SnowballAnalyzer})">
            <summary>
            An analyzer of type snowball that uses the standard tokenizer, with standard filter, lowercase filter, stop filter, and snowball filter.
            The Snowball Analyzer is a stemming analyzer from Lucene that is originally based on the snowball project from snowball.tartarus.org.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Snowball(System.Func{PlainElastic.Net.IndexSettings.SnowballAnalyzer,PlainElastic.Net.IndexSettings.SnowballAnalyzer})">
            <summary>
            An analyzer of type snowball that uses the standard tokenizer, with standard filter, lowercase filter, stop filter, and snowball filter.
            The Snowball Analyzer is a stemming analyzer from Lucene that is originally based on the snowball project from snowball.tartarus.org.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Custom(System.String,System.Func{PlainElastic.Net.IndexSettings.CustomAnalyzer,PlainElastic.Net.IndexSettings.CustomAnalyzer})">
            <summary>
            An analyzer of type custom that allows to combine a Tokenizer with zero or more Token Filters, and zero or more Char Filters.
            The custom analyzer accepts a logical/registered name of the tokenizer to use, and a list of logical/registered names of token filters.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/custom-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerSettings.Custom(PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases,System.Func{PlainElastic.Net.IndexSettings.CustomAnalyzer,PlainElastic.Net.IndexSettings.CustomAnalyzer})">
            <summary>
            An analyzer of type custom that allows to combine a Tokenizer with zero or more Token Filters, and zero or more Char Filters.
            The custom analyzer accepts a logical/registered name of the tokenizer to use, and a list of logical/registered names of token filters.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/custom-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerBase`1.Alias(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets analyzer aliases to have several registered lookup names associated with analyzer.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.AnalyzerBase`1.Alias(System.String[])">
            <summary>
            Sets analyzer aliases to have several registered lookup names associated with analyzer.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.CustomAnalyzer">
            <summary>
            An analyzer of type custom that allows to combine a Tokenizer with zero or more Token Filters, and zero or more Char Filters.
            The custom analyzer accepts a logical/registered name of the tokenizer to use, and a list of logical/registered names of token filters.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/custom-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.Tokenizer(System.String)">
            <summary>
            Sets the logical / registered name of the tokenizer to use.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.Tokenizer(PlainElastic.Net.IndexSettings.DefaultTokenizers)">
            <summary>
            Sets the logical / registered name of the tokenizer to use.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.Filter(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets an optional list of logical / registered name of token filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.Filter(System.String[])">
            <summary>
            Sets an optional list of logical / registered name of token filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.Filter(System.Collections.Generic.IEnumerable{PlainElastic.Net.IndexSettings.DefaultTokenFilters})">
            <summary>
            Sets an optional list of logical / registered name of token filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.Filter(PlainElastic.Net.IndexSettings.DefaultTokenFilters[])">
            <summary>
            Sets an optional list of logical / registered name of token filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.CharFilter(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets an optional list of logical / registered name of char filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.CharFilter(System.String[])">
            <summary>
            Sets an optional list of logical / registered name of char filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.CharFilter(System.Collections.Generic.IEnumerable{PlainElastic.Net.IndexSettings.DefaultCharFilters})">
            <summary>
            Sets an optional list of logical / registered name of char filters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.CustomAnalyzer.CharFilter(PlainElastic.Net.IndexSettings.DefaultCharFilters[])">
            <summary>
            Sets an optional list of logical / registered name of char filters.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.KeywordAnalyzer">
            <summary>
            An analyzer of type keyword that “tokenizes” an entire stream as a single token.
            This is useful for data like zip codes, ids and so on.
            Note, when using mapping definitions, it make more sense to simply mark the field as not_analyzed.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/keyword-analyzer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LanguageAnalyzer">
            <summary>
            A set of analyzers aimed at analyzing specific language text.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lang-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopwordsAnalyzerBase`1.Stopwords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopwordsAnalyzerBase`1.Stopwords(System.String[])">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StopwordsAnalyzerBase`1.StopwordsPath(System.String)">
            <summary>
            Sets a path (either relative to config location, or absolute) to a stopwords file configuration.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LanguageAnalyzer.Type(System.String)">
            <summary>
            Sets the language analyzer type.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LanguageAnalyzer.Type(PlainElastic.Net.IndexSettings.LanguageAnalyzerTypes)">
            <summary>
            Sets the language analyzer type.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LanguageAnalyzer.StemExclusion(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of stem exclusion words supported by the following languages:
            arabic, armenian, basque, brazilian, bulgarian, catalan, czech, danish, dutch, english, finnish, french, galician, german, hindi, hungarian, indonesian, italian, norwegian, portuguese, romanian, russian, spanish, swedish, turkish.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.LanguageAnalyzer.StemExclusion(System.String[])">
            <summary>
            Sets a list of stem exclusion words supported by the following languages:
            arabic, armenian, basque, brazilian, bulgarian, catalan, czech, danish, dutch, english, finnish, french, galician, german, hindi, hungarian, indonesian, italian, norwegian, portuguese, romanian, russian, spanish, swedish, turkish.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.PatternAnalyzer">
            <summary>
            An analyzer of type pattern that can flexibly separate text into terms via a regular expression.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/pattern-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternAnalyzer.Lowercase(System.Boolean)">
            <summary>
            Should terms be lowercased or not.
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternAnalyzer.Pattern(System.String)">
            <summary>
            Sets the regular expression pattern.
            Defaults to \W+.
            The regular expression should match the token separators, not the tokens themselves.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.PatternAnalyzer.Flags(PlainElastic.Net.RegexFlags)">
            <summary>
            Sets the regular expression flags.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.SimpleAnalyzer">
            <summary>
            An analyzer of type simple that is built using a Lower Case Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/simple-analyzer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.SnowballAnalyzer">
            <summary>
            An analyzer of type snowball that uses the standard tokenizer, with standard filter, lowercase filter, stop filter, and snowball filter.
            The Snowball Analyzer is a stemming analyzer from Lucene that is originally based on the snowball project from snowball.tartarus.org.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SnowballAnalyzer.Language(System.String)">
            <summary>
            Sets the Snowball-generated stemmer language.
            Defaults to English.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.SnowballAnalyzer.Language(PlainElastic.Net.IndexSettings.SnowballLanguages)">
            <summary>
            Sets the Snowball-generated stemmer language.
            Defaults to English.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StandardAnalyzer">
            <summary>
            An analyzer of type standard that is built of using
            Standard Tokenizer, with Standard Token Filter, Lower Case Token Filter, and Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/standard-analyzer.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.StandardAnalyzer.MaxTokenLength(System.Int32)">
            <summary>
            Sets the maximum token length. If a token is seen that exceeds this length then it is discarded.
            Defaults to 255.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StopAnalyzer">
            <summary>
            An analyzer of type stop that is built using a Lower Case Tokenizer, with Stop Token Filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stop-analyzer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.WhitespaceAnalyzer">
            <summary>
            An analyzer of type whitespace that is built using a Whitespace Tokenizer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/whitespace-analyzer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases">
            <summary>
            Used to define which analyzers will be used by default when none can be derived.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases.default">
            <summary>
            Allows one to configure an analyzer that will be used both for indexing and for searching APIs.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases.default_index">
            <summary>
            Can be used to configure a default analyzer that will be used just when indexing.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.IndexSettings.AnalyzersDefaultAliases.default_search">
            <summary>
            Can be used to configure a default analyzer that will be used just when searching.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.DefaultTokenizers">
            <summary>
            Tokenizers act as the first stage of the analysis process.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.DefaultTokenFilters">
            <summary>
            Token filters act as additional stages of the analysis process.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.DefaultCharFilters">
            <summary>
            Char filters allow one to filter out the stream of text before it gets tokenized.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LanguageAnalyzerTypes">
            <summary>
            Supported language analyzer types.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lang-analyzer.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.SnowballLanguages">
            <summary>
            Languages supported by a Snowball-generated stemmer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/snowball-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.LowercaseTokenFilterLanguages">
            <summary>
            Languages supported by the lowercase token filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/lowercase-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.StemmerTokenFilterLanguages">
            <summary>
            Languages supported by the stemmer token filter.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/stemmer-tokenfilter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.IndexSettings.IndexSettingsBuilder">
            <summary>
            Allows to build index level settings.
            see: http://www.elasticsearch.org/guide/reference/api/admin-indices-update-settings.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.IndexSettingsBuilder.Analysis(System.Func{PlainElastic.Net.IndexSettings.Analysis,PlainElastic.Net.IndexSettings.Analysis})">
            <summary>
            The index analysis module acts as a configurable registry of Analyzers
            that can be used in order to both break indexed (analyzed) fields when a document is indexed and process query strings.
            It maps to the Lucene Analyzer.
            see http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.IndexSettingsBuilder.NumberOfShards(System.Int32)">
            <summary>
            Defines the number of shards 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.IndexSettings.IndexSettingsBuilder.NumberOfReplicas(System.Int32)">
            <summary>
            Defines the number of replicas each shard has
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.Attachment`1">
            <summary>
            The attachment type allows to index different “attachment” type field (encoded as base64),
            for example, microsoft office formats, open document formats, ePub, HTML, and so on.
            see http://www.elasticsearch.org/guide/reference/mapping/attachment-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MappingBase`1.Custom(System.String,System.String[])">
            <summary>
            Adds a custom mapping to Map.
            You can use ' instead of " to simplify mapFormat creation.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MappingBase`1.RegisterCustomJsonMap(System.String,System.String[])">
            <summary>
            Adds a custom JSON mapping to Object Map.
            You can use ' instead of " to simplify mapFormat creation.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MappingBase`1.RegisterMapAsJson``2(System.Func{``0,``1})">
            <summary>
            Registers the passed map function as JSON got as result of its execution.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Attachment`1.Field(System.String)">
            <summary>
            The field name to assign to attachment mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Attachment`1.Field``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            The field to assign to attachment mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Attachment`1.Field``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}})">
            <summary>
            The field which is a collection to assign to attachment mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Attachment`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{`0},PlainElastic.Net.Mappings.CoreFields{`0}})">
            <summary>
            Mapping fields, to which attachment fields will be mapped.
            </summary>
        </member>
        <member name="P:PlainElastic.Net.Mappings.Attachment`1.Name">
            <summary>
            The name of mapped field.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.GeoPointMap`1">
            <summary>
            Represents geo based points
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/mapping-geo-point-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.PropertyBase`2.IndexName(System.String)">
            <summary>
            The name of the field that will be stored in the index. Defaults to the property/field name.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.PropertyBase`2.Store(System.Boolean)">
            <summary>
            Set to true the store actual field in the index, false to not store it.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.PropertyBase`2.Index(PlainElastic.Net.Mappings.IndexState)">
            <summary>
            Allows to control whether field searchable and analyzed.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.PropertyBase`2.Boost(System.Double)">
            <summary>
            Sets the boost value of the property. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.PropertyBase`2.NullValue(System.String)">
            <summary>
            When there is a (JSON) null value for the field, use the null_value as the field value. Defaults to not adding the field at all.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.PropertyBase`2.IncludeInAll(System.Boolean)">
            <summary>
            Defines should the field be included in the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.IndexLatLon(System.Boolean)">
            <summary>
            Set to true to also index the .lat and .lon as fields. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.IndexGeoHash(System.Boolean)">
            <summary>
            Set to true to also index the .geohash as a field. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.Validate(System.Boolean)">
            <summary>
            Set to true to reject geo points with invalid latitude or longitude (default is false) Note: Validation only works when normalization has been disabled.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.ValidateLat(System.Boolean)">
            <summary>
            Set to true to reject geo points with an invalid latitude
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.ValidateLon(System.Boolean)">
            <summary>
            Set to true to reject geo points with an invalid longitude
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.Normalize(System.Boolean)">
            <summary>
            Set to true to normalize latitude and longitude (default is true)
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.NormalizeLat(System.Boolean)">
            <summary>
            Set to true to normalize latitude
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.NormalizeLon(System.Boolean)">
            <summary>
            Set to true to normalize longitude
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.GeoHashPrefix(System.Boolean)">
            <summary>
            If this option is set to true, not only the geohash but also all its parent cells (true prefixes) will be indexed as well. The number of terms that will be indexed depends on the geohash_precision. Defaults to false. Note: This option implicitly enables geohash.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.GeoHashPrecision(System.Int32)">
            <summary>
            Sets the geohash precision. It can be set to an absolute geohash length or a distance value (eg 1km, 1m, 1ml) defining the size of the smallest cell. Defaults to an absolute length of 12.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.GeoPointMap`1.GeoHashPrecision(System.String)">
            <summary>
            Sets the geohash precision. It can be set to an absolute geohash length or a distance value (eg 1km, 1m, 1ml) defining the size of the smallest cell. Defaults to an absolute length of 12.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.CustomPropertyMap`1">
            <summary>
            Represents custom field mapping.
            This type of mapping useful when you need to provide runtime property mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.Type(System.String)">
            <summary>
            Allows explicitly specify ES type of the mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.When(System.Boolean,System.Func{PlainElastic.Net.Mappings.CustomPropertyMap{`0},PlainElastic.Net.Mappings.CustomPropertyMap{`0}})">
            <summary>
            Allows to build a condition dependent mapping. 
            Mapping will be applied only when condition true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.Format(System.String)">
            <summary>
            Allows to specify the date format.
            All dates are UTC.
            see  http://www.elasticsearch.org/guide/reference/mapping/date-format.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.PrecisionStep(System.Int32)">
            <summary>
             The precision step (number of terms generated for each number value). Defaults to 4.       
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.OmitNorms(System.Boolean)">
            <summary>
            Defines if norms should be omitted or not. 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.OmitTermFreqAndPositions(System.Boolean)">
            <summary>
            Defines if term freq and positions should be omitted.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.Analyzer(System.String)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing and when searching using a query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing and when searching using a query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.IndexAnalyzer(System.String)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.IndexAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.SearchAnalyzer(System.String)">
            <summary>
            The analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CustomPropertyMap`1.SearchAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.MultiField`1">
            <summary>
            The multi_field type allows to map several core types of the same value. 
            This can come very handy, for example, when wanting to map a string type, 
            once when its analyzed and once when its not_analyzed.
            see http://www.elasticsearch.org/guide/reference/mapping/multi-field-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MultiField`1.Field(System.String)">
            <summary>
            The field name to assign to multi field mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MultiField`1.Field``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            The field to assign to multi field mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MultiField`1.Field``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}})">
            <summary>
            The field which is a collection to assign to multi field mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.MultiField`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{`0},PlainElastic.Net.Mappings.CoreFields{`0}})">
            <summary>
            Mapping fields, to which original field will be mapped.
            </summary>
        </member>
        <member name="P:PlainElastic.Net.Mappings.MultiField`1.Name">
            <summary>
            The name of mapped field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.String``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.StringMap{`0},PlainElastic.Net.Mappings.StringMap{`0}})">
            <summary>
            Represents text fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.String(System.String,System.Func{PlainElastic.Net.Mappings.StringMap{`0},PlainElastic.Net.Mappings.StringMap{`0}})">
            <summary>
            Represents text fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Number``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.NumberMap{`0},PlainElastic.Net.Mappings.NumberMap{`0}})">
            <summary>
            Represents numeric fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Number(System.String,System.Func{PlainElastic.Net.Mappings.NumberMap{`0},PlainElastic.Net.Mappings.NumberMap{`0}})">
            <summary>
            Represents numeric fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Date``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.DateMap{`0},PlainElastic.Net.Mappings.DateMap{`0}})">
            <summary>
            Represents Date fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Date(System.String,System.Func{PlainElastic.Net.Mappings.DateMap{`0},PlainElastic.Net.Mappings.DateMap{`0}})">
            <summary>
            Represents Date fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Boolean``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.BooleanMap{`0},PlainElastic.Net.Mappings.BooleanMap{`0}})">
            <summary>
            Represents boolean fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Boolean(System.String,System.Func{PlainElastic.Net.Mappings.BooleanMap{`0},PlainElastic.Net.Mappings.BooleanMap{`0}})">
            <summary>
            Represents boolean fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Binary``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.BinaryMap{`0},PlainElastic.Net.Mappings.BinaryMap{`0}})">
            <summary>
            Represents binary data fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.CoreFields`1.Binary(System.String,System.Func{PlainElastic.Net.Mappings.BinaryMap{`0},PlainElastic.Net.Mappings.BinaryMap{`0}})">
            <summary>
            Represents binary data fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.IpMap`1">
            <summary>
            Represents IP fields mapping.
            Allows to store ipv4 addresses in a numeric form allowing to easily sort, and range query it (using ip values)
            see http://www.elasticsearch.org/guide/reference/mapping/ip-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.IpMap`1.PrecisionStep(System.Int32)">
            <summary>
            The precision step (number of terms generated for each number value). Defaults to 4.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.NestedObject`1">
            <summary>
            Builds a mapping that allows to map certain sections in the document indexed as nested allowing to query them as if they are separate docs joining with the parent owning doc.
            http://www.elasticsearch.org/guide/reference/mapping/nested-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Field(System.String)">
            <summary>
            The field name to assign to object mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Field``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            The field to assign to object mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Field``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}})">
            <summary>
            The field which is a collection to assign to object mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Properties(System.Func{PlainElastic.Net.Mappings.Properties{`0},PlainElastic.Net.Mappings.Properties{`0}})">
            <summary>
            Allows to Map object properties
            see http://www.elasticsearch.org/guide/reference/mapping/object-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Dynamic(System.Boolean)">
            <summary>
            Allows to disable dynamic JSOM object mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Enabled(System.Boolean)">
            <summary>
            Allows to disable parsing and adding a named object completely.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.Path(System.String)">
            <summary>
            Allows to specify custom path within index to mapped document. 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ObjectBase`2.IncludeInAll(System.Boolean)">
            <summary>
            Allows to control document and inner documents inclusion to _all field.
            </summary>
        </member>
        <member name="P:PlainElastic.Net.Mappings.ObjectBase`2.Name">
            <summary>
            The name of mapped object.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.NestedObject`1.IncludeInParent(System.Boolean)">
            <summary>
            Allows to object fields be automatically added to the immediate parent.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.NestedObject`1.IncludeInRoot(System.Boolean)">
            <summary>
            Allows to object fields be automatically added to the root object.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.BinaryMap`1">
            <summary>
            Represents binary data fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.BinaryMap`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.BinaryMap{`0}},PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.BinaryMap{`0}}})">
            <summary>
            The fields options allows to map several core types fields into a single json source field
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.BooleanMap`1">
            <summary>
            Represents boolean fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.BooleanMap`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.BooleanMap{`0}},PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.BooleanMap{`0}}})">
            <summary>
            The fields options allows to map several core types fields into a single json source field
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.AllField`1">
            <summary>
            Allows to control _all field behavior 
            see http://www.elasticsearch.org/guide/reference/mapping/all-field.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.Enabled(System.Boolean)">
            <summary>
            Allows to disable _all field.
            When disabling the _all field, it is a good practice to set index.query.default_field to a different value 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.Analyzer(System.String)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing and when searching using a query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing and when searching using a query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.IndexAnalyzer(System.String)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.IndexAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.SearchAnalyzer(System.String)">
            <summary>
            The analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.AllField`1.SearchAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.ParentField`1">
            <summary>
            Allows to control _parent field behavior 
            see http://www.elasticsearch.org/guide/reference/mapping/parent-field.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.ParentField`1.Type(System.String)">
            <summary>
            Allows to define the parent field mapping on a child mapping, and points to the parent type this child relates to.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.IdField`1">
            <summary>
            Allows to control _id field behavior 
            see http://www.elasticsearch.org/guide/reference/mapping/id-field.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.IdField`1.Store(System.Boolean)">
            <summary>
            Set to yes the store actual field in the index, no to not store it.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.IdField`1.Index(PlainElastic.Net.Mappings.IndexState)">
            <summary>
            Allows to control whether field searchable and analyzed.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.IdField`1.Path(System.String)">
            <summary>
            Allows to associate with a path that will be used to extract the id from a different location in the source document.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.DateMap`1">
            <summary>
            Represents Date fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.DateMap`1.Format(System.String)">
            <summary>
            Allows to specify the date format.
            All dates are UTC.
            see  http://www.elasticsearch.org/guide/reference/mapping/date-format.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.DateMap`1.PrecisionStep(System.Int32)">
            <summary>
             The precision step (number of terms generated for each number value). Defaults to 4.       
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.DateMap`1.FuzzyFactor(System.Int32)">
            <summary>
            Allow to configure a fuzzy_factor mapping value (defaults to 1), which will be used to multiply the fuzzy value by it when used in a query_string type query.
            see http://www.elasticsearch.org/guide/reference/query-dsl/fuzzy-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.DateMap`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.DateMap{`0}},PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.DateMap{`0}}})">
            <summary>
            The fields options allows to map several core types fields into a single json source field
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.NumberMap`1">
            <summary>
            Represents numeric fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.NumberMap`1.Type(PlainElastic.Net.Mappings.NumberMappingType)">
            <summary>
            Allows explicitly specify type of the number.
            By default type inferred from mapped property type.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.NumberMap`1.FuzzyFactor(System.Int32)">
            <summary>
            Allow to configure a fuzzy_factor mapping value (defaults to 1), which will be used to multiply the fuzzy value by it when used in a query_string type query.
            see http://www.elasticsearch.org/guide/reference/query-dsl/fuzzy-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.NumberMap`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.NumberMap{`0}},PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.NumberMap{`0}}})">
            <summary>
            The fields options allows to map several core types fields into a single json source field
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.Object`1">
            <summary>
            Builds a mapping that allows to map inner JSON object. 
            http://www.elasticsearch.org/guide/reference/mapping/object-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.String``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.StringMap{`0},PlainElastic.Net.Mappings.StringMap{`0}})">
            <summary>
            Represents text fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.String(System.String,System.Func{PlainElastic.Net.Mappings.StringMap{`0},PlainElastic.Net.Mappings.StringMap{`0}})">
            <summary>
            Represents text fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Number``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.NumberMap{`0},PlainElastic.Net.Mappings.NumberMap{`0}})">
            <summary>
            Represents numeric fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Number(System.String,System.Func{PlainElastic.Net.Mappings.NumberMap{`0},PlainElastic.Net.Mappings.NumberMap{`0}})">
            <summary>
            Represents numeric fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Date``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.DateMap{`0},PlainElastic.Net.Mappings.DateMap{`0}})">
            <summary>
            Represents Date fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Date(System.String,System.Func{PlainElastic.Net.Mappings.DateMap{`0},PlainElastic.Net.Mappings.DateMap{`0}})">
            <summary>
            Represents Date fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Boolean``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.BooleanMap{`0},PlainElastic.Net.Mappings.BooleanMap{`0}})">
            <summary>
            Represents boolean fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Boolean(System.String,System.Func{PlainElastic.Net.Mappings.BooleanMap{`0},PlainElastic.Net.Mappings.BooleanMap{`0}})">
            <summary>
            Represents boolean fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Binary``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.BinaryMap{`0},PlainElastic.Net.Mappings.BinaryMap{`0}})">
            <summary>
            Represents binary data fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Binary(System.String,System.Func{PlainElastic.Net.Mappings.BinaryMap{`0},PlainElastic.Net.Mappings.BinaryMap{`0}})">
            <summary>
            Represents binary data fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Object``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.Object{``0},PlainElastic.Net.Mappings.Object{``0}})">
            <summary>
            Builds a mapping that allows to map inner JSON object. 
            http://www.elasticsearch.org/guide/reference/mapping/object-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Object``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Func{PlainElastic.Net.Mappings.Object{``0},PlainElastic.Net.Mappings.Object{``0}})">
            <summary>
            Builds a mapping that allows to map inner JSON object. 
            http://www.elasticsearch.org/guide/reference/mapping/object-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Object``1(System.String,System.Func{PlainElastic.Net.Mappings.Object{``0},PlainElastic.Net.Mappings.Object{``0}})">
            <summary>
            Builds a mapping that allows to map inner JSON object. 
            http://www.elasticsearch.org/guide/reference/mapping/object-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.NestedObject``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.NestedObject{``0},PlainElastic.Net.Mappings.NestedObject{``0}})">
            <summary>
            Builds a mapping that allows to map certain sections in the document indexed as nested allowing to query them as if they are separate docs joining with the parent owning doc.
            http://www.elasticsearch.org/guide/reference/mapping/nested-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.NestedObject``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Func{PlainElastic.Net.Mappings.NestedObject{``0},PlainElastic.Net.Mappings.NestedObject{``0}})">
            <summary>
            Builds a mapping that allows to map certain sections in the document indexed as nested allowing to query them as if they are separate docs joining with the parent owning doc.
            http://www.elasticsearch.org/guide/reference/mapping/nested-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.NestedObject``1(System.String,System.Func{PlainElastic.Net.Mappings.NestedObject{``0},PlainElastic.Net.Mappings.NestedObject{``0}})">
            <summary>
            Builds a mapping that allows to map certain sections in the document indexed as nested allowing to query them as if they are separate docs joining with the parent owning doc.
            http://www.elasticsearch.org/guide/reference/mapping/nested-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.CustomProperties(System.Collections.Generic.IEnumerable{PlainElastic.Net.Mappings.CustomPropertyMap{`0}})">
            <summary>
            Allows to register collection of custom property mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.CustomProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.CustomPropertyMap{`0},PlainElastic.Net.Mappings.CustomPropertyMap{`0}})">
            <summary>
            Represents custom property mapping mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.CustomProperty(System.String,System.Func{PlainElastic.Net.Mappings.CustomPropertyMap{`0},PlainElastic.Net.Mappings.CustomPropertyMap{`0}})">
            <summary>
            Represents custom property mapping mapping.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Attachment``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.Attachment{`0},PlainElastic.Net.Mappings.Attachment{`0}})">
            <summary>
            The attachment type allows to index different “attachment” type field (encoded as base64),
            for example, microsoft office formats, open document formats, ePub, HTML, and so on.
            see http://www.elasticsearch.org/guide/reference/mapping/attachment-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.Attachment(System.String,System.Func{PlainElastic.Net.Mappings.Attachment{`0},PlainElastic.Net.Mappings.Attachment{`0}})">
            <summary>
            The attachment type allows to index different “attachment” type field (encoded as base64),
            for example, microsoft office formats, open document formats, ePub, HTML, and so on.
            see http://www.elasticsearch.org/guide/reference/mapping/attachment-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.MultiField``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.MultiField{`0},PlainElastic.Net.Mappings.MultiField{`0}})">
            <summary>
            The multi_field type allows to map several core types of the same value. 
            This can come very handy, for example, when wanting to map a string type, 
            once when its analyzed and once when its not_analyzed.
            see http://www.elasticsearch.org/guide/reference/mapping/multi-field-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.MultiField(System.String,System.Func{PlainElastic.Net.Mappings.MultiField{`0},PlainElastic.Net.Mappings.MultiField{`0}})">
            <summary>
            The multi_field type allows to map several core types of the same value. 
            This can come very handy, for example, when wanting to map a string type, 
            once when its analyzed and once when its not_analyzed.
            see http://www.elasticsearch.org/guide/reference/mapping/multi-field-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.GeoPoint``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{PlainElastic.Net.Mappings.GeoPointMap{`0},PlainElastic.Net.Mappings.GeoPointMap{`0}})">
            <summary>
            Represents geo_point fields mapping.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/mapping-geo-point-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.Properties`1.GeoPoint(System.String,System.Func{PlainElastic.Net.Mappings.GeoPointMap{`0},PlainElastic.Net.Mappings.GeoPointMap{`0}})">
            <summary>
            Represents geo_point fields mapping.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/0.90/mapping-geo-point-type.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.RootObject`1">
            <summary>
            Builds a mapping that allows to map root JSON object. 
            http://www.elasticsearch.org/guide/reference/mapping/root-object-type.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.IndexAnalyzer(System.String)">
            <summary>
            The type mapping level analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.IndexAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The type mapping level analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.SearchAnalyzer(System.String)">
            <summary>
            The type mapping level analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.SearchAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The type mapping level analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.DynamicDateFormats(System.String[])">
            <summary>
            Allows to set one or more date formats that will be used to detect date fields.
            Note: dynamic_date_formats are used only for dynamically added date fields, not for date fields that you specify in your mapping.
            see: http://www.elasticsearch.org/guide/reference/mapping/date-format.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.DateDetection(System.Boolean)">
            <summary>
            Allows to disable automatic date type detection (a new field introduced and matches the provided format)
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.NumericDetection(System.Boolean)">
            <summary>
            Allows to automatically detect numeric values from string.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.DynamicTemplates(System.String[])">
            <summary>
            Dynamic templates allow to define mapping templates that will be applied when dynamic introduction of fields / objects happens.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.Meta(System.String)">
            <summary>
            Simple storage for custom metadata associated with type mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/meta.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.All(System.Func{PlainElastic.Net.Mappings.AllField{`0},PlainElastic.Net.Mappings.AllField{`0}})">
            <summary>
            Allows to map _all field
            see http://www.elasticsearch.org/guide/reference/mapping/all-field.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.Id(System.Func{PlainElastic.Net.Mappings.IdField{`0},PlainElastic.Net.Mappings.IdField{`0}})">
            <summary>
            Allows to control _id field behavior 
            see http://www.elasticsearch.org/guide/reference/mapping/id-field.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.RootObject`1.Parent(System.Func{PlainElastic.Net.Mappings.ParentField{`0},PlainElastic.Net.Mappings.ParentField{`0}})">
            <summary>
            Allows to control _parent field behavior 
            see http://www.elasticsearch.org/guide/reference/mapping/parent-field.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Mappings.StringMap`1">
            <summary>
            Represents text fields mapping.
            see http://www.elasticsearch.org/guide/reference/mapping/core-types.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.OmitNorms(System.Boolean)">
            <summary>
            Defines if norms should be omitted or not. 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.OmitTermFreqAndPositions(System.Boolean)">
            <summary>
            Defines if term freq and positions should be omitted.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.Analyzer(System.String)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing and when searching using a query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing and when searching using a query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.IndexAnalyzer(System.String)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.IndexAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the text contents when analyzed during indexing.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/ 
            </summary>       
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.SearchAnalyzer(System.String)">
            <summary>
            The analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.SearchAnalyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer used to analyze the field when part of a query string.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Mappings.StringMap`1.Fields(System.Func{PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.StringMap{`0}},PlainElastic.Net.Mappings.CoreFields{PlainElastic.Net.Mappings.StringMap{`0}}})">
            <summary>
            The fields options allows to map several core types fields into a single json source field
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.ConstantScoreQuery`1">
            <summary>
            A query that wraps a filter or another query and simply returns a constant score equal 
            to the query boost for every document in the filter. Maps to Lucene ConstantScoreQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/constant-score-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.ConstantScoreQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.BoostingQuery`1">
            <summary>
            A query that can be used to effectively demote results that match a given query. 
            Unlike the “NOT” clause in bool query, this still selects documents 
            that contain undesirable terms, but reduces their overall score.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/boosting-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoostingQuery`1.Positive(System.Func{PlainElastic.Net.Queries.PositiveQuery{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The positive part of boosting query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoostingQuery`1.Negative(System.Func{PlainElastic.Net.Queries.NegativeQuery{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The negative part of boosting query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoostingQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the positive query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoostingQuery`1.NegativeBoost(System.Double)">
            <summary>
            Sets the boost value of the negative query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.DateHistogramFacet`1">
            <summary>
            The histogram facet works with numeric data by building a histogram across intervals of the field values.
            Each value is "rounded" into an interval (or placed in a bucket),
            and statistics are provided per interval/bucket (count and total)
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-facets-histogram-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FacetBase`2.FacetName(System.String)">
            <summary>
            The name of the facet used to identify facets results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FacetBase`2.FacetFilter(System.Func{PlainElastic.Net.Queries.FacetFilter{`1},PlainElastic.Net.Queries.Filter{`1}})">
            <summary>
            All facets can be configured with an additional filter, which will reduce the documents they use for computing results.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/index.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FacetBase`2.Scope(System.String)">
            <summary>
            Allows to specify a scope that facet computation will be restricted to.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FacetBase`2.Global(System.Boolean)">
            <summary>
            Controls whether facet computed within the global scope. 
            In this case it will return values computed across all documents in the index.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FacetBase`2.Nested(System.String)">
            <summary>
            Allows to run the facet on all the nested documents 
            matching the root objects that the main query will end up producing.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.Field(System.String)">
            <summary>
            The field to execute facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.Field(System.Linq.Expressions.Expression{System.Func{`1,System.Object}})">
            <summary>
            The field to execute facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to execute facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.KeyField(System.String)">
            <summary>
            The field to check if its value falls within an interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.KeyField(System.Linq.Expressions.Expression{System.Func{`1,System.Object}})">
            <summary>
            The field to check if its value falls within an interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.KeyFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to check if its value falls within an interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.ValueField(System.String)">
            <summary>
            The field to compute aggregated data per interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.ValueField(System.Linq.Expressions.Expression{System.Func{`1,System.Object}})">
            <summary>
            The field to compute aggregated data per interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.ValueFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`1,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to compute aggregated data per interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.KeyScript(System.String)">
            <summary>
            The script to get value to check if it falls within an interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.ValueScript(System.String)">
            <summary>
            The script to get value to compute aggregated data per interval.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacetBase`2.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.Interval(System.String)">
            <summary>
             The interval used to control the bucket "size" where each key value of a hit will fall into. Check
             the docs for all available values.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.PreZoneAdjustLargeInterval(System.Boolean)">
            <summary>
            Should pre zone be adjusted for large (day and above) intervals. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.PreZone(System.String)">
            <summary>
             Sets the pre time zone to use when bucketing the values. This timezone will be applied before 
             rounding off the result.
             Can either be in the form of "-10:00" or
             one of the values listed here: http://joda-time.sourceforge.net/timezones.html.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.PostZone(System.String)">
            <summary>
            Sets the post time zone to use when bucketing the values. This timezone will be applied after
            rounding off the result.
            Can either be in the form of "-10:00" or
            one of the values listed here: http://joda-time.sourceforge.net/timezones.html.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.PreOffset(System.String)">
            <summary>
            Sets a pre offset that will be applied before rounding the results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.PostOffset(System.String)">
            <summary>
            Sets a post offset that will be applied after rounding the results.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DateHistogramFacet`1.Factor(System.Double)">
            <summary>
            Sets the factor that will be used to multiply the value with before and divided
            by after the rounding of the results.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HistogramFacet`1">
            <summary>
            The histogram facet works with numeric data by building a histogram across intervals of the field values.
            Each value is "rounded" into an interval (or placed in a bucket),
            and statistics are provided per interval/bucket (count and total)
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-facets-histogram-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacet`1.Interval(System.Int64)">
            <summary>
             The interval used to control the bucket "size" where each key value of a hit will fall into.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HistogramFacet`1.TimeInterval(System.String)">
            <summary>
            The time based interval (using the time format).
            This mainly make sense when working on date fields or field that represent absolute milliseconds
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.BoostFactorFunction`1">
            <summary>
            Allows you to multiply the score by the provided boost_factor.
            This can sometimes be desired since boost value set on specific queries gets normalized,
            while for this score function it does not.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_boost_factor
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoostFactorFunction`1.BoostFactor(System.Double)">
            <summary>
            Allows you to multiply the score by the provided boost_factor.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.DecayFunction`1">
            <summary>
            Score a document with a function that decays depending on the distance of a numeric field value of the document
            from a user given origin. This is similar to a range query, but with smooth edges instead of boxes.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_decay_functions
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DecayFunction`1.Type(PlainElastic.Net.Queries.DecayFunctionType)">
            <summary>
            Type of the Decay function. Can be "linear", "exp" and "gauss".
            By default linear.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DecayFunction`1.Origin(System.String)">
            <summary>
            Defines the “central point” from which the distance is calculated.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DecayFunction`1.Scale(System.String)">
            <summary>
             Defines the rate of decay
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DecayFunction`1.Offset(System.String)">
            <summary>
            If an offset is defined, the decay function will only compute a the decay function
            for documents with a distance greater that the defined offset. The default is 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DecayFunction`1.Decay(System.Double)">
            <summary>
            Defines how documents are scored at the distance given at scale.
            If no decay is defined, documents at the distance scale will be scored 0.5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoreFunctionBase`2.ScriprtScore(System.Func{PlainElastic.Net.Queries.ScriptScoreFunction{`1},PlainElastic.Net.Queries.ScriptScoreFunction{`1}})">
            <summary>
            Allows you to wrap another query and customize the scoring of it
            optionally with a computation derived from other numeric field values in the doc 
            using a script expression.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_script_score
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoreFunctionBase`2.BoostFactor(System.Func{PlainElastic.Net.Queries.BoostFactorFunction{`1},PlainElastic.Net.Queries.BoostFactorFunction{`1}})">
            <summary>
            Allows you to multiply the score by the provided boost_factor.
            This can sometimes be desired since boost value set on specific queries gets normalized,
            while for this score function it does not.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_boost_factor
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoreFunctionBase`2.RandomScore(System.Func{PlainElastic.Net.Queries.RandomScoreFunction{`1},PlainElastic.Net.Queries.RandomScoreFunction{`1}})">
            <summary>
            The random_score generates scores via a pseudo random number algorithm that is initialized with a seed.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_random
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoreFunctionBase`2.DecayFunction(System.Func{PlainElastic.Net.Queries.DecayFunction{`1},PlainElastic.Net.Queries.DecayFunction{`1}})">
            <summary>
            Score a document with a function that decays depending on the distance of a numeric field value of the document
            from a user given origin. This is similar to a range query, but with smooth edges instead of boxes.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_decay_functions
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoreFunctionBase`2.Weight(System.Double)">
            <summary>
            The weight score allows you to multiply the score by the provided weight. This can sometimes be desired since boost value set on specific queries gets normalized, while for this score function it does not.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_weight
            </summary>
            <param name="weight"></param>
            <returns></returns>
        </member>
        <member name="T:PlainElastic.Net.Queries.FunctionScoreQuery`1">
            <summary>
            A query that allows you to modify the score of documents that are retrieved by a query. 
            This can be useful if, for example, a score function is computationally expensive 
            and it is sufficient to compute the score on a filtered set of documents.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FunctionScoreQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FunctionScoreQuery`1.BoostMode(PlainElastic.Net.Queries.FunctionBoostMode)">
            <summary>
            Defines how newly computed score is combined with the score of the query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FunctionScoreQuery`1.MaxBoost(System.Double)">
            <summary>
            Restricts the new score to not exceed a certain limit.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FunctionScoreQuery`1.ScoreMode(PlainElastic.Net.Queries.FunctionScoreMode)">
            <summary>
            Specifies how the computed scores are combined
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FunctionScoreQuery`1.Function(System.Func{PlainElastic.Net.Queries.ScoreFunction{`0},PlainElastic.Net.Queries.ScoreFunction{`0}})">
            <summary>
            Defines function that compute a new score for each document returned by the query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FunctionScoreQuery`1.Functions(System.Func{PlainElastic.Net.Queries.FilteredScoreFunctions{`0},PlainElastic.Net.Queries.FilteredScoreFunctions{`0}})">
            <summary>
            Defines functions that can be combined to calculate a new score for each document returnaed by the query.
            You can specify filter that will apply the function only if a document matches a given filter.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.GeoDistanceFacet`1">
            <summary>
            The geo_distance facet is a facet providing information for ranges of distances from a provided geo_point including count of the number of hits that fall within each range, and aggregation information (like total).
            http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-facets-geo-distance-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Field(System.String)">
            <summary>
            The field to execute Geo Distance Facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Field(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to execute Geo Distance Facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to execute Geo Distance Facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.GeoPoint(System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Defines the geo_point to calculate distances from.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Geohash(System.String)">
            <summary>
            Defines the geohash to calculate distances from.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Ranges(System.Func{PlainElastic.Net.Queries.RangeFromTo,PlainElastic.Net.Queries.RangeFromTo})">
            <summary>
            The ranges of values to aggregate against, in format (from, to)
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Unit(PlainElastic.Net.Queries.DistanceUnit)">
            <summary>
            The unit the ranges are provided in. Defaults to km. Can also be mi, miles, in, inch, yd, yards, kilometers, mm, millimeters, cm, centimeters, m or meters.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.DistanceType(PlainElastic.Net.Queries.DistanceType)">
            <summary>
            How to compute the distance. Can either be arc (better precision) or plane (faster). Defaults to arc.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.ValueField(System.String)">
            <summary>
            The field to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.ValueField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.ValueFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.ValueScript(System.String)">
            <summary>
            The script to get value to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFacet`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HasParentFilter`2">
            <summary>
            The has_parent filter accepts a query or filterand the parent type to run against,
            The filter is executed in the parent document space, which is specified by the parent type. 
            This filer return child documents which associated parents have matched.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-parent-filter/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentFilter`2.ParentType(System.String)">
            <summary>
            The child type to query against. 
            The parent type to return is automatically detected based on the mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentFilter`2.Query(System.Func{PlainElastic.Net.Queries.Query{`1},PlainElastic.Net.Queries.Query{`1}})">
            <summary>
            The query to run against parent documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentFilter`2.Filter(System.Func{PlainElastic.Net.Queries.Filter{`1},PlainElastic.Net.Queries.Filter{`1}})">
            <summary>
            The filter to run against parent documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentFilter`2.Scope(System.String)">
            <summary>
            Allows to run facets on the same scope name that will work against the child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentFilter`2.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RandomScoreFunction`1">
            <summary>
            The random_score generates scores via a pseudo random number algorithm that is initialized with a seed.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_random
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RandomScoreFunction`1.Seed(System.Double)">
            <summary>
            Defines intial state of random number algorithm. 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.ScriptScoreFunction`1">
            <summary>
            Allows you to wrap another query and customize the scoring of it
            optionally with a computation derived from other numeric field values in the doc 
            using a script expression.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html#_script_score
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptScoreFunction`1.Script(System.String)">
            <summary>
            Sets the script used to calculate score value.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptScoreFunction`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptScoreFunction`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptScoreFunction`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HasParentQuery`2">
            <summary>
            The has_parent query accepts a query and the parent type to run against,
            The query is executed in the parent document space, which is specified by the parent type. 
            This query return child documents which associated parents have matched.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-parent-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentQuery`2.ParentType(System.String)">
            <summary>
            The parent type to query against. 
            The parent type to return is automatically detected based on the mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentQuery`2.ScoreType(PlainElastic.Net.Queries.HasParentScoreType)">
            <summary>
            The score type for the has_parent query.
            The supported score types are "score" or "none". The default is "none" and this ignores the score from the parent document. 
            The score is in this case equal to the boost on the has_parent query (Defaults to 1).
            If the score type is set to "score", then the score of the matching parent document is aggregated into the child documents belonging to the matching parent document.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentQuery`2.Query(System.Func{PlainElastic.Net.Queries.Query{`1},PlainElastic.Net.Queries.Query{`1}})">
            <summary>
            The query to run against parents documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasParentQuery`2.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermsStatsFacet`1">
            <summary>
            Combines both the terms and statistical allowing to compute stats computed on a field, per term value driven by another field.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/terms-stats-facet/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.KeyField(System.String)">
            <summary>
            The field to group statistics against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.KeyField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to group statistics against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.KeyFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to group statistics against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.ValueField(System.String)">
            <summary>
            The field to compute statistics.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.ValueField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to compute statistics.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.ValueFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to compute statistics.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.Order(PlainElastic.Net.Queries.TermsStatsFacetOrder)">
            <summary>
            Allows to control the ordering of the terms_stats facets. The default is count.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.Size(System.Int32)">
            <summary>
            The number of facet entries to return.
            Default is 10.
            Setting it to 0 will return all terms matching the hits (be careful not to return too many results).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.AllTerms(System.Boolean)">
            <summary>
            Allow to get all the terms in facet. 
            Equals to size = 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.Script(System.String)">
            <summary>
            Allow to define a script for terms_stats facet to compute value.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.ValueScript(System.String)">
            <summary>
            Allow to define a script for terms_stats facet to compute value.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.ScriptField(System.String)">
            <summary>
            Allow to define a script for terms_stats facet to compute value.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsStatsFacet`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.StatisticalFacet`1">
            <summary>
            Allows to compute statistical data on a numeric fields.
            The statistical data include count, total, sum of squares, mean (average), minimum, maximum, variance, and standard deviation.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/statistical-facet/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Field(System.String)">
            <summary>
            The field to execute statistical facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Field(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to execute statistical facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to execute statistical facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Fields(System.String[])">
            <summary>
            The statistical facet can be executed against more than one field, returning the aggregation result across those fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Fields(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            The statistical facet can be executed against more than one field, returning the aggregation result across those fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.FieldsOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            The statistical facet can be executed against more than one field, returning the aggregation result across those fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Script(System.String)">
            <summary>
            Allow to define a script to evaluate, with its value used to compute the statistical information.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.StatisticalFacet`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HighlightField`1">
            <summary>
            Represents document's field with highlight options.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HighlightBase`2">
            <summary>
            A base class for Match and Text queries.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.PreTags(System.String[])">
            <summary>
            Set the post tags that will be used for highlighting.
            By default, the highlighting will wrap highlighted text in <em> and </em>.
            There can be a single tag or more, and the “importance” is ordered.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.PostTags(System.String[])">
            <summary>
            Set the post tags that will be used for highlighting.
            By default, the highlighting will wrap highlighted text in <em> and </em>
            There can be a single tag or more, and the “importance” is ordered.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.TagsSchema(PlainElastic.Net.Queries.HighlightTagsSchema)">
            <summary>
            Set a tag scheme that encapsulates a built in pre and post tags. 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.Order(PlainElastic.Net.Queries.HighlightOrder)">
            <summary>
            The order of fragments per field. By default, ordered by the order in the
            highlighted text. Can be "score", which then it will be ordered
            by score of the fragments.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.FragmentSize(System.Int32)">
            <summary>
            The size of the highlighted fragment in characters.
            Defaults to 100.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.NumberOfFragments(System.Int32)">
            <summary>
            The maximum number of fragments to return.
            Defaults to 5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.Encoder(PlainElastic.Net.Queries.HighlightEncoder)">
            <summary>
            Defines how highlighted text will be encoded.
            It can be either default (no encoding) or html (will escape html, if you use html highlighting tags).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.RequireFieldMatch(System.Boolean)">
            <summary>
            Controls whether field to be highlighted only if a query matched that field.
            False means that terms are highlighted on all requested fields regardless if the query matches specifically on them.
            Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.BoundaryMaxScan(System.Int32)">
            <summary>
            Allows to control how far to look for boundary characters.
            Defaults to 20
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.BoundaryChars(System.String)">
            <summary>
            Defines what constitutes a boundary for highlighting. 
            It’s a single string with each boundary character defined in it.
            Defaults to ".,!? t\n".
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.Type(PlainElastic.Net.Queries.HighlighterType)">
            <summary>
            Set type of highlighter to use. Supported types
            are "highlighter"(plain)  and "fast-vector-highlighter"(fvh).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightBase`2.Fragmenter(PlainElastic.Net.Queries.HighlightFragmenter)">
            <summary>
            Sets what fragmenter to use to break up text that is eligible for highlighting.
            This option is only applicable when using plain / normal highlighter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightField`1.FieldName(System.String)">
            <summary>
            Allows to specify field name to highlight.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightField`1.FieldName(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            Allows to specify field of object to highlight.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HighlightField`1.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            Allows to specify field of object from the collection of such objects to execute highlight.
            </summary>
            <param name="collectionField">The collection type field.</param>
            <param name="field">The field of object inside collection.</param>
        </member>
        <member name="T:PlainElastic.Net.Queries.Highlight`1">
            <summary>
            Allows to highlight search results on one or more fields.
            see http://www.elasticsearch.org/guide/reference/api/search/highlighting.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.Highlight`1.HighlightedFields">
            <summary>
            Holds the highlighting fields JSON.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.IdsQuery`1">
            <summary>
            A query that filters documents that only have the provided ids. 
            Note, this query does not require the _id field to be indexed since it works using the _uid field.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/ids-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsQuery`1.Type(System.String)">
            <summary>
            The index type this query apply to.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsQuery`1.Types(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            The collection of index types this query apply to.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsQuery`1.Values(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            The collection of ids to use as filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MatchQueryBase`2">
            <summary>
            A base class for Match and Text queries.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Query(System.String)">
            <summary>
            The text query to analyze.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Type(PlainElastic.Net.Queries.TextQueryType)">
            <summary>
            Sets the text query type. Defaults to "boolean".
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Operator(PlainElastic.Net.Operator)">
            <summary>
            Controls how boolean text query parts are combined.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Analyzer(System.String)">
            <summary>
            The analyzer name used to analyze the text query. Defaults to the field explicit mapping definition, or the default search analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer name used to analyze the text query. Defaults to the field explicit mapping definition, or the default search analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Fuzziness(System.Double)">
            <summary>
            Controls construction of fuzzy queries for each term analyzed. 
            Value determines the minimum similarity used when evaluated to a fuzzy query type. Defaults to "0.5".
            Value depends on the relevant type, for string types it should be a value between 0.0 and 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.PrefixLength(System.Int32)">
            <summary>
            Length of required common prefix on variant terms.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.MaxExpansions(System.Int32)">
            <summary>
            Controls to how many prefixes the last term will be expanded.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Slop(System.Int32)">
            <summary>
            Sets the slop for phrase query. If zero, then exact phrase matches are required.
            Default value is 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Rewrite(PlainElastic.Net.Queries.Rewrite,System.Int32)">
            <summary>
            Allows to control how multi term queries will get rewritten.
            see http://www.elasticsearch.org/guide/reference/query-dsl/multi-term-rewrite.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.CutoffFrequency(System.Double)">
            <summary>
            Set a cutoff value in [0..1] (or absolute number >=1) representing 
            the maximum threshold of a terms document frequency to be considered a low frequency term.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchQueryBase`2.Lenient(System.Boolean)">
            <summary>
            Sets whether format based failures will be ignored.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MultiMatchQuery`1">
            <summary>
            The multi_match query builds further on top of the match query by allowing multiple fields to be specified.
            The idea here is to allow to more easily build a concise match type query 
            over multiple fields instead of using a relatively more expressive query
            by using multiple match queries within a bool query.
            http://www.elasticsearch.org/guide/reference/query-dsl/multi-match-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MultiMatchQuery`1.Fields(System.String[])">
            <summary>
            A list of the fields to run the query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MultiMatchQuery`1.Fields(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            A list of the fields to run the query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MultiMatchQuery`1.FieldsOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            A list of the fields to run the query against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MultiMatchQuery`1.UseDisMax(System.Boolean)">
            <summary>
            Should the queries be combined using dis_max (set it to true), or a bool query (set it to false).
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MultiMatchQuery`1.TieBreaker(System.Double)">
            <summary>
            Multiplier value to balance the scores between lower and higher scoring fields.
            Only applicable when use_dis_max is set to true.
            Defaults to 0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MatchQuery`1">
            <summary>
            A family of match queries that accept text/numerics/dates, analyzes it, and constructs a query out of it.
            see http://www.elasticsearch.org/guide/reference/query-dsl/match-query/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RangeFacet`1">
            <summary>
            Allows to specify a set of ranges and get both the number of docs (count) that fall within each range,
            and aggregated data either based on the field, or using another field.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/range-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.Field(System.String)">
            <summary>
            The field to execute range facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.Field(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to execute range facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to execute range facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.KeyField(System.String)">
            <summary>
            The field to check if its value falls within a range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.KeyField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to check if its value falls within a range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.KeyFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to check if its value falls within a range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.ValueField(System.String)">
            <summary>
            The field to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.ValueField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.ValueFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.KeyScript(System.String)">
            <summary>
            The script to get value to check if it falls within a range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.ValueScript(System.String)">
            <summary>
            The script to get value to compute aggregated data per range.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFacet`1.Ranges(System.Func{PlainElastic.Net.Queries.RangeFromTo,PlainElastic.Net.Queries.RangeFromTo})">
            <summary>
            The ranges of values to aggregate against, in format (from, to)
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.GeoDistanceFilter`1">
            <summary>
            Filters documents that include only hits that exists within a specific distance from a geo point.
            see http://www.elasticsearch.org/guide/reference/query-dsl/geo-distance-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.Distance(System.Nullable{System.Double},PlainElastic.Net.Queries.DistanceUnit)">
            <summary>
            The distance to include hits in the filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.Distance(System.String)">
            <summary>
            The distance to include hits in the filter.
            Single string with the unit (either mi/miles or km) e.g. 20mi.
            Kilometers by default.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.DistanceType(PlainElastic.Net.Queries.DistanceType)">
            <summary>
            How to compute the distance.
            Can either be arc (better precision) or plane (faster). Defaults to arc.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.OptimizeBoundingBox(PlainElastic.Net.Queries.OptimizeBoundingBox)">
            <summary>
            Controls whether an optimization of using first a bounding box check will be used.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.CacheKey(System.String)">
            <summary>
            Allows to specify Cache Key that will be used as the caching key for that filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.GeoDistanceFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NumericRangeFilter`1">
            <summary>
            Filters documents with fields that have values within a certain numeric range.
            Similar to range filter, except that it works only with numeric values, and the filter execution works differently.
            see http://www.elasticsearch.org/guide/reference/query-dsl/numeric-range-filter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RangeFilter`1">
            <summary>
            Filters documents with fields that have terms within a certain range.
            Similar to range query, except that it acts as a filter
            see http://www.elasticsearch.org/guide/reference/query-dsl/range-filter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RangeBase`2">
            <summary>
            Base class for Range filter and range query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.From(System.String)">
            <summary>
            The lower bound. Defaults to start from the first.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.To(System.String)">
            <summary>
            The upper bound. Defaults to unbounded.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.IncludeLower(System.Boolean)">
            <summary>
            Should the first from (if set) be inclusive or not. Defaults to true
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.IncludeUpper(System.Boolean)">
            <summary>
            Should the last to (if set) be inclusive or not. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.Gt(System.String)">
            <summary>
            Same as setting from to the value, and include_lower to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.Gte(System.String)">
            <summary>
            Same as setting from to the value, and include_lower to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.Lt(System.String)">
            <summary>
            Same as setting to to the value, and include_upper to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeBase`2.Lte(System.String)">
            <summary>
            Same as setting to to the value, and include_upper to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFilter`1.CacheKey(System.String)">
            <summary>
            Allows to specify Cache Key that will be used as the caching key for that filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.ScriptFilter`1">
            <summary>
            A filter allowing to define scripts as filters
            see http://www.elasticsearch.org/guide/reference/query-dsl/script-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.Script(System.String)">
            <summary>
            Sets the script used to filter documents.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.CacheKey(System.String)">
            <summary>
            Allows to specify Cache Key that will be used as the caching key for that filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScriptFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.IndicesFilter`1">
            <summary>
            A filter that will execute the wrapped filter only for the specified indices, and "match_all" when it does not match those indices (by default).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesFilter`1.Indices(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            A list of indices to match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesFilter`1.Indices(System.String[])">
            <summary>
            A list of indices to match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesFilter`1.Filter(System.Func{PlainElastic.Net.Queries.Filter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            The filter to execute on matched indices.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesFilter`1.NoMatchFilter(System.Func{PlainElastic.Net.Queries.NoMatchFilter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            Sets the filter to use when it executes on an index that does not match the indices provided.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesFilter`1.NoMatchFilter(PlainElastic.Net.Queries.IndicesNoMatchMode)">
            <summary>
            Sets the no match filter, can either be <tt>all</tt> or <tt>none</tt>.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NoMatchFilter`1">
            <summary>
            Represents NoMatchFilter part of Indices filter.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.Filter`1">
            <summary>
            Allows to filter result hits without changing facet results.
            see http://www.elasticsearch.org/guide/reference/api/search/filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.And(System.Func{PlainElastic.Net.Queries.AndFilter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            A filter that matches documents using AND boolean operator on other queries.
            This filter is more performant then bool filter. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/and-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Or(System.Func{PlainElastic.Net.Queries.OrFilter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            A filter that matches documents using OR boolean operator on other queries. 
            This filter is more performant then bool filter. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/or-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Not(System.Func{PlainElastic.Net.Queries.NotFilter{`0},PlainElastic.Net.Queries.NotFilter{`0}})">
            <summary>
            A filter that filters out matched documents using a query.
            This filter is more performant then bool filter. Can be placed within queries that accept a filter
            see http://www.elasticsearch.org/guide/reference/query-dsl/not-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Term(System.Func{PlainElastic.Net.Queries.TermFilter{`0},PlainElastic.Net.Queries.TermFilter{`0}})">
            <summary>
            Filters documents that have fields that contain a term (not analyzed).
            see http://www.elasticsearch.org/guide/reference/query-dsl/term-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Terms(System.Func{PlainElastic.Net.Queries.TermsFilter{`0},PlainElastic.Net.Queries.TermsFilter{`0}})">
            <summary>
            Filters documents that have fields that match any of the provided terms (not analyzed).
            see http://www.elasticsearch.org/guide/reference/query-dsl/terms-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Exists(System.Func{PlainElastic.Net.Queries.ExistsFilter{`0},PlainElastic.Net.Queries.ExistsFilter{`0}})">
            <summary>
            Filters documents where a specific field has a value in them.
            see http://www.elasticsearch.org/guide/reference/query-dsl/exists-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Missing(System.Func{PlainElastic.Net.Queries.MissingFilter{`0},PlainElastic.Net.Queries.MissingFilter{`0}})">
            <summary>
            Filters documents where a specific field has no value in them.
            see http://www.elasticsearch.org/guide/reference/query-dsl/missing-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Ids(System.Func{PlainElastic.Net.Queries.IdsFilter{`0},PlainElastic.Net.Queries.IdsFilter{`0}})">
            <summary>
            A filter that filters documents that only have the provided ids. 
            Note, this filter does not require the _id field to be indexed since it works using the _uid field.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/ids-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Nested(System.Func{PlainElastic.Net.Queries.NestedFilter{`0},PlainElastic.Net.Queries.NestedFilter{`0}})">
            <summary>
            A filter that allows to filter nested objects / docs.
            The filter is executed against the nested objects / docs as if they were indexed 
            as separate docs (they are, internally) and resulting in the root parent doc (or parent nested mapping)
            see http://www.elasticsearch.org/guide/reference/query-dsl/nested-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.HasChild``1(System.Func{PlainElastic.Net.Queries.HasChildFilter{`0,``0},PlainElastic.Net.Queries.HasChildFilter{`0,``0}})">
            <summary>
            The has_child filter accepts a query and the child type to run against,
            and results in parent documents that have child docs matching the query.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-child-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.HasParent``1(System.Func{PlainElastic.Net.Queries.HasParentFilter{`0,``0},PlainElastic.Net.Queries.HasParentFilter{`0,``0}})">
            <summary>
            The has_parent filter accepts a query or filterand the parent type to run against,
            The filter is executed in the parent document space, which is specified by the parent type. 
            This filer return child documents which associated parents have matched.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-parent-filter/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Range(System.Func{PlainElastic.Net.Queries.RangeFilter{`0},PlainElastic.Net.Queries.RangeFilter{`0}})">
            <summary>
            Filters documents with fields that have terms within a certain range.
            Similar to range query, except that it acts as a filter
            see http://www.elasticsearch.org/guide/reference/query-dsl/range-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.NumericRange(System.Func{PlainElastic.Net.Queries.NumericRangeFilter{`0},PlainElastic.Net.Queries.RangeFilter{`0}})">
            <summary>
            Filters documents with fields that have values within a certain numeric range.
            Similar to range filter, except that it works only with numeric values, and the filter execution works differently.
            see http://www.elasticsearch.org/guide/reference/query-dsl/numeric-range-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Bool(System.Func{PlainElastic.Net.Queries.BoolFilter{`0},PlainElastic.Net.Queries.BoolFilter{`0}})">
            <summary>
            A filter that matches documents matching boolean combinations of other queries.
            Similar in concept to Boolean query, except that the clauses are other filters. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/bool-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Limit(System.Func{PlainElastic.Net.Queries.LimitFilter{`0},PlainElastic.Net.Queries.LimitFilter{`0}})">
            <summary>
            A limit filter limits the number of documents (per shard) to execute on.
            see http://www.elasticsearch.org/guide/reference/query-dsl/limit-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Type(System.Func{PlainElastic.Net.Queries.TypeFilter{`0},PlainElastic.Net.Queries.TypeFilter{`0}})">
            <summary>
            Filters documents matching the provided document / mapping type. 
            Note, this filter can work even when the _type field is not indexed (using the _uid field)
            see http://www.elasticsearch.org/guide/reference/query-dsl/type-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Query(System.Func{PlainElastic.Net.Queries.QueryFilter{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            Wraps any query to be used as a filter. Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/query-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Prefix(System.Func{PlainElastic.Net.Queries.PrefixFilter{`0},PlainElastic.Net.Queries.PrefixFilter{`0}})">
            <summary>
            Filters documents that have fields containing terms with a specified prefix (not analyzed). 
            Similar to phrase query, except that it acts as a filter. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/prefix-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.MatchAll">
            <summary>
            A filter that matches on all documents.
            see http://www.elasticsearch.org/guide/reference/query-dsl/match-all-filter.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Indices(System.Func{PlainElastic.Net.Queries.IndicesFilter{`0},PlainElastic.Net.Queries.IndicesFilter{`0}})">
            <summary>
            A filter that will execute the wrapped filter only for the specified indices, and "match_all" when it does not match those indices (by default).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.Script(System.Func{PlainElastic.Net.Queries.ScriptFilter{`0},PlainElastic.Net.Queries.ScriptFilter{`0}})">
            <summary>
            A filter allowing to define scripts as filters
            see http://www.elasticsearch.org/guide/reference/query-dsl/script-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.GeoDistance(System.Func{PlainElastic.Net.Queries.GeoDistanceFilter{`0},PlainElastic.Net.Queries.GeoDistanceFilter{`0}})">
            <summary>
            Filters documents that include only hits that exists within a specific distance from a geo point.
            see http://www.elasticsearch.org/guide/reference/query-dsl/geo-distance-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Filter`1.RegExp(System.Func{PlainElastic.Net.Queries.RegExpFilter{`0},PlainElastic.Net.Queries.RegExpFilter{`0}})">
            <summary>
            The regexp filter is similar to the regexp query, except that it is cacheable and can speedup performance in case you are reusing this filter in your queries.
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-regexp-filter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NoMatchQuery`1">
            <summary>
            Represents NoMatchQuery part of Indices query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.Query`1">
            <summary>
            The query element within the search request body allows to define a query using the Query DSL.
            see http://www.elasticsearch.org/guide/reference/api/search/query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Text(System.Func{PlainElastic.Net.Queries.TextQuery{`0},PlainElastic.Net.Queries.TextQuery{`0}})">
            <summary>
            A family of text queries that accept text, analyzes it, and constructs a query out of it.
            see http://www.elasticsearch.org/guide/reference/query-dsl/text-query.html
            </summary>  
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.TextPhrase(System.Func{PlainElastic.Net.Queries.TextPhraseQuery{`0},PlainElastic.Net.Queries.TextPhraseQuery{`0}})">
            <summary>
            Analyzes the text and creates a phrase query out of the analyzed text.
            see http://www.elasticsearch.org/guide/reference/query-dsl/text-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.TextPhrasePrefix(System.Func{PlainElastic.Net.Queries.TextPhrasePrefixQuery{`0},PlainElastic.Net.Queries.TextPhrasePrefixQuery{`0}})">
            <summary>
            Analyzes the text and creates a phrase query out of the analyzed text and
            allows for prefix matches on the last term in the text.
            see http://www.elasticsearch.org/guide/reference/query-dsl/text-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Match(System.Func{PlainElastic.Net.Queries.MatchQuery{`0},PlainElastic.Net.Queries.MatchQuery{`0}})">
            <summary>
            A family of match queries that accept text/numerics/dates, analyzes it, and constructs a query out of it.
            see http://www.elasticsearch.org/guide/reference/query-dsl/match-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.MultiMatch(System.Func{PlainElastic.Net.Queries.MultiMatchQuery{`0},PlainElastic.Net.Queries.MultiMatchQuery{`0}})">
            <summary>
            The multi_match query builds further on top of the match query by allowing multiple fields to be specified.
            The idea here is to allow to more easily build a concise match type query 
            over multiple fields instead of using a relatively more expressive query
            by using multiple match queries within a bool query.
            http://www.elasticsearch.org/guide/reference/query-dsl/multi-match-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Bool(System.Func{PlainElastic.Net.Queries.BoolQuery{`0},PlainElastic.Net.Queries.BoolQuery{`0}})">
            <summary>
            A query that matches documents matching boolean combinations of other queries. 
            The bool query maps to Lucene BooleanQuery. It is built using one or more boolean clauses, 
            each clause with a typed occurrence
            see: http://www.elasticsearch.org/guide/reference/query-dsl/bool-query.html
            </summary>  
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Boosting(System.Func{PlainElastic.Net.Queries.BoostingQuery{`0},PlainElastic.Net.Queries.BoostingQuery{`0}})">
            <summary>
            A query that can be used to effectively demote results that match a given query. 
            Unlike the “NOT” clause in bool query, this still selects documents 
            that contain undesirable terms, but reduces their overall score.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/boosting-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Ids(System.Func{PlainElastic.Net.Queries.IdsQuery{`0},PlainElastic.Net.Queries.IdsQuery{`0}})">
            <summary>
            A query that filters documents that only have the provided ids. 
            Note, this query does not require the _id field to be indexed since it works using the _uid field.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/ids-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.DisMax(System.Func{PlainElastic.Net.Queries.DisMaxQuery{`0},PlainElastic.Net.Queries.DisMaxQuery{`0}})">
            <summary>
            A query that generates the union of documents produced by its subqueries, 
            and that scores each document with the maximum score for that document as produced by any subquery, 
            plus a tie breaking increment for any additional matching subqueries.
            see http://www.elasticsearch.org/guide/reference/query-dsl/dis-max-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Field(System.Func{PlainElastic.Net.Queries.FieldQuery{`0},PlainElastic.Net.Queries.FieldQuery{`0}})">
            <summary>
            A query that executes a query string against a specific field.
            It is a simplified version of query_string query 
            (by setting the default_field to the field this query executed against). 
            see http://www.elasticsearch.org/guide/reference/query-dsl/field-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.FunctionScore(System.Func{PlainElastic.Net.Queries.FunctionScoreQuery{`0},PlainElastic.Net.Queries.FunctionScoreQuery{`0}})">
            <summary>
            A query that allows you to modify the score of documents that are retrieved by a query. 
            This can be useful if, for example, a score function is computationally expensive 
            and it is sufficient to compute the score on a filtered set of documents.
            see: http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/query-dsl-function-score-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.FuzzyLikeThis(System.Func{PlainElastic.Net.Queries.FuzzyLikeThisQuery{`0},PlainElastic.Net.Queries.FuzzyLikeThisQuery{`0}})">
            <summary>
            Fuzzy like this query find documents that are “like” provided text by running it against one or more fields.
            see http://www.elasticsearch.org/guide/reference/query-dsl/flt-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.FuzzyLikeThisField(System.Func{PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery{`0},PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery{`0}})">
            <summary>
            The fuzzy_like_this_field query is the same as the fuzzy_like_this query, 
            except that it runs against a single field. 
            It provides nicer query DSL over the generic fuzzy_like_this query, 
            and support typed fields query (automatically wraps typed fields with type filter to match only on the specific type).
            see http://www.elasticsearch.org/guide/reference/query-dsl/flt-field-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.MoreLikeThis(System.Func{PlainElastic.Net.Queries.MoreLikeThisQuery{`0},PlainElastic.Net.Queries.MoreLikeThisQuery{`0}})">
            <summary>
            More like this query find documents that are “like” provided text by running it against one or more fields.
            see http://www.elasticsearch.org/guide/reference/query-dsl/mlt-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.MoreLikeThisField(System.Func{PlainElastic.Net.Queries.MoreLikeThisFieldQuery{`0},PlainElastic.Net.Queries.MoreLikeThisFieldQuery{`0}})">
            <summary>
            The more_like_this_field query is the same as the more_like_this query, 
            except it runs against a single field. It provides nicer query DSL over the generic more_like_this query,
            and support typed fields query (automatically wraps typed fields with type filter to match only on the specific type).
            see http://www.elasticsearch.org/guide/reference/query-dsl/mlt-field-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.HasChild``1(System.Func{PlainElastic.Net.Queries.HasChildQuery{`0,``0},PlainElastic.Net.Queries.HasChildQuery{`0,``0}})">
            <summary>
            The has_child query accepts a query and the child type to run against,
            and results in parent documents that have child docs matching the query.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-child-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.HasParent``1(System.Func{PlainElastic.Net.Queries.HasParentQuery{`0,``0},PlainElastic.Net.Queries.HasParentQuery{`0,``0}})">
            <summary>
            The has_parent query accepts a query and the parent type to run against,
            The query is executed in the parent document space, which is specified by the parent type. 
            This query return child documents which associated parents have matched.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-parent-query/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.TopChildren(System.Func{PlainElastic.Net.Queries.TopChildrenQuery{`0},PlainElastic.Net.Queries.TopChildrenQuery{`0}})">
            <summary>
            The top_children query runs the child query with an estimated hits size,
            and out of the hit docs, aggregates it into parent docs. 
            If there aren’t enough parent docs matching the requested from/size search request,
            then it is run again with a wider (more hits) search.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/top-children-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Nested(System.Func{PlainElastic.Net.Queries.NestedQuery{`0},PlainElastic.Net.Queries.NestedQuery{`0}})">
            <summary>
            A query that allows to query nested objects / docs.
            The query is executed against the nested objects / docs as if they were indexed 
            as separate docs (they are, internally) and resulting in the root parent doc (or parent nested mapping)
            see http://www.elasticsearch.org/guide/reference/query-dsl/nested-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.CustomScore(System.Func{PlainElastic.Net.Queries.CustomScoreQuery{`0},PlainElastic.Net.Queries.CustomScoreQuery{`0}})">
            <summary>
            A query that allows to wrap another query and customize the scoring of it 
            optionally with a computation derived from other field values in the doc (numeric ones) using script expression. 
            see: http://www.elasticsearch.org/guide/reference/query-dsl/custom-score-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.CustomBoostFactor(System.Func{PlainElastic.Net.Queries.CustomBoostFactorQuery{`0},PlainElastic.Net.Queries.CustomBoostFactorQuery{`0}})">
            <summary>
            A query that allows  query allows to wrap another query and multiply its score by the provided boost_factor.
            This can sometimes be desired since boost value set on specific queries gets normalized, while this query boost factor does not.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/custom-boost-factor-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.ConstantScore(System.Func{PlainElastic.Net.Queries.ConstantScoreQuery{`0},PlainElastic.Net.Queries.ConstantScoreQuery{`0}})">
            <summary>
            A query that wraps a filter or another query and simply returns a constant score equal 
            to the query boost for every document in the filter. Maps to Lucene ConstantScoreQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/constant-score-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Filtered(System.Func{PlainElastic.Net.Queries.FilteredQuery{`0},PlainElastic.Net.Queries.FilteredQuery{`0}})">
            <summary>
            A query that applies a filter to the results of another query. This query maps to Lucene FilteredQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/filtered-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Fuzzy(System.Func{PlainElastic.Net.Queries.FuzzyQuery{`0},PlainElastic.Net.Queries.FuzzyQuery{`0}})">
            <summary>
            A fuzzy based query that uses similarity based on Levenshtein (edit distance) algorithm.
            see http://www.elasticsearch.org/guide/reference/query-dsl/fuzzy-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.MatchAll(System.Func{PlainElastic.Net.Queries.MatchAllQuery{`0},PlainElastic.Net.Queries.MatchAllQuery{`0}})">
            <summary>
            A query that matches all documents. Maps to Lucene MatchAllDocsQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/match-all-query.html        
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.QueryString(System.Func{PlainElastic.Net.Queries.QueryString{`0},PlainElastic.Net.Queries.QueryString{`0}})">
            <summary>
            A query that uses a query parser in order to parse its content
            see http://www.elasticsearch.org/guide/reference/query-dsl/query-string-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Prefix(System.Func{PlainElastic.Net.Queries.PrefixQuery{`0},PlainElastic.Net.Queries.PrefixQuery{`0}})">
            <summary>
            Matches documents that have fields containing terms with a specified prefix (not analyzed).
            The prefix query maps to Lucene PrefixQuery
            see http://www.elasticsearch.org/guide/reference/query-dsl/prefix-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Range(System.Func{PlainElastic.Net.Queries.RangeQuery{`0},PlainElastic.Net.Queries.RangeQuery{`0}})">
            <summary>
            Matches documents with fields that have terms within a certain range. 
            see http://www.elasticsearch.org/guide/reference/query-dsl/range-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Term(System.Func{PlainElastic.Net.Queries.TermQuery{`0},PlainElastic.Net.Queries.TermQuery{`0}})">
            <summary>
            Matches documents that have fields that contain a term (not analyzed). The term query maps to Lucene TermQuery
            see http://www.elasticsearch.org/guide/reference/query-dsl/term-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Terms(System.Func{PlainElastic.Net.Queries.TermsQuery{`0},PlainElastic.Net.Queries.TermsQuery{`0}})">
            <summary>
            A query that match on any (configurable) of the provided terms.
            This is a simpler syntax query for using a bool query with several term queries in the should clauses
            see: http://www.elasticsearch.org/guide/reference/query-dsl/terms-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Wildcard(System.Func{PlainElastic.Net.Queries.WildcardQuery{`0},PlainElastic.Net.Queries.WildcardQuery{`0}})">
            <summary>
            Matches documents that have fields matching a wildcard expression (not analyzed).
            Supported wildcards are *, which matches any character sequence (including the empty one), 
            and ?, which matches any single character.
            Note that this query can be slow, as it needs to iterate over many terms. 
            In order to prevent extremely slow wildcard queries, 
            a wildcard term should not start with one of the wildcards * or ?. 
            The wildcard query maps to Lucene WildcardQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/wildcard-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.Indices(System.Func{PlainElastic.Net.Queries.IndicesQuery{`0},PlainElastic.Net.Queries.IndicesQuery{`0}})">
            <summary>
            A query can be used when executed across multiple indices, 
            allowing to have a query that executes only when executed on an index that matches a specific list of indices,
            and another query that executes when it is executed on an index that does not match the listed indices.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/indices-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Query`1.CustomFiltersScore(System.Func{PlainElastic.Net.Queries.CustomFiltersScoreQuery{`0},PlainElastic.Net.Queries.CustomFiltersScoreQuery{`0}})">
            <summary>
            A query that allows to execute a query, and if the hit matches a provided filter (ordered),
            use either a boost or a script associated with it to compute the score.
            see http://www.elasticsearch.org/guide/reference/query-dsl/custom-filters-score-query.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.IndicesQuery`1">
            <summary>
            A query can be used when executed across multiple indices, 
            allowing to have a query that executes only when executed on an index that matches a specific list of indices,
            and another query that executes when it is executed on an index that does not match the listed indices.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/indices-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesQuery`1.Indices(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            A list of indices to match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesQuery`1.Indices(System.String[])">
            <summary>
            A list of indices to match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesQuery`1.Query(System.Func{PlainElastic.Net.Queries.Query{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The query to execute on matched indices.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesQuery`1.NoMatchQuery(System.Func{PlainElastic.Net.Queries.NoMatchQuery{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            Sets the query to use when it executes on an index that does not match the indices provided.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IndicesQuery`1.NoMatchQuery(PlainElastic.Net.Queries.IndicesNoMatchMode)">
            <summary>
            The simplified no match query. Use "all" to match all documents, or "none" to math none.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.SingleQueryBuilder`1.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents beautified JSON query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TopChildrenQuery`1">
            <summary>
            The top_children query runs the child query with an estimated hits size,
            and out of the hit docs, aggregates it into parent docs. 
            If there aren’t enough parent docs matching the requested from/size search request,
            then it is run again with a wider (more hits) search.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/top-children-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.Type(System.String)">
            <summary>
            The child type to query against. 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.Query(System.Func{PlainElastic.Net.Queries.Query{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The query to run against child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.Scope(System.String)">
            <summary>
            Allows to run facets on the same scope name that will work against the child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.Score(PlainElastic.Net.Queries.TopChildrenScoreMode)">
            <summary>
            Controls how to compute the score.
            Defaults to max.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.Factor(System.Int32)">
            <summary>
            Controls the multiplication factor of the initial hits required from the child query over the main query request.
            Defaults to 5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.IncrementalFactor(System.Int32)">
            <summary>
            Sets the incremental factor when the query needs to be re-run in order to fetch more results.
            Defaults to 2.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TopChildrenQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.PrefixFilter`1">
            <summary>
            Filters documents that have fields containing terms with a specified prefix (not analyzed). 
            Similar to phrase query, except that it acts as a filter. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/prefix-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PrefixFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PrefixFilter`1.CacheKey(System.String)">
            <summary>
            Allows to specify Cache Key that will be used as the caching key for that filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PrefixFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.PrefixQuery`1">
            <summary>
            Matches documents that have fields containing terms with a specified prefix (not analyzed).
            The prefix query maps to Lucene PrefixQuery
            see http://www.elasticsearch.org/guide/reference/query-dsl/prefix-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PrefixQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.PrefixQuery`1.Rewrite(PlainElastic.Net.Queries.Rewrite,System.Int32)">
            <summary>
            Allows to control how  multi term queries will get rewritten.
            see http://www.elasticsearch.org/guide/reference/query-dsl/multi-term-rewrite.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1">
            <summary>
            The more_like_this_field query is the same as the more_like_this query, 
            except it runs against a single field. It provides nicer query DSL over the generic more_like_this query,
            and support typed fields query (automatically wraps typed fields with type filter to match only on the specific type).
            see http://www.elasticsearch.org/guide/reference/query-dsl/mlt-field-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.LikeText(System.String)">
            <summary>
            The text to use in order to find documents that are "like" this.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.PercentTermsToMatch(System.Double)">
            <summary>
            The percentage of terms to match on (float value). Defaults to 0.3 (30 percent).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.MinTermFreq(System.Int32)">
            <summary>
            The frequency below which terms will be ignored in the source doc. The default frequency is 2.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.MaxQueryTerms(System.Int32)">
            <summary>
            The maximum number of query terms that will be included in any generated query.
            Defaults to 25.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.StopWords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.StopWords(System.String[])">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.MinDocFreq(System.Int32)">
            <summary>
            The frequency at which words will be ignored which do not occur in at least this many docs.
            Defaults to 5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.MaxDocFreq(System.Int32)">
            <summary>
            The maximum frequency in which words may still appear. Words that appear in more than this many docs will be ignored.
            Defaults to unbounded.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.MinWordLen(System.Int32)">
            <summary>
            The minimum word length below which words will be ignored. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.MaxWordLen(System.Int32)">
            <summary>
            The maximum word length above which words will be ignored.
            Defaults to unbounded (0).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.BoostTerms(System.Double)">
            <summary>
            Sets the boost factor to use when boosting terms. Defaults to 1.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.Analyzer(System.String)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisFieldQuery`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MoreLikeThisQuery`1">
            <summary>
            More like this query find documents that are “like” provided text by running it against one or more fields.
            see http://www.elasticsearch.org/guide/reference/query-dsl/mlt-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.Fields(System.String[])">
            <summary>
            A list of the fields to run the more like this query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.Fields(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            A list of the fields to run the more like this query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.FieldsOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            A list of the fields to run the more like this query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.LikeText(System.String)">
            <summary>
            The text to use in order to find documents that are "like" this.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.PercentTermsToMatch(System.Double)">
            <summary>
            The percentage of terms to match on (float value). Defaults to 0.3 (30 percent).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.MinTermFreq(System.Int32)">
            <summary>
            The frequency below which terms will be ignored in the source doc. The default frequency is 2.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.MaxQueryTerms(System.Int32)">
            <summary>
            The maximum number of query terms that will be included in any generated query.
            Defaults to 25.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.StopWords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.StopWords(System.String[])">
            <summary>
            Sets a list of stopwords to initialize the stop filter with.
            Defaults to the language stop words.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.MinDocFreq(System.Int32)">
            <summary>
            The frequency at which words will be ignored which do not occur in at least this many docs.
            Defaults to 5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.MaxDocFreq(System.Int32)">
            <summary>
            The maximum frequency in which words may still appear. Words that appear in more than this many docs will be ignored.
            Defaults to unbounded.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.MinWordLen(System.Int32)">
            <summary>
            The minimum word length below which words will be ignored. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.MaxWordLen(System.Int32)">
            <summary>
            The maximum word length above which words will be ignored.
            Defaults to unbounded (0).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.BoostTerms(System.Double)">
            <summary>
            Sets the boost factor to use when boosting terms. Defaults to 1.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.Analyzer(System.String)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MoreLikeThisQuery`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HasChildFilter`2">
            <summary>
            The has_child filter accepts a query and the child type to run against,
            and results in parent documents that have child docs matching the query.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-child-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildFilter`2.Type(System.String)">
            <summary>
            The child type to query against. 
            The parent type to return is automatically detected based on the mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildFilter`2.Query(System.Func{PlainElastic.Net.Queries.Query{`1},PlainElastic.Net.Queries.Query{`1}})">
            <summary>
            The query to run against child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildFilter`2.Filter(System.Func{PlainElastic.Net.Queries.Filter{`1},PlainElastic.Net.Queries.Filter{`1}})">
            <summary>
            The filter to run against child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildFilter`2.Scope(System.String)">
            <summary>
            Allows to run facets on the same scope name that will work against the child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildFilter`2.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.HasChildQuery`2">
            <summary>
            The has_child query accepts a query and the child type to run against,
            and results in parent documents that have child docs matching the query.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/has-child-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildQuery`2.Type(System.String)">
            <summary>
            The child type to query against. 
            The parent type to return is automatically detected based on the mappings.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildQuery`2.ScoreType(PlainElastic.Net.Queries.HasChildScoreType)">
            <summary>
            The score type for the has_child query.
            The supported score types are "max", "sum", "avg" or "none". 
            The default is "none" and yields the same behaviour as in previous versions.
            If the score type is set to another value than "none", the scores of all the matching child documents are aggregated into the associated parent documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildQuery`2.Query(System.Func{PlainElastic.Net.Queries.Query{`1},PlainElastic.Net.Queries.Query{`1}})">
            <summary>
            The query to run against child documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildQuery`2.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.HasChildQuery`2.Scope(System.String)">
            <summary>
            Allows to run facets on the same scope name that will work against the child documents.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1">
            <summary>
            The fuzzy_like_this_field query is the same as the fuzzy_like_this query, 
            except that it runs against a single field. 
            It provides nicer query DSL over the generic fuzzy_like_this query, 
            and support typed fields query (automatically wraps typed fields with type filter to match only on the specific type).
            see http://www.elasticsearch.org/guide/reference/query-dsl/flt-field-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.LikeText(System.String)">
            <summary>
            The text to use in order to find documents that are "like" this.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.IgnoreTf(System.Boolean)">
            <summary>
            Should term frequency be ignored. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.MaxQueryTerms(System.Int32)">
            <summary>
            The maximum number of query terms that will be included in any generated query.
            Defaults to 25.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.MinSimilarity(System.Double)">
            <summary>
            The minimum similarity of the term variants. Defaults to 0.5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.PrefixLength(System.Int32)">
            <summary>
            Length of required common prefix on variant terms. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.Analyzer(System.String)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisFieldQuery`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1">
            <summary>
            Fuzzy like this query find documents that are “like” provided text by running it against one or more fields.
            see http://www.elasticsearch.org/guide/reference/query-dsl/flt-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.Fields(System.String[])">
            <summary>
            A list of the fields to run the fuzzy like this query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.Fields(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            A list of the fields to run the fuzzy like this query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.FieldsOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            A list of the fields to run the fuzzy like this query against.
            Defaults to the _all field.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.LikeText(System.String)">
            <summary>
            The text to use in order to find documents that are "like" this.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.IgnoreTf(System.Boolean)">
            <summary>
            Should term frequency be ignored. Defaults to false.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.MaxQueryTerms(System.Int32)">
            <summary>
            The maximum number of query terms that will be included in any generated query.
            Defaults to 25.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.MinSimilarity(System.Double)">
            <summary>
            The minimum similarity of the term variants. Defaults to 0.5.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.PrefixLength(System.Int32)">
            <summary>
            Length of required common prefix on variant terms. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.Analyzer(System.String)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyLikeThisQuery`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer that will be used to analyze the text. Defaults to the analyzer associated with the field.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FieldQuery`1">
            <summary>
            A query that executes a query string against a specific field.
            It is a simplified version of query_string query 
            (by setting the default_field to the field this query executed against). 
            see http://www.elasticsearch.org/guide/reference/query-dsl/field-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.Query(System.String)">
            <summary>
            The actual query to be parsed.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.Rewrite(PlainElastic.Net.Queries.Rewrite,System.Int32)">
            <summary>
            Allows to control how  multi term queries will get rewritten.
            see http://www.elasticsearch.org/guide/reference/query-dsl/multi-term-rewrite.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.DefaultOperator(PlainElastic.Net.Operator)">
            <summary>
            The default operator used if no explicit operator is specified. 
            For example, with a default operator of OR, the query capital of Hungary is translated to capital OR of OR Hungary,
            and with default operator of AND, the same query is translated to capital AND of AND Hungary.
            The default value is OR.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.Analyzer(System.String)">
            <summary>
            The analyzer name used to analyze the query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer name used to analyze the query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.AllowLeadingWildcard(System.Boolean)">
            <summary>
            When set, * or ? are allowed as the first character. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.LowercaseExpandedTerms(System.Boolean)">
            <summary>
            Whether terms of wildcard, prefix, fuzzy, and range queries are to be automatically lower-cased or not (since they are not analyzed). 
            Default it true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.EnablePositionIncrements(System.Boolean)">
            <summary>
            Set to true to enable position increments in result queries. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.FuzzyPrefixLength(System.Int32)">
            <summary>
            Set the prefix length for fuzzy queries. Default is 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.FuzzyMinSim(System.Double)">
            <summary>
            Set the minimum similarity for fuzzy queries. Defaults to 0.5
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.PhraseSlop(System.Int32)">
            <summary>
            Sets the default slop for phrases. If zero, then exact phrase matches are required.
            Default value is 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.AnalyzeWildcard(System.Boolean)">
            <summary>
            By default, wildcards terms in a query string are not analyzed. 
            By setting this value to true, a best effort will be made to analyze those as well.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.AutoGeneratePhraseQueries(System.Boolean)">
            <summary>
             Set to true if phrase queries will be automatically generated
             when the analyzer returns more than one term from whitespace
             delimited text.
             NOTE: this behavior may not be suitable for all languages.
             Set to false if phrase queries should only be generated when
             surrounded by double quotes.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FieldQuery`1.MinimumShouldMatch(System.String)">
            <summary>
            A percent value (for example 20%) controlling how many “should” clauses in the resulting boolean query should match.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.CustomBoostFactorQuery`1">
            <summary>
            A query that allows  query allows to wrap another query and multiply its score by the provided boost_factor.
            This can sometimes be desired since boost value set on specific queries gets normalized, while this query boost factor does not.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/custom-boost-factor-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomBoostFactorQuery`1.BoostFactor(System.Double)">
            <summary>
            Sets the boost factor for this query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.CustomScoreQuery`1">
            <summary>
            A query that allows to wrap another query and customize the scoring of it 
            optionally with a computation derived from other field values in the doc (numeric ones) using script expression. 
            see: http://www.elasticsearch.org/guide/reference/query-dsl/custom-score-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomScoreQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomScoreQuery`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomScoreQuery`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomScoreQuery`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomScoreQuery`1.Script(System.String)">
            <summary>
            Sets the script used to calculate score value.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.WildcardQuery`1">
            <summary>
            Matches documents that have fields matching a wildcard expression (not analyzed).
            Supported wildcards are *, which matches any character sequence (including the empty one), 
            and ?, which matches any single character.
            Note that this query can be slow, as it needs to iterate over many terms. 
            In order to prevent extremely slow wildcard queries, 
            a wildcard term should not start with one of the wildcards * or ?. 
            The wildcard query maps to Lucene WildcardQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/wildcard-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.WildcardQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.WildcardQuery`1.Rewrite(PlainElastic.Net.Queries.Rewrite,System.Int32)">
            <summary>
            Allows to control how  multi term queries will get rewritten.
            see http://www.elasticsearch.org/guide/reference/query-dsl/multi-term-rewrite.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.IdsFilter`1">
            <summary>
            A filter that filters documents that only have the provided ids. 
            Note, this filter does not require the _id field to be indexed since it works using the _uid field.
            see: http://www.elasticsearch.org/guide/reference/query-dsl/ids-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsFilter`1.Type(System.String)">
            <summary>
            The index type this filter apply to.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsFilter`1.Types(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            The collection of index types this filter apply to.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.IdsFilter`1.Values(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            The collection of ids to use as filter.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NegativeQuery`1">
            <summary>
            Represents Negative part of Boosting query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.PositiveQuery`1">
            <summary>
            Represents Positive part of Boosting query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.QueryFilter`1">
            <summary>
            Wraps any query to be used as a filter. Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/query-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryFilter`1.CacheKey(System.String)">
            <summary>
            Allows to specify Cache Key that will be used as the caching key for that filter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.CustomFiltersScoreQuery`1">
            <summary>
            A query that allows to execute a query, and if the hit matches a provided filter (ordered),
            use either a boost or a script associated with it to compute the score. 
            see http://www.elasticsearch.org/guide/reference/query-dsl/custom-filters-score-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomFiltersScoreQuery`1.ScoreMode(PlainElastic.Net.Queries.CustomFiltersScoreMode)">
            <summary>
            Controls how multiple matching filters control the score.
            By default, it is set to first which means the first matching filter will control the score of the result.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomFiltersScoreQuery`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for filters boost calculation scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomFiltersScoreQuery`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for filters boost calculation scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomFiltersScoreQuery`1.Params(System.String)">
            <summary>
            Sets parameters used for filters boost calculation scripts.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.CustomFiltersScoreQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost for this query. Documents matching this query will (in addition to the normal weightings) 
            have their score multiplied by the boost provided.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoredFilter`1.Boost(System.Double)">
            <summary>
            Sets the constant boost value used in case of filter match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ScoredFilter`1.Script(System.String)">
            <summary>
            Sets the script used to calculate boost value in case of filter match.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MissingFilter`1">
            <summary>
            Filters documents where a specific field has no value in them.
            see http://www.elasticsearch.org/guide/reference/query-dsl/missing-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MissingFilter`1.ShouldMiss(System.Nullable{System.Boolean})">
            <summary>
            Controls whether filter will be applied.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MissingFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MatchAllFilter`1">
            <summary>
            A filter that matches on all documents.
            see http://www.elasticsearch.org/guide/reference/query-dsl/match-all-filter.html
            </summary>    
        </member>
        <member name="T:PlainElastic.Net.Queries.OrFilter`1">
            <summary>
            A filter that matches documents using OR boolean operator on other queries. 
            This filter is more performant then bool filter. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/or-filter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TypeFilter`1">
            <summary>
            Filters documents matching the provided document / mapping type. 
            Note, this filter can work even when the _type field is not indexed (using the _uid field)
            see http://www.elasticsearch.org/guide/reference/query-dsl/type-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TypeFilter`1.Value(System.String)">
            <summary>
            Limits the number of documents (per shard) to execute on.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.LimitFilter`1">
            <summary>
            A limit filter limits the number of documents (per shard) to execute on.
            see http://www.elasticsearch.org/guide/reference/query-dsl/limit-filter.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.LimitFilter`1.Value(System.Nullable{System.Int32})">
            <summary>
            Limits the number of documents (per shard) to execute on.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NotFilter`1">
            <summary>
            A filter that filters out matched documents using a query.
            This filter is more performant then bool filter. Can be placed within queries that accept a filter
            see http://www.elasticsearch.org/guide/reference/query-dsl/not-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NotFilter`1.Filter(System.Func{PlainElastic.Net.Queries.Filter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            The clause must appear in matching documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NotFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.MatchAllQuery`1">
            <summary>
            A query that matches all documents. Maps to Lucene MatchAllDocsQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/match-all-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchAllQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchAllQuery`1.NormsField(System.String)">
            <summary>
            Allows to specify which field the boosting will be done on.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchAllQuery`1.NormsField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            Allows to specify which field the boosting will be done on.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.MatchAllQuery`1.NormsFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            Allows to specify field of object from the collection of such objects to which the boosting will be done on.
            </summary>
            <param name="collectionField">The collection type field.</param>
            <param name="normsField">The field of object inside collection.</param>
        </member>
        <member name="T:PlainElastic.Net.Queries.TextPhrasePrefixQuery`1">
            <summary>
            Analyzes the text and creates a phrase query out of the analyzed text and
            allows for prefix matches on the last term in the text.
            see http://www.elasticsearch.org/guide/reference/query-dsl/text-query.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TextPhraseQuery`1">
            <summary>
            Analyzes the text and creates a phrase query out of the analyzed text.
            see http://www.elasticsearch.org/guide/reference/query-dsl/text-query.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TextQuery`1">
            <summary>
            A family of text queries that accept text, analyzes it, and constructs a query out of it.
            see http://www.elasticsearch.org/guide/reference/query-dsl/text-query.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FuzzyQuery`1">
            <summary>
            A fuzzy based query that uses similarity based on Levenshtein (edit distance) algorithm.
            see http://www.elasticsearch.org/guide/reference/query-dsl/fuzzy-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyQuery`1.Value(System.String)">
            <summary>
            The query value to compare to.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyQuery`1.MinSimilarity(System.Double)">
            <summary>
            The minimum similarity of the term variants.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyQuery`1.MaxExpansions(System.Int32)">
            <summary>
            Controls to how many prefixes the last term will be expanded
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FuzzyQuery`1.PrefixLength(System.Int32)">
            <summary>
            Length of required common prefix on variant terms.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FilterFacet`1">
            <summary>
            Allows you to return a count of the hits matching the filter.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/filter-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.FilterFacet`1.Filter(System.Func{PlainElastic.Net.Queries.Filter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            Filter that will be used to count facet matches.
            Note, filter facet filters are faster than query facet when using native filters (non query wrapper ones)
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FacetFilter`1">
            <summary>
            All facets can be configured with an additional filter, which will reduce the documents they use for computing results.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/index.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.Facets`1">
            <summary>
            Facets provide aggregated data based on a search query.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/index.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.Terms(System.Func{PlainElastic.Net.Queries.TermsFacet{`0},PlainElastic.Net.Queries.TermsFacet{`0}})">
            <summary>
            Allows to specify field facets that return the N most frequent terms
            see http://www.elasticsearch.org/guide/reference/api/search/facets/terms-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.FilterFacets(System.Func{PlainElastic.Net.Queries.FilterFacet{`0},PlainElastic.Net.Queries.FilterFacet{`0}})">
            <summary>
            Allows you to return a count of the hits matching the filter.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/filter-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.Range(System.Func{PlainElastic.Net.Queries.RangeFacet{`0},PlainElastic.Net.Queries.RangeFacet{`0}})">
            <summary>
            Allows to specify a set of ranges and get both the number of docs (count) that fall within each range,
            and aggregated data either based on the field, or using another field.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/range-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.Statistical(System.Func{PlainElastic.Net.Queries.StatisticalFacet{`0},PlainElastic.Net.Queries.StatisticalFacet{`0}})">
            <summary>
            Allows to compute statistical data on a numeric fields.
            The statistical data include count, total, sum of squares, mean (average), minimum, maximum, variance, and standard deviation.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/statistical-facet/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.TermsStats(System.Func{PlainElastic.Net.Queries.TermsStatsFacet{`0},PlainElastic.Net.Queries.TermsStatsFacet{`0}})">
            <summary>
            Combines both the terms and statistical allowing to compute stats computed on a field, per term value driven by another field.
            see http://www.elasticsearch.org/guide/reference/api/search/facets/terms-stats-facet/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.GeoDistance(System.Func{PlainElastic.Net.Queries.GeoDistanceFacet{`0},PlainElastic.Net.Queries.GeoDistanceFacet{`0}})">
            <summary>
            The geo_distance facet is a facet providing information for ranges of distances from a provided geo_point 
            including count of the number of hits that fall within each range, and aggregation information (like total).
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-facets-geo-distance-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.Histogram(System.Func{PlainElastic.Net.Queries.HistogramFacet{`0},PlainElastic.Net.Queries.HistogramFacet{`0}})">
            <summary>
            The histogram facet works with numeric data by building a histogram across intervals of the field values.
            Each value is "rounded" into an interval (or placed in a bucket),
            and statistics are provided per interval/bucket (count and total)
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-facets-histogram-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Facets`1.DateHistogram(System.Func{PlainElastic.Net.Queries.DateHistogramFacet{`0},PlainElastic.Net.Queries.DateHistogramFacet{`0}})">
            <summary>
            The histogram facet works with numeric data by building a histogram across intervals of the field values.
            Each value is "rounded" into an interval (or placed in a bucket),
            and statistics are provided per interval/bucket (count and total)
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-facets-histogram-facet.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermsFacet`1">
            <summary>
            Allows to specify field facets that return the N most frequent terms
            see http://www.elasticsearch.org/guide/reference/api/search/facets/terms-facet.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Field(System.String)">
            <summary>
            The field to execute term facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Field(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The field to execute term facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.FieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The field to execute term facet against.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Fields(System.String[])">
            <summary>
            The term facet can be executed against more than one field, returning the aggregation result across those fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Fields(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            The term facet can be executed against more than one field, returning the aggregation result across those fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.FieldsOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            The term facet can be executed against more than one field, returning the aggregation result across those fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Order(PlainElastic.Net.Queries.TermsFacetOrder)">
            <summary>
            Allows to control the ordering of the terms facets, to be ordered by count, term, reverse_count or reverse_term. The default is count.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Size(System.Int32)">
            <summary>
            The number of the most frequent terms to return.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.AllTerms(System.Boolean)">
            <summary>
            Allow to get all the terms in the terms facet, ones that do not match a hit, will have a count of 0.
            Note, this should not be used with fields that have many terms.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Exclude(System.String[])">
            <summary>
            Allows to specify a set of terms that should be excluded from the terms facet request result.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Regex(System.String)">
            <summary>
            The terms API allows to define regex expression that will control which terms will be included in the faceted list.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.RegexFlags(PlainElastic.Net.RegexFlags)">
            <summary>
            Allows to control the ordering of the terms facets, to be ordered by count, term, reverse_count or reverse_term. The default is count.
            see http://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html#field_summary
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Script(System.String)">
            <summary>
            Allow to define a script for terms facet to process the actual term
            that will be used in the term facet collection, and also optionally control its inclusion or not.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.ScriptField(System.String)">
            <summary>
            Allow to define a script for terms facet to process the actual term
            that will be used in the term facet collection, and also optionally control its inclusion or not.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Lang(System.String)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Lang(PlainElastic.Net.Queries.ScriptLangs)">
            <summary>
            Sets a scripting language used for scripts.
            By default used mvel language.
            see: http://www.elasticsearch.org/guide/reference/modules/scripting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFacet`1.Params(System.String)">
            <summary>
            Sets parameters used for scripts.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.FilteredQuery`1">
            <summary>
            A query that applies a filter to the results of another query. This query maps to Lucene FilteredQuery.
            see http://www.elasticsearch.org/guide/reference/query-dsl/filtered-query.html
            </summary>    
        </member>
        <member name="T:PlainElastic.Net.Queries.BoolFilter`1">
            <summary>
            A filter that matches documents matching boolean combinations of other queries.
            Similar in concept to Boolean query, except that the clauses are other filters. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/bool-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolFilter`1.Must(System.Func{PlainElastic.Net.Queries.MustFilter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            The clause must appear in matching documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolFilter`1.MustNot(System.Func{PlainElastic.Net.Queries.MustNotFilter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            The clause must not appear in the matching documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolFilter`1.Should(System.Func{PlainElastic.Net.Queries.ShouldFilter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            The clause should appear in the matching document. 
            A boolean filter with no must clauses, one or more should clauses must match a document. 
            The minimum number of should clauses to match can be set using MinimumNumberShouldMatch parameter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolFilter`1.MinimumNumberShouldMatch(System.Int32,System.Boolean)">
            <summary>
            The minimum number of should clauses to match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedBase`2.Query(System.Func{PlainElastic.Net.Queries.Query{`1},PlainElastic.Net.Queries.Query{`1}})">
            <summary>
            The query element within the search request body allows to define a query using the Query DSL.
            see http://www.elasticsearch.org/guide/reference/api/search/query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedBase`2.Path(System.Linq.Expressions.Expression{System.Func{`1,System.Object}})">
            <summary>
            Points to the nested object path, and the query includes 
            the query that will run on the nested docs matching the direct path, 
            and joining with the root parent docs.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedBase`2.Path(System.String)">
            <summary>
            Points to the nested object path, and the query includes 
            the query that will run on the nested docs matching the direct path, 
            and joining with the root parent docs.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NestedFilter`1">
            <summary>
            A filter that allows to filter nested objects / docs.
            The filter is executed against the nested objects / docs as if they were indexed 
            as separate docs (they are, internally) and resulting in the root parent doc (or parent nested mapping)
            see http://www.elasticsearch.org/guide/reference/query-dsl/nested-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermsFilter`1">
            <summary>
            Filters documents that have fields that match any of the provided terms (not analyzed).
            see http://www.elasticsearch.org/guide/reference/query-dsl/terms-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFilter`1.Execution(PlainElastic.Net.Queries.TermsFilterExecution)">
            <summary>
            Controls the way terms filter executes.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFilter`1.Cache(System.Boolean)">
            <summary>
            Controls whether the filter will be cached.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.NestedQuery`1">
            <summary>
            A query that allows to query nested objects / docs.
            The query is executed against the nested objects / docs as if they were indexed 
            as separate docs (they are, internally) and resulting in the root parent doc (or parent nested mapping)
            see http://www.elasticsearch.org/guide/reference/query-dsl/nested-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedQuery`1.ScoreMode(PlainElastic.Net.Queries.ScoreMode)">
            <summary>
            Allows to set how inner children matching affects scoring of parent.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.NestedQuery`1.Scope(System.String)">
            <summary>
            Allows to define scope associated with query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.DisMaxQuery`1">
            <summary>
            A query that generates the union of documents produced by its subqueries, 
            and that scores each document with the maximum score for that document as produced by any subquery, 
            plus a tie breaking increment for any additional matching subqueries.
            see http://www.elasticsearch.org/guide/reference/query-dsl/dis-max-query.html
            </summary>   
        </member>
        <member name="M:PlainElastic.Net.Queries.DisMaxQuery`1.Queries(System.Func{PlainElastic.Net.Queries.DisMaxQueries{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            Dis Max subqueries.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DisMaxQuery`1.TieBreaker(System.Double)">
            <summary>
            The tie breaker capability allows results that include the same term in multiple 
            fields to be judged better than results that include this term in only the best of 
            those multiple fields, without confusing this with the better case of two different terms in the multiple fields.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.DisMaxQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.IndicesNoMatchMode.all">
            <summary>
            Match all documents
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.IndicesNoMatchMode.none">
            <summary>
            match no documents
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.Rewrite.constant_score_default">
            <summary>
            Defaults to automatically choosing either constant_score_boolean or constant_score_filter based on query characteristics.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.Rewrite.scoring_boolean">
            <summary>
            A rewrite method that first translates each term into a should clause in a boolean query, 
            and keeps the scores as computed by the query. 
            Note that typically such scores are meaningless to the user, and require non-trivial CPU to compute, 
            so it’s almost always better to use constant_score_default. 
            This rewrite method will hit too many clauses failure if it exceeds the boolean query limit (defaults to 1024).
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.Rewrite.constant_score_boolean">
            <summary>
             Similar to scoring_boolean except scores are not computed. 
            Instead, each matching document receives a constant score equal to the query’s boost. 
            This rewrite method will hit too many clauses failure if it exceeds the boolean query limit (defaults to 1024).
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.Rewrite.constant_score_filter">
            <summary>
             A rewrite method that first creates a private Filter by visiting each term in sequence and marking all docs for that term.
             Matching documents are assigned a constant score equal to the query’s boost.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.Rewrite.top_terms_boost_n">
            <summary>
            A rewrite method that first translates each term into should clause in boolean query, and keeps the scores as computed by the query.
            This rewrite method only uses the top scoring terms so it will not overflow boolean max clause count.
            The N controls the size of the top scoring terms to use.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.Rewrite.top_terms_n">
            <summary>
            A rewrite method that first translates each term into should clause in boolean query, but the scores are only computed as the boost.
            This rewrite method only uses the top scoring terms so it will not overflow the boolean max clause count.
            The N controls the size of the top scoring terms to use.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.TextQueryType.boolean">
            <summary>
            The text provided is analyzed and the analysis process constructs a boolean query from the provided text.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.TextQueryType.phrase">
            <summary>
            Analyzes the text and creates a phrase query out of the analyzed text.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.TextQueryType.phrase_prefix">
            <summary>
            Analyzes the text and creates a phrase query out of the analyzed text. Allows for prefix matches on the last term in the text.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.DistanceType">
            <summary>
            How to compute the distance. Can either be arc (better precision) or plane (faster). Defaults to arc.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.DistanceType.arc">
            <summary>
            Calculates distance as points in a globe.
            Use arc for better precision.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.DistanceType.sloppy_arc">
            <summary>
            Calculates distance as points in a globe.
            Use sloppy_arc for faster calculation.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.DistanceType.plane">
            <summary>
            Calculates distance as points on a plane. Faster, but less accurate than arc
            Use plane for performance.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.OptimizeBoundingBox">
            <summary>
            Will an optimization of using first a bounding box check will be used. Defaults to memory which will do in memory checks.
            Can also have value of indexed to use indexed value check (make sure the geo_point type index lat lon in this case), or
            none which disables bounding box optimization.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.OptimizeBoundingBox.memory">
            <summary>
            Perform in memory check.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.OptimizeBoundingBox.indexed">
            <summary>
            Perform index value check.
            </summary>
        </member>
        <member name="F:PlainElastic.Net.Queries.OptimizeBoundingBox.none">
            <summary>
            Disables bounding box optimization.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.RangeQuery`1">
            <summary>
            Matches documents with fields that have terms within a certain range. 
            see http://www.elasticsearch.org/guide/reference/query-dsl/range-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.RangeQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="P:PlainElastic.Net.IElasticConnection.Timeout">
            <summary>
            Timeout in milliseconds.
            </summary>
        </member>
        <member name="P:PlainElastic.Net.ElasticConnection.Timeout">
            <summary>
            Timeout in milliseconds. Default value 1 minute or 60000 msec
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.AndFilter`1">
            <summary>
            A filter that matches documents using AND boolean operator on other queries.
            This filter is more performant then bool filter. 
            Can be placed within queries that accept a filter.
            see http://www.elasticsearch.org/guide/reference/query-dsl/and-filter.html
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.BoolQuery`1">
            <summary>
            A query that matches documents matching boolean combinations of other queries. 
            The bool query maps to Lucene BooleanQuery. It is built using one or more boolean clauses, 
            each clause with a typed occurrence
            see: http://www.elasticsearch.org/guide/reference/query-dsl/bool-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolQuery`1.Must(System.Func{PlainElastic.Net.Queries.MustQuery{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The clause (query) must appear in matching documents.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolQuery`1.MustNot(System.Func{PlainElastic.Net.Queries.MustNotQuery{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The clause (query) must not appear in the matching documents.
            Note that it is not possible to search on documents that only consists of a must_not clauses.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolQuery`1.Should(System.Func{PlainElastic.Net.Queries.ShouldQuery{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The clause (query) should appear in the matching document. 
            A boolean query with no must clauses, one or more should clauses must match a document. 
            The minimum number of should clauses to match can be set using MinimumNumberShouldMatch parameter.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolQuery`1.MinimumNumberShouldMatch(System.Int32,System.Boolean)">
            <summary>
            The minimum number of should clauses to match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.BoolQuery`1.DisableCoord(System.Boolean)">
            <summary>
            Disables Coord score factor in scoring.
            For example, this score factor does not make sense for most automatically generated queries, like WildcardQuery and FuzzyQuery.
            see http://lucene.apache.org/core/old_versioned_docs/versions/3_0_1/api/all/org/apache/lucene/search/Similarity.html#coord(int,+int)
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.Sort`1.Field(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},PlainElastic.Net.SortDirection,System.String,System.Boolean)">
            <summary>
            Sort result using specified field.
            There can be several Sort parameters (order is important).
            </summary>
            <param name="field">The field.</param>
            <param name="order">The sort order. By default order depends on chosen field (descending for "_scope", ascending for others) and field analyzer.</param>
            <param name="missing">The missing value handling strategy. Use _last, _first or custom value.</param>
            <param name="ignoreUnmapped"> The ignore_unmapped option allows to ignore fields that have no mapping and not sort by them. </param>
        </member>
        <member name="M:PlainElastic.Net.Queries.Sort`1.Field(System.String,PlainElastic.Net.SortDirection,System.String,System.Boolean)">
            <summary>
            Sort result using specified field.
            There can be several Sort parameters (order is important).
            </summary>
            <param name="field">The field. Use _score to sort by score.</param>
            <param name="order">The sort order. By default order depends on chosen field (descending for "_scope", ascending for others) and field analyzer.</param>
            <param name="missing">The missing value handling strategy. Use _last, _first or custom value.</param>
            <param name="ignoreUnmapped"> The ignore_unmapped option allows to ignore fields that have no mapping and not sort by them. </param>
        </member>
        <member name="M:PlainElastic.Net.Queries.Sort`1.GeoDistance(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Double,System.Double,PlainElastic.Net.Queries.DistanceUnit,PlainElastic.Net.SortDirection)">
            <summary>
            Sort result by geo distance using specified field.
            There can be several Sort parameters (order is important).
            </summary>
            <param name="field">The field.</param>
            <param name="lat">The latitude.</param>
            <param name="lon">The longitude.</param>
            <param name="unit">The distance unit.</param>
            <param name="order">The sort order. By default results will be sorted ascending.</param>
        </member>
        <member name="M:PlainElastic.Net.Queries.Sort`1.GeoDistance(System.String,System.Double,System.Double,PlainElastic.Net.Queries.DistanceUnit,PlainElastic.Net.SortDirection)">
            <summary>
            Sort result by geo distance using specified field.
            There can be several Sort parameters (order is important).
            </summary>
            <param name="field">The field.</param>
            <param name="lat">The latitude.</param>
            <param name="lon">The longitude.</param>
            <param name="unit">The distance unit.</param>
            <param name="order">The sort order. By default results will be sorted ascending.</param>
        </member>
        <member name="M:PlainElastic.Net.Queries.Sort`1.GeoDistance(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.String,PlainElastic.Net.Queries.DistanceUnit,PlainElastic.Net.SortDirection)">
            <summary>
            Sort result by geo distance using specified field.
            There can be several Sort parameters (order is important).
            </summary>
            <param name="field">The field.</param>
            <param name="geohash">The geohash.</param>
            <param name="unit">The distance unit.</param>
            <param name="order">The sort order. By default results will be sorted ascending.</param>
        </member>
        <member name="M:PlainElastic.Net.Queries.Sort`1.GeoDistance(System.String,System.String,PlainElastic.Net.Queries.DistanceUnit,PlainElastic.Net.SortDirection)">
            <summary>
            Sort result by geo distance using specified field.
            There can be several Sort parameters (order is important).
            </summary>
            <param name="field">The field.</param>
            <param name="geohash">The geohash.</param>
            <param name="unit">The distance unit.</param>
            <param name="order">The sort order. By default results will be sorted ascending.</param>
        </member>
        <member name="T:PlainElastic.Net.Queries.ExistsFilter`1">
            <summary>
            Filters documents where a specific field has a value in them.
            see http://www.elasticsearch.org/guide/reference/query-dsl/exists-filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ExistsFilter`1.ShouldExists(System.Nullable{System.Boolean})">
            <summary>
            Controls whether filter will be applied.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.ExistsFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="M:PlainElastic.Net.JsonConvertibleExtensions.Build(PlainElastic.Net.Builders.IJsonConvertible)">
            <summary>
            Builds JSON query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.JsonConvertibleExtensions.BuildBeautified(PlainElastic.Net.Builders.IJsonConvertible)">
            <summary>
            Builds beatified JSON query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.QueryBuilder`1">
            <summary>
            Provides sophisticated interface to construct ElasicSearch queries.
            For details about ES querying see: http://www.elasticsearch.org/guide/reference/api/search/request-body.html ,
            http://www.elasticsearch.org/guide/reference/query-dsl/ and http://www.elasticsearch.org/guide/reference/api/search/    
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Query(System.Func{PlainElastic.Net.Queries.Query{`0},PlainElastic.Net.Queries.Query{`0}})">
            <summary>
            The query element within the search request body allows to define a query using the Query DSL.
            see http://www.elasticsearch.org/guide/reference/api/search/query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Filter(System.Func{PlainElastic.Net.Queries.Filter{`0},PlainElastic.Net.Queries.Filter{`0}})">
            <summary>
            Allows to filter result hits without changing facet results.
            see http://www.elasticsearch.org/guide/reference/api/search/filter.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Facets(System.Func{PlainElastic.Net.Queries.Facets{`0},PlainElastic.Net.Queries.Facets{`0}})">
            <summary>
            Allows to collect aggregated data based on a search query. 
            see http://www.elasticsearch.org/guide/reference/api/search/facets/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Aggregations(System.Func{PlainElastic.Net.Queries.Aggregations{`0},PlainElastic.Net.Queries.Aggregations{`0}})">
            <summary>
            Allows to collect aggregated data based on a search query. 
            see http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/search-aggregations.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.From(System.Int32)">
            <summary>
            The starting from index of the hits to return. Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Size(System.Int32)">
            <summary>
            The number of hits to return. Defaults to 10.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.TrackScores(System.Boolean)">
            <summary>
            When sorting on a field, scores are not computed. 
            By setting track_scores to true, scores will still be computed and tracked.
            see http://www.elasticsearch.org/guide/reference/api/search/sort.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Sort(System.Func{PlainElastic.Net.Queries.Sort{`0},PlainElastic.Net.Queries.Sort{`0}})">
            <summary>
            Allows to add one or more sort on specific fields. Each sort can be reversed as well. 
            The sort is defined on a per field level, with special field name for _score to sort by score.
            see http://www.elasticsearch.org/guide/reference/api/search/sort.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Explain(System.Boolean)">
            <summary>
            Enables explanation for each hit on how its score was computed.
            see http://www.elasticsearch.org/guide/reference/api/search/explain.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Version(System.Boolean)">
            <summary>
            Returns a version for each search hit.
            see http://www.elasticsearch.org/guide/reference/api/search/version.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.MinScore(System.Double)">
            <summary>
            Allows to filter out documents based on a minimum score.
            see http://www.elasticsearch.org/guide/reference/api/search/min-score.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Highlight(System.Func{PlainElastic.Net.Queries.Highlight{`0},PlainElastic.Net.Queries.Highlight{`0}})">
            <summary>
            Allows to highlight search results on one or more fields.
            see http://www.elasticsearch.org/guide/reference/api/search/highlighting.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.Build">
            <summary>
            Builds JSON query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.BuildBeautified">
            <summary>
            Builds beatified JSON query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryBuilder`1.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents beautified JSON query.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.QueryString`1">
            <summary>
            A query that uses a query parser in order to parse its content
            see http://www.elasticsearch.org/guide/reference/query-dsl/query-string-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.DefaultField(System.String)">
            <summary>
            The default field for query terms if no prefix field is specified. 
            Defaults to the index.query.default_field index settings, which in turn defaults to _all
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.DefaultField(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            The default field for query terms if no prefix field is specified. 
            Defaults to the index.query.default_field index settings, which in turn defaults to _all
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.DefaultFieldOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            The default field for query terms if no prefix field is specified. 
            Defaults to the index.query.default_field index settings, which in turn defaults to _all
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Fields(System.String[])">
            <summary>
            Allows to running the query_string query against multiple fields by internally creating several queries for the same query string,
            each with default_field that match the fields provided.
            Since several queries are generated, combining them can be automatically done either using a dis_max query or a simple bool query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Fields(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            Allows to running the query_string query against multiple fields by internally creating several queries for the same query string,
            each with default_field that match the fields provided.
            Since several queries are generated, combining them can be automatically done either using a dis_max query or a simple bool query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.FieldsOfCollection``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            Allows to running the query_string query against multiple fields by internally creating several queries for the same query string,
            each with default_field that match the fields provided.
            Since several queries are generated, combining them can be automatically done either using a dis_max query or a simple bool query.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Query(System.String)">
            <summary>
            The actual query to be parsed.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Rewrite(PlainElastic.Net.Queries.Rewrite,System.Int32)">
            <summary>
            Allows to control how  multi term queries will get rewritten.
            see http://www.elasticsearch.org/guide/reference/query-dsl/multi-term-rewrite.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.DefaultOperator(PlainElastic.Net.Operator)">
            <summary>
            The default operator used if no explicit operator is specified. 
            For example, with a default operator of OR, the query capital of Hungary is translated to capital OR of OR Hungary,
            and with default operator of AND, the same query is translated to capital AND of AND Hungary.
            The default value is OR.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Analyzer(System.String)">
            <summary>
            The analyzer name used to analyze the query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Analyzer(PlainElastic.Net.DefaultAnalyzers)">
            <summary>
            The analyzer name used to analyze the query string. Defaults to the globally configured analyzer.
            see: http://www.elasticsearch.org/guide/reference/index-modules/analysis/
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.AllowLeadingWildcard(System.Boolean)">
            <summary>
            When set, * or ? are allowed as the first character. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.LowercaseExpandedTerms(System.Boolean)">
            <summary>
            Whether terms of wildcard, prefix, fuzzy, and range queries are to be automatically lower-cased or not (since they are not analyzed). 
            Default it true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.EnablePositionIncrements(System.Boolean)">
            <summary>
            Set to true to enable position increments in result queries. Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.FuzzyPrefixLength(System.Int32)">
            <summary>
            Set the prefix length for fuzzy queries. Default is 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.FuzzyMinSim(System.Double)">
            <summary>
            Set the minimum similarity for fuzzy queries. Defaults to 0.5
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.PhraseSlop(System.Int32)">
            <summary>
            Sets the default slop for phrases. If zero, then exact phrase matches are required.
             Default value is 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.AnalyzeWildcard(System.Boolean)">
            <summary>
            By default, wildcards terms in a query string are not analyzed. 
            By setting this value to true, a best effort will be made to analyze those as well.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.AutoGeneratePhraseQueries(System.Boolean)">
            <summary>
             Set to true if phrase queries will be automatically generated
             when the analyzer returns more than one term from whitespace
             delimited text.
             NOTE: this behavior may not be suitable for all languages.
             Set to false if phrase queries should only be generated when
             surrounded by double quotes.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.MinimumShouldMatch(System.String)">
            <summary>
            A percent value (for example 20%) controlling how many “should” clauses in the resulting boolean query should match.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.UseDisMax(System.Boolean)">
            <summary>
            Should the queries be combined using dis_max (set it to true), or a bool query (set it to false).
            Defaults to true.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.TieBreaker(System.Double)">
            <summary>
            When using dis_max, the disjunction max tie breaker. 
            Defaults to 0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Exists(System.String)">
            <summary>
             Allows to control docs that have fields that exists within them (have a value).
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.QueryString`1.Missing(System.String)">
            <summary>
             Allows to control docs that have fields that missing within them.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermQuery`1">
            <summary>
            Matches documents that have fields that contain a term (not analyzed). The term query maps to Lucene TermQuery
            see http://www.elasticsearch.org/guide/reference/query-dsl/term-query.html
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Queries.TermQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermFilter`1">
            <summary>
            Filters documents that have fields that contain a term (not analyzed).
            see http://www.elasticsearch.org/guide/reference/query-dsl/term-filter.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.TermFilter`1.Name(System.String)">
            <summary>
            Allows to name filter, so the search response will include for each hit the matched_filters 
            it matched on (note, this feature make sense for or / bool filters).
            http://www.elasticsearch.org/guide/reference/api/search/named-filters.html 
            </summary>
        </member>
        <member name="T:PlainElastic.Net.Queries.TermsQuery`1">
            <summary>
            A query that match on any (configurable) of the provided terms.
            This is a simpler syntax query for using a bool query with several term queries in the should clauses
            see: http://www.elasticsearch.org/guide/reference/query-dsl/terms-query.html
            </summary>    
        </member>
        <member name="M:PlainElastic.Net.Queries.TermsQuery`1.Boost(System.Double)">
            <summary>
            Sets the boost value of the query. Defaults to 1.0.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Utils.FuncExtensions.Bind``2(System.Func{``0,``1},System.Func{``1,``1})">
            <summary>
            Represents Monadic Bind operator.
            Provides an easy way to compose functions.
            Internally it passes current expression result to <paramref name="func"/> function, 
            thus it could be used to apply additional logic to current expression.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Utils.StringExtensions.AltQuoteF(System.String,System.Object[])">
            <summary>
            Provide string formatting alongside 
            with replacing ' by " quotation sign in passed format.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Utils.StringExtensions.AltQuote(System.String)">
            <summary>
            Replaces ' by " quotation sign and ` by '.
            Useful for JSON declarations.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Utils.StringExtensions.Quotate(System.String)">
            <summary>
            Quotates the specified value and escapes special JSON chars.
            </summary>
        </member>
        <member name="M:PlainElastic.Net.Utils.StringExtensions.Quotate(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Quotates the specified values and escapes special JSON chars.
            </summary>
        </member>
    </members>
</doc>
