﻿ ProductId = entity.ProductId,
 MallId = entity.MallId,
 UnitId = entity.UnitId,
 Price = entity.Price,
 WarrantyMonth = entity.WarrantyMonth,
 Freight = entity.Freight,
 FreightExplain = entity.FreightExplain,
 SaleRegion = entity.SaleRegion,
 Statuz = entity.Statuz,
 UserId = entity.UserId,
 ShelfTime = entity.ShelfTime,
 Reason = entity.Reason,
 AuditUserId = entity.AuditUserId,


 ProductId = model.ProductId,
 MallId = model.MallId,
 UnitId = model.UnitId,
 Price = model.Price,
 WarrantyMonth = model.WarrantyMonth,
 Freight = model.Freight,
 FreightExplain = model.FreightExplain,
 SaleRegion = model.SaleRegion,
 Statuz = model.Statuz,
 UserId = model.UserId,
 ShelfTime = model.ShelfTime,
 Reason = model.Reason,
 AuditUserId = model.AuditUserId,


 temp.ProductId = model.ProductId,
 temp.MallId = model.MallId,
 temp.UnitId = model.UnitId,
 temp.Price = model.Price,
 temp.WarrantyMonth = model.WarrantyMonth,
 temp.Freight = model.Freight,
 temp.FreightExplain = model.FreightExplain,
 temp.SaleRegion = model.SaleRegion,
 temp.Statuz = model.Statuz,
 temp.UserId = model.UserId,
 temp.ShelfTime = model.ShelfTime,
 temp.Reason = model.Reason,
 temp.AuditUserId = model.AuditUserId,

 ProductShelfId = item.ProductShelfId,
 ProductId = item.ProductId,
 MallId = item.MallId,
 UnitId = item.UnitId,
 Price = item.Price,
 WarrantyMonth = item.WarrantyMonth,
 Freight = item.Freight,
 FreightExplain = item.FreightExplain,
 SaleRegion = item.SaleRegion,
 Statuz = item.Statuz,
 UserId = item.UserId,
 ShelfTime = item.ShelfTime,
 Reason = item.Reason,
 AuditUserId = item.AuditUserId,

public class ProductShelfInputModel
{
 [Display(Name = "Id")] 
    public long ProductShelfId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "供应商Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "单价")] 
    public decimal Price {get; set; }
    
 [Display(Name = "质保(月)")] 
    public int WarrantyMonth {get; set; }
    
 [Display(Name = "运费(1:包含运费  0:不包含运费)")] 
    public int Freight {get; set; }
    
 [Display(Name = "运费说明")] 
    public string FreightExplain {get; set; }
    
 [Display(Name = "销售区域")] 
    public string SaleRegion {get; set; }
    
 [Display(Name = "状态（根据不同商城要求，需要审核默认0；无需审核默认1；审核不通过2；10临时下架；11：商城强制下架）=;状态（根据不同商城要求，待审核   = 0   ，审核通过  =1   ，审核不通过 = 2   ,暂停销售 = 3 ， 临时下架 = 10    ；商城强制下架 = 11 ）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "上架人Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "上架时间（状态为1的时间）")] 
    public DateTime ShelfTime {get; set; }
    
 [Display(Name = "审核不通过原因")] 
    public string Reason {get; set; }
    
 [Display(Name = "审核人Id（无需审核为0）")] 
    public long AuditUserId {get; set; }
    
 }
 
 public class ProductShelfViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ProductShelfId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 供应商Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public decimal Price {get; set; }
    
    /// <summary>
    /// 质保(月)
    /// </summary>
    public int WarrantyMonth {get; set; }
    
    /// <summary>
    /// 运费(1:包含运费  0:不包含运费)
    /// </summary>
    public int Freight {get; set; }
    
    /// <summary>
    /// 运费说明
    /// </summary>
    public string FreightExplain {get; set; }
    
    /// <summary>
    /// 销售区域
    /// </summary>
    public string SaleRegion {get; set; }
    
    /// <summary>
    /// 状态（根据不同商城要求，需要审核默认0；无需审核默认1；审核不通过2；10临时下架；11：商城强制下架）=;状态（根据不同商城要求，待审核   = 0   ，审核通过  =1   ，审核不通过 = 2   ,暂停销售 = 3 ， 临时下架 = 10    ；商城强制下架 = 11 ）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 上架人Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 上架时间（状态为1的时间）
    /// </summary>
    public DateTime ShelfTime {get; set; }
    
    /// <summary>
    /// 审核不通过原因
    /// </summary>
    public string Reason {get; set; }
    
    /// <summary>
    /// 审核人Id（无需审核为0）
    /// </summary>
    public long AuditUserId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Price, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Price, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.WarrantyMonth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.WarrantyMonth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入质保(月)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Freight, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Freight, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运费(1:包含运费  0:不包含运费)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FreightExplain, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FreightExplain, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运费说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SaleRegion, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SaleRegion, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入销售区域" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（根据不同商城要求，需要审核默认0；无需审核默认1；审核不通过2；10临时下架；11：商城强制下架）=;状态（根据不同商城要求，待审核   = 0   ，审核通过  =1   ，审核不通过 = 2   ,暂停销售 = 3 ， 临时下架 = 10    ；商城强制下架 = 11 ）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上架人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ShelfTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ShelfTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上架时间（状态为1的时间）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Reason, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Reason, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核不通过原因" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核人Id（无需审核为0）" } })                    
                </div>
           </div>
  




 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'UnitId', title: '供应商Id', sortable: true },
                 
 { field: 'Price', title: '单价', sortable: true },
                 
 { field: 'WarrantyMonth', title: '质保(月)', sortable: true },
                 
 { field: 'Freight', title: '运费(1:包含运费  0:不包含运费)', sortable: true },
                 
 { field: 'FreightExplain', title: '运费说明', sortable: true },
                 
 { field: 'SaleRegion', title: '销售区域', sortable: true },
                 
 { field: 'Statuz', title: '状态（根据不同商城要求，需要审核默认0；无需审核默认1；审核不通过2；10临时下架；11：商城强制下架）=;状态（根据不同商城要求，待审核   = 0   ，审核通过  =1   ，审核不通过 = 2   ,暂停销售 = 3 ， 临时下架 = 10    ；商城强制下架 = 11 ）', sortable: true },
                 
 { field: 'UserId', title: '上架人Id', sortable: true },
                 
 { field: 'ShelfTime', title: '上架时间（状态为1的时间）', sortable: true },
                 
 { field: 'Reason', title: '审核不通过原因', sortable: true },
                 
 { field: 'AuditUserId', title: '审核人Id（无需审核为0）', sortable: true },
                 
o.ProductId,                 
o.MallId,                 
o.UnitId,                 
o.Price,                 
o.WarrantyMonth,                 
o.Freight,                 
o.FreightExplain,                 
o.SaleRegion,                 
o.Statuz,                 
o.UserId,                 
o.ShelfTime,                 
o.Reason,                 
o.AuditUserId,                 
        
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#MallId').val(d.data.rows.MallId);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#Price').val(d.data.rows.Price);          
        $('#WarrantyMonth').val(d.data.rows.WarrantyMonth);          
        $('#Freight').val(d.data.rows.Freight);          
        $('#FreightExplain').val(d.data.rows.FreightExplain);          
        $('#SaleRegion').val(d.data.rows.SaleRegion);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#ShelfTime').val(d.data.rows.ShelfTime);          
        $('#Reason').val(d.data.rows.Reason);          
        $('#AuditUserId').val(d.data.rows.AuditUserId);          

 $('#th_ProductId').html(' 产品Id');               
 $('#th_MallId').html(' 商城Id');               
 $('#th_UnitId').html(' 供应商Id');               
 $('#th_Price').html(' 单价');               
 $('#th_WarrantyMonth').html(' 质保(月)');               
 $('#th_Freight').html(' 运费(1:包含运费  0:不包含运费)');               
 $('#th_FreightExplain').html(' 运费说明');               
 $('#th_SaleRegion').html(' 销售区域');               
 $('#th_Statuz').html(' 状态（根据不同商城要求，需要审核默认0；无需审核默认1；审核不通过2；10临时下架；11：商城强制下架）=;状态（根据不同商城要求，待审核   = 0   ，审核通过  =1   ，审核不通过 = 2   ,暂停销售 = 3 ， 临时下架 = 10    ；商城强制下架 = 11 ）');               
 $('#th_UserId').html(' 上架人Id');               
 $('#th_ShelfTime').html(' 上架时间（状态为1的时间）');               
 $('#th_Reason').html(' 审核不通过原因');               
 $('#th_AuditUserId').html(' 审核人Id（无需审核为0）');               
 
 $('#tr_ProductId').hide();               
 $('#tr_MallId').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_Price').hide();               
 $('#tr_WarrantyMonth').hide();               
 $('#tr_Freight').hide();               
 $('#tr_FreightExplain').hide();               
 $('#tr_SaleRegion').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_UserId').hide();               
 $('#tr_ShelfTime').hide();               
 $('#tr_Reason').hide();               
 $('#tr_AuditUserId').hide();               

 , "ProductId" : productId
 , "MallId" : mallId
 , "UnitId" : unitId
 , "Price" : price
 , "WarrantyMonth" : warrantyMonth
 , "Freight" : freight
 , "FreightExplain" : freightExplain
 , "SaleRegion" : saleRegion
 , "Statuz" : statuz
 , "UserId" : userId
 , "ShelfTime" : shelfTime
 , "Reason" : reason
 , "AuditUserId" : auditUserId

 var productId = $('#o_ProductId').val();
 var mallId = $('#o_MallId').val();
 var unitId = $('#o_UnitId').val();
 var price = $('#o_Price').val();
 var warrantyMonth = $('#o_WarrantyMonth').val();
 var freight = $('#o_Freight').val();
 var freightExplain = $('#o_FreightExplain').val();
 var saleRegion = $('#o_SaleRegion').val();
 var statuz = $('#o_Statuz').val();
 var userId = $('#o_UserId').val();
 var shelfTime = $('#o_ShelfTime').val();
 var reason = $('#o_Reason').val();
 var auditUserId = $('#o_AuditUserId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单价' : '产品名称', d.data.rows.Price);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '质保(月)' : '产品名称', d.data.rows.WarrantyMonth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运费(1:包含运费  0:不包含运费)' : '产品名称', d.data.rows.Freight);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运费说明' : '产品名称', d.data.rows.FreightExplain);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '销售区域' : '产品名称', d.data.rows.SaleRegion);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（根据不同商城要求，需要审核默认0；无需审核默认1；审核不通过2；10临时下架；11：商城强制下架）=;状态（根据不同商城要求，待审核   = 0   ，审核通过  =1   ，审核不通过 = 2   ,暂停销售 = 3 ， 临时下架 = 10    ；商城强制下架 = 11 ）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上架人Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上架时间（状态为1的时间）' : '产品名称', d.data.rows.ShelfTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核不通过原因' : '产品名称', d.data.rows.Reason);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核人Id（无需审核为0）' : '产品名称', d.data.rows.AuditUserId);



