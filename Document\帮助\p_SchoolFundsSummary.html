﻿ SchoolId = entity.SchoolId,
 SchoolAmount = entity.SchoolAmount,
 CountyAmount = entity.CountyAmount,
 CityAmount = entity.CityAmount,
 OtherAmount = entity.OtherAmount,


 SchoolId = model.SchoolId,
 SchoolAmount = model.SchoolAmount,
 CountyAmount = model.CountyAmount,
 CityAmount = model.CityAmount,
 OtherAmount = model.OtherAmount,


 temp.SchoolId = model.SchoolId,
 temp.SchoolAmount = model.SchoolAmount,
 temp.CountyAmount = model.CountyAmount,
 temp.CityAmount = model.CityAmount,
 temp.OtherAmount = model.OtherAmount,

 SchoolFundsSummaryId = item.SchoolFundsSummaryId,
 SchoolId = item.SchoolId,
 SchoolAmount = item.SchoolAmount,
 CountyAmount = item.CountyAmount,
 CityAmount = item.CityAmount,
 OtherAmount = item.OtherAmount,

public class SchoolFundsSummaryInputModel
{
 [Display(Name = "学校产品申报汇总表Id")] 
    public int SchoolFundsSummaryId {get; set; }
    
 [Display(Name = "学校Id （UnitId）")] 
    public int SchoolId {get; set; }
    
 [Display(Name = "校级资金")] 
    public decimal SchoolAmount {get; set; }
    
 [Display(Name = "区级资金")] 
    public decimal CountyAmount {get; set; }
    
 [Display(Name = "市级资金")] 
    public decimal CityAmount {get; set; }
    
 [Display(Name = "其它资金")] 
    public decimal OtherAmount {get; set; }
    
 }
 
 public class SchoolFundsSummaryViewModel
 {
    /// <summary>
    /// 学校产品申报汇总表Id
    /// </summary>
    public int SchoolFundsSummaryId {get; set; }
    
    /// <summary>
    /// 学校Id （UnitId）
    /// </summary>
    public int SchoolId {get; set; }
    
    /// <summary>
    /// 校级资金
    /// </summary>
    public decimal SchoolAmount {get; set; }
    
    /// <summary>
    /// 区级资金
    /// </summary>
    public decimal CountyAmount {get; set; }
    
    /// <summary>
    /// 市级资金
    /// </summary>
    public decimal CityAmount {get; set; }
    
    /// <summary>
    /// 其它资金
    /// </summary>
    public decimal OtherAmount {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校Id （UnitId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入校级资金" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountyAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountyAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区级资金" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CityAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CityAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市级资金" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OtherAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OtherAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入其它资金" } })                    
                </div>
           </div>
  




 { field: 'SchoolId', title: '学校Id （UnitId）', sortable: true },
                 
 { field: 'SchoolAmount', title: '校级资金', sortable: true },
                 
 { field: 'CountyAmount', title: '区级资金', sortable: true },
                 
 { field: 'CityAmount', title: '市级资金', sortable: true },
                 
 { field: 'OtherAmount', title: '其它资金', sortable: true },
                 
o.SchoolId,                 
o.SchoolAmount,                 
o.CountyAmount,                 
o.CityAmount,                 
o.OtherAmount,                 
        
        $('#SchoolId').val(d.data.rows.SchoolId);          
        $('#SchoolAmount').val(d.data.rows.SchoolAmount);          
        $('#CountyAmount').val(d.data.rows.CountyAmount);          
        $('#CityAmount').val(d.data.rows.CityAmount);          
        $('#OtherAmount').val(d.data.rows.OtherAmount);          

 $('#th_SchoolId').html(' 学校Id （UnitId）');               
 $('#th_SchoolAmount').html(' 校级资金');               
 $('#th_CountyAmount').html(' 区级资金');               
 $('#th_CityAmount').html(' 市级资金');               
 $('#th_OtherAmount').html(' 其它资金');               
 
 $('#tr_SchoolId').hide();               
 $('#tr_SchoolAmount').hide();               
 $('#tr_CountyAmount').hide();               
 $('#tr_CityAmount').hide();               
 $('#tr_OtherAmount').hide();               

 , "SchoolId" : schoolId
 , "SchoolAmount" : schoolAmount
 , "CountyAmount" : countyAmount
 , "CityAmount" : cityAmount
 , "OtherAmount" : otherAmount

 var schoolId = $('#o_SchoolId').val();
 var schoolAmount = $('#o_SchoolAmount').val();
 var countyAmount = $('#o_CountyAmount').val();
 var cityAmount = $('#o_CityAmount').val();
 var otherAmount = $('#o_OtherAmount').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校Id （UnitId）' : '产品名称', d.data.rows.SchoolId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '校级资金' : '产品名称', d.data.rows.SchoolAmount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区级资金' : '产品名称', d.data.rows.CountyAmount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市级资金' : '产品名称', d.data.rows.CityAmount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '其它资金' : '产品名称', d.data.rows.OtherAmount);



