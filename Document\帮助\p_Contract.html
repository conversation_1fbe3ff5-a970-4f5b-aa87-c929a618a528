﻿ OrderId = entity.OrderId,
 MallId = entity.MallId,
 WarrantyPeriod = entity.WarrantyPeriod,
 Name = entity.Name,
 Amount = entity.Amount,
 SupplierId = entity.SupplierId,
 SupplierName = entity.SupplierName,
 SupplierCode = entity.SupplierCode,
 SupplierUserLink = entity.SupplierUserLink,
 SupplierMobile = entity.SupplierMobile,
 SupplierTel = entity.SupplierTel,
 SupplierBank = entity.SupplierBank,
 SupplierBankNo = entity.SupplierBankNo,
 PurchaseUnitId = entity.PurchaseUnitId,
 PurchaseUnit = entity.PurchaseUnit,
 Purchaser = entity.Purchaser,
 PurchaseArea = entity.PurchaseArea,
 PurchaseAddress = entity.PurchaseAddress,
 PurchaseTel = entity.PurchaseTel,
 PurchaseMobile = entity.PurchaseMobile,
 ContractMainId = entity.ContractMainId,
 ContractMainName = entity.ContractMainName,
 Freight = entity.Freight,
 FreightExplain = entity.FreightExplain,
 SalesExplain = entity.SalesExplain,
 Clause = entity.Clause,
 CreateUserId = entity.CreateUserId,
 RegTime = entity.RegTime,
 IsCurrent = entity.IsCurrent,


 OrderId = model.OrderId,
 MallId = model.MallId,
 WarrantyPeriod = model.WarrantyPeriod,
 Name = model.Name,
 Amount = model.Amount,
 SupplierId = model.SupplierId,
 SupplierName = model.SupplierName,
 SupplierCode = model.SupplierCode,
 SupplierUserLink = model.SupplierUserLink,
 SupplierMobile = model.SupplierMobile,
 SupplierTel = model.SupplierTel,
 SupplierBank = model.SupplierBank,
 SupplierBankNo = model.SupplierBankNo,
 PurchaseUnitId = model.PurchaseUnitId,
 PurchaseUnit = model.PurchaseUnit,
 Purchaser = model.Purchaser,
 PurchaseArea = model.PurchaseArea,
 PurchaseAddress = model.PurchaseAddress,
 PurchaseTel = model.PurchaseTel,
 PurchaseMobile = model.PurchaseMobile,
 ContractMainId = model.ContractMainId,
 ContractMainName = model.ContractMainName,
 Freight = model.Freight,
 FreightExplain = model.FreightExplain,
 SalesExplain = model.SalesExplain,
 Clause = model.Clause,
 CreateUserId = model.CreateUserId,
 RegTime = model.RegTime,
 IsCurrent = model.IsCurrent,


 temp.OrderId = model.OrderId,
 temp.MallId = model.MallId,
 temp.WarrantyPeriod = model.WarrantyPeriod,
 temp.Name = model.Name,
 temp.Amount = model.Amount,
 temp.SupplierId = model.SupplierId,
 temp.SupplierName = model.SupplierName,
 temp.SupplierCode = model.SupplierCode,
 temp.SupplierUserLink = model.SupplierUserLink,
 temp.SupplierMobile = model.SupplierMobile,
 temp.SupplierTel = model.SupplierTel,
 temp.SupplierBank = model.SupplierBank,
 temp.SupplierBankNo = model.SupplierBankNo,
 temp.PurchaseUnitId = model.PurchaseUnitId,
 temp.PurchaseUnit = model.PurchaseUnit,
 temp.Purchaser = model.Purchaser,
 temp.PurchaseArea = model.PurchaseArea,
 temp.PurchaseAddress = model.PurchaseAddress,
 temp.PurchaseTel = model.PurchaseTel,
 temp.PurchaseMobile = model.PurchaseMobile,
 temp.ContractMainId = model.ContractMainId,
 temp.ContractMainName = model.ContractMainName,
 temp.Freight = model.Freight,
 temp.FreightExplain = model.FreightExplain,
 temp.SalesExplain = model.SalesExplain,
 temp.Clause = model.Clause,
 temp.CreateUserId = model.CreateUserId,
 temp.RegTime = model.RegTime,
 temp.IsCurrent = model.IsCurrent,

 ContractId = item.ContractId,
 OrderId = item.OrderId,
 MallId = item.MallId,
 WarrantyPeriod = item.WarrantyPeriod,
 Name = item.Name,
 Amount = item.Amount,
 SupplierId = item.SupplierId,
 SupplierName = item.SupplierName,
 SupplierCode = item.SupplierCode,
 SupplierUserLink = item.SupplierUserLink,
 SupplierMobile = item.SupplierMobile,
 SupplierTel = item.SupplierTel,
 SupplierBank = item.SupplierBank,
 SupplierBankNo = item.SupplierBankNo,
 PurchaseUnitId = item.PurchaseUnitId,
 PurchaseUnit = item.PurchaseUnit,
 Purchaser = item.Purchaser,
 PurchaseArea = item.PurchaseArea,
 PurchaseAddress = item.PurchaseAddress,
 PurchaseTel = item.PurchaseTel,
 PurchaseMobile = item.PurchaseMobile,
 ContractMainId = item.ContractMainId,
 ContractMainName = item.ContractMainName,
 Freight = item.Freight,
 FreightExplain = item.FreightExplain,
 SalesExplain = item.SalesExplain,
 Clause = item.Clause,
 CreateUserId = item.CreateUserId,
 RegTime = item.RegTime,
 IsCurrent = item.IsCurrent,

public class ContractInputModel
{
 [Display(Name = "合同Id")] 
    public long ContractId {get; set; }
    
 [Display(Name = "订单Id")] 
    public long OrderId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "供货期")] 
    public decimal WarrantyPeriod {get; set; }
    
 [Display(Name = "合同名称")] 
    public string Name {get; set; }
    
 [Display(Name = "合同金额")] 
    public decimal Amount {get; set; }
    
 [Display(Name = "供应商单位Id")] 
    public int SupplierId {get; set; }
    
 [Display(Name = "供应商单位名称")] 
    public string SupplierName {get; set; }
    
 [Display(Name = "供应商统一社会信用代码")] 
    public string SupplierCode {get; set; }
    
 [Display(Name = "联系人")] 
    public string SupplierUserLink {get; set; }
    
 [Display(Name = "手机号码")] 
    public string SupplierMobile {get; set; }
    
 [Display(Name = "电话号码")] 
    public string SupplierTel {get; set; }
    
 [Display(Name = "单位开户行")] 
    public string SupplierBank {get; set; }
    
 [Display(Name = "银行账号")] 
    public string SupplierBankNo {get; set; }
    
 [Display(Name = "单位(购买方单位Id)")] 
    public int PurchaseUnitId {get; set; }
    
 [Display(Name = "购买单位名称")] 
    public string PurchaseUnit {get; set; }
    
 [Display(Name = "购买人")] 
    public string Purchaser {get; set; }
    
 [Display(Name = "购买人所在地区")] 
    public int PurchaseArea {get; set; }
    
 [Display(Name = "购买人地址")] 
    public string PurchaseAddress {get; set; }
    
 [Display(Name = "购买人电话")] 
    public string PurchaseTel {get; set; }
    
 [Display(Name = "购买人手机号码")] 
    public string PurchaseMobile {get; set; }
    
 [Display(Name = "合同主体Id")] 
    public int ContractMainId {get; set; }
    
 [Display(Name = "合同主体名称")] 
    public string ContractMainName {get; set; }
    
 [Display(Name = "运输费")] 
    public decimal Freight {get; set; }
    
 [Display(Name = "运输费说明")] 
    public string FreightExplain {get; set; }
    
 [Display(Name = "销售区域说明")] 
    public string SalesExplain {get; set; }
    
 [Display(Name = "合同条款")] 
    public string Clause {get; set; }
    
 [Display(Name = "创建人")] 
    public long CreateUserId {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "是否有效(1：有效  0：无效)")] 
    public bool IsCurrent {get; set; }
    
 }
 
 public class ContractViewModel
 {
    /// <summary>
    /// 合同Id
    /// </summary>
    public long ContractId {get; set; }
    
    /// <summary>
    /// 订单Id
    /// </summary>
    public long OrderId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 供货期
    /// </summary>
    public decimal WarrantyPeriod {get; set; }
    
    /// <summary>
    /// 合同名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal Amount {get; set; }
    
    /// <summary>
    /// 供应商单位Id
    /// </summary>
    public int SupplierId {get; set; }
    
    /// <summary>
    /// 供应商单位名称
    /// </summary>
    public string SupplierName {get; set; }
    
    /// <summary>
    /// 供应商统一社会信用代码
    /// </summary>
    public string SupplierCode {get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string SupplierUserLink {get; set; }
    
    /// <summary>
    /// 手机号码
    /// </summary>
    public string SupplierMobile {get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    public string SupplierTel {get; set; }
    
    /// <summary>
    /// 单位开户行
    /// </summary>
    public string SupplierBank {get; set; }
    
    /// <summary>
    /// 银行账号
    /// </summary>
    public string SupplierBankNo {get; set; }
    
    /// <summary>
    /// 单位(购买方单位Id)
    /// </summary>
    public int PurchaseUnitId {get; set; }
    
    /// <summary>
    /// 购买单位名称
    /// </summary>
    public string PurchaseUnit {get; set; }
    
    /// <summary>
    /// 购买人
    /// </summary>
    public string Purchaser {get; set; }
    
    /// <summary>
    /// 购买人所在地区
    /// </summary>
    public int? PurchaseArea {get; set; }
    
    /// <summary>
    /// 购买人地址
    /// </summary>
    public string PurchaseAddress {get; set; }
    
    /// <summary>
    /// 购买人电话
    /// </summary>
    public string PurchaseTel {get; set; }
    
    /// <summary>
    /// 购买人手机号码
    /// </summary>
    public string PurchaseMobile {get; set; }
    
    /// <summary>
    /// 合同主体Id
    /// </summary>
    public int ContractMainId {get; set; }
    
    /// <summary>
    /// 合同主体名称
    /// </summary>
    public string ContractMainName {get; set; }
    
    /// <summary>
    /// 运输费
    /// </summary>
    public decimal Freight {get; set; }
    
    /// <summary>
    /// 运输费说明
    /// </summary>
    public string FreightExplain {get; set; }
    
    /// <summary>
    /// 销售区域说明
    /// </summary>
    public string SalesExplain {get; set; }
    
    /// <summary>
    /// 合同条款
    /// </summary>
    public string Clause {get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public long CreateUserId {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 是否有效(1：有效  0：无效)
    /// </summary>
    public bool IsCurrent {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.WarrantyPeriod, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.WarrantyPeriod, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供货期" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Amount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Amount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同金额" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商单位名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商统一社会信用代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierUserLink, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierUserLink, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierMobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierMobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierTel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierTel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入电话号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierBank, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierBank, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位开户行" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SupplierBankNo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SupplierBankNo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入银行账号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PurchaseUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PurchaseUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位(购买方单位Id)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PurchaseUnit, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PurchaseUnit, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入购买单位名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Purchaser, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Purchaser, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入购买人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PurchaseArea, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PurchaseArea, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入购买人所在地区" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PurchaseAddress, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PurchaseAddress, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入购买人地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PurchaseTel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PurchaseTel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入购买人电话" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PurchaseMobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PurchaseMobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入购买人手机号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractMainId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractMainId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同主体Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractMainName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractMainName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同主体名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Freight, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Freight, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运输费" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FreightExplain, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FreightExplain, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运输费说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SalesExplain, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SalesExplain, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入销售区域说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Clause, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Clause, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同条款" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CreateUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CreateUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsCurrent, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsCurrent, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否有效(1：有效  0：无效)" } })                    
                </div>
           </div>
  




 { field: 'OrderId', title: '订单Id', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'WarrantyPeriod', title: '供货期', sortable: true },
                 
 { field: 'Name', title: '合同名称', sortable: true },
                 
 { field: 'Amount', title: '合同金额', sortable: true },
                 
 { field: 'SupplierId', title: '供应商单位Id', sortable: true },
                 
 { field: 'SupplierName', title: '供应商单位名称', sortable: true },
                 
 { field: 'SupplierCode', title: '供应商统一社会信用代码', sortable: true },
                 
 { field: 'SupplierUserLink', title: '联系人', sortable: true },
                 
 { field: 'SupplierMobile', title: '手机号码', sortable: true },
                 
 { field: 'SupplierTel', title: '电话号码', sortable: true },
                 
 { field: 'SupplierBank', title: '单位开户行', sortable: true },
                 
 { field: 'SupplierBankNo', title: '银行账号', sortable: true },
                 
 { field: 'PurchaseUnitId', title: '单位(购买方单位Id)', sortable: true },
                 
 { field: 'PurchaseUnit', title: '购买单位名称', sortable: true },
                 
 { field: 'Purchaser', title: '购买人', sortable: true },
                 
 { field: 'PurchaseArea', title: '购买人所在地区', sortable: true },
                 
 { field: 'PurchaseAddress', title: '购买人地址', sortable: true },
                 
 { field: 'PurchaseTel', title: '购买人电话', sortable: true },
                 
 { field: 'PurchaseMobile', title: '购买人手机号码', sortable: true },
                 
 { field: 'ContractMainId', title: '合同主体Id', sortable: true },
                 
 { field: 'ContractMainName', title: '合同主体名称', sortable: true },
                 
 { field: 'Freight', title: '运输费', sortable: true },
                 
 { field: 'FreightExplain', title: '运输费说明', sortable: true },
                 
 { field: 'SalesExplain', title: '销售区域说明', sortable: true },
                 
 { field: 'Clause', title: '合同条款', sortable: true },
                 
 { field: 'CreateUserId', title: '创建人', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
 { field: 'IsCurrent', title: '是否有效(1：有效  0：无效)', sortable: true },
                 
o.OrderId,                 
o.MallId,                 
o.WarrantyPeriod,                 
o.Name,                 
o.Amount,                 
o.SupplierId,                 
o.SupplierName,                 
o.SupplierCode,                 
o.SupplierUserLink,                 
o.SupplierMobile,                 
o.SupplierTel,                 
o.SupplierBank,                 
o.SupplierBankNo,                 
o.PurchaseUnitId,                 
o.PurchaseUnit,                 
o.Purchaser,                 
o.PurchaseArea,                 
o.PurchaseAddress,                 
o.PurchaseTel,                 
o.PurchaseMobile,                 
o.ContractMainId,                 
o.ContractMainName,                 
o.Freight,                 
o.FreightExplain,                 
o.SalesExplain,                 
o.Clause,                 
o.CreateUserId,                 
o.RegTime,                 
o.IsCurrent,                 
        
        $('#OrderId').val(d.data.rows.OrderId);          
        $('#MallId').val(d.data.rows.MallId);          
        $('#WarrantyPeriod').val(d.data.rows.WarrantyPeriod);          
        $('#Name').val(d.data.rows.Name);          
        $('#Amount').val(d.data.rows.Amount);          
        $('#SupplierId').val(d.data.rows.SupplierId);          
        $('#SupplierName').val(d.data.rows.SupplierName);          
        $('#SupplierCode').val(d.data.rows.SupplierCode);          
        $('#SupplierUserLink').val(d.data.rows.SupplierUserLink);          
        $('#SupplierMobile').val(d.data.rows.SupplierMobile);          
        $('#SupplierTel').val(d.data.rows.SupplierTel);          
        $('#SupplierBank').val(d.data.rows.SupplierBank);          
        $('#SupplierBankNo').val(d.data.rows.SupplierBankNo);          
        $('#PurchaseUnitId').val(d.data.rows.PurchaseUnitId);          
        $('#PurchaseUnit').val(d.data.rows.PurchaseUnit);          
        $('#Purchaser').val(d.data.rows.Purchaser);          
        $('#PurchaseArea').val(d.data.rows.PurchaseArea);          
        $('#PurchaseAddress').val(d.data.rows.PurchaseAddress);          
        $('#PurchaseTel').val(d.data.rows.PurchaseTel);          
        $('#PurchaseMobile').val(d.data.rows.PurchaseMobile);          
        $('#ContractMainId').val(d.data.rows.ContractMainId);          
        $('#ContractMainName').val(d.data.rows.ContractMainName);          
        $('#Freight').val(d.data.rows.Freight);          
        $('#FreightExplain').val(d.data.rows.FreightExplain);          
        $('#SalesExplain').val(d.data.rows.SalesExplain);          
        $('#Clause').val(d.data.rows.Clause);          
        $('#CreateUserId').val(d.data.rows.CreateUserId);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#IsCurrent').val(d.data.rows.IsCurrent);          

 $('#th_OrderId').html(' 订单Id');               
 $('#th_MallId').html(' 商城Id');               
 $('#th_WarrantyPeriod').html(' 供货期');               
 $('#th_Name').html(' 合同名称');               
 $('#th_Amount').html(' 合同金额');               
 $('#th_SupplierId').html(' 供应商单位Id');               
 $('#th_SupplierName').html(' 供应商单位名称');               
 $('#th_SupplierCode').html(' 供应商统一社会信用代码');               
 $('#th_SupplierUserLink').html(' 联系人');               
 $('#th_SupplierMobile').html(' 手机号码');               
 $('#th_SupplierTel').html(' 电话号码');               
 $('#th_SupplierBank').html(' 单位开户行');               
 $('#th_SupplierBankNo').html(' 银行账号');               
 $('#th_PurchaseUnitId').html(' 单位(购买方单位Id)');               
 $('#th_PurchaseUnit').html(' 购买单位名称');               
 $('#th_Purchaser').html(' 购买人');               
 $('#th_PurchaseArea').html(' 购买人所在地区');               
 $('#th_PurchaseAddress').html(' 购买人地址');               
 $('#th_PurchaseTel').html(' 购买人电话');               
 $('#th_PurchaseMobile').html(' 购买人手机号码');               
 $('#th_ContractMainId').html(' 合同主体Id');               
 $('#th_ContractMainName').html(' 合同主体名称');               
 $('#th_Freight').html(' 运输费');               
 $('#th_FreightExplain').html(' 运输费说明');               
 $('#th_SalesExplain').html(' 销售区域说明');               
 $('#th_Clause').html(' 合同条款');               
 $('#th_CreateUserId').html(' 创建人');               
 $('#th_RegTime').html(' 创建时间');               
 $('#th_IsCurrent').html(' 是否有效(1：有效  0：无效)');               
 
 $('#tr_OrderId').hide();               
 $('#tr_MallId').hide();               
 $('#tr_WarrantyPeriod').hide();               
 $('#tr_Name').hide();               
 $('#tr_Amount').hide();               
 $('#tr_SupplierId').hide();               
 $('#tr_SupplierName').hide();               
 $('#tr_SupplierCode').hide();               
 $('#tr_SupplierUserLink').hide();               
 $('#tr_SupplierMobile').hide();               
 $('#tr_SupplierTel').hide();               
 $('#tr_SupplierBank').hide();               
 $('#tr_SupplierBankNo').hide();               
 $('#tr_PurchaseUnitId').hide();               
 $('#tr_PurchaseUnit').hide();               
 $('#tr_Purchaser').hide();               
 $('#tr_PurchaseArea').hide();               
 $('#tr_PurchaseAddress').hide();               
 $('#tr_PurchaseTel').hide();               
 $('#tr_PurchaseMobile').hide();               
 $('#tr_ContractMainId').hide();               
 $('#tr_ContractMainName').hide();               
 $('#tr_Freight').hide();               
 $('#tr_FreightExplain').hide();               
 $('#tr_SalesExplain').hide();               
 $('#tr_Clause').hide();               
 $('#tr_CreateUserId').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_IsCurrent').hide();               

 , "OrderId" : orderId
 , "MallId" : mallId
 , "WarrantyPeriod" : warrantyPeriod
 , "Name" : name
 , "Amount" : amount
 , "SupplierId" : supplierId
 , "SupplierName" : supplierName
 , "SupplierCode" : supplierCode
 , "SupplierUserLink" : supplierUserLink
 , "SupplierMobile" : supplierMobile
 , "SupplierTel" : supplierTel
 , "SupplierBank" : supplierBank
 , "SupplierBankNo" : supplierBankNo
 , "PurchaseUnitId" : purchaseUnitId
 , "PurchaseUnit" : purchaseUnit
 , "Purchaser" : purchaser
 , "PurchaseArea" : purchaseArea
 , "PurchaseAddress" : purchaseAddress
 , "PurchaseTel" : purchaseTel
 , "PurchaseMobile" : purchaseMobile
 , "ContractMainId" : contractMainId
 , "ContractMainName" : contractMainName
 , "Freight" : freight
 , "FreightExplain" : freightExplain
 , "SalesExplain" : salesExplain
 , "Clause" : clause
 , "CreateUserId" : createUserId
 , "RegTime" : regTime
 , "IsCurrent" : isCurrent

 var orderId = $('#o_OrderId').val();
 var mallId = $('#o_MallId').val();
 var warrantyPeriod = $('#o_WarrantyPeriod').val();
 var name = $('#o_Name').val();
 var amount = $('#o_Amount').val();
 var supplierId = $('#o_SupplierId').val();
 var supplierName = $('#o_SupplierName').val();
 var supplierCode = $('#o_SupplierCode').val();
 var supplierUserLink = $('#o_SupplierUserLink').val();
 var supplierMobile = $('#o_SupplierMobile').val();
 var supplierTel = $('#o_SupplierTel').val();
 var supplierBank = $('#o_SupplierBank').val();
 var supplierBankNo = $('#o_SupplierBankNo').val();
 var purchaseUnitId = $('#o_PurchaseUnitId').val();
 var purchaseUnit = $('#o_PurchaseUnit').val();
 var purchaser = $('#o_Purchaser').val();
 var purchaseArea = $('#o_PurchaseArea').val();
 var purchaseAddress = $('#o_PurchaseAddress').val();
 var purchaseTel = $('#o_PurchaseTel').val();
 var purchaseMobile = $('#o_PurchaseMobile').val();
 var contractMainId = $('#o_ContractMainId').val();
 var contractMainName = $('#o_ContractMainName').val();
 var freight = $('#o_Freight').val();
 var freightExplain = $('#o_FreightExplain').val();
 var salesExplain = $('#o_SalesExplain').val();
 var clause = $('#o_Clause').val();
 var createUserId = $('#o_CreateUserId').val();
 var regTime = $('#o_RegTime').val();
 var isCurrent = $('#o_IsCurrent').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单Id' : '产品名称', d.data.rows.OrderId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供货期' : '产品名称', d.data.rows.WarrantyPeriod);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同金额' : '产品名称', d.data.rows.Amount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商单位Id' : '产品名称', d.data.rows.SupplierId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商单位名称' : '产品名称', d.data.rows.SupplierName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商统一社会信用代码' : '产品名称', d.data.rows.SupplierCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系人' : '产品名称', d.data.rows.SupplierUserLink);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机号码' : '产品名称', d.data.rows.SupplierMobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '电话号码' : '产品名称', d.data.rows.SupplierTel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位开户行' : '产品名称', d.data.rows.SupplierBank);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '银行账号' : '产品名称', d.data.rows.SupplierBankNo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位(购买方单位Id)' : '产品名称', d.data.rows.PurchaseUnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '购买单位名称' : '产品名称', d.data.rows.PurchaseUnit);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '购买人' : '产品名称', d.data.rows.Purchaser);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '购买人所在地区' : '产品名称', d.data.rows.PurchaseArea);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '购买人地址' : '产品名称', d.data.rows.PurchaseAddress);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '购买人电话' : '产品名称', d.data.rows.PurchaseTel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '购买人手机号码' : '产品名称', d.data.rows.PurchaseMobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同主体Id' : '产品名称', d.data.rows.ContractMainId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同主体名称' : '产品名称', d.data.rows.ContractMainName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运输费' : '产品名称', d.data.rows.Freight);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运输费说明' : '产品名称', d.data.rows.FreightExplain);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '销售区域说明' : '产品名称', d.data.rows.SalesExplain);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同条款' : '产品名称', d.data.rows.Clause);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建人' : '产品名称', d.data.rows.CreateUserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否有效(1：有效  0：无效)' : '产品名称', d.data.rows.IsCurrent);



