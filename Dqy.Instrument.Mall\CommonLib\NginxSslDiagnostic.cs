using System;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Instrument.Mall.CommonLib
{
    /// <summary>
    /// 针对nginx代理环境的SSL诊断工具
    /// 用于诊断和解决通过nginx中转的HTTPS API连接问题
    /// </summary>
    public static class NginxSslDiagnostic
    {
        /// <summary>
        /// 诊断指定URL的SSL连接问题
        /// </summary>
        /// <param name="url">要诊断的URL，如：https://jzzxmall.czedu.cn</param>
        /// <returns>诊断结果</returns>
        public static async Task<string> DiagnoseUrl(string url)
        {
            var result = new StringBuilder();
            result.AppendLine($"=== SSL连接诊断报告 ===");
            result.AppendLine($"目标URL: {url}");
            result.AppendLine($"诊断时间: {DateTime.Now}");
            result.AppendLine();

            try
            {
                var uri = new Uri(url);
                
                // 1. DNS解析测试
                result.AppendLine("1. DNS解析测试:");
                await TestDnsResolution(uri.Host, result);
                result.AppendLine();

                // 2. 端口连通性测试
                result.AppendLine("2. 端口连通性测试:");
                await TestPortConnectivity(uri.Host, uri.Port == -1 ? 443 : uri.Port, result);
                result.AppendLine();

                // 3. SSL握手测试
                result.AppendLine("3. SSL握手测试:");
                await TestSslHandshake(uri.Host, uri.Port == -1 ? 443 : uri.Port, result);
                result.AppendLine();

                // 4. 证书信息检查
                result.AppendLine("4. 证书信息检查:");
                await TestCertificateInfo(uri.Host, uri.Port == -1 ? 443 : uri.Port, result);
                result.AppendLine();

                // 5. HTTP请求测试
                result.AppendLine("5. HTTP请求测试:");
                await TestHttpRequest(url, result);
                result.AppendLine();

            }
            catch (Exception ex)
            {
                result.AppendLine($"诊断过程中发生错误: {ex.Message}");
            }

            return result.ToString();
        }

        /// <summary>
        /// 测试DNS解析
        /// </summary>
        private static async Task TestDnsResolution(string hostname, StringBuilder result)
        {
            try
            {
                var addresses = await Dns.GetHostAddressesAsync(hostname);
                result.AppendLine($"   DNS解析成功，找到 {addresses.Length} 个IP地址:");
                foreach (var addr in addresses)
                {
                    result.AppendLine($"   - {addr}");
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"   DNS解析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试端口连通性
        /// </summary>
        private static async Task TestPortConnectivity(string hostname, int port, StringBuilder result)
        {
            try
            {
                using (var client = new TcpClient())
                {
                    var connectTask = client.ConnectAsync(hostname, port);
                    var timeoutTask = Task.Delay(5000); // 5秒超时

                    var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                    
                    if (completedTask == connectTask && client.Connected)
                    {
                        result.AppendLine($"   端口 {port} 连接成功");
                    }
                    else
                    {
                        result.AppendLine($"   端口 {port} 连接失败或超时");
                    }
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"   端口连接测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试SSL握手
        /// </summary>
        private static async Task TestSslHandshake(string hostname, int port, StringBuilder result)
        {
            try
            {
                using (var client = new TcpClient())
                {
                    await client.ConnectAsync(hostname, port);
                    
                    using (var sslStream = new SslStream(client.GetStream(), false, ValidateServerCertificate))
                    {
                        await sslStream.AuthenticateAsClientAsync(hostname);
                        
                        result.AppendLine($"   SSL握手成功");
                        result.AppendLine($"   SSL协议: {sslStream.SslProtocol}");
                        result.AppendLine($"   加密算法: {sslStream.CipherAlgorithm}");
                        result.AppendLine($"   哈希算法: {sslStream.HashAlgorithm}");
                        result.AppendLine($"   密钥交换算法: {sslStream.KeyExchangeAlgorithm}");
                    }
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"   SSL握手失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试证书信息
        /// </summary>
        private static async Task TestCertificateInfo(string hostname, int port, StringBuilder result)
        {
            try
            {
                using (var client = new TcpClient())
                {
                    await client.ConnectAsync(hostname, port);
                    
                    using (var sslStream = new SslStream(client.GetStream(), false, ValidateServerCertificate))
                    {
                        await sslStream.AuthenticateAsClientAsync(hostname);
                        
                        var cert = sslStream.RemoteCertificate as X509Certificate2;
                        if (cert != null)
                        {
                            result.AppendLine($"   证书主题: {cert.Subject}");
                            result.AppendLine($"   证书颁发者: {cert.Issuer}");
                            result.AppendLine($"   有效期: {cert.NotBefore} 到 {cert.NotAfter}");
                            result.AppendLine($"   序列号: {cert.SerialNumber}");
                            result.AppendLine($"   指纹: {cert.Thumbprint}");
                            
                            // 检查证书是否过期
                            if (DateTime.Now < cert.NotBefore || DateTime.Now > cert.NotAfter)
                            {
                                result.AppendLine($"   ⚠️ 警告: 证书已过期或尚未生效");
                            }
                            else
                            {
                                result.AppendLine($"   ✓ 证书在有效期内");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"   证书信息获取失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试HTTP请求
        /// </summary>
        private static async Task TestHttpRequest(string url, StringBuilder result)
        {
            try
            {
                // 配置SSL
                SslHelper.ConfigureSslProtocols();
                
                var request = WebRequest.Create(url) as HttpWebRequest;
                request.Method = "GET";
                request.Timeout = 10000; // 10秒超时
                request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
                
                using (var response = await request.GetResponseAsync() as HttpWebResponse)
                {
                    result.AppendLine($"   HTTP请求成功");
                    result.AppendLine($"   状态码: {response.StatusCode}");
                    result.AppendLine($"   服务器: {response.Server}");
                    result.AppendLine($"   内容类型: {response.ContentType}");
                    result.AppendLine($"   内容长度: {response.ContentLength}");
                }
            }
            catch (WebException webEx)
            {
                result.AppendLine($"   HTTP请求失败: {webEx.Message}");
                if (webEx.Response is HttpWebResponse errorResponse)
                {
                    result.AppendLine($"   错误状态码: {errorResponse.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"   HTTP请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 证书验证回调（用于诊断）
        /// </summary>
        private static bool ValidateServerCertificate(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            // 在诊断模式下，我们接受所有证书以便获取详细信息
            return true;
        }

        /// <summary>
        /// 获取针对nginx代理的推荐配置
        /// </summary>
        /// <returns></returns>
        public static string GetNginxProxyRecommendations()
        {
            var recommendations = new StringBuilder();
            recommendations.AppendLine("=== 针对nginx代理的推荐配置 ===");
            recommendations.AppendLine();
            
            recommendations.AppendLine("1. 客户端配置 (.NET应用):");
            recommendations.AppendLine("   - 确保已调用 ApplicationStartup.Initialize()");
            recommendations.AppendLine("   - 使用 TLS 1.2 协议");
            recommendations.AppendLine("   - 禁用 Expect100Continue");
            recommendations.AppendLine("   - 设置合适的超时时间");
            recommendations.AppendLine();
            
            recommendations.AppendLine("2. nginx配置建议:");
            recommendations.AppendLine("   proxy_ssl_protocols TLSv1.2 TLSv1.3;");
            recommendations.AppendLine("   proxy_ssl_ciphers HIGH:!aNULL:!MD5;");
            recommendations.AppendLine("   proxy_ssl_verify off;  # 如果后端是内网服务");
            recommendations.AppendLine("   proxy_connect_timeout 30s;");
            recommendations.AppendLine("   proxy_send_timeout 30s;");
            recommendations.AppendLine("   proxy_read_timeout 30s;");
            recommendations.AppendLine();
            
            recommendations.AppendLine("3. Windows Server 2016特殊配置:");
            recommendations.AppendLine("   - 确保安装了最新的Windows更新");
            recommendations.AppendLine("   - 在注册表中启用TLS 1.2支持");
            recommendations.AppendLine("   - 检查防火墙和网络策略");
            
            return recommendations.ToString();
        }
    }
}
