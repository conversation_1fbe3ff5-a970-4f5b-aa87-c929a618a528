﻿ AttachPath = entity.AttachPath,
 RegTime = entity.RegTime,


 AttachPath = model.AttachPath,
 RegTime = model.RegTime,


 temp.AttachPath = model.AttachPath,
 temp.RegTime = model.RegTime,

 AttachmentTempId = item.AttachmentTempId,
 AttachPath = item.AttachPath,
 RegTime = item.RegTime,

public class AttachmentTempInputModel
{
 [Display(Name = "Id")] 
    public long AttachmentTempId {get; set; }
    
 [Display(Name = "图片路径")] 
    public string AttachPath {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class AttachmentTempViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long AttachmentTempId {get; set; }
    
    /// <summary>
    /// 图片路径
    /// </summary>
    public string AttachPath {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.AttachPath, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AttachPath, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
  




 { field: 'AttachPath', title: '图片路径', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
o.AttachPath,                 
o.RegTime,                 
        
        $('#AttachPath').val(d.data.rows.AttachPath);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_AttachPath').html(' 图片路径');               
 $('#th_RegTime').html(' 创建时间');               
 
 $('#tr_AttachPath').hide();               
 $('#tr_RegTime').hide();               

 , "AttachPath" : attachPath
 , "RegTime" : regTime

 var attachPath = $('#o_AttachPath').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片路径' : '产品名称', d.data.rows.AttachPath);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);



