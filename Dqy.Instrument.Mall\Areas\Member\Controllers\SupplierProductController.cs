﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Dqy.TrainManage.Base.Util;
using Org.BouncyCastle.Crypto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Results;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class SupplierProductController : ControllerMember
    {
        #region 产品录入和保存

        /// <summary>
        /// 产品列表-产品管理列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> ProductList(SearchArgumentsInputModel args)
        {
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }

            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            args.Cid = 1;//类型。1：产品列表
            string url = "supplier/searchproduct";

            QueryResult<ProductShelfViewModel> ret = null;
            try
            {
                ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("获取产品列表“supplier/searchproduct”请求失败！：" + e.Message);
            }

            ViewBag.Args = args;
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }

                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

                if (ret.Entity == null)
                {
                    //return ReturnHome();
                }

            }
            return View(ret);
        }

        /// <summary>
        /// 产品上架列表-产品管理列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> ProductShelf(SearchArgumentsInputModel args)
        {
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }

            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Token = Operater.Token;
            args.Cid = 2;//类型。2：上架产品列表
            string url = "supplier/searchproduct";

            QueryResult<ProductShelfViewModel> ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, args);
            ViewBag.Args = args;
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }

                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

                if (ret.Entity == null)
                {
                    //return ReturnHome();
                }

            }
            return View(ret);
        }

        public async Task<JsonResult> FindProduct(string code)
        {
            ProductInputModel model = new ProductInputModel();

            string url = "supplier/findproduct?code=" + code + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            QueryResult<ProductShelfViewModel> ret = await WebApiHelper.SendAsync<QueryResult<ProductShelfViewModel>>(url, null, CommonJsonSendType.GET);

            if (ret == null || ret.Data == null || ret.Data.Count <= 0)
            {
                ret.flag = -3;
                ret.msg = "没有查询到同类产品";
            }
            return Json(ret, JsonRequestBehavior.AllowGet);
        }
        /// <summary>
        /// lss获取二级，三级分类
        /// </summary>
        /// <param name="id">产品分类Id（下一级的Pid）</param>
        /// <returns>父级为id的产品分类的集合</returns>
        public async Task<JsonResult> GetClassify(int id)
        {

            if (id <= 0)
            {
                //非法操作
                return null;
            }

            string url = "supplier/getinstrument?id=" + id;

            var list = await WebApiHelper.SendAsync<List<SelectListViewModel>>(url, null, CommonJsonSendType.GET);

            return Json(list, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 获取二级，三级分类（搜索分类时）：改用select 暂不用
        /// lss
        /// </summary>
        /// <param name="currpage">当前页数</param>
        /// <param name="limit">获取数量</param>
        /// <param name="name">所有的关键字（搜索的值）</param>
        /// <returns>分类列表集合</returns>
        public async Task<JsonResult> SearchClassify(int currpage, int limit, string name)
        {
            JsonResult result = new JsonResult();
            if (string.IsNullOrEmpty(name))
            {
                return Json(new { }, JsonRequestBehavior.AllowGet);
            }
            SearchArgumentsInputModel args = new SearchArgumentsInputModel();
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Q = name;
            args.Limit = limit;
            args.CurrPage = currpage;//次数 对应页数 
            string url = "supplier/searchclassify";
            var data = await WebApiHelper.SendAsync<QueryResult<ClassifyInputModel>>(url, args);

            return Json(data.Data, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// select2查询 
        /// </summary>
        /// <param name="q"></param>
        /// <param name="s"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetDataList(string q, int s)
        {
            //List<DiseaseSimpleViewModel> list = new List<DiseaseSimpleViewModel>();
            //list = _diseaseApplicationService.GetByKey(q, s);
            JsonResult result = new JsonResult();
            if (string.IsNullOrEmpty(q))
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
            SearchArgumentsInputModel args = new SearchArgumentsInputModel();
            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            args.Q = q;
            args.Limit = s;
            string url = "supplier/searchclassify";
            var data = await WebApiHelper.SendAsync<QueryResult<Select2ViewModel>>(url, args);
            if (data.Data == null || data.Data.Count <= 0)
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
            return Json(data.Data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 根据三级分类设备Id获取对应的学科
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<JsonResult> GetClassifyCourse(int id)
        {
            if (id <= 0)
            {
                //非法操作
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            string url = "supplier/getclassifycourse?id=" + id;
            var data = await WebApiHelper.SendAsync<List<SelectListViewModel>>(url, null, CommonJsonSendType.GET);
            return Json(data, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// lss获取学段(根据产品Id获取相关的学段列表)
        /// </summary>
        /// <param name="id">产品Id</param>
        /// <returns>Json格式的字段列表</returns>

        /// <summary>
        /// 获取规格型号
        /// </summary>
        /// <param name="id">产品分类逻辑课Id</param>
        /// <returns></returns>
        public async Task<JsonResult> GetModelList(int id)
        {
            if (id <= 0)
            {
                return Json(null, JsonRequestBehavior.AllowGet);
            }

            string url = "supplier/getmodellist" + "?id=" + id;
            var result = await WebApiHelper.SendAsync<QueryResult<InstrumentLogicStandardInputModel>>(url, null, CommonJsonSendType.GET);
            if (result.flag > 0 && result.Data != null && result.Data.Count > 0)
            {
                return Json(result.Data, JsonRequestBehavior.AllowGet);
            }
            return Json(null, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// lss添加和修改产品（获取添加，修改页面的Model）
        /// </summary>
        /// <param name="id"></param>
        /// <param name="type"> 编辑类型：0为修改  1：为查找同类。</param>
        /// <returns></returns>
        public async Task<ActionResult> ProductEdit(int id = 0, int type = 0, string url = "")
        {
            ProductInputModel model = new ProductInputModel();
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;

            string s_url = "supplier/getproduct" + "?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<ProductInputModel>>(s_url, null, CommonJsonSendType.GET);

            if (ret.flag == -1)
            {
                return ApiTimeOut();
            }
            else if (ret.flag == -2)
            {
                return ReturnHome();
            }
            else if (ret.flag == 0)
            {
                return View("../Error/Index");
            }

            if (ret.Entity == null)
            {
                // return ReturnHome();
            }

            string QualificationFile = "0";
            s_url = "configset/getconfigset2" + "?moduleCode=9002&typeCode=QualificationFile&unitType=0&unitId=0&configType=0";
            var config = await WebApiHelper.SendAsync<List<ConfigSetViewModel>>(s_url, null, CommonJsonSendType.GET);
            if (config != null)
            {
                QualificationFile = config[0].ConfigValue;
            }
            ViewBag.QualificationFile = QualificationFile;

            model = ret.Entity;

            model.Token = Operater.Token;
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            ViewBag.Url = url;

            if (type == 1)
            {
                model.ProductId = 0;
                model.Type = 1;
            }
            if (model.Price != null && model.Price > 0)
            {
                model.Price = Math.Round((decimal)model.Price, 2);
            }
            return View(model);
        }


        public async Task<ActionResult> ProductPreview(string key)
        {
            try
            {
                var data = Session[key];
                if (data != null)
                {

                    var entity = data as ProductInputModel;
                    if (entity != null)
                    {
                        ProductDetailViewModel model = new ProductDetailViewModel();

                        string url = "product/getproductpreviewdetail?id=" + entity.InstrumentLogicId + "&courses=" + entity.Courses + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
                        var r = await WebApiHelper.SendAsync<QueryResult<ProductDetailViewModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
                        if (r != null && r.Entity != null)
                        {
                            model = r.Entity;
                        }
                        //产品基本信息
                        //detail = _productService.GetProductDetail(productShelfId);
                        //判断为null，直接返回null
                        //if (detail == null) return null;


                        //产品对应规格价格 
                        List<ProductNormPriceViewModel> priceList = new List<ProductNormPriceViewModel>();
                        priceList.Add(new ProductNormPriceViewModel()
                        {
                            Price = entity.Price ?? 0,
                            Stock = (int)entity.Stock,
                            Model = entity.Modelz,
                        });
                        model.ProductNormPriceList = priceList;

                        //产品照片 
                        List<ProductImgViewModel> imgList = new List<ProductImgViewModel>() { };
                        if (entity.ImageList != null && entity.ImageList.Count > 0)
                        {
                            foreach (ProductImgInputModel item in entity.ImageList)
                            {
                                if (!string.IsNullOrEmpty(item.ImgPath))
                                {
                                    imgList.Add(new ProductImgViewModel()
                                    {
                                        ImgPath = item.ImgPath,
                                        IsMainImg = item.IsMainImg
                                    });
                                }
                            }

                        }
                        model.ProductImgList = imgList;
                        //同类产品
                        //detail.SimilarProductList = _productService.GetSimilarProductList(detail.MallId, detail.ThreeGradeLogicId, productShelfId);

                        model.UnitName = entity.UnitName;
                        model.Name = entity.Name;
                        model.Brand = entity.Brand;
                        model.Price = entity.Price ?? 0;
                        model.Manufacturer = entity.Manufacturer;
                        model.ExecutiveStandard = entity.ExecutiveStandard;
                        model.Code = entity.Code;
                        model.WarrantyMonth = entity.WarrantyMonth.ToInt();
                        model.TradeSucessCount = 0;
                        model.UnitId = Operater.UnitId;
                        model.MallId = Operater.CurrentMallId;
                        model.Detail = entity.Detail;
                        if (entity.ProductDetailList != null && entity.ProductDetailList.Count > 0)
                        {
                            model.DetailInfoList = (from item in entity.ProductDetailList
                                                    select new ProductDetailInfoViewModel()
                                                    {
                                                        Sort = item.Sort,
                                                        Explain = item.Explain,
                                                        ImageSrc = item.ImageSrc,
                                                        ImageTitle = item.ImageTitle
                                                    }).ToList();
                        }
                        if (entity.ExperimentImage == null)
                        {
                            model.ExperimentImage = new AttachmentImgModel();
                        }
                        else
                        {
                            model.ExperimentImage = new AttachmentImgModel()
                            {
                                Path = entity.ExperimentImage.Path ?? ""
                            };
                        };

                        List<AttachmentImgModel> qualiFiedList = new List<AttachmentImgModel>();
                        List<AttachmentImgModel> awardList = new List<AttachmentImgModel>();
                        List<AttachmentImgModel> patentList = new List<AttachmentImgModel>();
                        if (entity.QualifiedImageList != null && entity.QualifiedImageList.Count > 0)
                        {
                            foreach (AttachmentImgInputModel item in entity.QualifiedImageList)
                            {
                                if (item.Path != null && item.Path.Length > 0)
                                {
                                    qualiFiedList.Add(new AttachmentImgModel()
                                    {
                                        Path = item.Path
                                    });
                                }
                            }
                        }
                        if (entity.AwardCertificateImageList != null && entity.AwardCertificateImageList.Count > 0)
                        {
                            foreach (AttachmentImgInputModel item in entity.AwardCertificateImageList)
                            {
                                if (item.Path != null && item.Path.Length > 0)
                                {
                                    awardList.Add(new AttachmentImgModel()
                                    {
                                        Path = item.Path
                                    });
                                }
                            }
                        }
                        if (entity.PatentCertificateImageList != null && entity.PatentCertificateImageList.Count > 0)
                        {
                            foreach (AttachmentImgInputModel item in entity.PatentCertificateImageList)
                            {
                                if (item.Path != null && item.Path.Length > 0)
                                {
                                    patentList.Add(new AttachmentImgModel()
                                    {
                                        Path = item.Path
                                    });
                                }
                            }
                        }
                        model.QualifiedImageList = qualiFiedList;//
                        model.AwardCertificateImageList = awardList;//
                        model.PatentCertificateImageList = patentList;//
                        model.ApplicableContent = entity.ApplicableContent;
                        model.ExperimentName = entity.ExperimentName;//实验名称
                        model.GradeId = entity.GradeId;//年级
                        model.TextbookVersion = entity.TextbookVersion;//教材版本
                        model.TextbookChapter = entity.TextbookChapter;//课本章节
                        model.UseDescription = entity.UseDescription;//使用说明

                        if (entity.ProductDetailList != null && entity.ProductDetailList.Count > 0)
                        {
                            var detail = "";
                            for (int i = 0; i < entity.ProductDetailList.Count; i++)
                            {
                                detail += ComLib.GetProductDetailHtml(entity.ProductDetailList[i].ImageSrc, entity.ProductDetailList[i].Explain);
                            }
                            model.Detail = detail;
                        }
                        return View(model);
                    }
                }
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("产品预览" + e.Message);
            }

            return Content("数据已过期，请重新操作。");
        }

        /// <summary>
        /// lss保存（添加、修改产品的保存）
        /// </summary>
        /// <param name="model">保存实体（图片、价格、学段、学科</param>
        ///  <param name="model.type"> type = 2,</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [System.Web.Mvc.HttpPost]
        [ValidateInput(false)]
        public async Task<JsonResult> SaveProduct(ProductInputModel model)
        {
            ReturnResult result = new ReturnResult();
            string errorMsg = "";
            if (string.IsNullOrEmpty(model.Courses))
            {
                errorMsg+= "请选择学科。<br/>"; 
            }
            if (model.Price == null || model.Price <= 0)
            {
                errorMsg += "请填写产品单价。<br/>";
            }
            if (string.IsNullOrEmpty(model.ExecutiveStandard))
            {
                errorMsg += "请填写执行标准。<br/>";
            }
            if (!string.IsNullOrEmpty(errorMsg))
            {
                result.flag = 0;
                result.msg = errorMsg;
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            if (model.ImageList != null && model.ImageList.Count > 0)
            {
                result.flag = 0;
                foreach (var item in model.ImageList)
                {
                    if (!string.IsNullOrEmpty(item.ImgPath))
                    {
                        result.flag = 1;
                        break;
                    }
                }
                //必须有主图
                if (result.flag == 0)
                {
                    result.flag = 0;
                    result.msg = "必须上传图片。";
                    return Json(result, JsonRequestBehavior.AllowGet);
                }

            }

            if (!RegExp.IsSymbol(model.Brand))
            {
                //品牌不允许有符号
                result.flag = 0;
                result.msg = "品牌不允许有符号。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }

            if (model.ProductDetailList==null || model.ProductDetailList.Count == 0)
            {
                result.flag = 0;
                result.msg = "请填写产品详情。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            else
            {
                var templist01 = model.ProductDetailList.Where(m => m.Explain != null && m.Explain.Length > 5000).ToList();
                if (templist01 !=null && templist01 .Count > 0)
                {
                    result.flag = 0;
                    result.msg = "产品详情说明文字长度请控制在5000以内。";
                    return Json(result, JsonRequestBehavior.AllowGet);
                }
            }

            if (!ModelState.IsValid)
            {
                foreach (var item in ModelState.Values)
                {
                    // err.Add(item.Errors);
                    if (item.Errors.Count > 0)
                    {
                        result.msg = item.Errors[0].ErrorMessage;
                        break;
                    }
                }
            }
            else
            {
                //产品上架增加验证,判断是否需要验证合格证明文件
                string surl = "configset/getconfigset2" + "?moduleCode=9002&typeCode=QualificationFile&unitType=0&unitId=0&configType=0";
                var config = await WebApiHelper.SendAsync<List<ConfigSetViewModel>>(surl, null, CommonJsonSendType.GET);
                if (config != null)
                {
                    if (config[0].ConfigValue.Equals("1"))
                    {
                        //验证是否上传了合格证明文件
                        var listImg = model.QualifiedImageList.Where(a => a.Path != "").ToList();
                        if(listImg.Count == 0)
                        {
                            result.flag = 0;
                            result.msg = "“合格证明文件”未上传!。";
                            return Json(result, JsonRequestBehavior.AllowGet);
                        }
                    }
                }


                if (model.Type == 2)
                {
                    string key = DateTime.Now.ToString("yyyyMMddHHmmss");
                    key = SecurityHelper.MD5(SecurityHelper.MD5(key));
                    Session[key] = model;
                    result.data.rows = key;
                    result.flag = 2;
                    return Json(result, JsonRequestBehavior.AllowGet);
                }


                try
                {
                    model.BaseUnitId = Operater.UnitId;
                    model.BaseUserId = Operater.UserId;
                    model.Token = Operater.Token;
                    string url = "supplier/saveproduct";
                    result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                    Log.Supplier(
                        new
                        {
                            User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                            Memo = "保存（添加、修改产品的保存）",
                            Action = "supplier/saveproduct",
                            Param = model,
                            Result = result
                        }, result.flag);
                }
                catch (Exception e)
                {
                    result.flag = 0;
                    result.msg = "保存产品异常，无法保存请联系管理员。";
                    FileLog.SendExceptionLog("保存产品异常：" + e.Message);
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// lss保存（添加、修改产品详情的保存）
        /// </summary>
        /// <param name="model">保存实体（图片、价格、学段、学科</param>
        ///  <param name="model.type"> type = 2,</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [System.Web.Mvc.HttpPost]
        [ValidateInput(false)]
        public async Task<JsonResult> SaveProductDetail(PProductDetailEditInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (!(model.ProductId > 0))
            {
                result.flag = 0;
                result.msg = "只有修改产品才可直接添加详情。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            if (model.Explain != null && model.Explain.Length > 5000)
            {
                result.flag = 0;
                result.msg = "产品详情说明文字长度请控制在5000以内。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            try
            {
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                string url = "supplier/saveproductdetail";
                result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "保存（添加、修改产品详情的保存）",
                        Action = "supplier/saveproductdetail",
                        Param = model,
                        Result = result
                    }, result.flag);
            }
            catch (Exception e)
            {
                result.flag = 0;
                result.msg = "保存产品详情异常，无法保存请联系管理员。";
                FileLog.SendExceptionLog("保存产品异常：" + e.Message);
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// lss保存（添加、修改产品详情的保存）
        /// </summary>
        /// <param name="model">保存实体（图片、价格、学段、学科</param>
        ///  <param name="model.type"> type = 2,</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [System.Web.Mvc.HttpPost]
        [ValidateInput(false)]
        public async Task<JsonResult> DelProductDetail(PProductDetailEditInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (!(model.ProductId > 0))
            {
                result.flag = 0;
                result.msg = "只有修改产品才可直接修改删除详情。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            if (!(model.ProductDetailId > 0))
            {
                result.flag = 0;
                result.msg = "产品详情不存在，请刷新页面重新操作。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            try
            {
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                string url = "supplier/delproductdetail";
                result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "删除产品详情",
                        Action = "supplier/delproductdetail",
                        Param = model,
                        Result = result
                    }, result.flag);
            }
            catch (Exception e)
            {
                result.flag = 0;
                result.msg = "删除产品详情异常，无法保存请联系管理员。";
                FileLog.SendExceptionLog("删除产品详情异常：" + e.Message);
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 视频列表选择
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        public ActionResult ProductVideoList()
        {
            if (Operater == null)
            {
                return ApiTimeOut();
            }
            SearchVideoInputModel args = new SearchVideoInputModel();
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            //string url = Constant.ApiPath + "experiment/searchvedio";
            //var result = await WebApiHelper.SendAsync<QueryResult<TExperimentVideoViewModel>>(url, args);
            //if (result.flag == -1)
            //{
            //    return this.ApiTimeOut();
            //}
            //else if (result.flag == -2)
            //{
            //    return this.ReturnHome();
            //}
            ViewBag.Args = args;
            return View("_ProductVideoList");
        }

        public async Task<JsonResult> QueryVideoList(SearchVideoInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = "experiment/queryvideolist";
            var result = await WebApiHelper.SendAsync<QueryResult<TExperimentVideoViewModel>>(url, args);
            if (result.flag == -1)
            {
                result.flag = 0;
                result.msg = "";
            }
            else if (result.flag == -2)
            {
                result.flag = 0;
                result.msg = "";
            }
            ViewBag.Args = args;
            var data = new { total = result.TotalCount, rows = result.Data, flag = result.flag, msg = result.msg };
            return Json(data, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 产品管理- 单个上架 -- 多个上架 --删除 -- 调整库存 -- 标注核心产品


        /// <summary>
        /// 验证上架
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<JsonResult> CheckBatchShelf(string ids)
        {
            ReturnResult r = new ReturnResult();
            r.flag = 1;
            r.msg = "";

            //产品上架增加验证,判断是否需要验证合格证明文件
            string surl = "configset/getconfigset2" + "?moduleCode=9002&typeCode=QualificationFile&unitType=0&unitId=0&configType=0";
            var config = await WebApiHelper.SendAsync<List<ConfigSetViewModel>>(surl, null, CommonJsonSendType.GET);
            if (config != null && config.Count > 0)
            {
                if (config[0].ConfigValue.Equals("1"))
                {
                    //查询合格证明文件是否存在
                    surl = WebApiHelper.GetRequest("attachment/checkproductfile", "token=" + Operater.Token + "&ids=" + ids + "&fileCategory=102&unitId=" + Operater.UnitId + "");
                    if (surl == "null")
                    {
                        r.flag = 0;
                        r.msg = $"非法操作";
                        return Json(r, JsonRequestBehavior.AllowGet);
                    }
                    List<string> listProduct = ComLib.JSON2Object<List<string>>(surl);
                    if (listProduct.Count > 0)
                    {
                        r.flag = 0;
                        r.msg = $"产品【{string.Join("、",listProduct)}】未上传“合格证明文件”不能上架";
                        return Json(r, JsonRequestBehavior.AllowGet);
                    }
                }
            }

            return Json(r, JsonRequestBehavior.AllowGet);
        }


        public async Task<ActionResult> SignleShelf(int id, string url)
        {
            if (id < 0)
            {
                return Content("非法操作");
            }
            try
            {
                
                string surl = "supplier/getproductshelf?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
                var ret = await WebApiHelper.SendAsync<QueryResult<SignleShelfInputModel>>(surl, null, CommonJsonSendType.GET);
                if (ret.flag <= 0)
                {
                    if (ret.flag == -1)
                    {
                        return ApiTimeOut();
                    }

                }
                ViewBag.Url = url;
                return View(ret.Entity);
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("产品上架异常：" + e.Message);
                return Content("产品上架异常，暂产品无法上架请联系管理员。");
            }
        }

        public async Task<JsonResult> SaveSignleShelf(SignleShelfInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (model == null)
            {
                //异常
                result.flag = 0;
                result.msg = "非法操作。";
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            else
            {
                if (!ModelState.IsValid)
                {
                    foreach (var item in ModelState.Values)
                    {
                        // err.Add(item.Errors);
                        if (item.Errors.Count > 0)
                        {
                            result.flag = 0;
                            result.msg = item.Errors[0].ErrorMessage;
                            break;
                        }
                    }
                }
                else
                {
                    try
                    {
                        model.BaseUnitId = Operater.UnitId;
                        model.BaseUserId = Operater.UserId;
                        model.Token = Operater.Token;
                        string url = "supplier/saveproductshelf";
                        result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                        Log.Supplier(
                       new
                       {
                           User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                           Memo = "单个产品上架",
                           Action = "supplier/saveproductshelf",
                           Param = model,
                           Result = result
                       }, result.flag);
                    }
                    catch (Exception e)
                    {
                        FileLog.SendExceptionLog("产品上架保存异常：" + e.Message);
                        result.flag = -3;
                        result.msg = "产品上架异常，暂无法上架请联系管理员处理。";
                    }


                }
            }

            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 批量上架
        /// </summary>
        /// <param name="ids">产品上架Id</param>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<ActionResult> BatchShelf(string ids, string url)
        {
            if (string.IsNullOrEmpty(ids))
            {
                return Content("非法操作");
            }

            string surl = "supplier/getbatchshelf?ids=" + ids + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<BatchShelfInputModel>>(surl, null, CommonJsonSendType.GET);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }

            ViewBag.Url = url;
            return View(ret.Entity);
        }

        /// <summary>
        /// 批量产品上架-保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveBatchShelf(BatchShelfInputModel model)
        {
            ReturnResult r = new ReturnResult();
            if (model.MallId <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作。";
                return Json(r, JsonRequestBehavior.AllowGet);
            }

            if (model.Ps == null || model.Ps.Count <= 0)
            {
                r.flag = 0;
                r.msg = "非法操作。";
                return Json(r, JsonRequestBehavior.AllowGet);
            }

            try
            {
                var tempList = model.Ps.Where(m => m.id > 0).ToList();
                if (tempList != null && tempList.Count > 0)
                {
                    foreach (var item in tempList)
                    {
                        if (item.P < 0 || item.P > 10000000 || item.WM.ToInt() < 0 || item.WM.ToInt() > 100)
                        {
                            r.flag = -3;
                            r.msg = "你输入的价格或者质保期不符合要求。";
                            return Json(r, JsonRequestBehavior.AllowGet);
                        }

                    }
                    string url = "supplier/SaveBatchProductShelf";
                    model.BaseUnitId = Operater.UnitId;
                    model.BaseUserId = Operater.UserId;
                    model.Token = Operater.Token;
                    r = await WebApiHelper.SendAsync<ReturnResult>(url, model);

                    if (r == null)
                    {
                        r.flag = 0;
                        r.msg = "服务器请求异常";
                    }
                    Log.Supplier(
                    new
                    {
                        User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                        Memo = "批量产品上架",
                        Action = "supplier/SaveBatchProductShelf",
                        Param = model,
                        Result = r
                    }, r.flag);
                }
                else
                {
                    r.flag = -3;
                    r.msg = "未提交要上架的产品。";
                    return Json(r, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("批量上架:" + e.Message);
                r.flag = -3;
                r.msg = "批量上架异常。";
            }
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 删除产品
        /// </summary>
        /// <param name="ids">产品Id</param>
        /// <returns></returns>
        public async Task<JsonResult> ProductDel(string ids)
        {
            ReturnResult r = new ReturnResult();
            if (string.IsNullOrEmpty(ids))
            {
                r.flag = 0;
                r.msg = "请选择要删除的产品。";
                return Json(r, JsonRequestBehavior.AllowGet);
            }
            string url = "supplier/productdel?ids=" + ids + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            r = await WebApiHelper.SendAsync<ReturnResult>(url, null, CommonJsonSendType.GET);
            Log.Supplier(
            new
            {
                User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                Memo = "产品删除",
                Action = "supplier/productdel",
                Param = string.Format("Ids:{0}", ids),
                Result = r
            }, r.flag);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 设置核心产品
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult ProductSetCore(string ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                //非法操作
            }
            SetCoreInputModel model = new SetCoreInputModel();
            model.Ids = ids;
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            return View("_SetCore", model);
        }

        /// <summary>
        /// 保存设置核心产品
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveProductSetCore(SetCoreInputModel model)
        {
            if (string.IsNullOrEmpty(model.Ids))
            {
                //非法操作
            }
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            string url = "supplier/productsetcore";
            var result = await WebApiHelper.SendAsync<ReturnResult>(url, model);

            Log.Supplier(
            new
            {
                User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                Memo = "设置核心产品",
                Action = "supplier/productsetcore",
                Param = model,
                Result = result
            }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 设置库存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult ProductSetStock(string ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                //非法操作
            }
            SetStockInputModel model = new SetStockInputModel();
            model.Ids = ids;
            model.Stock = null;
            return View("_SetStock", model);
        }
        /// <summary>
        /// 保存设置库存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<JsonResult> SaveProductSetStock(SetStockInputModel model)
        {
            if (string.IsNullOrEmpty(model.Ids))
            {
                //非法操作
            }
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            string url = "supplier/productsetstock";
            var result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            Log.Supplier(
              new
              {
                  User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                  Memo = "设置库存",
                  Action = "supplier/productsetstock",
                  Param = model,
                  Result = result
              }, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 备注信息
        public async Task<ActionResult> Remark()
        {
            string url = "supplier/getremark?mallId=0&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&freight=" + FreightType.yes + "&token=" + Operater.Token;
            var ret = await WebApiHelper.SendAsync<QueryResult<SupplierRemarkInputModel>>(url, null, CommonJsonSendType.GET);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            if (ret.Entity == null)
            {
                //异常
                ret.Entity = new SupplierRemarkInputModel();
            }
            return View(ret.Entity);
        }


        public async Task<JsonResult> GetRemark(int id = 0, int type = 0)
        {
            var freightType = FreightType.yes;
            if (type == 0)
            {
                freightType = FreightType.no;
            }
            string url = "supplier/getremark?mallId=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&freight=" + freightType + "&token=" + Operater.Token;
            var result = await WebApiHelper.SendAsync<QueryResult<SupplierRemarkInputModel>>(url, null, CommonJsonSendType.GET);
            if (result == null || result.Entity == null)
            {
                //异常
                FileLog.SendExceptionLog("获取上架备注信息异常!");
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            return Json(result.Entity, JsonRequestBehavior.AllowGet);
        }


        public async Task<JsonResult> SaveRemark(SupplierRemarkInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (model != null)
            {
                //异常
            }

            string url = Constant.ApiPath + "supplier/saveRemark";
            model.BaseUnitId = Operater.UnitId;
            model.UnitId = Operater.UnitId;
            model.UserId = Operater.UserId;
            model.BaseUserId = Operater.UserId;
            model.Token = Operater.Token;
            result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            Log.Supplier(
            new
            {
                User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                Memo = "设置商城备注信息",
                Action = "supplier/saveRemark",
                Param = model,
                Result = result
            }, result.flag);
            if (result == null)
            {
                //异常
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region 订单--待确认订单列表--确认订单 ---取消订单
        /// <summary>
        /// 待确认定点列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> Order(SearchArgumentsInputModel args)
        {
            QueryResult<OrderViewModel> ret = new QueryResult<OrderViewModel>();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }

            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            string url = Constant.ApiPath + "supplier/searchorder";
            args.Token = Operater.Token;
            ret = await WebApiHelper.SendAsync<QueryResult<OrderViewModel>>(url, args);

            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Args = args;

            return View(ret);
        }

        /// <summary>
        /// 确认订单
        /// </summary>
        /// <param name="id">订单Id</param>
        /// <returns></returns>
        public async Task<ActionResult> ConfirmOrder(int id, string url)
        {
            OrderAgreementInputModel model = new OrderAgreementInputModel();
            if (id <= 0)
            {
                //异常
            }
            string urls = Constant.ApiPath + "supplier/confirmorder?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<OrderAgreementInputModel>>(urls, null, CommonJsonSendType.GET);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ret.Entity.UnitType = (UnitType)Operater.UnitType;
            ViewBag.Url = url;
            return View(ret.Entity);
        }

        /// <summary>
        /// 确认订单保存
        /// </summary>
        /// <param name="model">确认订单实体</param>
        /// <returns></returns>
        public async Task<JsonResult> SaveConfirmOrder(OrderAgreementInputModel model)
        {
            ReturnResult result = new ReturnResult();
            model.InputUserId = Operater.UserId;
            model.InputUnitId = Operater.UnitId;
            model.UnitId = Operater.UnitId;
            model.RegTime = DateTime.Now;
            string url = Constant.ApiPath + "supplier/saveconfirmorder";
            model.BaseUnitId = Operater.UnitId;
            model.BaseUserId = Operater.UserId;
            model.BaseCurrentMallId = Operater.CurrentMallId;
            model.Token = Operater.Token;
            model.OtherAppointment = model.OtherAppointment == null ? "" : ComLib.NoHTML(model.OtherAppointment);
            result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("确认订单 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(result));
            Log.OrderLog(log, result.flag);
            if (result.flag == 1)
            {
                if (!model.IsUpdateContract)
                {
                    string message = string.Empty;
                    int type = 0;
                    //发送短信
                    if (Operater.UnitType == UnitType.AgentFirm.ToInt())
                    {
                        message = Constant.SendMessage_Event_SupplierConfirmOrder;
                        type = 2;
                    }
                    else
                    {
                        message = Constant.SendMessage_Event_BuyerConfirmOrder;
                        type = 3;
                    }
                    string messageUrl = Constant.ApiPath + "order/getordermessage?message=" + message + "&orderId=" + model.OrderId + "&mallId=" + Operater.CurrentMallId + "&type=" + type;
                    var messageResult = await WebApiHelper.SendAsync<QueryResult<SendMessageViewModel>>(messageUrl, null, CommonJsonSendType.GET);
                    if (messageResult.flag == 1 && messageResult.Entity != null)
                    {
                        //发送短信
                        SendMessage.SendToMobile(messageResult.Entity.Mobile, messageResult.Entity.Message);
                    }
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="ids">订单Id（以逗号(,)组成的字符串）</param>
        /// <returns></returns>
        public ActionResult GetCancelOrder(string ids, int operationType)
        {
            if (string.IsNullOrEmpty(ids))
            {
                return Json(new ReturnResult() { flag = 0, msg = "没有选择要取消的订单" }, JsonRequestBehavior.AllowGet);
                //异常
            }
            OrderCancelInputModel model = new OrderCancelInputModel();
            model.OrderIds = ids;
            model.OperationType = operationType;
            return View("_CancelOrder", model);
        }
        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="ids">订单Id（以逗号(,)组成的字符串）</param>
        /// <returns></returns>
        public async Task<JsonResult> SaveCancelOrder(OrderCancelInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (!ModelState.IsValid)
            {
                foreach (var item in ModelState.Values)
                {
                    // err.Add(item.Errors);
                    if (item.Errors.Count > 0)
                    {
                        result.flag = 0;
                        result.msg = item.Errors[0].ErrorMessage;
                        break;
                    }
                }
            }
            else
            {
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;

                model.Token = Operater.Token;
                string url = Constant.ApiPath + "supplier/saveconfirmorder";
                result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            }
            //调用日志
            var logStr = "取消订单 \r\n" + ComLib.Object2JSON(model);
            Log.OrderLog(logStr, result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        #endregion

        #region 订单--已确认订单列表--合同查看 ---修改合同
        /// <summary>
        /// 待确认定点列表(合同订单)
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ActionResult> Contract(SearchArgumentsInputModel args)
        {
            QueryResult<OrderViewModel> ret = new QueryResult<OrderViewModel>();
            if (args == null)
            {
                args = new SearchArgumentsInputModel();
            }

            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            string url = Constant.ApiPath + "supplier/searchContract";
            args.Token = Operater.Token;
            ret = await WebApiHelper.SendAsync<QueryResult<OrderViewModel>>(url, args);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();
                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }
            }
            ViewBag.Args = args;

            return View(ret);
        }

        /// <summary>
        /// 查看合同
        /// </summary>
        /// <param name="id">订单Id</param>
        /// <returns></returns>
        public async Task<ActionResult> LookContract(int id, string url)
        {
            OrderAgreementInputModel model = new OrderAgreementInputModel();
            if (id <= 0)
            {
                //异常
            }
            string r_url = Constant.ApiPath + "supplier/editcontract" + "?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<OrderAgreementInputModel>>(r_url, null, CommonJsonSendType.GET);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Url = url;
            model = ret.Entity;
            return View(model);
        }


        /// <summary>
        /// 修改合同
        /// </summary>
        /// <param name="id">订单Id</param>
        /// <returns></returns>
        public async Task<ActionResult> EditContract(int id, string url)
        {
            OrderAgreementInputModel model = new OrderAgreementInputModel();
            if (id <= 0)
            {
                //异常
            }
            string r_url = Constant.ApiPath + "supplier/editcontract" + "?id=" + id + "&unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;

            var ret = await WebApiHelper.SendAsync<QueryResult<OrderAgreementInputModel>>(r_url, null, CommonJsonSendType.GET);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Url = url;
            model = ret.Entity;
            return View(model);
        }

        public async Task<ActionResult> ContractItem(OrderItemSearchInputModel args)
        {
            QueryResult<OrderViewModel> ret = new QueryResult<OrderViewModel>();
            if (args == null)
            {
                //异常，无法产看清单
                args = new OrderItemSearchInputModel();
            }

            args.UnitId = Operater.UnitId;
            args.UserId = Operater.UserId;
            string url = Constant.ApiPath + "supplier/contractitem";
            args.Token = Operater.Token;
            ret = await WebApiHelper.SendAsync<QueryResult<OrderViewModel>>(url, args);
            if (ret.flag <= 0)
            {
                if (ret.flag == -1)
                {
                    return ApiTimeOut();

                }
                if (ret.flag == -2)
                {
                    return ReturnHome();
                }

            }
            ViewBag.Args = args;

            return View(ret);
        }
        #endregion

        #region 后台设置-合同信息

        /// <summary>
        /// 合同信息
        /// </summary> 
        /// <returns></returns>
        public async Task<ActionResult> UnitContract()
        {
            string url = Constant.ApiPath + "supplier/getunitcontract?unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token;
            QueryResult<UnitContractInputModel> ret = await WebApiHelper.SendAsync<QueryResult<UnitContractInputModel>>(url, null, CommonJsonSendType.GET);
            if (ret != null)
            {
                if (ret.flag <= 0)
                {
                    if (ret.flag == -1)
                    {
                        return ApiTimeOut();

                    }

                    if (ret.flag == -2)
                    {
                        return ReturnHome();
                    }

                }
            }
            else
            {
                //服务器异常，无法访问
            }
            return View(ret.Entity);
        }

        /// <summary>
        /// 保存合同信息
        /// </summary>
        /// <param name="model">合同信息实体</param>
        /// <returns></returns>
        public async Task<JsonResult> SaveUnitContract(UnitContractInputModel model)
        {
            ReturnResult result = new ReturnResult();
            if (!ModelState.IsValid)
            {
                foreach (var item in ModelState.Values)
                {
                    // err.Add(item.Errors);
                    if (item.Errors.Count > 0)
                    {
                        result.msg = item.Errors[0].ErrorMessage;
                        break;
                    }
                }
            }
            else
            {
                string url = Constant.ApiPath + "supplier/saveunitcontract";
                model.BaseUnitId = Operater.UnitId;
                model.BaseUserId = Operater.UserId;
                model.Token = Operater.Token;
                model.RegTime = DateTime.Now;
                result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
                Log.Supplier(
                 new
                 {
                     User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                     Memo = "添加修改合同信息",
                     Action = "supplier/saveunitcontract",
                     Param = model,
                     Result = result
                 }, result.flag);
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        #endregion

        #region 
        /// <summary>
        /// 展示，证书列表提供选择。
        /// </summary>
        /// <param name="fileCategory">类型</param>
        /// <returns></returns>
        public async Task<ActionResult> ProductCertificate(int fileCategory)
        {
            CertificateInputModel result = new CertificateInputModel();
            string url = Constant.ApiPath + "supplier/getcertificatelist?unitId=" + Operater.UnitId + "&userId=" + Operater.UserId + "&token=" + Operater.Token + "&fileCategory=" + fileCategory;
            QueryResult<AttachmentImgInputModel> queryList = await WebApiHelper.SendAsync<QueryResult<AttachmentImgInputModel>>(url, null, CommonJsonSendType.GET);
            result.BaseUnitId = Operater.UnitId;
            result.BaseUserId = Operater.UserId;
            result.FileCategory = fileCategory;
            result.Token = Operater.Token;
            if (queryList != null && queryList.Data != null && queryList.Data.Count > 0)
            {
                result.ImageList = queryList.Data.ToList();
            }
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            return View("_SelectImageList", result);
        }

        /// <summary>
        /// 保存合同信息
        /// </summary>
        /// <param name="model">合同信息实体</param>
        /// <returns></returns>
        public async Task<JsonResult> SaveCertificateImage(string path, int category)
        {
            ReturnResult result = new ReturnResult(); 
            string url = Constant.ApiPath + "supplier/savetcertificate";
            var objParam = new CertificateInputModel
            {
                Path = path,
                FileCategory = category,
                BaseUnitId = Operater.UnitId,
                BaseUserId = Operater.UserId,
                Token = Operater.Token,
                RegTime = DateTime.Now
            };
            result = await WebApiHelper.SendAsync<ReturnResult>(url, objParam);
            Log.Supplier(
             new
             {
                 User = string.Format("单位【{0},Id:{1}】-用户【{2},Id:{3}】", Operater.UnitName, Operater.UnitId, Operater.Name, Operater.UserId),
                 Memo = "添加修改合同信息",
                 Action = "supplier/saveunitcontract",
                 Param = objParam,
                 Result = result
             }, result.flag); 
            return Json(result, JsonRequestBehavior.AllowGet);
    }
    #endregion
}
}
