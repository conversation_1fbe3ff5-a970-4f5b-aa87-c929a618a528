﻿using Dqy.Instrument.ApplicationServices;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Search;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.ViewModels;
using Dqy.Instrument.UI.ViewModels.SearchViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Dqy.Instrument.Api.CommonLib
{
    /// <summary>
    /// 搜索索引
    /// </summary>
    public class SearchProductIndex
    {
        private readonly IPProductShelfApplicationService _productSheltService;
        public SearchProductIndex(IPProductShelfApplicationService psas)
        {
            _productSheltService = psas;
        }

        /// <summary>
        /// 上架产品，添加索引
        /// </summary>
        /// <param name="productShelfId">产品上架Id</param>
        /// <returns>是否成功</returns>
        public bool AddProductIndex(string productShelfIds)
        {
            if (_productSheltService != null)
            {
                var list = _productSheltService.AddProductIndex(productShelfIds);
                if (list != null)
                {
                    try
                    {
                        foreach (var item in list)
                        {
                            ElasticSearchHelper.IndexProduct("ProductSearchSimpleYun", item.ProductShelfId.ToString(), item);
                        }
                    }
                    catch (Exception e)
                    {
                        FileLog.SendExceptionLog(e.Message);
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 删除产品索引
        /// </summary>
        /// <param name="productShelfId"></param>
        /// <returns></returns>
        public bool DeleteProductIndex(string productShelfIds)
        {
            if (!string.IsNullOrEmpty(productShelfIds))
            {
                var ids = productShelfIds.Split(',');
                try
                {
                    foreach (var id in ids)
                    {
                        ElasticSearchHelper.DeleteIndex(ElasticSearchHelper.db_product_yun, "ProductSearchSimpleYun", id);
                    }
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog(string.Format("删除产品索引【上架Id：{0}】异常信息：{1}", ids, e.InnerException.Message));
                }
                return true;
            }
            return false;
        }
        /// <summary>
        /// 更新产品点击量
        /// </summary>
        /// <param name="productShelfId"></param>
        /// <returns></returns>
        public bool UpdateProductHitNum(long productid, int htinum)
        {
            try
            {
                ElasticSearchHelper.UpdateProductHitNum("ProductSearchSimpleYun", productid.ToString(), htinum);
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog(string.Format("更新产品点击量【产品Id：{0}】异常信息：{1}", productid, e.InnerException.Message));
            }
            return true;
        }
    }
    public class SearchDealerIndex
    {
        private readonly IUSupplierInMallApplicationService _supplierInMallService;
        public SearchDealerIndex(IUSupplierInMallApplicationService simas)
        {
            _supplierInMallService = simas;
        }

        /// <summary>
        /// 添加。供应商索引
        /// </summary>
        /// <param name="productShelfId">产品上架Id</param>
        /// <returns>是否成功</returns>
        public bool AddDealerIndex(string supplierInMallIds)
        {
            if (_supplierInMallService != null)
            {
                var list = _supplierInMallService.AddDealerIndex(supplierInMallIds);
                if (list != null)
                {
                    try
                    {
                        foreach (var item in list)
                        {
                            ElasticSearchHelper.IndexDealer(ElasticSearchHelper.db_dealer_yun, "DealerSearchSimpleYun", item.SupplierInMallId.ToString(), item);
                        }
                    }
                    catch (Exception e)
                    {
                        FileLog.SendExceptionLog(e.Message + "添加供应商索引");
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 删除供应商索引
        /// </summary>
        /// <param name="productShelfId"></param>
        /// <returns></returns>
        public bool DeleteDealerIndex(string supplierIds)
        {
            if (!string.IsNullOrEmpty(supplierIds))
            {
                var ids = supplierIds.Split(',');
                try
                {
                    foreach (var id in ids)
                    {
                        ElasticSearchHelper.DeleteIndex(ElasticSearchHelper.db_dealer_yun, "DealerSearchSimpleYun", id.ToString());
                    }
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog(e.Message + "删除供应商索引");
                }
                return true;
            }
            return false;
        }

    }

    public class SearchMarketAreaIndex
    {
        private readonly IPProductShelfApplicationService _productSheltService;
        public SearchMarketAreaIndex(IPProductShelfApplicationService productShelt)
        {
            _productSheltService = productShelt;
        }
        /// <summary>
        /// 跟新销售区域索引
        /// </summary>
        /// <param name="mallid">商城id</param>
        /// <param name="supplierid">供应商id</param>
        /// <param name="areaids">区域id</param>
        /// <returns>是否成功</returns>
        public bool UpdateMarketAreaIndex(int mallid, int supplierid)
        {
            if (mallid > 0 && supplierid > 0)
            {
                try
                {
                    var SaleAreas = "";
                    var SaleCitys = "";
                    var SaleProvinces = "";
                    QueryResult<SupplierMarketAreaViewModel> r = _productSheltService.GetMarketAreaIds(mallid, supplierid);
                    if (r != null && r.Data != null && r.Data.Count > 0)
                    {
                        SaleAreas = string.Join(",", r.Data.Select(m => m.EduAreaName).ToList());
                        SaleCitys = string.Join(",", r.Data.Select(m => m.CityEduName).Distinct());
                        SaleProvinces = string.Join(",", r.Data.Select(m => m.ProvinceEduName).Distinct());
                    }
                    ElasticSearchHelper.UpdateDealerSalesArea("ProductSearchSimpleYun", supplierid.ToString(), mallid.ToString(), SaleAreas, SaleCitys, SaleProvinces);
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog(e.Message);
                }
                return true;
            }
            return false;
        }
    }
}