﻿ Pid = entity.Pid,
 Name = entity.Name,
 Depth = entity.Depth,
 Path = entity.Path,
 Sort = entity.Sort,
 Icon1 = entity.Icon1,
 Icon2 = entity.Icon2,
 ConfigCode = entity.ConfigCode,
 CateType = entity.CateType,
 UserId = entity.UserId,


 Pid = model.Pid,
 Name = model.Name,
 Depth = model.Depth,
 Path = model.Path,
 Sort = model.Sort,
 Icon1 = model.Icon1,
 Icon2 = model.Icon2,
 ConfigCode = model.ConfigCode,
 CateType = model.CateType,
 UserId = model.UserId,


 temp.Pid = model.Pid,
 temp.Name = model.Name,
 temp.Depth = model.Depth,
 temp.Path = model.Path,
 temp.Sort = model.Sort,
 temp.Icon1 = model.Icon1,
 temp.Icon2 = model.Icon2,
 temp.ConfigCode = model.ConfigCode,
 temp.CateType = model.CateType,
 temp.UserId = model.UserId,

 Id = item.Id,
 Pid = item.Pid,
 Name = item.Name,
 Depth = item.Depth,
 Path = item.Path,
 Sort = item.Sort,
 Icon1 = item.Icon1,
 Icon2 = item.Icon2,
 ConfigCode = item.ConfigCode,
 CateType = item.CateType,
 UserId = item.UserId,

public class ArticleCategoryInputModel
{
 [Display(Name = "ID")] 
    public int Id {get; set; }
    
 [Display(Name = "父ID")] 
    public int Pid {get; set; }
    
 [Display(Name = "名称")] 
    public string Name {get; set; }
    
 [Display(Name = "深度")] 
    public int Depth {get; set; }
    
 [Display(Name = "路径(排序)")] 
    public string Path {get; set; }
    
 [Display(Name = "用户定义排序")] 
    public int Sort {get; set; }
    
 [Display(Name = "分类图标（小）")] 
    public string Icon1 {get; set; }
    
 [Display(Name = "分类图标（大）")] 
    public string Icon2 {get; set; }
    
 [Display(Name = "自定义编码（唯一，链接后面参数）")] 
    public string ConfigCode {get; set; }
    
 [Display(Name = "栏目类型(1:公共栏目；2:：运营商栏目（只有运营商才可以发布）)")] 
    public int CateType {get; set; }
    
 [Display(Name = "创建人id")] 
    public int UserId {get; set; }
    
 }
 
 public class ArticleCategoryViewModel
 {
    /// <summary>
    /// ID
    /// </summary>
    public int Id {get; set; }
    
    /// <summary>
    /// 父ID
    /// </summary>
    public int Pid {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 深度
    /// </summary>
    public int Depth {get; set; }
    
    /// <summary>
    /// 路径(排序)
    /// </summary>
    public string Path {get; set; }
    
    /// <summary>
    /// 用户定义排序
    /// </summary>
    public int Sort {get; set; }
    
    /// <summary>
    /// 分类图标（小）
    /// </summary>
    public string Icon1 {get; set; }
    
    /// <summary>
    /// 分类图标（大）
    /// </summary>
    public string Icon2 {get; set; }
    
    /// <summary>
    /// 自定义编码（唯一，链接后面参数）
    /// </summary>
    public string ConfigCode {get; set; }
    
    /// <summary>
    /// 栏目类型(1:公共栏目；2:：运营商栏目（只有运营商才可以发布）)
    /// </summary>
    public int CateType {get; set; }
    
    /// <summary>
    /// 创建人id
    /// </summary>
    public int UserId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Pid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父ID" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Depth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Depth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入深度" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Path, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Path, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入路径(排序)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sort, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sort, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户定义排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Icon1, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Icon1, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入分类图标（小）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Icon2, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Icon2, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入分类图标（大）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ConfigCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ConfigCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入自定义编码（唯一，链接后面参数）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CateType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CateType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入栏目类型(1:公共栏目；2:：运营商栏目（只有运营商才可以发布）)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建人id" } })                    
                </div>
           </div>
  




 { field: 'Pid', title: '父ID', sortable: true },
                 
 { field: 'Name', title: '名称', sortable: true },
                 
 { field: 'Depth', title: '深度', sortable: true },
                 
 { field: 'Path', title: '路径(排序)', sortable: true },
                 
 { field: 'Sort', title: '用户定义排序', sortable: true },
                 
 { field: 'Icon1', title: '分类图标（小）', sortable: true },
                 
 { field: 'Icon2', title: '分类图标（大）', sortable: true },
                 
 { field: 'ConfigCode', title: '自定义编码（唯一，链接后面参数）', sortable: true },
                 
 { field: 'CateType', title: '栏目类型(1:公共栏目；2:：运营商栏目（只有运营商才可以发布）)', sortable: true },
                 
 { field: 'UserId', title: '创建人id', sortable: true },
                 
o.Pid,                 
o.Name,                 
o.Depth,                 
o.Path,                 
o.Sort,                 
o.Icon1,                 
o.Icon2,                 
o.ConfigCode,                 
o.CateType,                 
o.UserId,                 
        
        $('#Pid').val(d.data.rows.Pid);          
        $('#Name').val(d.data.rows.Name);          
        $('#Depth').val(d.data.rows.Depth);          
        $('#Path').val(d.data.rows.Path);          
        $('#Sort').val(d.data.rows.Sort);          
        $('#Icon1').val(d.data.rows.Icon1);          
        $('#Icon2').val(d.data.rows.Icon2);          
        $('#ConfigCode').val(d.data.rows.ConfigCode);          
        $('#CateType').val(d.data.rows.CateType);          
        $('#UserId').val(d.data.rows.UserId);          

 $('#th_Pid').html(' 父ID');               
 $('#th_Name').html(' 名称');               
 $('#th_Depth').html(' 深度');               
 $('#th_Path').html(' 路径(排序)');               
 $('#th_Sort').html(' 用户定义排序');               
 $('#th_Icon1').html(' 分类图标（小）');               
 $('#th_Icon2').html(' 分类图标（大）');               
 $('#th_ConfigCode').html(' 自定义编码（唯一，链接后面参数）');               
 $('#th_CateType').html(' 栏目类型(1:公共栏目；2:：运营商栏目（只有运营商才可以发布）)');               
 $('#th_UserId').html(' 创建人id');               
 
 $('#tr_Pid').hide();               
 $('#tr_Name').hide();               
 $('#tr_Depth').hide();               
 $('#tr_Path').hide();               
 $('#tr_Sort').hide();               
 $('#tr_Icon1').hide();               
 $('#tr_Icon2').hide();               
 $('#tr_ConfigCode').hide();               
 $('#tr_CateType').hide();               
 $('#tr_UserId').hide();               

 , "Pid" : pid
 , "Name" : name
 , "Depth" : depth
 , "Path" : path
 , "Sort" : sort
 , "Icon1" : icon1
 , "Icon2" : icon2
 , "ConfigCode" : configCode
 , "CateType" : cateType
 , "UserId" : userId

 var pid = $('#o_Pid').val();
 var name = $('#o_Name').val();
 var depth = $('#o_Depth').val();
 var path = $('#o_Path').val();
 var sort = $('#o_Sort').val();
 var icon1 = $('#o_Icon1').val();
 var icon2 = $('#o_Icon2').val();
 var configCode = $('#o_ConfigCode').val();
 var cateType = $('#o_CateType').val();
 var userId = $('#o_UserId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父ID' : '产品名称', d.data.rows.Pid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '深度' : '产品名称', d.data.rows.Depth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '路径(排序)' : '产品名称', d.data.rows.Path);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户定义排序' : '产品名称', d.data.rows.Sort);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '分类图标（小）' : '产品名称', d.data.rows.Icon1);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '分类图标（大）' : '产品名称', d.data.rows.Icon2);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '自定义编码（唯一，链接后面参数）' : '产品名称', d.data.rows.ConfigCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '栏目类型(1:公共栏目；2:：运营商栏目（只有运营商才可以发布）)' : '产品名称', d.data.rows.CateType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建人id' : '产品名称', d.data.rows.UserId);



