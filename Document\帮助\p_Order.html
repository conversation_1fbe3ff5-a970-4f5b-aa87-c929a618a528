﻿ OrderNo = entity.OrderNo,
 BatchNo = entity.BatchNo,
 MallId = entity.MallId,
 ContractMainId = entity.ContractMainId,
 ContractMainName = entity.ContractMainName,
 OrderTotalAmount = entity.OrderTotalAmount,
 PriceDecline = entity.PriceDecline,
 Requitement = entity.Requitement,
 OtherRequitement = entity.OtherRequitement,
 Tel = entity.Tel,
 ConsigneeId = entity.ConsigneeId,
 Consignee = entity.Consignee,
 Statuz = entity.Statuz,
 UnitId = entity.UnitId,
 GenerateId = entity.GenerateId,
 GenerateUnitId = entity.GenerateUnitId,
 Memo = entity.Memo,
 RegTime = entity.RegTime,


 OrderNo = model.OrderNo,
 BatchNo = model.BatchNo,
 MallId = model.MallId,
 ContractMainId = model.ContractMainId,
 ContractMainName = model.ContractMainName,
 OrderTotalAmount = model.OrderTotalAmount,
 PriceDecline = model.PriceDecline,
 Requitement = model.Requitement,
 OtherRequitement = model.OtherRequitement,
 Tel = model.Tel,
 ConsigneeId = model.ConsigneeId,
 Consignee = model.Consignee,
 Statuz = model.Statuz,
 UnitId = model.UnitId,
 GenerateId = model.GenerateId,
 GenerateUnitId = model.GenerateUnitId,
 Memo = model.Memo,
 RegTime = model.RegTime,


 temp.OrderNo = model.OrderNo,
 temp.BatchNo = model.BatchNo,
 temp.MallId = model.MallId,
 temp.ContractMainId = model.ContractMainId,
 temp.ContractMainName = model.ContractMainName,
 temp.OrderTotalAmount = model.OrderTotalAmount,
 temp.PriceDecline = model.PriceDecline,
 temp.Requitement = model.Requitement,
 temp.OtherRequitement = model.OtherRequitement,
 temp.Tel = model.Tel,
 temp.ConsigneeId = model.ConsigneeId,
 temp.Consignee = model.Consignee,
 temp.Statuz = model.Statuz,
 temp.UnitId = model.UnitId,
 temp.GenerateId = model.GenerateId,
 temp.GenerateUnitId = model.GenerateUnitId,
 temp.Memo = model.Memo,
 temp.RegTime = model.RegTime,

 OrderId = item.OrderId,
 OrderNo = item.OrderNo,
 BatchNo = item.BatchNo,
 MallId = item.MallId,
 ContractMainId = item.ContractMainId,
 ContractMainName = item.ContractMainName,
 OrderTotalAmount = item.OrderTotalAmount,
 PriceDecline = item.PriceDecline,
 Requitement = item.Requitement,
 OtherRequitement = item.OtherRequitement,
 Tel = item.Tel,
 ConsigneeId = item.ConsigneeId,
 Consignee = item.Consignee,
 Statuz = item.Statuz,
 UnitId = item.UnitId,
 GenerateId = item.GenerateId,
 GenerateUnitId = item.GenerateUnitId,
 Memo = item.Memo,
 RegTime = item.RegTime,

public class OrderInputModel
{
 [Display(Name = "Id")] 
    public long OrderId {get; set; }
    
 [Display(Name = "订单编号")] 
    public string OrderNo {get; set; }
    
 [Display(Name = "批次编号（区县、市级一次生成多个订单）")] 
    public string BatchNo {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "合同主体Id")] 
    public int ContractMainId {get; set; }
    
 [Display(Name = "合同主体名称")] 
    public string ContractMainName {get; set; }
    
 [Display(Name = "订单总价")] 
    public decimal OrderTotalAmount {get; set; }
    
 [Display(Name = "价格下浮")] 
    public decimal PriceDecline {get; set; }
    
 [Display(Name = "供货要求（多少天）")] 
    public decimal Requitement {get; set; }
    
 [Display(Name = "其它要求")] 
    public string OtherRequitement {get; set; }
    
 [Display(Name = "联系电话")] 
    public string Tel {get; set; }
    
 [Display(Name = "收货人Id")] 
    public long ConsigneeId {get; set; }
    
 [Display(Name = "收货人")] 
    public string Consignee {get; set; }
    
 [Display(Name = "状态(1：待卖方确认，2：待买方确认 ，3：买方申请取消订单，4：卖方申请取消订单，5：买方申请取消合同，6：卖方申请取消合同，7：已生成合同 8：已撤销订单 9：已取消)")] 
    public int Statuz {get; set; }
    
 [Display(Name = "供应商Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "生成人Id")] 
    public long GenerateId {get; set; }
    
 [Display(Name = "生成人单位Id")] 
    public int GenerateUnitId {get; set; }
    
 [Display(Name = "备注（如取消原因）")] 
    public string Memo {get; set; }
    
 [Display(Name = "记录时间（创建时间，审批时间）")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class OrderViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long OrderId {get; set; }
    
    /// <summary>
    /// 订单编号
    /// </summary>
    public string OrderNo {get; set; }
    
    /// <summary>
    /// 批次编号（区县、市级一次生成多个订单）
    /// </summary>
    public string BatchNo {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 合同主体Id
    /// </summary>
    public int ContractMainId {get; set; }
    
    /// <summary>
    /// 合同主体名称
    /// </summary>
    public string ContractMainName {get; set; }
    
    /// <summary>
    /// 订单总价
    /// </summary>
    public decimal OrderTotalAmount {get; set; }
    
    /// <summary>
    /// 价格下浮
    /// </summary>
    public decimal PriceDecline {get; set; }
    
    /// <summary>
    /// 供货要求（多少天）
    /// </summary>
    public decimal Requitement {get; set; }
    
    /// <summary>
    /// 其它要求
    /// </summary>
    public string OtherRequitement {get; set; }
    
    /// <summary>
    /// 联系电话
    /// </summary>
    public string Tel {get; set; }
    
    /// <summary>
    /// 收货人Id
    /// </summary>
    public long ConsigneeId {get; set; }
    
    /// <summary>
    /// 收货人
    /// </summary>
    public string Consignee {get; set; }
    
    /// <summary>
    /// 状态(1：待卖方确认，2：待买方确认 ，3：买方申请取消订单，4：卖方申请取消订单，5：买方申请取消合同，6：卖方申请取消合同，7：已生成合同 8：已撤销订单 9：已取消)
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 供应商Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 生成人Id
    /// </summary>
    public long GenerateId {get; set; }
    
    /// <summary>
    /// 生成人单位Id
    /// </summary>
    public int GenerateUnitId {get; set; }
    
    /// <summary>
    /// 备注（如取消原因）
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 记录时间（创建时间，审批时间）
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderNo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderNo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.BatchNo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.BatchNo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入批次编号（区县、市级一次生成多个订单）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractMainId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractMainId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同主体Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractMainName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractMainName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同主体名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderTotalAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderTotalAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单总价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PriceDecline, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PriceDecline, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入价格下浮" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Requitement, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Requitement, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供货要求（多少天）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OtherRequitement, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OtherRequitement, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入其它要求" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Tel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Tel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系电话" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ConsigneeId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ConsigneeId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入收货人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Consignee, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Consignee, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入收货人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态(1：待卖方确认，2：待买方确认 ，3：买方申请取消订单，4：卖方申请取消订单，5：买方申请取消合同，6：卖方申请取消合同，7：已生成合同 8：已撤销订单 9：已取消)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.GenerateId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.GenerateId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入生成人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.GenerateUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.GenerateUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入生成人单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注（如取消原因）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间（创建时间，审批时间）" } })                    
                </div>
           </div>
  




 { field: 'OrderNo', title: '订单编号', sortable: true },
                 
 { field: 'BatchNo', title: '批次编号（区县、市级一次生成多个订单）', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'ContractMainId', title: '合同主体Id', sortable: true },
                 
 { field: 'ContractMainName', title: '合同主体名称', sortable: true },
                 
 { field: 'OrderTotalAmount', title: '订单总价', sortable: true },
                 
 { field: 'PriceDecline', title: '价格下浮', sortable: true },
                 
 { field: 'Requitement', title: '供货要求（多少天）', sortable: true },
                 
 { field: 'OtherRequitement', title: '其它要求', sortable: true },
                 
 { field: 'Tel', title: '联系电话', sortable: true },
                 
 { field: 'ConsigneeId', title: '收货人Id', sortable: true },
                 
 { field: 'Consignee', title: '收货人', sortable: true },
                 
 { field: 'Statuz', title: '状态(1：待卖方确认，2：待买方确认 ，3：买方申请取消订单，4：卖方申请取消订单，5：买方申请取消合同，6：卖方申请取消合同，7：已生成合同 8：已撤销订单 9：已取消)', sortable: true },
                 
 { field: 'UnitId', title: '供应商Id', sortable: true },
                 
 { field: 'GenerateId', title: '生成人Id', sortable: true },
                 
 { field: 'GenerateUnitId', title: '生成人单位Id', sortable: true },
                 
 { field: 'Memo', title: '备注（如取消原因）', sortable: true },
                 
 { field: 'RegTime', title: '记录时间（创建时间，审批时间）', sortable: true },
                 
o.OrderNo,                 
o.BatchNo,                 
o.MallId,                 
o.ContractMainId,                 
o.ContractMainName,                 
o.OrderTotalAmount,                 
o.PriceDecline,                 
o.Requitement,                 
o.OtherRequitement,                 
o.Tel,                 
o.ConsigneeId,                 
o.Consignee,                 
o.Statuz,                 
o.UnitId,                 
o.GenerateId,                 
o.GenerateUnitId,                 
o.Memo,                 
o.RegTime,                 
        
        $('#OrderNo').val(d.data.rows.OrderNo);          
        $('#BatchNo').val(d.data.rows.BatchNo);          
        $('#MallId').val(d.data.rows.MallId);          
        $('#ContractMainId').val(d.data.rows.ContractMainId);          
        $('#ContractMainName').val(d.data.rows.ContractMainName);          
        $('#OrderTotalAmount').val(d.data.rows.OrderTotalAmount);          
        $('#PriceDecline').val(d.data.rows.PriceDecline);          
        $('#Requitement').val(d.data.rows.Requitement);          
        $('#OtherRequitement').val(d.data.rows.OtherRequitement);          
        $('#Tel').val(d.data.rows.Tel);          
        $('#ConsigneeId').val(d.data.rows.ConsigneeId);          
        $('#Consignee').val(d.data.rows.Consignee);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#GenerateId').val(d.data.rows.GenerateId);          
        $('#GenerateUnitId').val(d.data.rows.GenerateUnitId);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_OrderNo').html(' 订单编号');               
 $('#th_BatchNo').html(' 批次编号（区县、市级一次生成多个订单）');               
 $('#th_MallId').html(' 商城Id');               
 $('#th_ContractMainId').html(' 合同主体Id');               
 $('#th_ContractMainName').html(' 合同主体名称');               
 $('#th_OrderTotalAmount').html(' 订单总价');               
 $('#th_PriceDecline').html(' 价格下浮');               
 $('#th_Requitement').html(' 供货要求（多少天）');               
 $('#th_OtherRequitement').html(' 其它要求');               
 $('#th_Tel').html(' 联系电话');               
 $('#th_ConsigneeId').html(' 收货人Id');               
 $('#th_Consignee').html(' 收货人');               
 $('#th_Statuz').html(' 状态(1：待卖方确认，2：待买方确认 ，3：买方申请取消订单，4：卖方申请取消订单，5：买方申请取消合同，6：卖方申请取消合同，7：已生成合同 8：已撤销订单 9：已取消)');               
 $('#th_UnitId').html(' 供应商Id');               
 $('#th_GenerateId').html(' 生成人Id');               
 $('#th_GenerateUnitId').html(' 生成人单位Id');               
 $('#th_Memo').html(' 备注（如取消原因）');               
 $('#th_RegTime').html(' 记录时间（创建时间，审批时间）');               
 
 $('#tr_OrderNo').hide();               
 $('#tr_BatchNo').hide();               
 $('#tr_MallId').hide();               
 $('#tr_ContractMainId').hide();               
 $('#tr_ContractMainName').hide();               
 $('#tr_OrderTotalAmount').hide();               
 $('#tr_PriceDecline').hide();               
 $('#tr_Requitement').hide();               
 $('#tr_OtherRequitement').hide();               
 $('#tr_Tel').hide();               
 $('#tr_ConsigneeId').hide();               
 $('#tr_Consignee').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_GenerateId').hide();               
 $('#tr_GenerateUnitId').hide();               
 $('#tr_Memo').hide();               
 $('#tr_RegTime').hide();               

 , "OrderNo" : orderNo
 , "BatchNo" : batchNo
 , "MallId" : mallId
 , "ContractMainId" : contractMainId
 , "ContractMainName" : contractMainName
 , "OrderTotalAmount" : orderTotalAmount
 , "PriceDecline" : priceDecline
 , "Requitement" : requitement
 , "OtherRequitement" : otherRequitement
 , "Tel" : tel
 , "ConsigneeId" : consigneeId
 , "Consignee" : consignee
 , "Statuz" : statuz
 , "UnitId" : unitId
 , "GenerateId" : generateId
 , "GenerateUnitId" : generateUnitId
 , "Memo" : memo
 , "RegTime" : regTime

 var orderNo = $('#o_OrderNo').val();
 var batchNo = $('#o_BatchNo').val();
 var mallId = $('#o_MallId').val();
 var contractMainId = $('#o_ContractMainId').val();
 var contractMainName = $('#o_ContractMainName').val();
 var orderTotalAmount = $('#o_OrderTotalAmount').val();
 var priceDecline = $('#o_PriceDecline').val();
 var requitement = $('#o_Requitement').val();
 var otherRequitement = $('#o_OtherRequitement').val();
 var tel = $('#o_Tel').val();
 var consigneeId = $('#o_ConsigneeId').val();
 var consignee = $('#o_Consignee').val();
 var statuz = $('#o_Statuz').val();
 var unitId = $('#o_UnitId').val();
 var generateId = $('#o_GenerateId').val();
 var generateUnitId = $('#o_GenerateUnitId').val();
 var memo = $('#o_Memo').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单编号' : '产品名称', d.data.rows.OrderNo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '批次编号（区县、市级一次生成多个订单）' : '产品名称', d.data.rows.BatchNo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同主体Id' : '产品名称', d.data.rows.ContractMainId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同主体名称' : '产品名称', d.data.rows.ContractMainName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单总价' : '产品名称', d.data.rows.OrderTotalAmount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '价格下浮' : '产品名称', d.data.rows.PriceDecline);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供货要求（多少天）' : '产品名称', d.data.rows.Requitement);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '其它要求' : '产品名称', d.data.rows.OtherRequitement);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系电话' : '产品名称', d.data.rows.Tel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '收货人Id' : '产品名称', d.data.rows.ConsigneeId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '收货人' : '产品名称', d.data.rows.Consignee);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态(1：待卖方确认，2：待买方确认 ，3：买方申请取消订单，4：卖方申请取消订单，5：买方申请取消合同，6：卖方申请取消合同，7：已生成合同 8：已撤销订单 9：已取消)' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '生成人Id' : '产品名称', d.data.rows.GenerateId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '生成人单位Id' : '产品名称', d.data.rows.GenerateUnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注（如取消原因）' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间（创建时间，审批时间）' : '产品名称', d.data.rows.RegTime);



