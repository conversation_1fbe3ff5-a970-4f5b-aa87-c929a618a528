﻿ TypeCode = entity.TypeCode,
 TypeName = entity.TypeName,
 DicName = entity.DicName,
 DicValue = entity.DicValue,
 DicValue2 = entity.DicValue2,
 DicValue3 = entity.DicValue3,
 Memo = entity.Memo,
 Sequence = entity.Sequence,


 TypeCode = model.TypeCode,
 TypeName = model.TypeName,
 DicName = model.DicName,
 DicValue = model.DicValue,
 DicValue2 = model.DicValue2,
 DicValue3 = model.DicValue3,
 Memo = model.Memo,
 Sequence = model.Sequence,


 temp.TypeCode = model.TypeCode,
 temp.TypeName = model.TypeName,
 temp.DicName = model.DicName,
 temp.DicValue = model.DicValue,
 temp.DicValue2 = model.DicValue2,
 temp.DicValue3 = model.DicValue3,
 temp.Memo = model.Memo,
 temp.Sequence = model.Sequence,

 DictionaryId = item.DictionaryId,
 TypeCode = item.TypeCode,
 TypeName = item.TypeName,
 DicName = item.DicName,
 DicValue = item.DicValue,
 DicValue2 = item.DicValue2,
 DicValue3 = item.DicValue3,
 Memo = item.Memo,
 Sequence = item.Sequence,

public class DictionaryInputModel
{
 [Display(Name = "Id")] 
    public int DictionaryId {get; set; }
    
 [Display(Name = "类别编码")] 
    public TypeCode TypeCode {get; set; }
    
 [Display(Name = "类别名称")] 
    public string TypeName {get; set; }
    
 [Display(Name = "名称")] 
    public string DicName {get; set; }
    
 [Display(Name = "取值")] 
    public string DicValue {get; set; }
    
 [Display(Name = "值2")] 
    public string DicValue2 {get; set; }
    
 [Display(Name = "值3")] 
    public string DicValue3 {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 [Display(Name = "排序")] 
    public int Sequence {get; set; }
    
 }
 
 public class DictionaryViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int DictionaryId {get; set; }
    
    /// <summary>
    /// 类别编码
    /// </summary>
    public TypeCode TypeCode {get; set; }
    
    /// <summary>
    /// 类别名称
    /// </summary>
    public string TypeName {get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string DicName {get; set; }
    
    /// <summary>
    /// 取值
    /// </summary>
    public string DicValue {get; set; }
    
    /// <summary>
    /// 值2
    /// </summary>
    public string DicValue2 {get; set; }
    
    /// <summary>
    /// 值3
    /// </summary>
    public string DicValue3 {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sequence {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.TypeCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.TypeCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类别编码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.TypeName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.TypeName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类别名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DicName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DicName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DicValue, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DicValue, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入取值" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DicValue2, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DicValue2, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入值2" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DicValue3, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DicValue3, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入值3" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sequence, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sequence, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入排序" } })                    
                </div>
           </div>
  




 { field: 'TypeCode', title: '类别编码', sortable: true },
                 
 { field: 'TypeName', title: '类别名称', sortable: true },
                 
 { field: 'DicName', title: '名称', sortable: true },
                 
 { field: 'DicValue', title: '取值', sortable: true },
                 
 { field: 'DicValue2', title: '值2', sortable: true },
                 
 { field: 'DicValue3', title: '值3', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
 { field: 'Sequence', title: '排序', sortable: true },
                 
o.TypeCode,                 
o.TypeName,                 
o.DicName,                 
o.DicValue,                 
o.DicValue2,                 
o.DicValue3,                 
o.Memo,                 
o.Sequence,                 
        
        $('#TypeCode').val(d.data.rows.TypeCode);          
        $('#TypeName').val(d.data.rows.TypeName);          
        $('#DicName').val(d.data.rows.DicName);          
        $('#DicValue').val(d.data.rows.DicValue);          
        $('#DicValue2').val(d.data.rows.DicValue2);          
        $('#DicValue3').val(d.data.rows.DicValue3);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#Sequence').val(d.data.rows.Sequence);          

 $('#th_TypeCode').html(' 类别编码');               
 $('#th_TypeName').html(' 类别名称');               
 $('#th_DicName').html(' 名称');               
 $('#th_DicValue').html(' 取值');               
 $('#th_DicValue2').html(' 值2');               
 $('#th_DicValue3').html(' 值3');               
 $('#th_Memo').html(' 备注');               
 $('#th_Sequence').html(' 排序');               
 
 $('#tr_TypeCode').hide();               
 $('#tr_TypeName').hide();               
 $('#tr_DicName').hide();               
 $('#tr_DicValue').hide();               
 $('#tr_DicValue2').hide();               
 $('#tr_DicValue3').hide();               
 $('#tr_Memo').hide();               
 $('#tr_Sequence').hide();               

 , "TypeCode" : type
 , "TypeName" : typeName
 , "DicName" : dicName
 , "DicValue" : dicValue
 , "DicValue2" : dicValue2
 , "DicValue3" : dicValue3
 , "Memo" : memo
 , "Sequence" : sequence

 var type = $('#o_TypeCode').val();
 var typeName = $('#o_TypeName').val();
 var dicName = $('#o_DicName').val();
 var dicValue = $('#o_DicValue').val();
 var dicValue2 = $('#o_DicValue2').val();
 var dicValue3 = $('#o_DicValue3').val();
 var memo = $('#o_Memo').val();
 var sequence = $('#o_Sequence').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类别编码' : '产品名称', d.data.rows.TypeCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类别名称' : '产品名称', d.data.rows.TypeName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '名称' : '产品名称', d.data.rows.DicName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '取值' : '产品名称', d.data.rows.DicValue);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '值2' : '产品名称', d.data.rows.DicValue2);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '值3' : '产品名称', d.data.rows.DicValue3);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '排序' : '产品名称', d.data.rows.Sequence);



