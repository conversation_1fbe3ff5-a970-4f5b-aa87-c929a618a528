﻿ CourseId = entity.CourseId,
 StudySectionId = entity.StudySectionId,


 CourseId = model.CourseId,
 StudySectionId = model.StudySectionId,


 temp.CourseId = model.CourseId,
 temp.StudySectionId = model.StudySectionId,

 CoursePeriodRelationShipId = item.CoursePeriodRelationShipId,
 CourseId = item.CourseId,
 StudySectionId = item.StudySectionId,

public class CoursePeriodRelationShipInputModel
{
 [Display(Name = "Id")] 
    public int CoursePeriodRelationShipId {get; set; }
    
 [Display(Name = "学科Id")] 
    public int CourseId {get; set; }
    
 [Display(Name = "学段Id")] 
    public int StudySectionId {get; set; }
    
 }
 
 public class CoursePeriodRelationShipViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int CoursePeriodRelationShipId {get; set; }
    
    /// <summary>
    /// 学科Id
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 学段Id
    /// </summary>
    public int StudySectionId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StudySectionId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StudySectionId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学段Id" } })                    
                </div>
           </div>
  




 { field: 'CourseId', title: '学科Id', sortable: true },
                 
 { field: 'StudySectionId', title: '学段Id', sortable: true },
                 
o.CourseId,                 
o.StudySectionId,                 
        
        $('#CourseId').val(d.data.rows.CourseId);          
        $('#StudySectionId').val(d.data.rows.StudySectionId);          

 $('#th_CourseId').html(' 学科Id');               
 $('#th_StudySectionId').html(' 学段Id');               
 
 $('#tr_CourseId').hide();               
 $('#tr_StudySectionId').hide();               

 , "CourseId" : courseId
 , "StudySectionId" : studySectionId

 var courseId = $('#o_CourseId').val();
 var studySectionId = $('#o_StudySectionId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科Id' : '产品名称', d.data.rows.CourseId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学段Id' : '产品名称', d.data.rows.StudySectionId);



