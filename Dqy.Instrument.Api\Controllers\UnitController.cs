﻿using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.Api.Filters;
using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Dqy.Instrument.Api.Controllers
{
    [RoutePrefix("api/unit")]
    public class UnitController : ApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IUUnitApplicationService _iUnitApplicationService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="iUnitApplicationService"></param>
        public UnitController(IUUnitApplicationService iUnitApplicationService)
        {
            _iUnitApplicationService = iUnitApplicationService;
        }

        /// <summary>
        /// 根据单位Id获取区县单位信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcountyunit")]
        public async Task<ReturnResult> GetCountyUnit(string token, int unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能。";
                    return r;
                }
                r = _iUnitApplicationService.GetCountyCityUnit(unitId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 根据单位Id获取市级单位信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcityunit")]
        public async Task<ReturnResult> GetCityUnit(string token, int unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能。";
                    return r;
                }
                r = _iUnitApplicationService.GetCountyCityUnit(unitId);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 保存区县信息
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsavecounty")]
        [ValidateModel]
        public async Task<ReturnResult> SaveCountyUnit(CountyCityInputModel countyInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(countyInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != countyInput.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能。";
                    return r;
                }
                r = _iUnitApplicationService.SaveCountyCityUnit(countyInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 保存市级信息
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsavecity")]
        [ValidateModel]
        public async Task<ReturnResult> SaveCityUnit(CountyCityInputModel cityInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(cityInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != cityInput.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CityAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能。";
                    return r;
                }
                r = _iUnitApplicationService.SaveCountyCityUnit(cityInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 根据单位Id获取市级单位信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getunitmodel")]
        public async Task<QueryResult<UnitViewModel>> GetUnitModel(string token)
        {
            var result = await Task.Run(() =>
            {
                QueryResult<UnitViewModel> r = new QueryResult<UnitViewModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                r = _iUnitApplicationService.GetUnitModel(sessionBag.UnitId);
                return r;
            });
            return result;
        }

        #region 第三方登录
        /// <summary>
        /// 根据用户Id获取用户信息
        /// </summary>     
        /// <param name="thirdUnitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getunitidbythirdunitid")]
        public async Task<long> GetUnitIdByThirdUnitId(string thirdUnitId)
        {
            thirdUnitId = StringFilter.SearchSql(thirdUnitId);
            var UserId = await Task.Run(() =>
            {
                return _iUnitApplicationService.GetUnitIdByThirdUnitId(thirdUnitId);
            });
            return UserId;
        }

        #endregion 第三方登录    
    }
}
