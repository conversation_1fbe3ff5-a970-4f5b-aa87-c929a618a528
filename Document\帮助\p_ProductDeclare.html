﻿ ProductShelvesId = entity.ProductShelvesId,
 MallId = entity.MallId,
 CourseId = entity.CourseId,
 InstrumentModelId = entity.InstrumentModelId,
 ModelDescription = entity.ModelDescription,
 Price = entity.Price,
 Num = entity.Num,
 Sum = entity.Sum,
 DeclareTime = entity.DeclareTime,
 DeclareUserId = entity.DeclareUserId,
 AuditStatuz = entity.AuditStatuz,
 OrderStatuz = entity.OrderStatuz,
 SchoolId = entity.SchoolId,
 MainPartId = entity.MainPartId,
 MainPartType = entity.MainPartType,
 LastApprovalType = entity.LastApprovalType,
 SourceFundId = entity.SourceFundId,
 FundType = entity.FundType,
 ApprovalTime = entity.ApprovalTime,


 ProductShelvesId = model.ProductShelvesId,
 MallId = model.MallId,
 CourseId = model.CourseId,
 InstrumentModelId = model.InstrumentModelId,
 ModelDescription = model.ModelDescription,
 Price = model.Price,
 Num = model.Num,
 Sum = model.Sum,
 DeclareTime = model.DeclareTime,
 DeclareUserId = model.DeclareUserId,
 AuditStatuz = model.AuditStatuz,
 OrderStatuz = model.OrderStatuz,
 SchoolId = model.SchoolId,
 MainPartId = model.MainPartId,
 MainPartType = model.MainPartType,
 LastApprovalType = model.LastApprovalType,
 SourceFundId = model.SourceFundId,
 FundType = model.FundType,
 ApprovalTime = model.ApprovalTime,


 temp.ProductShelvesId = model.ProductShelvesId,
 temp.MallId = model.MallId,
 temp.CourseId = model.CourseId,
 temp.InstrumentModelId = model.InstrumentModelId,
 temp.ModelDescription = model.ModelDescription,
 temp.Price = model.Price,
 temp.Num = model.Num,
 temp.Sum = model.Sum,
 temp.DeclareTime = model.DeclareTime,
 temp.DeclareUserId = model.DeclareUserId,
 temp.AuditStatuz = model.AuditStatuz,
 temp.OrderStatuz = model.OrderStatuz,
 temp.SchoolId = model.SchoolId,
 temp.MainPartId = model.MainPartId,
 temp.MainPartType = model.MainPartType,
 temp.LastApprovalType = model.LastApprovalType,
 temp.SourceFundId = model.SourceFundId,
 temp.FundType = model.FundType,
 temp.ApprovalTime = model.ApprovalTime,

 ProductDeclareId = item.ProductDeclareId,
 ProductShelvesId = item.ProductShelvesId,
 MallId = item.MallId,
 CourseId = item.CourseId,
 InstrumentModelId = item.InstrumentModelId,
 ModelDescription = item.ModelDescription,
 Price = item.Price,
 Num = item.Num,
 Sum = item.Sum,
 DeclareTime = item.DeclareTime,
 DeclareUserId = item.DeclareUserId,
 AuditStatuz = item.AuditStatuz,
 OrderStatuz = item.OrderStatuz,
 SchoolId = item.SchoolId,
 MainPartId = item.MainPartId,
 MainPartType = item.MainPartType,
 LastApprovalType = item.LastApprovalType,
 SourceFundId = item.SourceFundId,
 FundType = item.FundType,
 ApprovalTime = item.ApprovalTime,

public class ProductDeclareInputModel
{
 [Display(Name = "")] 
    public long ProductDeclareId {get; set; }
    
 [Display(Name = "产品上架Id")] 
    public long ProductShelvesId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "学科Id")] 
    public int CourseId {get; set; }
    
 [Display(Name = "规格Id")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "规格型号描述")] 
    public string ModelDescription {get; set; }
    
 [Display(Name = "价格")] 
    public decimal Price {get; set; }
    
 [Display(Name = "数量")] 
    public decimal Num {get; set; }
    
 [Display(Name = "金额")] 
    public decimal Sum {get; set; }
    
 [Display(Name = "申报时间")] 
    public DateTime DeclareTime {get; set; }
    
 [Display(Name = "申报人Id")] 
    public long DeclareUserId {get; set; }
    
 [Display(Name = "审批状态（1:待学校审批；2： 学校审批退回； 3：待区县审批； 4：区县审批退回；5：区县退回申报人 6：待市级审批；7：市级审批退回；8：市级退回申报人 ；10：待网上采购；20：待线下采购；40：已被删除；）")] 
    public int AuditStatuz {get; set; }
    
 [Display(Name = "订单状态（1：已生成； 9：已取消）")] 
    public int OrderStatuz {get; set; }
    
 [Display(Name = "学校Id")] 
    public int SchoolId {get; set; }
    
 [Display(Name = "主体Id（线下采购，生成订单的单位Id）")] 
    public int MainPartId {get; set; }
    
 [Display(Name = "主体类型（1：市级； 2：区县；3：学校）")] 
    public int MainPartType {get; set; }
    
 [Display(Name = "末级审批单位类型（1：市级； 2：区县；3：学校）")] 
    public int LastApprovalType {get; set; }
    
 [Display(Name = "资金来源Id")] 
    public int SourceFundId {get; set; }
    
 [Display(Name = "资金类型(1：市级；2：区级；3：校级；4：其他)")] 
    public int FundType {get; set; }
    
 [Display(Name = "审批时间")] 
    public DateTime ApprovalTime {get; set; }
    
 }
 
 public class ProductDeclareViewModel
 {
    /// <summary>
    /// 
    /// </summary>
    public long ProductDeclareId {get; set; }
    
    /// <summary>
    /// 产品上架Id
    /// </summary>
    public long ProductShelvesId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 学科Id
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 规格Id
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 规格型号描述
    /// </summary>
    public string ModelDescription {get; set; }
    
    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price {get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal Num {get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Sum {get; set; }
    
    /// <summary>
    /// 申报时间
    /// </summary>
    public DateTime DeclareTime {get; set; }
    
    /// <summary>
    /// 申报人Id
    /// </summary>
    public long DeclareUserId {get; set; }
    
    /// <summary>
    /// 审批状态（1:待学校审批；2： 学校审批退回； 3：待区县审批； 4：区县审批退回；5：区县退回申报人 6：待市级审批；7：市级审批退回；8：市级退回申报人 ；10：待网上采购；20：待线下采购；40：已被删除；）
    /// </summary>
    public int AuditStatuz {get; set; }
    
    /// <summary>
    /// 订单状态（1：已生成； 9：已取消）
    /// </summary>
    public int OrderStatuz {get; set; }
    
    /// <summary>
    /// 学校Id
    /// </summary>
    public int SchoolId {get; set; }
    
    /// <summary>
    /// 主体Id（线下采购，生成订单的单位Id）
    /// </summary>
    public int MainPartId {get; set; }
    
    /// <summary>
    /// 主体类型（1：市级； 2：区县；3：学校）
    /// </summary>
    public int MainPartType {get; set; }
    
    /// <summary>
    /// 末级审批单位类型（1：市级； 2：区县；3：学校）
    /// </summary>
    public int LastApprovalType {get; set; }
    
    /// <summary>
    /// 资金来源Id
    /// </summary>
    public int SourceFundId {get; set; }
    
    /// <summary>
    /// 资金类型(1：市级；2：区级；3：校级；4：其他)
    /// </summary>
    public int FundType {get; set; }
    
    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime? ApprovalTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductShelvesId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductShelvesId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品上架Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentModelId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentModelId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ModelDescription, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ModelDescription, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号描述" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Price, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Price, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入价格" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Num, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Num, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入金额" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DeclareTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DeclareTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入申报时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DeclareUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DeclareUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入申报人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditStatuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditStatuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批状态（1:待学校审批；2： 学校审批退回； 3：待区县审批； 4：区县审批退回；5：区县退回申报人 6：待市级审批；7：市级审批退回；8：市级退回申报人 ；10：待网上采购；20：待线下采购；40：已被删除；）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderStatuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderStatuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单状态（1：已生成； 9：已取消）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MainPartId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MainPartId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入主体Id（线下采购，生成订单的单位Id）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MainPartType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MainPartType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入主体类型（1：市级； 2：区县；3：学校）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LastApprovalType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LastApprovalType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入末级审批单位类型（1：市级； 2：区县；3：学校）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SourceFundId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SourceFundId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资金来源Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FundType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FundType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资金类型(1：市级；2：区级；3：校级；4：其他)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ApprovalTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ApprovalTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批时间" } })                    
                </div>
           </div>
  




 { field: 'ProductShelvesId', title: '产品上架Id', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'CourseId', title: '学科Id', sortable: true },
                 
 { field: 'InstrumentModelId', title: '规格Id', sortable: true },
                 
 { field: 'ModelDescription', title: '规格型号描述', sortable: true },
                 
 { field: 'Price', title: '价格', sortable: true },
                 
 { field: 'Num', title: '数量', sortable: true },
                 
 { field: 'Sum', title: '金额', sortable: true },
                 
 { field: 'DeclareTime', title: '申报时间', sortable: true },
                 
 { field: 'DeclareUserId', title: '申报人Id', sortable: true },
                 
 { field: 'AuditStatuz', title: '审批状态（1:待学校审批；2： 学校审批退回； 3：待区县审批； 4：区县审批退回；5：区县退回申报人 6：待市级审批；7：市级审批退回；8：市级退回申报人 ；10：待网上采购；20：待线下采购；40：已被删除；）', sortable: true },
                 
 { field: 'OrderStatuz', title: '订单状态（1：已生成； 9：已取消）', sortable: true },
                 
 { field: 'SchoolId', title: '学校Id', sortable: true },
                 
 { field: 'MainPartId', title: '主体Id（线下采购，生成订单的单位Id）', sortable: true },
                 
 { field: 'MainPartType', title: '主体类型（1：市级； 2：区县；3：学校）', sortable: true },
                 
 { field: 'LastApprovalType', title: '末级审批单位类型（1：市级； 2：区县；3：学校）', sortable: true },
                 
 { field: 'SourceFundId', title: '资金来源Id', sortable: true },
                 
 { field: 'FundType', title: '资金类型(1：市级；2：区级；3：校级；4：其他)', sortable: true },
                 
 { field: 'ApprovalTime', title: '审批时间', sortable: true },
                 
o.ProductShelvesId,                 
o.MallId,                 
o.CourseId,                 
o.InstrumentModelId,                 
o.ModelDescription,                 
o.Price,                 
o.Num,                 
o.Sum,                 
o.DeclareTime,                 
o.DeclareUserId,                 
o.AuditStatuz,                 
o.OrderStatuz,                 
o.SchoolId,                 
o.MainPartId,                 
o.MainPartType,                 
o.LastApprovalType,                 
o.SourceFundId,                 
o.FundType,                 
o.ApprovalTime,                 
        
        $('#ProductShelvesId').val(d.data.rows.ProductShelvesId);          
        $('#MallId').val(d.data.rows.MallId);          
        $('#CourseId').val(d.data.rows.CourseId);          
        $('#InstrumentModelId').val(d.data.rows.InstrumentModelId);          
        $('#ModelDescription').val(d.data.rows.ModelDescription);          
        $('#Price').val(d.data.rows.Price);          
        $('#Num').val(d.data.rows.Num);          
        $('#Sum').val(d.data.rows.Sum);          
        $('#DeclareTime').val(d.data.rows.DeclareTime);          
        $('#DeclareUserId').val(d.data.rows.DeclareUserId);          
        $('#AuditStatuz').val(d.data.rows.AuditStatuz);          
        $('#OrderStatuz').val(d.data.rows.OrderStatuz);          
        $('#SchoolId').val(d.data.rows.SchoolId);          
        $('#MainPartId').val(d.data.rows.MainPartId);          
        $('#MainPartType').val(d.data.rows.MainPartType);          
        $('#LastApprovalType').val(d.data.rows.LastApprovalType);          
        $('#SourceFundId').val(d.data.rows.SourceFundId);          
        $('#FundType').val(d.data.rows.FundType);          
        $('#ApprovalTime').val(d.data.rows.ApprovalTime);          

 $('#th_ProductShelvesId').html(' 产品上架Id');               
 $('#th_MallId').html(' 商城Id');               
 $('#th_CourseId').html(' 学科Id');               
 $('#th_InstrumentModelId').html(' 规格Id');               
 $('#th_ModelDescription').html(' 规格型号描述');               
 $('#th_Price').html(' 价格');               
 $('#th_Num').html(' 数量');               
 $('#th_Sum').html(' 金额');               
 $('#th_DeclareTime').html(' 申报时间');               
 $('#th_DeclareUserId').html(' 申报人Id');               
 $('#th_AuditStatuz').html(' 审批状态（1:待学校审批；2： 学校审批退回； 3：待区县审批； 4：区县审批退回；5：区县退回申报人 6：待市级审批；7：市级审批退回；8：市级退回申报人 ；10：待网上采购；20：待线下采购；40：已被删除；）');               
 $('#th_OrderStatuz').html(' 订单状态（1：已生成； 9：已取消）');               
 $('#th_SchoolId').html(' 学校Id');               
 $('#th_MainPartId').html(' 主体Id（线下采购，生成订单的单位Id）');               
 $('#th_MainPartType').html(' 主体类型（1：市级； 2：区县；3：学校）');               
 $('#th_LastApprovalType').html(' 末级审批单位类型（1：市级； 2：区县；3：学校）');               
 $('#th_SourceFundId').html(' 资金来源Id');               
 $('#th_FundType').html(' 资金类型(1：市级；2：区级；3：校级；4：其他)');               
 $('#th_ApprovalTime').html(' 审批时间');               
 
 $('#tr_ProductShelvesId').hide();               
 $('#tr_MallId').hide();               
 $('#tr_CourseId').hide();               
 $('#tr_InstrumentModelId').hide();               
 $('#tr_ModelDescription').hide();               
 $('#tr_Price').hide();               
 $('#tr_Num').hide();               
 $('#tr_Sum').hide();               
 $('#tr_DeclareTime').hide();               
 $('#tr_DeclareUserId').hide();               
 $('#tr_AuditStatuz').hide();               
 $('#tr_OrderStatuz').hide();               
 $('#tr_SchoolId').hide();               
 $('#tr_MainPartId').hide();               
 $('#tr_MainPartType').hide();               
 $('#tr_LastApprovalType').hide();               
 $('#tr_SourceFundId').hide();               
 $('#tr_FundType').hide();               
 $('#tr_ApprovalTime').hide();               

 , "ProductShelvesId" : productShelvesId
 , "MallId" : mallId
 , "CourseId" : courseId
 , "InstrumentModelId" : instrumentModelId
 , "ModelDescription" : modelDescription
 , "Price" : price
 , "Num" : num
 , "Sum" : sum
 , "DeclareTime" : declareTime
 , "DeclareUserId" : declareUserId
 , "AuditStatuz" : auditStatuz
 , "OrderStatuz" : orderStatuz
 , "SchoolId" : schoolId
 , "MainPartId" : mainPartId
 , "MainPartType" : mainPartType
 , "LastApprovalType" : lastApprovalType
 , "SourceFundId" : sourceFundId
 , "FundType" : fundType
 , "ApprovalTime" : approvalTime

 var productShelvesId = $('#o_ProductShelvesId').val();
 var mallId = $('#o_MallId').val();
 var courseId = $('#o_CourseId').val();
 var instrumentModelId = $('#o_InstrumentModelId').val();
 var modelDescription = $('#o_ModelDescription').val();
 var price = $('#o_Price').val();
 var num = $('#o_Num').val();
 var sum = $('#o_Sum').val();
 var declareTime = $('#o_DeclareTime').val();
 var declareUserId = $('#o_DeclareUserId').val();
 var auditStatuz = $('#o_AuditStatuz').val();
 var orderStatuz = $('#o_OrderStatuz').val();
 var schoolId = $('#o_SchoolId').val();
 var mainPartId = $('#o_MainPartId').val();
 var mainPartType = $('#o_MainPartType').val();
 var lastApprovalType = $('#o_LastApprovalType').val();
 var sourceFundId = $('#o_SourceFundId').val();
 var fundType = $('#o_FundType').val();
 var approvalTime = $('#o_ApprovalTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品上架Id' : '产品名称', d.data.rows.ProductShelvesId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科Id' : '产品名称', d.data.rows.CourseId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格Id' : '产品名称', d.data.rows.InstrumentModelId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号描述' : '产品名称', d.data.rows.ModelDescription);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '价格' : '产品名称', d.data.rows.Price);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '数量' : '产品名称', d.data.rows.Num);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '金额' : '产品名称', d.data.rows.Sum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '申报时间' : '产品名称', d.data.rows.DeclareTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '申报人Id' : '产品名称', d.data.rows.DeclareUserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批状态（1:待学校审批；2： 学校审批退回； 3：待区县审批； 4：区县审批退回；5：区县退回申报人 6：待市级审批；7：市级审批退回；8：市级退回申报人 ；10：待网上采购；20：待线下采购；40：已被删除；）' : '产品名称', d.data.rows.AuditStatuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单状态（1：已生成； 9：已取消）' : '产品名称', d.data.rows.OrderStatuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校Id' : '产品名称', d.data.rows.SchoolId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '主体Id（线下采购，生成订单的单位Id）' : '产品名称', d.data.rows.MainPartId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '主体类型（1：市级； 2：区县；3：学校）' : '产品名称', d.data.rows.MainPartType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '末级审批单位类型（1：市级； 2：区县；3：学校）' : '产品名称', d.data.rows.LastApprovalType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资金来源Id' : '产品名称', d.data.rows.SourceFundId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资金类型(1：市级；2：区级；3：校级；4：其他)' : '产品名称', d.data.rows.FundType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批时间' : '产品名称', d.data.rows.ApprovalTime);



