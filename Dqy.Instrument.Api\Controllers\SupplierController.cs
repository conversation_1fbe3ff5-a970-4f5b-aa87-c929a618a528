﻿using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Search;
using Dqy.Instrument.UI.ViewModels.SearchViewModels;
using Dqy.Instrument.Api.CommonLib;
using Dqy.Instrument.Api.Filters;
using Dqy.Instrument.Api.Common;

namespace Dqy.Instrument.Api.Controllers
{

    [RoutePrefix("api/supplier")]
    public class SupplierController : ApiController
    {
        private readonly IPProductApplicationService _productApplicationService;
        private readonly IPProductNormPriceApplicationService _priceService;
        private readonly IUSupplierInMallApplicationService _supplierInMallService;
        private readonly IPProductShelfApplicationService _productShelfApplicationService;
        private readonly IUUnitApplicationService _unitApplicationService;
        private readonly IPOrderApplicationService _orderApplicationService;
        private readonly IBEduAreaApplicationService _eduAreaApplication;
        private readonly IUAttachmentApplicationService _uattachmentService;
        private readonly IUUnitContractApplicationService _unitContractService;
        private readonly IUSupplierSchoolAuditApplicationService _iUSupplierSchoolAuditApplicationService;
        private readonly IBInstrumentModelApplicationService _modelService;
        private readonly IPProductDeclareApplicationService _productDeclareApplicationService;
        private readonly IAAuditLogApplicationService _auditLogApplicationService;
        private readonly IPProductRecordApplicationService _productRecordService;
        private readonly IUSupplierMarketAreaApplicationService _supplierMarketAreaService;
        public SupplierController(IPProductApplicationService productApplicationService, IUSupplierInMallApplicationService supplierInMallService, IPProductShelfApplicationService productShelfApplicationService, IUUnitApplicationService unitApplicationService, IPOrderApplicationService orderApplicationService, IBEduAreaApplicationService eduAreaApplication, IUAttachmentApplicationService uattachmentService, IUUnitContractApplicationService unitContractService, IUSupplierSchoolAuditApplicationService iUSupplierSchoolAuditApplicationService, IBInstrumentModelApplicationService modelService, IPProductNormPriceApplicationService priceService, IPProductDeclareApplicationService productDeclareApplicationService, IAAuditLogApplicationService auditLogApplicationService, IPProductRecordApplicationService productRecordService, IUSupplierMarketAreaApplicationService supplierMarketAreaService)
        {
            _productApplicationService = productApplicationService;
            _supplierInMallService = supplierInMallService;
            _productShelfApplicationService = productShelfApplicationService;
            _unitApplicationService = unitApplicationService;
            _orderApplicationService = orderApplicationService;
            _eduAreaApplication = eduAreaApplication;
            _uattachmentService = uattachmentService;
            _unitContractService = unitContractService;
            _iUSupplierSchoolAuditApplicationService = iUSupplierSchoolAuditApplicationService;
            _modelService = modelService;
            _priceService = priceService;
            _productDeclareApplicationService = productDeclareApplicationService;
            _auditLogApplicationService = auditLogApplicationService;
            _productRecordService = productRecordService;
            _supplierMarketAreaService = supplierMarketAreaService;
        }

        #region 公共方法( 获取业务罗晋分类Pid、)
        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <param name="id">获取当前级别的Pid</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getinstrument")]
        public async Task<List<SelectListViewModel>> GetInstrument(int id)
        {
            return await Task.Run(() =>
            {
                return _productShelfApplicationService.GetInstrumentLogicList(id);
            });
        }


        #endregion

        #region 产品录入
        /// <summary>
        /// lss添加和修改产品（获取添加，修改页面的Model）
        /// </summary>
        /// <param name="id">产品Id</param>
        /// <returns>产品详情的Model</returns>
        [HttpGet]
        [Route("getproduct")]
        public async Task<QueryResult<ProductInputModel>> GetProduct(int id, int unitId, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<ProductInputModel> ret = new QueryResult<ProductInputModel>();
                //ProductViewModel modeddl = new ProductViewModel();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -2;
                    ret.msg = "权限不足。。";
                    return ret;
                }

                return _productShelfApplicationService.GetProductModel(id);
            });
        }


        [HttpGet]
        [Route("findproduct")]
        [AntiSqlInject]
        public async Task<QueryResult<ProductShelfViewModel>> FindProduct(string code, int unitId, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> ret = new QueryResult<ProductShelfViewModel>();
                //ProductViewModel modeddl = new ProductViewModel();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -2;
                    ret.msg = "权限不足。。";
                    return ret;
                }
                SearchArgumentsInputModel args = new SearchArgumentsInputModel();
                args.UnitId = sessionBag.UnitId;
                args.UserId = sessionBag.UserId;
                args.Limit = 50;
                code = StringFilter.SearchSql(code);
                ret = _productApplicationService.GetProductList(args, code);

                return ret;
            });
        }
        /// <summary>
        /// lss 获取分类的方法 select2查询
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchclassify")]
        public async Task<QueryResult<Select2ViewModel>> SearchClassify(SearchProductEvaluateInputModel args)
        {
            return await Task.Run(() =>
            {
                return _productApplicationService.SearchClassify(args);
            });
        }

        /// <summary>
        /// lss根据三级分类设备Id获取对应的学科
        /// </summary>
        /// <param name="id">产品Id</param>
        /// <returns>Json格式学科列表集合</returns>
        [HttpGet]
        [Route("getclassifycourse")]
        public async Task<List<SelectListViewModel>> GetClassifyCourse(int id)
        {
            return await Task.Run(() =>
            {
                if (id <= 0)
                {
                    return null;
                }
                return _productApplicationService.GetClassifyCourse(id);
            });
        }

        /// <summary>
        /// lss获取当前分类条目是否有规格型号配置(根据标准分类Id)
        /// </summary>
        /// <param name="id">标准分类Id</param>
        /// <returns>Json格式的字段列表</returns>

        [HttpGet]
        [Route("getmodellist")]
        public async Task<QueryResult<InstrumentLogicStandardInputModel>> GetModelList(int id)
        {
            QueryResult<InstrumentLogicStandardInputModel> ret = new QueryResult<InstrumentLogicStandardInputModel>();
            if (id <= 0)
            {
                ret.flag = 0;
                ret.msg = "你获取的数据不存在。";
                return ret;
            }
            return await Task.Run(() =>
            {
                ret = _modelService.GetModelList(id);

                return ret;

            });
        }
        /// <summary>
        /// lss保存（添加、修改产品的保存）
        /// </summary>
        /// <param name="model">保存实体（图片、价格、学段、学科</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [HttpPost]
        [Route("saveproduct")]
        [ValidateModel]
        public async Task<ReturnResult> SaveProduct(ProductInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "权限不足，无法访问。";
                    return r;
                }
                model.Statuz = ProductStatuz.InLiBray.ToInt();//直接入库
                r = _productApplicationService.SaveProduct(model);

                //删除索引
                var ids = (string)r.data.rows;
                if (!string.IsNullOrEmpty(ids))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.DeleteProductIndex(r.data.rows.ToString());
                    //添加日志。
                    ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                    record.InsertBatch("", ProductAction.Shelf, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "产品修改");
                }

                return r;
            });
        }

        /// <summary>
        /// lss保存（添加、修改产品详情的保存）
        /// </summary>
        /// <param name="model">保存实体（图片、价格、学段、学科</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [HttpPost]
        [Route("saveproductdetail")]
        [ValidateModel]
        public async Task<ReturnResult> SaveProductDetail(PProductDetailEditInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "权限不足，无法访问。";
                    return r;
                }
                r = _productApplicationService.SaveProductDetail(model); 
                return r;
            });
        }

        /// <summary>
        /// lss保存（del产品详情的）
        /// </summary>
        /// <param name="model">删除产品详情</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [HttpPost]
        [Route("delproductdetail")]
        [ValidateModel]
        public async Task<ReturnResult> DelProductDetail(PProductDetailEditInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "权限不足，无法访问。";
                    return r;
                }
                r = _productApplicationService.DelProductDetail(model);
                return r;
            });
        }
        #endregion


        #region lss 商城列表、商城申请入驻

        /// <summary>
        /// lss供应商-商城管理列表。
        /// </summary>
        /// <param name="args">列表搜索条件</param>
        /// <returns>商城列表</returns>
        [HttpPost]
        [Route("searchmall")]
        public async Task<QueryResult<SupplierInMallViewModel>> SearchMall(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierInMallViewModel> ret = new QueryResult<SupplierInMallViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }

                return _supplierInMallService.SearchSupplierInMall(args, MallStatuz.Normal, (int)args.Status);
            });
        }

        /// <summary>
        /// lss获取供应商-申请入驻Model实体（展示申请页面）。
        /// </summary>
        /// <param name="mallId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("applyfor")]
        public async Task<QueryResult<SupplierApplyForViewModel>> ApplyFor(int mallId = 0, int unitId = 0, int userId = 0, string token = "")
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierApplyForViewModel> ret = new QueryResult<SupplierApplyForViewModel>();

                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId || unitId == 0 || userId == 0)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }

                ret.Entity = _supplierInMallService.GetSupplierApplayInMall(unitId, mallId);

                return ret;

            });
        }

        /// <summary>
        ///  保存-申请入驻
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns> 
        [HttpPost]
        [Route("saveapplyfor")]
        [ValidateModel]
        public async Task<ReturnResult> SaveApplyFor(SupplierInMallInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }

                model.RegTime = DateTime.Now;
                r = _supplierInMallService.SaveApplyFor(model);

                //添加索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchDealerIndex index = new SearchDealerIndex(_supplierInMallService);
                    index.AddDealerIndex(r.data.rows.ToString());
                }

                return r;
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id">入驻平台Id （supplierInmallId</param>
        /// <param name="unitId">单位Id</param>
        /// <param name="userId">操作用户Id</param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getinmalllink")]
        public async Task<QueryResult<InMallLinkInputModel>> GetInMallLink(int id, int unitId, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<InMallLinkInputModel> ret = new QueryResult<InMallLinkInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -2;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                ret = _supplierInMallService.GetInMallLink(id, unitId, userId);
                return ret;
            });
        }

        [HttpPost]
        [Route("saveinmalllink")]
        [ValidateModel]
        public async Task<ReturnResult> SaveInMallLink(InMallLinkInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }

                r = _supplierInMallService.SaveInMallLink(model);

                return r;
            });
        }
        #endregion

        #region 产品列表、单个产品上架、多个产品上架、产品删除

        /// <summary>
        /// 产品修改分类查询列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchclassifyproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchClassifyProduct([FromBody] SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> ret = new QueryResult<ProductShelfViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                return _productApplicationService.SearchClassifyProduct(args);
            });
        }

        /// <summary>
        /// 产品上架列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchProduct([FromBody] SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
             {
                 QueryResult<ProductShelfViewModel> ret = new QueryResult<ProductShelfViewModel>();
                 var sessionBag = SessionContainer.GetSession(args.Token);
                 if (sessionBag == null)
                 {
                     ret.flag = -1;
                     ret.msg = "登录超时，请重新登录。";
                     return ret;
                 }
                 if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                 {
                     ret.flag = -1;
                     ret.msg = "登录超时，请重新登录。";
                     return ret;
                 }
                 if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                 {
                     ret.flag = -2;
                     ret.msg = "登录超时，请重新登录。";
                     return ret;
                 }
                 return _productApplicationService.SearchSupplierProduct(args);
             });
        }

        /// <summary>
        /// 单个产品上架，获取Model展示页面
        /// </summary>
        /// <param name="id">产品Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getproductshelf")]
        public async Task<QueryResult<SignleShelfInputModel>> GetProductShelf(long id, int unitId, long userId, string token = "")
        {
            //状态为1的为入驻审核通过的。
            return await Task.Run(() =>
            {
                QueryResult<SignleShelfInputModel> ret = new QueryResult<SignleShelfInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }
                SignleShelfInputModel entity = new SignleShelfInputModel();
                var priceEntity = _priceService.GetModel(id);
                if (priceEntity != null)
                {
                    entity.Price = (decimal)priceEntity.Price;
                }
                var productEntity = _productApplicationService.GetModel(id);
                if (productEntity != null)
                {
                    entity.WarrantyMonth = (WarrantyMonth)productEntity.WarrantyMonth;
                }
                else
                {
                    entity.WarrantyMonth = WarrantyMonth.Month12;
                }
                entity.ProductId = id;
                entity.MallList = _productApplicationService.GetSupplierInMall(unitId);//单位id
                entity.CertificationStatuz = _unitApplicationService.GetUnitApplyAuthen(unitId);
                entity.Freight = FreightType.yes;
                ret.Entity = entity;
                return ret;
            });


        }

        /// <summary>
        /// 单个产品上架，保存
        /// </summary>
        /// <param name="SignleShelfInputModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveproductshelf")]
        [ValidateModel]
        public async Task<ReturnResult> SaveProductShelf(SignleShelfInputModel model)
        {
            //保存产品上架
            //数据校验。 
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.BaseUnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "权限不足，无法操作。";
                    return r;
                }
                r = _productApplicationService.SaveProductShelf(model);

                //添加索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.AddProductIndex(r.data.rows.ToString());

                    //添加日志。
                    ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                    record.InsertBatch("", ProductAction.Shelf, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "产品上架，无需审核");
                }
                return r;
            });
        }


        /// <summary>
        /// 批量产品上架，获取Model展示页面
        /// </summary>
        /// <param name="ids"> 批量产品Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbatchshelf")]
        [AntiSqlInject]
        public async Task<QueryResult<BatchShelfInputModel>> GetBatchShelf(string ids, int unitId, long userId, string token)
        {
            QueryResult<BatchShelfInputModel> ret = new QueryResult<BatchShelfInputModel>();
            var sessionBag = SessionContainer.GetSession(token);
            if (sessionBag == null)
            {
                ret.flag = -1;
                ret.msg = "登录超时，请重新登录。";
                return ret;
            }
            if (sessionBag.UnitId != unitId)
            {
                ret.flag = -1;
                ret.msg = "登录超时，请重新登录。";
                return ret;
            }
            if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
            {
                ret.flag = -2;
                ret.msg = "您无权操作此功能。";
                return ret;
            }
            BatchShelfInputModel model = new BatchShelfInputModel();
            //获取产品列表
            var productList = await Task.Run(() =>
            {
                ids = StringFilter.IdsArrSql(ids);
                  return _productApplicationService.GetShelfProduct(unitId, ids);//输入当前单位Id
            });
            model.Ps = productList;

            var mallList = await Task.Run(() =>
            {
                //获取商城列表
                return _productApplicationService.GetSupplierInMall(unitId, null);//传递当前登录用户的单位（UnitId）
            });
            if (mallList == null || mallList.Count <= 0)
            {
                //数据异常，无法获取入驻的商城。
            }
            model.MallList = mallList;// new System.Web.Mvc.SelectList(mallList, "MallId", "Name", mallList[0].MallId);
            model.F = FreightType.yes;
            model.CertificationStatuz = _unitApplicationService.GetUnitApplyAuthen(unitId);
            ret.Entity = model;
            ret.flag = 1;
            return ret;
        }

        /// <summary>
        ///批量产品上架，保存
        /// </summary>
        /// <param name="BatchShelfInputModel">批量对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("savebatchproductshelf")]
        [ValidateModel]
        public async Task<ReturnResult> SaveBatchProductShelf(BatchShelfInputModel model)
        {
            //保存产品上架
            //数据校验。 
            return await Task.Run(() =>
            {

                ReturnResult r = new ReturnResult();
                try
                {
                    var sessionBag = SessionContainer.GetSession(model.Token);
                    if (sessionBag == null)
                    {
                        r.flag = -1;
                        r.msg = "登录超时，请重新登录。";
                        return r;
                    }
                    if (sessionBag.UnitId != model.BaseUnitId)
                    {
                        r.flag = -1;
                        r.msg = "登录信息验证失败，请重新登录。";
                        return r;
                    }
                    if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                    {
                        r.flag = -2;
                        r.msg = "您无权操作此功能。";
                        return r;
                    }
                    r = _productApplicationService.SaveBatchProductShelf(model);
                    //添加索引
                    if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                    {
                        SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                        index.AddProductIndex(r.data.rows.ToString());

                        //添加日志。
                        ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                        record.InsertBatch("", ProductAction.Shelf, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "产品上架，无需审核");

                    }
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog("批量上架：" + e.Message);
                    r.flag = -3;
                    r.msg = "批量上架异常。";
                }
                return r;
            });
        }

        /// <summary>
        /// 产品删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("productdel")]
        public async Task<ReturnResult> ProductDel(string ids, int unitId, int userId, string token)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != unitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _productApplicationService.ProductDel(ids, unitId);
                ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                record.InsertBatch(ids, ProductAction.ShelfDel, Default.IsTrue, sessionBag.UserId, 0, "", "产品删除");
                return r;
            });
        }

        /// <summary>
        /// 标记为核心产品
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("productsetcore")]
        [ValidateModel]
        public async Task<ReturnResult> ProductSetCore(SetCoreInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.BaseUnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                return _productApplicationService.ProductSetCore(model);
            });
        }

        /// <summary>
        /// 设置库存
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("productsetstock")]
        [ValidateModel]
        public async Task<ReturnResult> ProductSetStock(SetStockInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.BaseUnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                return _productApplicationService.ProductSetStock(model);

            });
        }

        #endregion
        #region 销售范围获取保存
        /// <summary>
        /// 供应商根据申请的商场，获取销售区域列表不分页。
        /// </summary>
        /// <param name="token"></param>
        /// <param name="mallid"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getmarketarea")]
        public async Task<QueryResult<SupplierMarketAreaViewModel>> GetMarketArea(string token, int mallid)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierMarketAreaViewModel> ret = new QueryResult<SupplierMarketAreaViewModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }

                return _supplierMarketAreaService.GetMarketArea(mallid, sessionBag.UnitId);
            });
        }
        /// <summary>
        /// 供应商申请销售区域审核
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("applymarketarea")]
        [ValidateModel]
        public async Task<ReturnResult> ApplyMarketArea(SupplierMarketAreaInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierMarketAreaService.ApplyMarketArea(model);

                return r;
            });
        }
        /// <summary>
        /// 平台管理员，审核销售区域查询统计列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("getmarketareaaudit")]
        public async Task<QueryResult<SupplierMarketAreaViewModel>> GetMarketAreaAuditList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierMarketAreaViewModel> ret = new QueryResult<SupplierMarketAreaViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (!(sessionBag.UserType == UserRoleType.MallAdministrator.ToInt() || sessionBag.UserType == UserRoleType.SellerAdmin.ToInt() || sessionBag.UserType == UserRoleType.SellerMallAdmin.ToInt() || sessionBag.UserType == UserRoleType.SellerMallAdmin.ToInt()))
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }
                int supplierid = 0;
                if (sessionBag.UserType == UserRoleType.SellerAdmin.ToInt() || sessionBag.UserType == UserRoleType.SellerMallAdmin.ToInt() || sessionBag.UserType == UserRoleType.SellerMallAdmin.ToInt())
                {
                    supplierid = sessionBag.UnitId;
                }

                return _supplierMarketAreaService.GetMarketAreaAuditList(args, supplierid);
            });
        }
        /// <summary>
        /// 平台管理员，审核销售区域
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("auditmarketarea")]
        [ValidateModel]
        public async Task<ReturnResult> AuditMarketArea(SupplierMarketAreaInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (model.Statuz == 3 || model.Statuz == 4)
                {
                    if (model.Statuz == 3 && (model.Reason == null || model.Reason.Trim().Length == 0))
                    {
                        r.flag = 0;
                        r.msg = "请审核不通过，请填写原因。";
                        return r;
                    }
                }
                else
                {
                    r.flag = 0;
                    r.msg = "请选择审核结果。";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierMarketAreaService.AuditMarketArea(model);
                if (r.flag == 1)
                {
                    SearchMarketAreaIndex index = new SearchMarketAreaIndex(_productShelfApplicationService);
                    index.UpdateMarketAreaIndex(model.MallId, model.UnitId);

                }
                return r;
            });
        }
        /// <summary>
        /// 获取销售区域申请实体信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getmarketareamodel")]
        public async Task<QueryResult<SupplierMarketAreaInputModel>> GetMarketAreaModel(string token, int id)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierMarketAreaInputModel> ret = new QueryResult<SupplierMarketAreaInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                return _supplierMarketAreaService.GetModel(id, sessionBag.CurrentMallId); 
            });
        }

        #endregion

        #region 版本2 销售区域管理

        [HttpPost]
        [Route("getareaall")]
        public async Task<QueryResult<EduAreaViewModel>> GetEduAreaAll(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<EduAreaViewModel> ret = new QueryResult<EduAreaViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }
                var list = _eduAreaApplication.GetAll(); 
                if (list != null && list.Count > 0)
                {
                    ret.flag = 1;
                    ret.msg = "查询成功。";
                    ret.Data = list;
                    ret.TotalCount = list.Count;

                    var result = _supplierMarketAreaService.GetLastAddCity(args);
                    if (result != null && result.Entity!=null)
                    {
                        ret.Entity = result.Entity;
                    }
                    else
                    {
                        var unitEntity = _unitApplicationService.GetCurrentUnitInfo(sessionBag.UnitId);
                        if (unitEntity != null)
                        {
                            ret.Entity = new EduAreaViewModel()
                            {
                                EduAreaId = unitEntity.CountyId,
                                PId = unitEntity.CityId
                            };
                        }
                    }
                }
                return ret;
            });
        }

        /// <summary>
        /// 供应商添加销售区域
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addmarketarea")]
        [ValidateModel]
        public async Task<ReturnResult> AddMarketArea(SupplierMarketAreaInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierMarketAreaService.AddMarketArea(model);

                return r;
            });
        }

        /// <summary>
        /// 供应商撤销销售区域申请
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("cancelapply")]
        [ValidateModel]
        public async Task<ReturnResult> CancelMarketAreaApply(SupplierMarketAreaInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierMarketAreaService.CancelMarketAreaApply(model);

                return r;
            });
        }

        #endregion
        #region 产品备注信息
        /// <summary>
        /// 商城备注信息Model
        /// </summary>
        /// <param name="mallId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getremark")]
        public async Task<QueryResult<SupplierRemarkInputModel>> GetRemark(int mallId, int unitId, int userId, FreightType freight, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierRemarkInputModel> ret = new QueryResult<SupplierRemarkInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = 0;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = 0;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = 0;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                ret.Entity = _supplierInMallService.GetSupplierRemark(mallId, unitId, userId, freight);//输入单位Id 
                ret.flag = 1;
                ret.msg = "执行成功。";
                return ret;
            });
        }

        [HttpPost]
        [Route("saveremark")]
        [ValidateModel]
        public async Task<ReturnResult> SaveRemark(SupplierRemarkInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.BaseUnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                model.RegTime = DateTime.Now;
                return _supplierInMallService.SaveSupplierRemark(model);
            });
        }

        #endregion  

        #region 周跃峰(学校保存、申请认证、重新认证处理)
        /// <summary>
        /// 根据单位供应商单位Id获取供应商信息
        /// </summary>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsupplierinformation")]
        public async Task<ReturnResult> GetSupplierInformation(string token, int UnitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _unitApplicationService.GetSupplierInfoById(UnitId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 根据单位供应商单位Id获取申请认证信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="UnitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsupplierbyunitid")]
        public async Task<ReturnResult> GetSupplierByUnitId(string token, int UnitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _iUSupplierSchoolAuditApplicationService.GetSupplierByUnitId(UnitId);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 保存供应商信息
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsavesupplierinformation")]
        public async Task<ReturnResult> PostSaveSupplierInformation([FromBody] SupplierInfoInputModel model)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(model.Name) || string.IsNullOrEmpty(model.SocialCreditCode)
                || string.IsNullOrEmpty(model.Introduction) || string.IsNullOrEmpty(model.BusLicense)
                || string.IsNullOrEmpty(model.Logo) || string.IsNullOrEmpty(model.MainProduct))
                {
                    r.flag = -2;
                    r.msg = "请检查企业全称、统一社会信用代码、公司简介、营业执照、企业Logo、主营产品是否输入。";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _unitApplicationService.UpdateSupplierInfo(model);
                //此处要查找"入住商城u_SupplierInMall"表查看是否存在多个，SupplierInMallId
                //修改索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchDealerIndex index = new SearchDealerIndex(_supplierInMallService);
                    index.AddDealerIndex(r.data.rows.ToString());
                }
                return r;
            });
            return result;
        }

        /// <summary>
        /// 供应商申请认证
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postapplysupplierinfo")]
        public async Task<ReturnResult> PostApplySupplierInfo([FromBody] SupplierInfoInputModel model)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(model.Name) || string.IsNullOrEmpty(model.SocialCreditCode)
               || string.IsNullOrEmpty(model.Introduction) || string.IsNullOrEmpty(model.BusLicense)
               || string.IsNullOrEmpty(model.Logo) || string.IsNullOrEmpty(model.MainProduct))
                {
                    r.flag = -2;
                    r.msg = "请检查企业全称、统一社会信用代码、公司简介、营业执照、企业Logo、主营产品是否输入。";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _unitApplicationService.ApplySupplierInfo(model);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 重新申请认证
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsaveapply")]
        public async Task<ReturnResult> PostSaveApply([FromBody] SupplierInfoInputModel model)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(model.Name) || string.IsNullOrEmpty(model.SocialCreditCode))
                {
                    r.flag = -2;
                    r.msg = "请检查企业全称、统一社会信用代码是否输入。";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.UnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _unitApplicationService.SaveApply(model);
                return r;
            });
            return result;
        }

        #endregion

        #region lss 商城管理  ，产品退回列表 、在上架、删除
        /// <summary>
        /// 商城产品退回数量
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchsuppliermall")]
        public async Task<QueryResult<SupplierInMallViewModel>> SearchSupplierMall(SearchArgumentsInputModel args)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run(() =>
            {
                QueryResult<SupplierInMallViewModel> r = new QueryResult<SupplierInMallViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -2;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }

                return _supplierInMallService.SearchSupplierInMall(args, MallStatuz.Normal, (int)SupplierInMallStatuz.ApprovalAdopt);
            });
            return result;
        }

        /// <summary>
        /// 产品退回列表（供应商入驻平台）
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchbackoutproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchBackoutProduct(SearchArgumentsInputModel args)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> r = new QueryResult<ProductShelfViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -2;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                ProductShelfViewModel entity = new ProductShelfViewModel();
                List<UI.InputModels.ShelfMallInputModel> mallList = _supplierInMallService.GetSupplierInMall((int)args.UnitId);
                entity.MallId = args.MallId;
                if (mallList != null && mallList.Count > 0)
                {
                    entity.MallList = (from item in mallList
                                       select new MallViewModel()
                                       {
                                           Name = item.Name,
                                           MallId = item.MallId

                                       }).ToList();
                    if (args.MallId <= 0)
                    {
                        args.MallId = mallList[0].MallId;
                        entity.MallId = mallList[0].MallId;
                    }
                }
                r = _productShelfApplicationService.SearchSupplierMallProduct(args, ProductShelfStatuz.AuditNoPass.ToInt().ToString());
                r.Entity = entity;
                return r;
            });
            return result;
        }

        /// <summary>
        /// lss商城 退回产品删除（支持批量）
        /// </summary>
        /// <param name="ids">上架表Id</param>
        /// <param name="mallId">商城Id</param>
        /// <param name="unitId">供应商Id</param>
        /// <param name="userId">操作用户</param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("backoutproductdel")]
        public async Task<ReturnResult> BackoutProductDel(string ids, int mallId, int unitId, long userId, string token)
        {
            //状态为1的为入驻审核通过的。
            return await Task.Run(() =>
           {
               ReturnResult r = new ReturnResult();
               var sessionBag = SessionContainer.GetSession(token);
               if (sessionBag == null)
               {
                   r.flag = -1;
                   r.msg = "登录超时，请重新登录。";
                   return r;
               }
               if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
               {
                   r.flag = -1;
                   r.msg = "登录信息验证失败，请重新登录。";
                   return r;
               }
               //判断用户是否为 卖家超管 或者  卖家平台管理员
               if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
               {
                   r.flag = -1;
                   r.msg = "操作失败，权限不足。";
                   return r;
               }
               ids = StringFilter.IdsArrSql(ids);
               r = _productShelfApplicationService.ShelfProductDel(ids, unitId, mallId);

               //添加日志。
               ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
               record.InsertBatch("", ProductAction.ShelfDel, Default.IsTrue, sessionBag.UserId, 0, ids, "产品下架");
               return r;

           });
        }
        #endregion

        #region  lss 商城统计

        /// <summary>
        /// 商城统计
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchsupplierstatistic")]
        public async Task<QueryResult<SupplierStatisticViewModel>> SearchSupplierStatistic(SearchArgumentsInputModel args)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run(() =>
            {
                QueryResult<SupplierStatisticViewModel> r = new QueryResult<SupplierStatisticViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -2;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                return _supplierInMallService.SearchSupplierStatistic(args, (int)SupplierInMallStatuz.ApprovalAdopt);
            });
            return result;
        }

        #endregion

        #region 已上架产品管理列表   、修改 、下架、更新
        /// <summary>
        /// 供应商已上架产品管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchshelfproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchShelfProduct(SearchArgumentsInputModel args)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> r = new QueryResult<ProductShelfViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -1;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                ProductShelfViewModel entity = new ProductShelfViewModel();
                List<UI.InputModels.ShelfMallInputModel> mallList = _supplierInMallService.GetSupplierInMall((int)args.UnitId);
                entity.MallId = args.MallId;
                if (mallList != null && mallList.Count > 0)
                {
                    entity.MallList = (from item in mallList
                                       select new MallViewModel()
                                       {
                                           Name = item.Name,
                                           MallId = item.MallId

                                       }).ToList();
                    if (args.MallId <= 0)
                    {
                        args.MallId = mallList[0].MallId;
                        entity.MallId = mallList[0].MallId;
                    }
                }
                string statuz = ProductShelfStatuz.AuditAdopt.ToInt().ToString() + "," + ProductShelfStatuz.StopSale.ToInt().ToString();
                r = _productShelfApplicationService.SearchSupplierMallProduct(args, statuz, 1);
                r.Entity = entity;
                return r;
            });
            return result;
        }


        /// <summary>
        /// 上架产品，下架
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("shelfproductoff")]
        public async Task<ReturnResult> ShelfProductOff(string ids, int mallId, int unitId, long userId, string token)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -2;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
               ids = StringFilter.IdsArrSql(ids);
                r = _productShelfApplicationService.ShelfProductOff(ids, unitId, mallId);

                //删除索引索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.DeleteProductIndex(r.data.rows.ToString());

                }
                //添加日志。
                ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                record.InsertBatch("", ProductAction.ShelfEdit, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "产品下架");

                return r;

            });
            return result;
        }


        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="id">产品上架Id</param>
        /// <param name="mallId">商城Id</param>
        /// <param name="unitId">单位Id</param>
        /// <param name="userId">当前操作用户id</param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("shelfproductrenewal")]
        public async Task<ReturnResult> ShelfProductRenewal(int id, int mallId, int unitId, long userId, string token)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -2;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }

                return _productShelfApplicationService.ShelfProductRenewal(id, unitId, mallId);
            });
            return result;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="id">产品上架Id</param>
        /// <param name="mallId"></param>
        /// <param name="unitid"></param>
        /// <param name="userId"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("editshelf")]
        public async Task<QueryResult<EditSignleShelfInputModel>> EditShelf(long id, int mallId, int unitid, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<EditSignleShelfInputModel> ret = new QueryResult<EditSignleShelfInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitid != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                ret.Entity = _productShelfApplicationService.GetModel(id, unitid, mallId);
                return ret;
            });
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("saveeditshelf")]
        [ValidateModel]
        public async Task<ReturnResult> SaveEditShelf(EditSignleShelfInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _productShelfApplicationService.SaveEditShelf(model);
                if (!string.IsNullOrEmpty(r.data.rows.ToString()) && !string.IsNullOrEmpty(r.data.id.ToString()))
                {
                    if (r.data.id.ToString() == "0")
                    {
                        SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                        index.AddProductIndex(r.data.rows.ToString());
                    }
                    else if (r.data.id.ToString() == "1")
                    {
                        SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                        index.DeleteProductIndex(r.data.rows.ToString());
                    }
                    //添加日志。
                    ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                    record.Insert(0, ProductAction.ShelfEdit, Default.IsTrue, sessionBag.UserId, 0, model.ProductShelfId, "编辑上架产品");
                }
                return r;
            });
        }

        /// <summary>
        /// 上架产品_批量修改列表
        /// </summary>
        /// <param name="id"></param>
        /// <param name="mallId"></param>
        /// <param name="unitid"></param>
        /// <param name="userId"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("editbatchshelf")]
        public async Task<QueryResult<EditSignleShelfInputModel>> EditBatchShelf(long id, int mallId, int unitid, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<EditSignleShelfInputModel> ret = new QueryResult<EditSignleShelfInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitid != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -1;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                //获取该商城的运费信息。
                ret.Entity = _productShelfApplicationService.GetModel(id, unitid, mallId);
                return ret;
            });
        }

        /// <summary>
        /// 上架产品   批量修改列表
        /// </summary>
        /// <param name="id"></param>
        /// <param name="mallId"></param>
        /// <param name="unitid"></param>
        /// <param name="userId"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveeditbatchshelf")]
        [ValidateModel]
        public async Task<ReturnResult> SaveEditBatchShelf(EditBatchShelfInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _productShelfApplicationService.SaveBatchEdit(model);

                if (!string.IsNullOrEmpty(r.data.rows.ToString()) && !string.IsNullOrEmpty(r.data.id.ToString()))
                {
                    if (r.data.id.ToString() == "0")
                    {
                        SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                        index.AddProductIndex(r.data.rows.ToString());
                    }
                    else if (r.data.id.ToString() == "1")
                    {
                        SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                        index.DeleteProductIndex(r.data.rows.ToString());
                    }
                }
                //添加日志。
                ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                record.InsertBatch("", ProductAction.ShelfEdit, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "编辑上架产品");
                return r;
            });
        }

        #endregion

        #region 待审核产品列表

        /// <summary>
        /// 供应商已下架产品管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("auditproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> AuditProduct(SearchArgumentsInputModel args)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run((Func<QueryResult<ProductShelfViewModel>>)(() =>
            {
                QueryResult<ProductShelfViewModel> ret = new QueryResult<ProductShelfViewModel>();
                ProductShelfViewModel entity = new ProductShelfViewModel();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "权限不足，无法操作。";
                    return ret;
                }

                List<UI.InputModels.ShelfMallInputModel> mallList = _supplierInMallService.GetSupplierInMall((int)args.UnitId);
                entity.MallId = args.MallId;
                if (mallList != null && mallList.Count > 0)
                {
                    entity.MallList = (from item in mallList
                                       select new MallViewModel()
                                       {
                                           Name = item.Name,
                                           MallId = item.MallId

                                       }).ToList();
                    if (args.MallId <= 0)
                    {
                        args.MallId = mallList[0].MallId;
                        entity.MallId = mallList[0].MallId;
                    }
                }


                string statuz = ProductShelfStatuz.WaitAudit.ToInt().ToString();

                ret = _productShelfApplicationService.SearchSupplierMallProduct(args, statuz);
                ret.Entity = entity;

                return ret;
            }));
            return result;
        }
        #endregion

        #region 已下架产品管理列表 。再上架、删除

        /// <summary>
        /// 供应商已下架产品管理
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchOffshelfproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchOffShelfProduct(SearchArgumentsInputModel args)
        {
            //状态为1的为入驻审核通过的。
            var result = await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> ret = new QueryResult<ProductShelfViewModel>();

                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "权限不足，无法操作。";
                    return ret;
                }
                ProductShelfViewModel entity = new ProductShelfViewModel();
                List<UI.InputModels.ShelfMallInputModel> mallList = _supplierInMallService.GetSupplierInMall((int)args.UnitId);
                entity.MallId = args.MallId;
                if (mallList != null && mallList.Count > 0)
                {
                    entity.MallList = (from item in mallList
                                       select new MallViewModel()
                                       {
                                           Name = item.Name,
                                           MallId = item.MallId

                                       }).ToList();
                    if (args.MallId <= 0)
                    {
                        args.MallId = mallList[0].MallId;
                        entity.MallId = mallList[0].MallId;
                    }
                }
                string statuz = ProductShelfStatuz.TempOffShelf.ToInt().ToString() + "," + ProductShelfStatuz.ForceOffShelf.ToInt().ToString();

                ret = _productShelfApplicationService.SearchSupplierMallProduct(args, statuz, 2);
                ret.Entity = entity;
                return ret;
            });
            return result;
        }
        #endregion

        #region 在上架  下架在上架，退回在上架 ，单个上架，批量上架
        /// <summary>
        /// 单个产品上架，获取Model展示页面
        /// </summary>
        /// <param name="id">产品Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("signleagainshelf")]
        public async Task<QueryResult<SignleShelfInputModel>> SignleAgainShelf(long id, int unitId, long userId, string token = "")
        {
            //状态为1的为入驻审核通过的。
            return await Task.Run(() =>
            {
                QueryResult<SignleShelfInputModel> ret = new QueryResult<SignleShelfInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }
                SignleShelfInputModel entity = new SignleShelfInputModel();
                var productShelf = _productShelfApplicationService.GetModel(id, unitId, 0);

                var mallEntity = _supplierInMallService.GetMallModel(productShelf.MallId);
                if (mallEntity != null)
                {
                    entity.MallIds = mallEntity.MallId.ToString();
                    entity.MallName = mallEntity.Name;
                }
                else
                {
                    entity.MallIds = "0";
                    entity.MallName = "";
                }
                if (productShelf != null)
                {
                    entity.MallId = productShelf.MallId;
                    entity.ProductShelfId = productShelf.ProductShelfId;
                    entity.ProductId = productShelf.ProductId;
                    entity.Price = productShelf.Price;
                    entity.WarrantyMonth = productShelf.WarrantyMonth;
                    entity.Freight = productShelf.Freight;
                    entity.FreightExplain = productShelf.FreightExplain;
                    entity.SaleRegion = productShelf.SaleRegion;
                    ret.flag = 1;
                }
                else
                {
                    ret.flag = -1;
                    ret.msg = "该产品不存在，无法上架。";
                }


                ret.Entity = entity;
                return ret;
            });


        }

        /// <summary>
        /// 单个产品上架，保存
        /// </summary>
        /// <param name="SignleShelfInputModel"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveproductAginshelf")]
        [ValidateModel]
        public async Task<ReturnResult> SaveSignleAgainShelf(SignleShelfInputModel model)
        {
            //保存产品上架
            //数据校验。 
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UnitId != model.BaseUnitId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "权限不足，无法操作。";
                    return r;
                }

                r = _productShelfApplicationService.SaveSignleAgainShelf(model);
                //添加索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.AddProductIndex(r.data.rows.ToString());

                    //添加日志。
                    ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                    record.Insert(0, ProductAction.Shelf, Default.IsTrue, sessionBag.UserId, 0, model.ProductShelfId, "产品再上架");
                }
                return r;
            });
        }


        /// <summary>
        /// 批量再产品上架，获取Model展示页面
        /// </summary>
        /// <param name="ids"> 批量产品Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("getbatchagainshelf")]
        public async Task<QueryResult<BatchShelfInputModel>> GetBatchAgainShelf(string ids, int mallId, int unitId, long userId, string token)
        {
            QueryResult<BatchShelfInputModel> ret = new QueryResult<BatchShelfInputModel>();
            var sessionBag = SessionContainer.GetSession(token);
            if (sessionBag == null)
            {
                ret.flag = -1;
                ret.msg = "登录超时，请重新登录。";
                return ret;
            }
            if (sessionBag.UnitId != unitId)
            {
                ret.flag = -1;
                ret.msg = "登录超时，请重新登录。";
                return ret;
            }
            if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
            {
                ret.flag = -2;
                ret.msg = "您无权操作此功能。";
                return ret;
            }

            //获取产品列表
            return await Task.Run(() =>
            {
                BatchShelfInputModel model = new BatchShelfInputModel();
                var productList = _productApplicationService.GetAgainShelfProduct(unitId, mallId, ids);//输入当前单位Id

                var mallEntity = _supplierInMallService.GetMallModel(mallId);
                if (mallEntity != null)
                {
                    model.MallName = mallEntity.Name;
                }
                else
                {
                    model.MallName = "";
                }

                model.Ps = productList;

                model.F = FreightType.yes;
                ret.Entity = model;
                ret.flag = 1;
                return ret;

            });
        }

        /// <summary>
        ///批量产品上架，保存
        /// </summary>
        /// <param name="BatchShelfInputModel">批量对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("savebatchAgainshelf")]
        [ValidateModel]
        public async Task<ReturnResult> SaveBatchAgainShelf(BatchShelfInputModel model)
        {
            //保存产品上架
            //数据校验。 
            return await Task.Run(() =>
            {

                ReturnResult r = new ReturnResult();
                try
                {
                    var sessionBag = SessionContainer.GetSession(model.Token);
                    if (sessionBag == null)
                    {
                        r.flag = -1;
                        r.msg = "登录超时，请重新登录。";
                        return r;
                    }
                    if (sessionBag.UnitId != model.BaseUnitId)
                    {
                        r.flag = -1;
                        r.msg = "登录信息验证失败，请重新登录。";
                        return r;
                    }
                    if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                    {
                        r.flag = -2;
                        r.msg = "您无权操作此功能。";
                        return r;
                    }

                    r = _productShelfApplicationService.SaveBatchAgainShelf(model);
                    //添加索引
                    if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                    {
                        SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                        index.AddProductIndex(r.data.rows.ToString());

                        //添加日志。
                        ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                        record.InsertBatch("", ProductAction.Shelf, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "产品再上架");
                    }
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog("批量上架：" + e.Message);
                    r.flag = -3;
                    r.msg = "批量上架异常。";
                }
                return r;
            });
        }
        #endregion

        #region 周跃峰(商城管理：资质审核、产品管理、在售产品、停售产品)

        /// <summary>
        /// 获取资质待审核列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsearchauditlist")]
        public async Task<QueryResult<SupplierAuditViewModel>> PostSearchAuditList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierAuditViewModel> listAudit = new QueryResult<SupplierAuditViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listAudit.flag = -1;
                    return listAudit;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    listAudit.flag = -2;
                    return listAudit;
                }
                listAudit = _supplierInMallService.SearchSupplierInMallAll(args);
                return listAudit;
            });
        }

        /// <summary>
        /// 获取供应商产品待审核列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsearchproductauditlist")]
        public async Task<QueryResult<SupplierProductAuditModel>> PostSearchProductAuditList(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierProductAuditModel> listAudit = new QueryResult<SupplierProductAuditModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listAudit.flag = -1;
                    return listAudit;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    listAudit.flag = -2;
                    return listAudit;
                }
                listAudit = _supplierInMallService.SearchSupplierInMallProductAll(args);
                return listAudit;
            });
        }


        /// <summary>
        /// 获取待审核信息
        /// </summary>
        /// <param name="SupplierInMallId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getsupplierinmallinputmodel")]
        public async Task<ReturnResult> GetSupplierInMallInputModel(string token, long userId, int SupplierInMallId)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能。";
                    return r;
                }
                r = _supplierInMallService.GetSupplierInMallInputModel(SupplierInMallId);
                return r;
            });
        }

        /// <summary>
        /// 保存审核信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsaveaudit")]
        [ValidateModel]
        public async Task<ReturnResult> PostSaveAudit(SupplierAuditInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                r = _supplierInMallService.SaveAudit(model, sessionBag.LoginName);


                //添加日志
                string reason = model.Reason;
                int statuz = int.Parse(r.data.header.ToString());
                if (statuz == 1)
                {
                    reason = "";
                }
                AuditLogInputModel log = new AuditLogInputModel();
                log.MallId = sessionBag.CurrentMallId;
                log.ModuleType = ModuleType.Aptitude;
                log.NodeId = model.SupplierInMallId;
                log.UserId = sessionBag.UserId;
                log.Reason = reason;
                log.AuditResult = int.Parse(r.data.header.ToString());
                log.Ip = model.IpAddress;
                log.LogContent = "" + sessionBag.LoginName + " 【审核入驻供应商】" + model.Name + "【" + r.data.footer + "】";
                _auditLogApplicationService.AddLog(log);

                //添加索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchDealerIndex index = new SearchDealerIndex(_supplierInMallService);
                    index.AddDealerIndex(r.data.rows.ToString());
                }
                return r;
            });
        }


        /// <summary>
        /// 获取待审核产品列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsearchwaitproductaudit")]
        public async Task<QueryResult<ProductAuditViewModel>> PostSearchWaitProductAudit(SearchProductInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<ProductAuditViewModel> listAudit = new QueryResult<ProductAuditViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listAudit.flag = -1;
                    return listAudit;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    listAudit.flag = -2;
                    return listAudit;
                }
                listAudit = _supplierInMallService.SearchWaitProductAudit(args);

                return listAudit;
            });
        }


        /// <summary>
        /// 审核产品
        /// </summary>
        /// <param name="productAudit"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsaveproductaudit")]
        public async Task<ReturnResult> PostSaveProductAudit(ProductAuditInputModel productAudit)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(productAudit.ProductShelfId))
                {
                    r.flag = -2;
                    r.msg = "请至少选择一个产品";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(productAudit.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                productAudit.UserId = sessionBag.UserId;
                productAudit.UserName = sessionBag.LoginName;
                r = _supplierInMallService.SaveProductAudit(productAudit);
                //添加索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.AddProductIndex(r.data.rows.ToString());
                }
                return r;
            });
        }

        /// <summary>
        /// 暂停销售、强制下架
        /// </summary>
        /// <param name="productAudit"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsaveonsaleproduce")]
        [ValidateModel]
        public async Task<ReturnResult> PostSaveOnSaleProduce(ProductAuditInputModel productAudit)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(productAudit.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                productAudit.UserId = sessionBag.UserId;
                productAudit.UserName = sessionBag.LoginName;
                productAudit.ProductShelfId = StringFilter.IdsArrSql(productAudit.ProductShelfId);
                productAudit.ProductNames = StringFilter.SearchSql(productAudit.ProductNames);
                r = _supplierInMallService.SaveOnSaleProduce(productAudit);
                //删除索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.DeleteProductIndex(r.data.rows.ToString());
                }
                return r;
            });
        }

        /// <summary>
        /// 恢复销售
        /// </summary>
        /// <param name="productAudit"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsaverecoverysale")]
        public async Task<ReturnResult> PostSaveRecoverySale(ProductAuditInputModel productAudit)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (string.IsNullOrEmpty(productAudit.ProductShelfId))
                {
                    r.flag = -2;
                    r.msg = "请至少选择一个产品";
                    return r;
                }
                var sessionBag = SessionContainer.GetSession(productAudit.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    r.flag = -2;
                    r.msg = "您无权操作此功能";
                    return r;
                }
                productAudit.UserId = sessionBag.UserId;
                productAudit.UserName = sessionBag.LoginName;
                productAudit.ProductShelfId = StringFilter.IdsArrSql(productAudit.ProductShelfId);
                productAudit.ProductNames = StringFilter.SearchSql(productAudit.ProductNames);
                r = _supplierInMallService.SaveRecoverySale(productAudit);
                //添加索引
                if (!string.IsNullOrEmpty(r.data.rows.ToString()))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.AddProductIndex(r.data.rows.ToString());
                }
                return r;
            });
        }
        #endregion

        /// <summary>
        /// 获取待确认订单列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchorder")]
        public async Task<QueryResult<OrderViewModel>> SearchOrder(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<OrderViewModel> ret = new QueryResult<OrderViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                //_productDeclareApplicationService.UpdateDeclareShelvesDifference();
                //_orderApplicationService.UpdateDetailShelvesDifferenceData();
                ret = _orderApplicationService.SearchOrder(args);
                return ret;
            });
        }





        /// <summary>
        /// 确认订单。
        /// </summary>
        /// <param name="id">OrderId 订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("confirmorder")]
        public async Task<QueryResult<OrderAgreementInputModel>> ConfirmOrder(int id, int unitId, int userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<OrderAgreementInputModel> ret = new QueryResult<OrderAgreementInputModel>();

                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt()
                        && sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() && sessionBag.UserType != UserRoleType.CityAuditor.ToInt() && sessionBag.UserType != UserRoleType.CityAdmin.ToInt()
                        && sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() && sessionBag.UserType != UserRoleType.CountyAuditor.ToInt())
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }

                ret.Entity = _orderApplicationService.ConfirmOrder(id, unitId, userId, sessionBag.UnitType);
                ret.flag = 1;
                ret.msg = "查询成功。";
                return ret;
            });
        }

        /// <summary>
        /// 确认订单。
        /// </summary>
        /// <param name="id">OrderId 订单Id</param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveconfirmorder")]
        [ValidateModel]
        public async Task<ReturnResult> SaveConfirmOrder(OrderAgreementInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin
                     && sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt()
                     && sessionBag.UserType != UserRoleType.CountyAdmin.ToInt() && sessionBag.UserType != UserRoleType.CountyAuditor.ToInt()
                     && sessionBag.UserType != UserRoleType.CityAdmin.ToInt() && sessionBag.UserType != UserRoleType.CityAuditor.ToInt())
                {
                    r.flag = -2;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                model.UnitType = (UnitType)sessionBag.UnitType;
                r = _orderApplicationService.SaveConfirmOrder(model);
                //记录日志
                FileLog.SendApiLog(string.Format("确认订单 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}-{4}】", sessionBag.UnitId, sessionBag.UserId, ComLib.Object2JSON(model), r.flag, r.msg));
                return r;
            });
        }

        /// <summary>
        /// 判断单位名称是否存在
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("checkexistsunitname")]
        public bool CheckExistsUnitName(string UnitName)
        {
            UnitName = StringFilter.SearchSql(UnitName);
            return _unitApplicationService.IsExistsName(UnitName);
        }

        #region 已确认订单，合同列表  、修改合同  
        /// <summary>
        /// 待确认定点列表(合同订单)
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchContract")]
        public async Task<QueryResult<OrderViewModel>> SearchContract(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<OrderViewModel> ret = new QueryResult<OrderViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                //_productDeclareApplicationService.UpdateDeclareShelvesDifference();
                //_orderApplicationService.UpdateDetailShelvesDifferenceData();
                return _orderApplicationService.SearchContract(args);
            });
        }

        /// <summary>
        /// 修改合同
        /// </summary>
        /// <param name="id">订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [Route("editcontract")]
        public async Task<QueryResult<OrderAgreementInputModel>> EditContract(int id, int unitId, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<OrderAgreementInputModel> ret = new QueryResult<OrderAgreementInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                ret.Entity = _orderApplicationService.EditContract(id, unitId, userId);

                return ret;
            });
        }

        [HttpPost]
        [Route("contractitem")]
        public async Task<QueryResult<ContractItemViewModel>> ContractItem(OrderItemSearchInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<ContractItemViewModel> ret = new QueryResult<ContractItemViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    ret.flag = -2;
                    ret.msg = "操作失败，权限不足。";
                    return ret;
                }
                return _orderApplicationService.ContractItem(args);
            });
        }

        #endregion

        #region 后台设置-合同信息

        /// <summary>
        /// 获取合同信息（合同信息实体）
        /// </summary>
        /// <param name="unitId">单位信息</param>
        /// <param name="userId">操作用户</param>
        /// <param name="token"></param>
        /// <returns>合同信息实体</returns>
        [HttpGet]
        [Route("getunitcontract")]
        public async Task<QueryResult<UnitContractInputModel>> GetUnitContract(int unitId, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<UnitContractInputModel> ret = new QueryResult<UnitContractInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.msg = "登录超时，请重新登录。";
                    ret.flag = -1;
                    return ret;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.msg = "登录信息验证失败，请重新登录。";
                    ret.flag = -1;
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                //if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                //{
                //    ret.msg = "操作失败，权限不足。";
                //    ret.flag = -2;
                //    return ret;
                //}
                return _unitContractService.GetModel(unitId, userId);
            });
        }


        /// <summary>
        /// 保存合同信息
        /// </summary> 
        [HttpPost]
        [Route("saveunitcontract")]
        [ValidateModel]
        public async Task<ReturnResult> SaveUnitContract(UnitContractInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                //if (sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                //{
                //    r.flag = -2;
                //    r.msg = "操作失败，权限不足。";
                //    return r;
                //}
                model.IsDefault = true;
                return _unitContractService.SaveModel(model);
            });
        }
        #endregion


        #region 

        /// <summary>
        ///  lss获取索引中的，供应商信息及供应商下的产品，产品获取12个。
        /// </summary>
        /// <param name="id">供应商Id</param
        /// <param name = "mallId" > 商城Id </ param >
        /// <returns></returns>
        [HttpPost]
        [Route("getsupplierdetail")]
        public async Task<SuppliersViewModel> GetSupplierDetail(MallProductSearchInputModel args)
        {
            return await Task.Run(() =>
            {
                SuppliersViewModel model = new SuppliersViewModel();
                //产品
                try
                {
                    model.Product = ElasticSearchHelper.SearchProductByDealerId("ProductSearchSimpleYun", args);
                    model.Supplier = ElasticSearchHelper.SearchDealerDetail("DealerSearchSimpleYun", args.d, args.MallId);
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog("获取供应商详情信息异常：" + e.Message);
                }
                return model;
            });
        }

        /// <summary>
        ///  lss获取索引中的，供应商信息及供应商下的产品，产品获取12个。
        /// </summary>
        /// <param name="id">供应商Id</param
        /// <param name = "mallId" > 商城Id </ param >
        /// <returns></returns>
        [HttpGet]
        [Route("getsupplierdesc")]
        public async Task<SuppliersViewModel> GetSupplierDesc(int id, int mallId)
        {
            return await Task.Run(() =>
            {
                SuppliersViewModel model = new SuppliersViewModel();
                //产品
                try
                {
                    model.Supplier = ElasticSearchHelper.SearchDealerDetail("DealerSearchSimpleYun", id, mallId);
                }
                catch (Exception e)
                {
                    FileLog.SendExceptionLog("获取供应商详情信息异常：" + e.Message);
                }
                return model;
            });
        }
        #endregion


        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsupplierlist")]
        public async Task<DealerList> SearchDealer(MallDealerSearchInputModel args)
        {
            var result = await Task.Run(() =>
            {
                return ElasticSearchHelper.SearchDealer<DealerSearchSimpleYun>("DealerSearchSimpleYun", args);
            });
            return result;
        }

        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsearchsupplierstatistic")]
        public async Task<QueryResult<SupplierStatistic>> PostSearchSupplierStatistic(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierStatistic> listStatistic = new QueryResult<SupplierStatistic>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listStatistic.flag = -1;
                    return listStatistic;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    listStatistic.flag = -2;
                    return listStatistic;
                }
                listStatistic = _supplierInMallService.SearchSupplierStatistic(args);
                return listStatistic;
            });
        }

        /// <summary>
        /// 保存供应商信息
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("updatesupplier")]
        public async Task<ReturnResult> UpdateSuplier(int id)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                SearchArgumentsInputModel args = new SearchArgumentsInputModel();
                args.Status = SupplierInMallStatuz.ApprovalAdopt.ToInt();
                args.MallId = id;
                args.Limit = int.MaxValue;
                var data = _supplierInMallService.SearchSupplierInMallAll(args);
                if (data != null && data.Data != null && data.Data.Count > 0)
                {
                    var ids = string.Join(",", data.Data.Select(m => m.SupplierInMallId).ToList());
                    if (!string.IsNullOrEmpty(ids))
                    {
                        SearchDealerIndex index = new SearchDealerIndex(_supplierInMallService);
                        index.AddDealerIndex(ids);
                        r.flag = 1;
                        r.msg = "更新成功。";
                    }
                }
                return r;
            });
            return result;
        }


        /// <summary>
        /// 获取供应商产品审核数量信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postsupplierproductauditlist")]
        public async Task<QueryResult<SupplierProductAuditViewModel>> PostSupplierProductAuditList(SearchAuditInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierProductAuditViewModel> listStatistic = new QueryResult<SupplierProductAuditViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listStatistic.flag = -1;
                    return listStatistic;
                }
                if (sessionBag.UserType != UserRoleType.MallAdministrator.ToInt())
                {
                    listStatistic.flag = -2;
                    return listStatistic;
                }
                listStatistic = _supplierInMallService.SearchProductAuditList(args);
                return listStatistic;
            });
        }


        [HttpPost]
        [Route("productrecord")]
        public async Task<QueryResult<PProductRecordViewModel>> ProductRecord(SearchAuditInputModel args)
        {
            return await Task.Run(() =>
            {
                return _productRecordService.SearchProductRecord(args);
            });
        }

        /// <summary>
        /// 产品已推荐列表
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchrecommendproduct")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchRecommendProduct(SearchArgumentsInputModel args)
        {
            var result = await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> r = new QueryResult<ProductShelfViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -1;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                ProductShelfViewModel entity = new ProductShelfViewModel();
                List<ShelfMallInputModel> mallList = _supplierInMallService.GetSupplierInMall((int)args.UnitId);
                entity.MallId = args.MallId;
                if (mallList != null && mallList.Count > 0)
                {
                    entity.MallList = (from item in mallList
                                       select new MallViewModel()
                                       {
                                           Name = item.Name,
                                           MallId = item.MallId

                                       }).ToList();
                    if (args.MallId <= 0)
                    {
                        args.MallId = mallList[0].MallId;
                        entity.MallId = mallList[0].MallId;
                    }
                }
                r = _productShelfApplicationService.SearchRecommendProduct(args);
                r.Entity = entity;
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取供应商已上架的所有平台产品
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("searchproductallplatformlist")]
        public async Task<QueryResult<ProductShelfViewModel>> SearchProductAllPlatformShelfList(SearchArgumentsInputModel args)
        {
            var result = await Task.Run(() =>
            {
                QueryResult<ProductShelfViewModel> r = new QueryResult<ProductShelfViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -1;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _productApplicationService.SearchProductAllPlatformShelfList(args);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取供应商已上架的所有平台产品
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcertificatelist")]
        public async Task<QueryResult<AttachmentImgInputModel>> GetCertificateListByCategory(int unitId, int userId, string token, string fileCategory)
        {
            var result = await Task.Run(() =>
            {
                QueryResult<AttachmentImgInputModel> r = new QueryResult<AttachmentImgInputModel>();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -1;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                //   r = _productApplicationService.SearchProductAllPlatformShelfList(fileCategory);
                var list = _uattachmentService.GetCertificateListByCategory(sessionBag.UnitId, fileCategory);
                if (list != null && list.Count > 0)
                {
                    r.Data = (from item in list
                              select new AttachmentImgInputModel
                              {
                                  Path = item.Path,
                                  PathM = FileUpload.GetThumbAbsoluteUrl(item.Path, "m")
                              }).ToList();
                    r.TotalCount = list.Count;
                }
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取供应商已上架的所有平台产品
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("savetcertificate")]
        public async Task<ReturnResult> SaveCertificateImage(CertificateInputModel model)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                //QueryResult<ProductShelfViewModel> r = new QueryResult<ProductShelfViewModel>();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != (int)UserRoleType.SellerProductAdmin && sessionBag.UserType != (int)UserRoleType.SellerMallAdmin && sessionBag.UserType != (int)UserRoleType.SellerAdmin)
                {
                    r.flag = -1;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                var resultId = _uattachmentService.AttachmentAdd(new AttachmentInputModel()
                {
                    UnitId = model.BaseUnitId,
                    UserId = model.BaseUserId,
                    Path = model.Path,
                    FileCategory = model.FileCategory
                });
                if (resultId > 0)
                {
                    r.flag = 1;
                    r.msg = "添加成功。";
                }
                return r;
            });
            return result;
        }

        #region 供应商产品共享功能
        /// <summary>
        /// lss供应商-供应商共享。
        /// </summary>
        /// <param name="args">列表搜索条件</param>
        /// <returns>商城列表</returns>
        [HttpPost]
        [Route("searchauthorize")]
        public async Task<QueryResult<SupplierAuthorizeViewModel>> SearchSupplierAuthorize(SearchAuthorizeShareInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierAuthorizeViewModel> ret = new QueryResult<SupplierAuthorizeViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }

                return _supplierInMallService.SearchSupplierAuthorize(args);
            });
        }

        /// <summary>
        /// 获取申请授权单位列表
        /// </summary>
        /// <param name="args">列表搜索条件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("getauthorizeunit")]
        public async Task<QueryResult<SelectListViewModel>> GetAuthorizeUnit(SearchArgumentsInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SelectListViewModel> ret = new QueryResult<SelectListViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }

                return _supplierInMallService.GetSupplierListAll(args);
            });
        }

        /// <summary>
        /// 产品共享，供应商授权申请
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("authorizeapply")]
        [ValidateModel]
        public async Task<ReturnResult> AuthorizeApply(SupplierAuthorizeInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                if (model.AuthorizeUnitId <= 0)
                {
                    r.flag = 0;
                    r.msg = "请填写共享单位";
                    return r;
                }
                if (model.ApplyNum <= 0)
                {
                    r.flag = 0;
                    r.msg = "请填写共享产品数量";
                    return r;
                }
                if (model.ApplyRemark!=null && model.ApplyRemark.Length > 500)
                {
                    r.flag = 0;
                    r.msg = "说明信息字数长度请小于500";
                    return r;
                } 
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierInMallService.AuthorizeApply(model); 
                return r;
            });
        }

        /// <summary>
        /// 取消 申请授权功能
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("cancelapplyauthorize")]
        [ValidateModel]
        public async Task<ReturnResult> CancelApplyAuthorize(SupplierAuthorizeInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult(); 
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierInMallService.CancelApplyAuthorize(model);
                return r;
            });
        }

        /// <summary>
        /// 共享授权功能
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("shareauthorize")]
        [ValidateModel]
        public async Task<ReturnResult> ShareAuthorize(SupplierAuthorizeInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierInMallService.ShareAuthorize(model);
                return r;
            });
        }

        /// <summary>
        /// lss供应商-产品共享列表。
        /// </summary>
        /// <param name="args">列表搜索条件</param>
        /// <returns>商城列表</returns>
        [HttpPost]
        [Route("searchshareproduct")]
        public async Task<QueryResult<SupplierShareProductViewModel>> SearchShareProduct(SearchAuthorizeShareInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierShareProductViewModel> ret = new QueryResult<SupplierShareProductViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }

                return _supplierInMallService.SearchShareProduct(args);
            });
        }

        /// <summary>
        /// lss供应商-查询已共享产品列表。
        /// </summary>
        /// <param name="args">列表搜索条件</param>
        /// <returns>商城列表</returns>
        [HttpPost]
        [Route("searchsharedproduct")]
        public async Task<QueryResult<SupplierShareProductViewModel>> SearchSharedProductList(SearchAuthorizeShareInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<SupplierShareProductViewModel> ret = new QueryResult<SupplierShareProductViewModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (args.UnitId != sessionBag.UnitId || args.UserId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    ret.flag = -1;
                    ret.msg = "权限不足，无法访问。";
                    return ret;
                }

                return _supplierInMallService.SearchSharedProductList(args);
            });
        }

        /// <summary>
        /// 共享产品
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("shareproduct")]
        [ValidateModel]
        public async Task<ReturnResult> ShareProduct(SupplierShareProductInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = 0;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }

                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = 0;
                    r.msg = "登录信息验证失败，请重新登录。";
                    return r;
                }

                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt())
                {
                    r.flag = 0;
                    r.msg = "操作失败，权限不足。";
                    return r;
                }
                r = _supplierInMallService.ShareProduct(model);
                return r;
            });
        }
        #endregion

        #region 修改产品分类信息

        /// <summary>
        /// lss添加和修改产品（获取添加，修改页面的Model）
        /// </summary>
        /// <param name="id">产品Id</param>
        /// <returns>产品详情的Model</returns>
        [HttpGet]
        [Route("getproductinfo")]
        public async Task<QueryResult<ProductInputModel>> GetProductInfo(int id, int unitId, long userId, string token)
        {
            return await Task.Run(() =>
            {
                QueryResult<ProductInputModel> ret = new QueryResult<ProductInputModel>();
                //ProductViewModel modeddl = new ProductViewModel();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }

                if (unitId != sessionBag.UnitId || userId != sessionBag.UserId)
                {
                    ret.flag = -1;
                    ret.msg = "登录超时，请重新登录。";
                    return ret;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    ret.flag = -2;
                    ret.msg = "权限不足。。";
                    return ret;
                }

                return _productShelfApplicationService.GetProductInfoModel(id);
            });
        }

        /// <summary>
        /// lss保存（添加、修改产品的保存）
        /// </summary>
        /// <param name="model">保存实体（图片、价格、学段、学科</param>
        /// <returns>ReturnResult（Json格式）</returns>
        [HttpPost]
        [Route("saveproductclassify")]
        [ValidateModel]
        public async Task<ReturnResult> SaveProductClassify(ProductClassifyInputModel model)
        {
            return await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(model.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (model.BaseUnitId != sessionBag.UnitId || model.BaseUserId != sessionBag.UserId)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                //判断用户是否为 卖家超管 或者  卖家平台管理员
                if (sessionBag.UserType != UserRoleType.SellerProductAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerMallAdmin.ToInt() && sessionBag.UserType != UserRoleType.SellerAdmin.ToInt())
                {
                    r.flag = -2;
                    r.msg = "权限不足，无法访问。";
                    return r;
                }
                r = _productApplicationService.SaveProductClassify(model);

                //更新产品索引
                var ids = (string)r.data.rows;
                if (!string.IsNullOrEmpty(ids))
                {
                    SearchProductIndex index = new SearchProductIndex(_productShelfApplicationService);
                    index.AddProductIndex(r.data.rows.ToString());
                    //添加日志。
                    ProductRecordInsert record = new ProductRecordInsert(_productRecordService);
                    record.InsertBatch("", ProductAction.Shelf, Default.IsTrue, sessionBag.UserId, 0, r.data.rows.ToString(), "产品分类修改");
                }

                return r;
            });
        }

        #endregion
    }
}