﻿ MallId = entity.MallId,
 CheckAuthentication = entity.CheckAuthentication,
 ShelveAuthentication = entity.ShelveAuthentication,
 OrderQuotaMax = entity.OrderQuotaMax,
 OrderQuotaMin = entity.OrderQuotaMin,
 Clause = entity.Clause,
 SetTime = entity.SetTime,
 SetUserId = entity.SetUserId,
 IsOpenMallControl = entity.IsOpenMallControl,
 IsAllowShelves = entity.IsAllowShelves,
 IsAllowEdit = entity.IsAllowEdit,
 IsAllowOffShelves = entity.IsAllowOffShelves,


 MallId = model.MallId,
 CheckAuthentication = model.CheckAuthentication,
 ShelveAuthentication = model.ShelveAuthentication,
 OrderQuotaMax = model.OrderQuotaMax,
 OrderQuotaMin = model.OrderQuotaMin,
 Clause = model.Clause,
 SetTime = model.SetTime,
 SetUserId = model.SetUserId,
 IsOpenMallControl = model.IsOpenMallControl,
 IsAllowShelves = model.IsAllowShelves,
 IsAllowEdit = model.IsAllowEdit,
 IsAllowOffShelves = model.IsAllowOffShelves,


 temp.MallId = model.MallId,
 temp.CheckAuthentication = model.CheckAuthentication,
 temp.ShelveAuthentication = model.ShelveAuthentication,
 temp.OrderQuotaMax = model.OrderQuotaMax,
 temp.OrderQuotaMin = model.OrderQuotaMin,
 temp.Clause = model.Clause,
 temp.SetTime = model.SetTime,
 temp.SetUserId = model.SetUserId,
 temp.IsOpenMallControl = model.IsOpenMallControl,
 temp.IsAllowShelves = model.IsAllowShelves,
 temp.IsAllowEdit = model.IsAllowEdit,
 temp.IsAllowOffShelves = model.IsAllowOffShelves,

 MallConfigureId = item.MallConfigureId,
 MallId = item.MallId,
 CheckAuthentication = item.CheckAuthentication,
 ShelveAuthentication = item.ShelveAuthentication,
 OrderQuotaMax = item.OrderQuotaMax,
 OrderQuotaMin = item.OrderQuotaMin,
 Clause = item.Clause,
 SetTime = item.SetTime,
 SetUserId = item.SetUserId,
 IsOpenMallControl = item.IsOpenMallControl,
 IsAllowShelves = item.IsAllowShelves,
 IsAllowEdit = item.IsAllowEdit,
 IsAllowOffShelves = item.IsAllowOffShelves,

public class MallConfigureInputModel
{
 [Display(Name = "Id")] 
    public long MallConfigureId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "供应商入驻平台认证（0：否 1：是）")] 
    public int CheckAuthentication {get; set; }
    
 [Display(Name = "产品上架认证（0：否 1：是）")] 
    public int ShelveAuthentication {get; set; }
    
 [Display(Name = "订单限额上限")] 
    public decimal OrderQuotaMax {get; set; }
    
 [Display(Name = "订单限额下限")] 
    public decimal OrderQuotaMin {get; set; }
    
 [Display(Name = "网上订购合同条款")] 
    public string Clause {get; set; }
    
 [Display(Name = "设置时间")] 
    public DateTime SetTime {get; set; }
    
 [Display(Name = "设置人")] 
    public long SetUserId {get; set; }
    
 [Display(Name = "是否允许商城控制供应商行为()[为1的时候下列字段才生效]")] 
    public bool IsOpenMallControl {get; set; }
    
 [Display(Name = "是否允许上架（0：否  1：是）")] 
    public bool IsAllowShelves {get; set; }
    
 [Display(Name = "是否允许修改产品（0：否  1：是）")] 
    public bool IsAllowEdit {get; set; }
    
 [Display(Name = "是否允许下架（0：否  1：是）")] 
    public bool IsAllowOffShelves {get; set; }
    
 }
 
 public class MallConfigureViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long MallConfigureId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 供应商入驻平台认证（0：否 1：是）
    /// </summary>
    public int CheckAuthentication {get; set; }
    
    /// <summary>
    /// 产品上架认证（0：否 1：是）
    /// </summary>
    public int ShelveAuthentication {get; set; }
    
    /// <summary>
    /// 订单限额上限
    /// </summary>
    public decimal OrderQuotaMax {get; set; }
    
    /// <summary>
    /// 订单限额下限
    /// </summary>
    public decimal OrderQuotaMin {get; set; }
    
    /// <summary>
    /// 网上订购合同条款
    /// </summary>
    public string Clause {get; set; }
    
    /// <summary>
    /// 设置时间
    /// </summary>
    public DateTime SetTime {get; set; }
    
    /// <summary>
    /// 设置人
    /// </summary>
    public long SetUserId {get; set; }
    
    /// <summary>
    /// 是否允许商城控制供应商行为()[为1的时候下列字段才生效]
    /// </summary>
    public bool IsOpenMallControl {get; set; }
    
    /// <summary>
    /// 是否允许上架（0：否  1：是）
    /// </summary>
    public bool IsAllowShelves {get; set; }
    
    /// <summary>
    /// 是否允许修改产品（0：否  1：是）
    /// </summary>
    public bool IsAllowEdit {get; set; }
    
    /// <summary>
    /// 是否允许下架（0：否  1：是）
    /// </summary>
    public bool IsAllowOffShelves {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CheckAuthentication, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CheckAuthentication, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商入驻平台认证（0：否 1：是）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ShelveAuthentication, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ShelveAuthentication, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品上架认证（0：否 1：是）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderQuotaMax, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderQuotaMax, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单限额上限" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderQuotaMin, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderQuotaMin, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单限额下限" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Clause, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Clause, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入网上订购合同条款" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SetTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SetTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入设置时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SetUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SetUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入设置人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsOpenMallControl, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsOpenMallControl, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否允许商城控制供应商行为()[为1的时候下列字段才生效]" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsAllowShelves, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsAllowShelves, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否允许上架（0：否  1：是）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsAllowEdit, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsAllowEdit, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否允许修改产品（0：否  1：是）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsAllowOffShelves, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsAllowOffShelves, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否允许下架（0：否  1：是）" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'CheckAuthentication', title: '供应商入驻平台认证（0：否 1：是）', sortable: true },
                 
 { field: 'ShelveAuthentication', title: '产品上架认证（0：否 1：是）', sortable: true },
                 
 { field: 'OrderQuotaMax', title: '订单限额上限', sortable: true },
                 
 { field: 'OrderQuotaMin', title: '订单限额下限', sortable: true },
                 
 { field: 'Clause', title: '网上订购合同条款', sortable: true },
                 
 { field: 'SetTime', title: '设置时间', sortable: true },
                 
 { field: 'SetUserId', title: '设置人', sortable: true },
                 
 { field: 'IsOpenMallControl', title: '是否允许商城控制供应商行为()[为1的时候下列字段才生效]', sortable: true },
                 
 { field: 'IsAllowShelves', title: '是否允许上架（0：否  1：是）', sortable: true },
                 
 { field: 'IsAllowEdit', title: '是否允许修改产品（0：否  1：是）', sortable: true },
                 
 { field: 'IsAllowOffShelves', title: '是否允许下架（0：否  1：是）', sortable: true },
                 
o.MallId,                 
o.CheckAuthentication,                 
o.ShelveAuthentication,                 
o.OrderQuotaMax,                 
o.OrderQuotaMin,                 
o.Clause,                 
o.SetTime,                 
o.SetUserId,                 
o.IsOpenMallControl,                 
o.IsAllowShelves,                 
o.IsAllowEdit,                 
o.IsAllowOffShelves,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#CheckAuthentication').val(d.data.rows.CheckAuthentication);          
        $('#ShelveAuthentication').val(d.data.rows.ShelveAuthentication);          
        $('#OrderQuotaMax').val(d.data.rows.OrderQuotaMax);          
        $('#OrderQuotaMin').val(d.data.rows.OrderQuotaMin);          
        $('#Clause').val(d.data.rows.Clause);          
        $('#SetTime').val(d.data.rows.SetTime);          
        $('#SetUserId').val(d.data.rows.SetUserId);          
        $('#IsOpenMallControl').val(d.data.rows.IsOpenMallControl);          
        $('#IsAllowShelves').val(d.data.rows.IsAllowShelves);          
        $('#IsAllowEdit').val(d.data.rows.IsAllowEdit);          
        $('#IsAllowOffShelves').val(d.data.rows.IsAllowOffShelves);          

 $('#th_MallId').html(' 商城Id');               
 $('#th_CheckAuthentication').html(' 供应商入驻平台认证（0：否 1：是）');               
 $('#th_ShelveAuthentication').html(' 产品上架认证（0：否 1：是）');               
 $('#th_OrderQuotaMax').html(' 订单限额上限');               
 $('#th_OrderQuotaMin').html(' 订单限额下限');               
 $('#th_Clause').html(' 网上订购合同条款');               
 $('#th_SetTime').html(' 设置时间');               
 $('#th_SetUserId').html(' 设置人');               
 $('#th_IsOpenMallControl').html(' 是否允许商城控制供应商行为()[为1的时候下列字段才生效]');               
 $('#th_IsAllowShelves').html(' 是否允许上架（0：否  1：是）');               
 $('#th_IsAllowEdit').html(' 是否允许修改产品（0：否  1：是）');               
 $('#th_IsAllowOffShelves').html(' 是否允许下架（0：否  1：是）');               
 
 $('#tr_MallId').hide();               
 $('#tr_CheckAuthentication').hide();               
 $('#tr_ShelveAuthentication').hide();               
 $('#tr_OrderQuotaMax').hide();               
 $('#tr_OrderQuotaMin').hide();               
 $('#tr_Clause').hide();               
 $('#tr_SetTime').hide();               
 $('#tr_SetUserId').hide();               
 $('#tr_IsOpenMallControl').hide();               
 $('#tr_IsAllowShelves').hide();               
 $('#tr_IsAllowEdit').hide();               
 $('#tr_IsAllowOffShelves').hide();               

 , "MallId" : mallId
 , "CheckAuthentication" : checkAuthentication
 , "ShelveAuthentication" : shelveAuthentication
 , "OrderQuotaMax" : orderQuotaMax
 , "OrderQuotaMin" : orderQuotaMin
 , "Clause" : clause
 , "SetTime" : setTime
 , "SetUserId" : setUserId
 , "IsOpenMallControl" : isOpenMallControl
 , "IsAllowShelves" : isAllowShelves
 , "IsAllowEdit" : isAllowEdit
 , "IsAllowOffShelves" : isAllowOffShelves

 var mallId = $('#o_MallId').val();
 var checkAuthentication = $('#o_CheckAuthentication').val();
 var shelveAuthentication = $('#o_ShelveAuthentication').val();
 var orderQuotaMax = $('#o_OrderQuotaMax').val();
 var orderQuotaMin = $('#o_OrderQuotaMin').val();
 var clause = $('#o_Clause').val();
 var setTime = $('#o_SetTime').val();
 var setUserId = $('#o_SetUserId').val();
 var isOpenMallControl = $('#o_IsOpenMallControl').val();
 var isAllowShelves = $('#o_IsAllowShelves').val();
 var isAllowEdit = $('#o_IsAllowEdit').val();
 var isAllowOffShelves = $('#o_IsAllowOffShelves').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商入驻平台认证（0：否 1：是）' : '产品名称', d.data.rows.CheckAuthentication);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品上架认证（0：否 1：是）' : '产品名称', d.data.rows.ShelveAuthentication);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单限额上限' : '产品名称', d.data.rows.OrderQuotaMax);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单限额下限' : '产品名称', d.data.rows.OrderQuotaMin);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '网上订购合同条款' : '产品名称', d.data.rows.Clause);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '设置时间' : '产品名称', d.data.rows.SetTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '设置人' : '产品名称', d.data.rows.SetUserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否允许商城控制供应商行为()[为1的时候下列字段才生效]' : '产品名称', d.data.rows.IsOpenMallControl);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否允许上架（0：否  1：是）' : '产品名称', d.data.rows.IsAllowShelves);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否允许修改产品（0：否  1：是）' : '产品名称', d.data.rows.IsAllowEdit);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否允许下架（0：否  1：是）' : '产品名称', d.data.rows.IsAllowOffShelves);



