﻿ UnitId = entity.UnitId,
 ParentUnitId = entity.ParentUnitId,


 UnitId = model.UnitId,
 ParentUnitId = model.ParentUnitId,


 temp.UnitId = model.UnitId,
 temp.ParentUnitId = model.ParentUnitId,

 UnitRelationId = item.UnitRelationId,
 UnitId = item.UnitId,
 ParentUnitId = item.ParentUnitId,

public class UnitRelationInputModel
{
 [Display(Name = "Id")] 
    public long UnitRelationId {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "父级单位Id")] 
    public int ParentUnitId {get; set; }
    
 }
 
 public class UnitRelationViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long UnitRelationId {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 父级单位Id
    /// </summary>
    public int ParentUnitId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ParentUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ParentUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父级单位Id" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'ParentUnitId', title: '父级单位Id', sortable: true },
                 
o.UnitId,                 
o.ParentUnitId,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#ParentUnitId').val(d.data.rows.ParentUnitId);          

 $('#th_UnitId').html(' 单位Id');               
 $('#th_ParentUnitId').html(' 父级单位Id');               
 
 $('#tr_UnitId').hide();               
 $('#tr_ParentUnitId').hide();               

 , "UnitId" : unitId
 , "ParentUnitId" : parentUnitId

 var unitId = $('#o_UnitId').val();
 var parentUnitId = $('#o_ParentUnitId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父级单位Id' : '产品名称', d.data.rows.ParentUnitId);



