﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class ArticleController : ControllerMember
    {
        // GET: Member/Article
        public async Task<ActionResult> List(SearchArgumentsInputModel args)
        {
            args.UserId = Operater.UserId;//
            args.UnitId = Operater.UnitId;//
            args.MallId = Operater.CurrentMallId;//
            args.Token = Operater.Token;
            string url = "article/articlelistbymall";
            var result = await WebApiHelper.SendAsync<QueryResult<ArticleBaseViewModel>>(url, args);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            ViewBag.Args = args;
            return View(result);
        }

        public async Task<ActionResult> Edit(int? id, string backUrl = "")
        {
            QueryResult<ArticleInputModel> model = new QueryResult<ArticleInputModel>();
            int catalogPid = 0;
            if (id.HasValue)
            {
                string url = Constant.ApiPath + "article/getarticlebymall?id=" + id.Value + "&token=" + Operater.Token;
                model = await WebApiHelper.SendAsync<QueryResult<ArticleInputModel>>(url, null, CommonTypes.CommonJsonSendType.GET); ;
                if (model.flag > 0 && model.Entity != null)
                {
                    catalogPid = model.Entity.Cid;
                }
            }
            else
            {
                model.flag = 1;
            }

            //获取资讯分类
            string url2 = Constant.ApiPath + "article/categorylistbymall?token=" + Operater.Token;
            var categoryList = await WebApiHelper.SendAsync<List<ArticleCategoryBaseViewModel>>(url2, null, CommonTypes.CommonJsonSendType.GET);
            var cateoptions = categoryList.Select(r => new SelectListItem
            {
                Selected = r.Id == catalogPid,
                Value = r.Id.ToString(),
                Text = r.Name
            });
            if (model.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (model.flag == -2)
            {
                return this.ReturnHome();
            }
            if (!ComLib.IsValidUrl(backUrl, Request.Url.Host))
            {
                backUrl = "~/";
            }
            ViewBag.CateParents = cateoptions;
            ViewBag.BackUrl = backUrl;
            ViewBag.ApiPath = Constant.ApiPath;
            ViewBag.UserId = Operater.UserId;
            ViewBag.UnitId = Operater.UnitId;
            ViewBag.Token = Operater.Token;
            return View(model.Entity);
        }

        [ValidateInput(false)]
        [HttpPost]
        public async Task<ActionResult> AddOrUpdateArticle(ArticleInputModel model)
        {
            model.BaseUserId = Operater.UserId;//
            model.BaseUnitId = Operater.UnitId;//
            model.BaseCurrentMallId = Operater.CurrentMallId;//
            model.Token = Operater.Token;
            if (model.Id == 0)
            {
                model.Statuz = ArticleStatuz.Publish.ToInt();
                model.BeginTime = DateTime.Now;
            }
            if (!string.IsNullOrEmpty(model.ImageUrl) && model.ImageUrl.Trim().Length > 3)
            {
                model.HasImage = true;
            }
            else
            {
                model.HasImage = false;
            }
            model.DisplayRange = DisplayRange.All.ToInt();
            model.EditDate = DateTime.Now;
            model.BeginTime = DateTime.Now;
            model.EndTime = model.AuditDate = DateTime.Now.AddYears(100);
            model.UserType = ArticleUserType.MallAdmin.ToInt();
            model.UserId = (int)Operater.UserId;
            model.MallId = Operater.CurrentMallId;
            model.DisplayRange = DisplayRange.Current.ToInt();
            string url = Constant.ApiPath + "article/editArticlebymall";
            var result = await WebApiHelper.SendAsync<ReturnResult>(url, model);
            //调用日志
            string log = string.Format("添加/修改资讯 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, ComLib.Object2JSON(model), ComLib.Object2JSON(result));
            Log.ArticleLog(log,result.flag);
            if (result.flag == -1)
            {
                return this.ApiTimeOut();
            }
            else if (result.flag == -2)
            {
                return this.ReturnHome();
            }
            return RedirectToAction("List");
        }

        /// <summary>
        /// 删除资讯信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> DelArticle(int id)
        {
            string url = Constant.ApiPath + "article/delarticlebymall?id=" + id + "&token=" + Operater.Token;
            var result = await WebApiHelper.SendAsync<ReturnResult>(url,null,CommonJsonSendType.GET);
            string log = string.Format("删除资讯 \r\n 操作人：【{0}-{1}】；数据信息：【{2}】，执行结果：【{3}】", Operater.UnitId, Operater.UserId, url, ComLib.Object2JSON(result));
            Log.ArticleLog(log,result.flag);
            return Json(result);
        }

        public async Task<ActionResult> Detail(string code)
        {
            string url = Constant.ApiPath + "article/getarticlebycode?code=" + code + "&token=" + Operater.Token;
            var model = await WebApiHelper.SendAsync<ArticleViewModel>(url, null, CommonJsonSendType.GET);
            
            ViewBag.ApiPath = Constant.ApiPath;
            return View(model);
        }

        public async Task<ActionResult> Help()
        {
            string url = Constant.ApiPath + "article/getarticlebycode?code=HYZXTS&token=" + Operater.Token;
            var model = await WebApiHelper.SendAsync<ArticleViewModel>(url, null, CommonJsonSendType.GET);
            
            ViewBag.ApiPath = Constant.ApiPath;
            return View(model);
        }
    }
}