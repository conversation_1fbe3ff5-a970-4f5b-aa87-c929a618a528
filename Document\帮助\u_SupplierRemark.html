﻿ UnitId = entity.UnitId,
 MallId = entity.MallId,
 Freight = entity.Freight,
 FreightExplain = entity.FreightExplain,
 SalesExplain = entity.SalesExplain,
 RegTime = entity.RegTime,
 UserId = entity.UserId,


 UnitId = model.UnitId,
 MallId = model.MallId,
 Freight = model.Freight,
 FreightExplain = model.FreightExplain,
 SalesExplain = model.SalesExplain,
 RegTime = model.RegTime,
 UserId = model.UserId,


 temp.UnitId = model.UnitId,
 temp.MallId = model.MallId,
 temp.Freight = model.Freight,
 temp.FreightExplain = model.FreightExplain,
 temp.SalesExplain = model.SalesExplain,
 temp.RegTime = model.RegTime,
 temp.UserId = model.UserId,

 SupplierRemarksId = item.SupplierRemarksId,
 UnitId = item.UnitId,
 MallId = item.MallId,
 Freight = item.Freight,
 FreightExplain = item.FreightExplain,
 SalesExplain = item.SalesExplain,
 RegTime = item.RegTime,
 UserId = item.UserId,

public class SupplierRemarkInputModel
{
 [Display(Name = "id")] 
    public long SupplierRemarksId {get; set; }
    
 [Display(Name = "供应商id(UnitId)")] 
    public int UnitId {get; set; }
    
 [Display(Name = "商城id")] 
    public int MallId {get; set; }
    
 [Display(Name = "运输费")] 
    public int Freight {get; set; }
    
 [Display(Name = "运输费说明")] 
    public string FreightExplain {get; set; }
    
 [Display(Name = "销售区域说明")] 
    public string SalesExplain {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "记录人")] 
    public long UserId {get; set; }
    
 }
 
 public class SupplierRemarkViewModel
 {
    /// <summary>
    /// id
    /// </summary>
    public long SupplierRemarksId {get; set; }
    
    /// <summary>
    /// 供应商id(UnitId)
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 商城id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 运输费
    /// </summary>
    public int Freight {get; set; }
    
    /// <summary>
    /// 运输费说明
    /// </summary>
    public string FreightExplain {get; set; }
    
    /// <summary>
    /// 销售区域说明
    /// </summary>
    public string SalesExplain {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long UserId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供应商id(UnitId)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Freight, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Freight, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运输费" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FreightExplain, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FreightExplain, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运输费说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SalesExplain, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SalesExplain, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入销售区域说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '供应商id(UnitId)', sortable: true },
                 
 { field: 'MallId', title: '商城id', sortable: true },
                 
 { field: 'Freight', title: '运输费', sortable: true },
                 
 { field: 'FreightExplain', title: '运输费说明', sortable: true },
                 
 { field: 'SalesExplain', title: '销售区域说明', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
 { field: 'UserId', title: '记录人', sortable: true },
                 
o.UnitId,                 
o.MallId,                 
o.Freight,                 
o.FreightExplain,                 
o.SalesExplain,                 
o.RegTime,                 
o.UserId,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#MallId').val(d.data.rows.MallId);          
        $('#Freight').val(d.data.rows.Freight);          
        $('#FreightExplain').val(d.data.rows.FreightExplain);          
        $('#SalesExplain').val(d.data.rows.SalesExplain);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#UserId').val(d.data.rows.UserId);          

 $('#th_UnitId').html(' 供应商id(UnitId)');               
 $('#th_MallId').html(' 商城id');               
 $('#th_Freight').html(' 运输费');               
 $('#th_FreightExplain').html(' 运输费说明');               
 $('#th_SalesExplain').html(' 销售区域说明');               
 $('#th_RegTime').html(' 记录时间');               
 $('#th_UserId').html(' 记录人');               
 
 $('#tr_UnitId').hide();               
 $('#tr_MallId').hide();               
 $('#tr_Freight').hide();               
 $('#tr_FreightExplain').hide();               
 $('#tr_SalesExplain').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_UserId').hide();               

 , "UnitId" : unitId
 , "MallId" : mallId
 , "Freight" : freight
 , "FreightExplain" : freightExplain
 , "SalesExplain" : salesExplain
 , "RegTime" : regTime
 , "UserId" : userId

 var unitId = $('#o_UnitId').val();
 var mallId = $('#o_MallId').val();
 var freight = $('#o_Freight').val();
 var freightExplain = $('#o_FreightExplain').val();
 var salesExplain = $('#o_SalesExplain').val();
 var regTime = $('#o_RegTime').val();
 var userId = $('#o_UserId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供应商id(UnitId)' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运输费' : '产品名称', d.data.rows.Freight);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运输费说明' : '产品名称', d.data.rows.FreightExplain);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '销售区域说明' : '产品名称', d.data.rows.SalesExplain);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.UserId);



