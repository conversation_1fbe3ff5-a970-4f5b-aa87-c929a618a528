﻿using Dqy.Instrument.Api.Models;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.UI.InputModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Web;

namespace Dqy.Instrument.Api.Common
{
    /// <summary>
    /// 文件上传
    /// </summary>
    public class FileUpload
    {

        /// <summary>
        /// 可上传文件类型
        /// </summary>
        public static readonly string FILE_UPLOAD_EXT_ALLOW = ComLib.GetAppSetting<string>("File.Upload.Ext.Allow");

        /// <summary>
        /// 可上传文件类型编码
        /// </summary>
        public static readonly string FILE_UPLOAD_EXTCODE_ALLOW = ComLib.GetAppSetting<string>("File.Upload.ExtCode.Allow");
        /// <summary>
        /// 
        /// </summary>
        /// <param name="file"></param>
        /// <param name="path"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public static DqyFileInfo Save(MultipartFileData file, string path, List<dynamic> size = null)
        {
            DqyFileInfo ufi = new DqyFileInfo();
            string uploadPath = path + "/" + DateTime.Now.Year + "/" + DateTime.Now.Month + "/";
            string orfilename = StringFilter.FilterBadChar(file.Headers.ContentDisposition.FileName).TrimStart('"').TrimEnd('"');
            string fileExt = orfilename.Substring(orfilename.LastIndexOf('.'));
            if (!FILE_UPLOAD_EXT_ALLOW.Contains(fileExt.ToLower() + "."))
            {
                ufi.error = "-1";
                ufi.msg = "非法文件，上传失败！";
            }
            else
            {
                string strSaveName = DateTime.Now.ToString("yyyyMMddHHmmss") + Guid.NewGuid().ToString("n").Substring(0, 6);
                string strNewFileName = strSaveName + fileExt;
                string strMapPath = HttpContext.Current.Server.MapPath("~/" + uploadPath);
                if (!Directory.Exists(strMapPath))
                {
                    Directory.CreateDirectory(strMapPath);
                }

                FileInfo fileinfo = new FileInfo(file.LocalFileName);
                
                fileinfo.CopyTo(Path.Combine(strMapPath, strNewFileName), true);
                File.Delete(file.LocalFileName);

                //生成缩略图BMP、JPG、JPEG、PNG、GIF
                if (".BMP.JPG.JPEG.PNG.GIF".Contains(fileExt.ToUpper()))
                {

                    ComLib.MakeThumbnail(Path.Combine(strMapPath, strNewFileName), Path.Combine(strMapPath, strSaveName + "_s" + fileExt), 200, 100, true);
                    ComLib.MakeThumbnail(Path.Combine(strMapPath, strNewFileName), Path.Combine(strMapPath, strSaveName + "_m" + fileExt), 260, 200, true);
                }

                ufi.filePath = uploadPath + strNewFileName;
                ufi.filePathM = uploadPath + strSaveName + "_m" + fileExt;
                ufi.filePathS = uploadPath + strSaveName + "_s" + fileExt;
            }
            return ufi;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="file"></param>
        /// <param name="path"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public static DqyFileInfo SaveHeadPhoto(MultipartFileData file, string path, List<dynamic> size = null)
        {
            DqyFileInfo ufi = new DqyFileInfo();
            string uploadPath = path + "/" + DateTime.Now.Year + "/" + DateTime.Now.Month + "/";         
            string orfilename = StringFilter.FilterBadChar(file.Headers.ContentDisposition.FileName).TrimStart('"').TrimEnd('"');
            string fileExt = orfilename.Substring(orfilename.LastIndexOf('.'));
            string strSaveName = DateTime.Now.ToString("yyyyMMddHHmmss") + Guid.NewGuid().ToString("n").Substring(0, 6);
            string strNewFileName = strSaveName + fileExt;
            string strMapPath = HttpContext.Current.Server.MapPath("~/" + uploadPath);
            if (!FILE_UPLOAD_EXT_ALLOW.Contains(fileExt.ToLower() + "."))
            {
                ufi.error = "-1";
                ufi.msg = "非法文件，上传失败！";
            }
            else
            {
                if (!Directory.Exists(strMapPath))
                {
                    Directory.CreateDirectory(strMapPath);
                }
                FileInfo fileinfo = new FileInfo(file.LocalFileName);
                fileinfo.CopyTo(Path.Combine(strMapPath, "m_" + strNewFileName), true);
                File.Delete(file.LocalFileName);
                ufi.filePath = uploadPath + strNewFileName;
                //生成缩略图BMP、JPG、JPEG、PNG、GIF
                if (".BMP.JPG.JPEG.PNG.GIF".Contains(fileExt.ToUpper()))
                {
                    ComLib.MakeThumbnail(Path.Combine(strMapPath, "m_" + strNewFileName), Path.Combine(strMapPath, strNewFileName), 200, 200, true);
                }
                //删除大图
                File.Delete(Path.Combine(strMapPath, "m_" + strNewFileName));
                ufi.filePath = uploadPath + strNewFileName;
            }
            return ufi;
        }
        
        public static string GetThumbAbsoluteUrl(string url, string suffix = null)
        {
            suffix = suffix ?? "s";
            var ext = url.Substring(url.LastIndexOf('.'));
            var head = url.Substring(0, url.LastIndexOf('.'));
            url = head + "_" + suffix + ext;
            return url;
        }
    }
}