﻿ ArticleId = entity.ArticleId,
 ImageSrc = entity.ImageSrc,
 ImageTitle = entity.ImageTitle,
 ImageDesc = entity.ImageDesc,
 ImageType = entity.ImageType,
 ShowOrder = entity.ShowOrder,
 Field1 = entity.Field1,
 Field2 = entity.Field2,


 ArticleId = model.ArticleId,
 ImageSrc = model.ImageSrc,
 ImageTitle = model.ImageTitle,
 ImageDesc = model.ImageDesc,
 ImageType = model.ImageType,
 ShowOrder = model.ShowOrder,
 Field1 = model.Field1,
 Field2 = model.Field2,


 temp.ArticleId = model.ArticleId,
 temp.ImageSrc = model.ImageSrc,
 temp.ImageTitle = model.ImageTitle,
 temp.ImageDesc = model.ImageDesc,
 temp.ImageType = model.ImageType,
 temp.ShowOrder = model.ShowOrder,
 temp.Field1 = model.Field1,
 temp.Field2 = model.Field2,

 Id = item.Id,
 ArticleId = item.ArticleId,
 ImageSrc = item.ImageSrc,
 ImageTitle = item.ImageTitle,
 ImageDesc = item.ImageDesc,
 ImageType = item.ImageType,
 ShowOrder = item.ShowOrder,
 Field1 = item.Field1,
 Field2 = item.Field2,

public class ArticleImageInputModel
{
 [Display(Name = "主键")] 
    public int Id {get; set; }
    
 [Display(Name = "资讯编号")] 
    public int ArticleId {get; set; }
    
 [Display(Name = "资讯图片")] 
    public string ImageSrc {get; set; }
    
 [Display(Name = "图片主题")] 
    public string ImageTitle {get; set; }
    
 [Display(Name = "图片说明")] 
    public string ImageDesc {get; set; }
    
 [Display(Name = "图片属性（0：普通；1：封面；")] 
    public int ImageType {get; set; }
    
 [Display(Name = "排序")] 
    public int ShowOrder {get; set; }
    
 [Display(Name = "备用字段1")] 
    public string Field1 {get; set; }
    
 [Display(Name = "备用字段2")] 
    public string Field2 {get; set; }
    
 }
 
 public class ArticleImageViewModel
 {
    /// <summary>
    /// 主键
    /// </summary>
    public int Id {get; set; }
    
    /// <summary>
    /// 资讯编号
    /// </summary>
    public int ArticleId {get; set; }
    
    /// <summary>
    /// 资讯图片
    /// </summary>
    public string ImageSrc {get; set; }
    
    /// <summary>
    /// 图片主题
    /// </summary>
    public string ImageTitle {get; set; }
    
    /// <summary>
    /// 图片说明
    /// </summary>
    public string ImageDesc {get; set; }
    
    /// <summary>
    /// 图片属性（0：普通；1：封面；
    /// </summary>
    public int ImageType {get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int ShowOrder {get; set; }
    
    /// <summary>
    /// 备用字段1
    /// </summary>
    public string Field1 {get; set; }
    
    /// <summary>
    /// 备用字段2
    /// </summary>
    public string Field2 {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ArticleId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ArticleId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImageSrc, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImageSrc, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯图片" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImageTitle, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImageTitle, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片主题" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImageDesc, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImageDesc, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片说明" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImageType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImageType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片属性（0：普通；1：封面；" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ShowOrder, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ShowOrder, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入排序" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Field1, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Field1, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备用字段1" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Field2, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Field2, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备用字段2" } })                    
                </div>
           </div>
  




 { field: 'ArticleId', title: '资讯编号', sortable: true },
                 
 { field: 'ImageSrc', title: '资讯图片', sortable: true },
                 
 { field: 'ImageTitle', title: '图片主题', sortable: true },
                 
 { field: 'ImageDesc', title: '图片说明', sortable: true },
                 
 { field: 'ImageType', title: '图片属性（0：普通；1：封面；', sortable: true },
                 
 { field: 'ShowOrder', title: '排序', sortable: true },
                 
 { field: 'Field1', title: '备用字段1', sortable: true },
                 
 { field: 'Field2', title: '备用字段2', sortable: true },
                 
o.ArticleId,                 
o.ImageSrc,                 
o.ImageTitle,                 
o.ImageDesc,                 
o.ImageType,                 
o.ShowOrder,                 
o.Field1,                 
o.Field2,                 
        
        $('#ArticleId').val(d.data.rows.ArticleId);          
        $('#ImageSrc').val(d.data.rows.ImageSrc);          
        $('#ImageTitle').val(d.data.rows.ImageTitle);          
        $('#ImageDesc').val(d.data.rows.ImageDesc);          
        $('#ImageType').val(d.data.rows.ImageType);          
        $('#ShowOrder').val(d.data.rows.ShowOrder);          
        $('#Field1').val(d.data.rows.Field1);          
        $('#Field2').val(d.data.rows.Field2);          

 $('#th_ArticleId').html(' 资讯编号');               
 $('#th_ImageSrc').html(' 资讯图片');               
 $('#th_ImageTitle').html(' 图片主题');               
 $('#th_ImageDesc').html(' 图片说明');               
 $('#th_ImageType').html(' 图片属性（0：普通；1：封面；');               
 $('#th_ShowOrder').html(' 排序');               
 $('#th_Field1').html(' 备用字段1');               
 $('#th_Field2').html(' 备用字段2');               
 
 $('#tr_ArticleId').hide();               
 $('#tr_ImageSrc').hide();               
 $('#tr_ImageTitle').hide();               
 $('#tr_ImageDesc').hide();               
 $('#tr_ImageType').hide();               
 $('#tr_ShowOrder').hide();               
 $('#tr_Field1').hide();               
 $('#tr_Field2').hide();               

 , "ArticleId" : articleId
 , "ImageSrc" : imageSrc
 , "ImageTitle" : imageTitle
 , "ImageDesc" : imageDesc
 , "ImageType" : imageType
 , "ShowOrder" : showOrder
 , "Field1" : field1
 , "Field2" : field2

 var articleId = $('#o_ArticleId').val();
 var imageSrc = $('#o_ImageSrc').val();
 var imageTitle = $('#o_ImageTitle').val();
 var imageDesc = $('#o_ImageDesc').val();
 var imageType = $('#o_ImageType').val();
 var showOrder = $('#o_ShowOrder').val();
 var field1 = $('#o_Field1').val();
 var field2 = $('#o_Field2').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯编号' : '产品名称', d.data.rows.ArticleId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯图片' : '产品名称', d.data.rows.ImageSrc);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片主题' : '产品名称', d.data.rows.ImageTitle);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片说明' : '产品名称', d.data.rows.ImageDesc);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片属性（0：普通；1：封面；' : '产品名称', d.data.rows.ImageType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '排序' : '产品名称', d.data.rows.ShowOrder);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备用字段1' : '产品名称', d.data.rows.Field1);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备用字段2' : '产品名称', d.data.rows.Field2);



