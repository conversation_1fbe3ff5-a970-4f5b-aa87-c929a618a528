﻿ ObjId = entity.ObjId,
 ObjType = entity.ObjType,
 FunId = entity.FunId,
 FunType = entity.FunType,


 ObjId = model.ObjId,
 ObjType = model.ObjType,
 FunId = model.FunId,
 FunType = model.FunType,


 temp.ObjId = model.ObjId,
 temp.ObjType = model.ObjType,
 temp.FunId = model.FunId,
 temp.FunType = model.FunType,

 ObjPemId = item.ObjPemId,
 ObjId = item.ObjId,
 ObjType = item.ObjType,
 FunId = item.FunId,
 FunType = item.FunType,

public class ObjectPermissionInputModel
{
 [Display(Name = "Id")] 
    public long ObjPemId {get; set; }
    
 [Display(Name = "对象Id（用户id/角色Id）")] 
    public int ObjId {get; set; }
    
 [Display(Name = "对象类型（1：用户；2：角色）")] 
    public int ObjType {get; set; }
    
 [Display(Name = "功能Id（pageId/funId）")] 
    public int FunId {get; set; }
    
 [Display(Name = "功能类型（1：页面；2：函数）")] 
    public int FunType {get; set; }
    
 }
 
 public class ObjectPermissionViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ObjPemId {get; set; }
    
    /// <summary>
    /// 对象Id（用户id/角色Id）
    /// </summary>
    public int ObjId {get; set; }
    
    /// <summary>
    /// 对象类型（1：用户；2：角色）
    /// </summary>
    public int ObjType {get; set; }
    
    /// <summary>
    /// 功能Id（pageId/funId）
    /// </summary>
    public int FunId {get; set; }
    
    /// <summary>
    /// 功能类型（1：页面；2：函数）
    /// </summary>
    public int FunType {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入对象Id（用户id/角色Id）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入对象类型（1：用户；2：角色）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FunId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FunId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入功能Id（pageId/funId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FunType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FunType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入功能类型（1：页面；2：函数）" } })                    
                </div>
           </div>
  




 { field: 'ObjId', title: '对象Id（用户id/角色Id）', sortable: true },
                 
 { field: 'ObjType', title: '对象类型（1：用户；2：角色）', sortable: true },
                 
 { field: 'FunId', title: '功能Id（pageId/funId）', sortable: true },
                 
 { field: 'FunType', title: '功能类型（1：页面；2：函数）', sortable: true },
                 
o.ObjId,                 
o.ObjType,                 
o.FunId,                 
o.FunType,                 
        
        $('#ObjId').val(d.data.rows.ObjId);          
        $('#ObjType').val(d.data.rows.ObjType);          
        $('#FunId').val(d.data.rows.FunId);          
        $('#FunType').val(d.data.rows.FunType);          

 $('#th_ObjId').html(' 对象Id（用户id/角色Id）');               
 $('#th_ObjType').html(' 对象类型（1：用户；2：角色）');               
 $('#th_FunId').html(' 功能Id（pageId/funId）');               
 $('#th_FunType').html(' 功能类型（1：页面；2：函数）');               
 
 $('#tr_ObjId').hide();               
 $('#tr_ObjType').hide();               
 $('#tr_FunId').hide();               
 $('#tr_FunType').hide();               

 , "ObjId" : objId
 , "ObjType" : objType
 , "FunId" : funId
 , "FunType" : funType

 var objId = $('#o_ObjId').val();
 var objType = $('#o_ObjType').val();
 var funId = $('#o_FunId').val();
 var funType = $('#o_FunType').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '对象Id（用户id/角色Id）' : '产品名称', d.data.rows.ObjId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '对象类型（1：用户；2：角色）' : '产品名称', d.data.rows.ObjType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '功能Id（pageId/funId）' : '产品名称', d.data.rows.FunId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '功能类型（1：页面；2：函数）' : '产品名称', d.data.rows.FunType);



