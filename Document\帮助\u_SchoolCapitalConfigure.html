﻿ SchoolId = entity.SchoolId,
 IsReport = entity.IsReport,
 FundTypes = entity.FundTypes,
 FundTypeName = entity.FundTypeName,
 IsQuota = entity.IsQuota,
 Quota = entity.Quota,
 UserId = entity.UserId,
 RegTime = entity.RegTime,


 SchoolId = model.SchoolId,
 IsReport = model.IsReport,
 FundTypes = model.FundTypes,
 FundTypeName = model.FundTypeName,
 IsQuota = model.IsQuota,
 Quota = model.Quota,
 UserId = model.UserId,
 RegTime = model.RegTime,


 temp.SchoolId = model.SchoolId,
 temp.IsReport = model.IsReport,
 temp.FundTypes = model.FundTypes,
 temp.FundTypeName = model.FundTypeName,
 temp.IsQuota = model.IsQuota,
 temp.Quota = model.Quota,
 temp.UserId = model.UserId,
 temp.RegTime = model.RegTime,

 SchoolCapitalConfigureId = item.SchoolCapitalConfigureId,
 SchoolId = item.SchoolId,
 IsReport = item.IsReport,
 FundTypes = item.FundTypes,
 FundTypeName = item.FundTypeName,
 IsQuota = item.IsQuota,
 Quota = item.Quota,
 UserId = item.UserId,
 RegTime = item.RegTime,

public class SchoolCapitalConfigureInputModel
{
 [Display(Name = "学校资金配置Id")] 
    public int SchoolCapitalConfigureId {get; set; }
    
 [Display(Name = "学校id(UnitId)")] 
    public int SchoolId {get; set; }
    
 [Display(Name = "是否上报")] 
    public int IsReport {get; set; }
    
 [Display(Name = "上报资金(存储资金类型表示以（，）分隔)")] 
    public string FundTypes {get; set; }
    
 [Display(Name = "上报资金类型名称以（，）分隔")] 
    public string FundTypeName {get; set; }
    
 [Display(Name = "是否限额")] 
    public int IsQuota {get; set; }
    
 [Display(Name = "采购限额值")] 
    public decimal Quota {get; set; }
    
 [Display(Name = "记录人")] 
    public long UserId {get; set; }
    
 [Display(Name = "记录时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class SchoolCapitalConfigureViewModel
 {
    /// <summary>
    /// 学校资金配置Id
    /// </summary>
    public int SchoolCapitalConfigureId {get; set; }
    
    /// <summary>
    /// 学校id(UnitId)
    /// </summary>
    public int SchoolId {get; set; }
    
    /// <summary>
    /// 是否上报
    /// </summary>
    public int IsReport {get; set; }
    
    /// <summary>
    /// 上报资金(存储资金类型表示以（，）分隔)
    /// </summary>
    public string FundTypes {get; set; }
    
    /// <summary>
    /// 上报资金类型名称以（，）分隔
    /// </summary>
    public string FundTypeName {get; set; }
    
    /// <summary>
    /// 是否限额
    /// </summary>
    public int IsQuota {get; set; }
    
    /// <summary>
    /// 采购限额值
    /// </summary>
    public decimal Quota {get; set; }
    
    /// <summary>
    /// 记录人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                <label class="col-md-3 control-label" for="SchoolId">学校id(UnitId) </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校id(UnitId)" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="IsReport">是否上报 </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsReport, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否上报" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="FundTypes">上报资金(存储资金类型表示以（，）分隔) </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FundTypes, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上报资金(存储资金类型表示以（，）分隔)" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="FundTypeName">上报资金类型名称以（，）分隔 </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FundTypeName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入上报资金类型名称以（，）分隔" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="IsQuota">是否限额 </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsQuota, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否限额" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="Quota">采购限额值 </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Quota, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入采购限额值" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="UserId">记录人 </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录人" } })                    
                </div>
           </div>
        <div class="form-group">
                <label class="col-md-3 control-label" for="RegTime">记录时间 </label>
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入记录时间" } })                    
                </div>
           </div>
  




 { field: 'SchoolId', title: '学校id(UnitId)', sortable: true },
                 
 { field: 'IsReport', title: '是否上报', sortable: true },
                 
 { field: 'FundTypes', title: '上报资金(存储资金类型表示以（，）分隔)', sortable: true },
                 
 { field: 'FundTypeName', title: '上报资金类型名称以（，）分隔', sortable: true },
                 
 { field: 'IsQuota', title: '是否限额', sortable: true },
                 
 { field: 'Quota', title: '采购限额值', sortable: true },
                 
 { field: 'UserId', title: '记录人', sortable: true },
                 
 { field: 'RegTime', title: '记录时间', sortable: true },
                 
o.SchoolId,                 
o.IsReport,                 
o.FundTypes,                 
o.FundTypeName,                 
o.IsQuota,                 
o.Quota,                 
o.UserId,                 
o.RegTime,                 
        
        $('#SchoolId').val(d.data.rows.SchoolId);          
        $('#IsReport').val(d.data.rows.IsReport);          
        $('#FundTypes').val(d.data.rows.FundTypes);          
        $('#FundTypeName').val(d.data.rows.FundTypeName);          
        $('#IsQuota').val(d.data.rows.IsQuota);          
        $('#Quota').val(d.data.rows.Quota);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_SchoolId').html(' 学校id(UnitId)');               
 $('#th_IsReport').html(' 是否上报');               
 $('#th_FundTypes').html(' 上报资金(存储资金类型表示以（，）分隔)');               
 $('#th_FundTypeName').html(' 上报资金类型名称以（，）分隔');               
 $('#th_IsQuota').html(' 是否限额');               
 $('#th_Quota').html(' 采购限额值');               
 $('#th_UserId').html(' 记录人');               
 $('#th_RegTime').html(' 记录时间');               
 
 $('#tr_SchoolId').hide();               
 $('#tr_IsReport').hide();               
 $('#tr_FundTypes').hide();               
 $('#tr_FundTypeName').hide();               
 $('#tr_IsQuota').hide();               
 $('#tr_Quota').hide();               
 $('#tr_UserId').hide();               
 $('#tr_RegTime').hide();               

 , "SchoolId" : schoolId
 , "IsReport" : isReport
 , "FundTypes" : fundTypes
 , "FundTypeName" : fundTypeName
 , "IsQuota" : isQuota
 , "Quota" : quota
 , "UserId" : userId
 , "RegTime" : regTime

 var schoolId = $('#o_SchoolId').val();
 var isReport = $('#o_IsReport').val();
 var fundTypes = $('#o_FundTypes').val();
 var fundTypeName = $('#o_FundTypeName').val();
 var isQuota = $('#o_IsQuota').val();
 var quota = $('#o_Quota').val();
 var userId = $('#o_UserId').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校id(UnitId)' : '产品名称', d.data.rows.SchoolId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否上报' : '产品名称', d.data.rows.IsReport);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上报资金(存储资金类型表示以（，）分隔)' : '产品名称', d.data.rows.FundTypes);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '上报资金类型名称以（，）分隔' : '产品名称', d.data.rows.FundTypeName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否限额' : '产品名称', d.data.rows.IsQuota);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '采购限额值' : '产品名称', d.data.rows.Quota);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '记录时间' : '产品名称', d.data.rows.RegTime);



