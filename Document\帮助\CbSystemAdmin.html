﻿ LoginName = entity.LoginName,
 Password = entity.Password,
 IsLock = entity.IsLock,


 LoginName = model.LoginName,
 Password = model.Password,
 IsLock = model.IsLock,


 temp.LoginName = model.LoginName,
 temp.Password = model.Password,
 temp.IsLock = model.IsLock,

 AdminId = item.AdminId,
 LoginName = item.LoginName,
 Password = item.Password,
 IsLock = item.IsLock,

public class SystemAdminInputModel
{
 [Display(Name = "系统管理员Id")] 
    public int AdminId {get; set; }
    
 [Display(Name = "登录名")] 
    public string LoginName {get; set; }
    
 [Display(Name = "密码")] 
    public string Password {get; set; }
    
 [Display(Name = "是否锁定")] 
    public bool IsLock {get; set; }
    
 }
 
 public class SystemAdminViewModel
 {
    /// <summary>
    /// 系统管理员Id
    /// </summary>
    public int AdminId {get; set; }
    
    /// <summary>
    /// 登录名
    /// </summary>
    public string LoginName {get; set; }
    
    /// <summary>
    /// 密码
    /// </summary>
    public string Password {get; set; }
    
    /// <summary>
    /// 是否锁定
    /// </summary>
    public bool IsLock {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.LoginName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LoginName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入登录名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Password, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Password, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入密码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsLock, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsLock, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否锁定" } })                    
                </div>
           </div>
  




 { field: 'LoginName', title: '登录名', sortable: true },
                 
 { field: 'Password', title: '密码', sortable: true },
                 
 { field: 'IsLock', title: '是否锁定', sortable: true },
                 
o.LoginName,                 
o.Password,                 
o.IsLock,                 
        
        $('#LoginName').val(d.data.rows.LoginName);          
        $('#Password').val(d.data.rows.Password);          
        $('#IsLock').val(d.data.rows.IsLock);          

 $('#th_LoginName').html(' 登录名');               
 $('#th_Password').html(' 密码');               
 $('#th_IsLock').html(' 是否锁定');               
 
 $('#tr_LoginName').hide();               
 $('#tr_Password').hide();               
 $('#tr_IsLock').hide();               

 , "LoginName" : loginName
 , "Password" : password
 , "IsLock" : isLock

 var loginName = $('#o_LoginName').val();
 var password = $('#o_Password').val();
 var isLock = $('#o_IsLock').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '登录名' : '产品名称', d.data.rows.LoginName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '密码' : '产品名称', d.data.rows.Password);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否锁定' : '产品名称', d.data.rows.IsLock);



