﻿using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.Api.Filters;
using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Dqy.Instrument.Api.Controllers
{
    [RoutePrefix("api/unitlink")]
    public class UnitLinkController : ApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IUUnitLinkApplicationService _iUnitLinkApplicationService;
        private readonly IBDictionaryApplicationService _iBDictionaryApplicationService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="iUnitLinkApplicationService"></param>
        public UnitLinkController(IUUnitLinkApplicationService iUnitLinkApplicationService, IBDictionaryApplicationService iBDictionaryApplicationService)
        {
            _iUnitLinkApplicationService = iUnitLinkApplicationService;
            _iBDictionaryApplicationService = iBDictionaryApplicationService;
        }

        /// <summary>
        /// 获取单位联系人信息
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postunitlink")]
        public async Task<QueryResult<UnitLinkInputModel>> SearchUnitLink(SearchUnitInputModel args)
        {
            return await Task.Run(() =>
            {
                QueryResult<UnitLinkInputModel> listUnitLink = new QueryResult<UnitLinkInputModel>();
                var sessionBag = SessionContainer.GetSession(args.Token);
                if (sessionBag == null)
                {
                    listUnitLink.flag = -1;
                    return listUnitLink;
                }
                if (sessionBag.UnitId != args.UnitId)
                {
                    //无权操作
                    listUnitLink.flag = -2;
                    return listUnitLink;
                }
                listUnitLink= _iUnitLinkApplicationService.SearchUnitLink(args);
                return listUnitLink;
            });
        }

        /// <summary>
        ///  添加单位联系人
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postaddunitlink")]
        [ValidateModel]
        public async Task<ReturnResult> AddUnitLink(UnitLinkInputModel unitLink)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(unitLink.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAuditor.ToInt() &&
                sessionBag.UserType!=UserRoleType.SchoolAuditor.ToInt() &&
                sessionBag.UserType!=UserRoleType.CityAuditor.ToInt() &&
                sessionBag.UserType!=UserRoleType.SellerAdmin.ToInt()
                )
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                r = _iUnitLinkApplicationService.AddUnitLInk(unitLink);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取单位联系人信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getunitlink")]
        public async Task<ReturnResult> GetUnitLink(string token, int unitId, int unitLinkId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAuditor.ToInt() &&
               sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
               sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
               sessionBag.UserType != UserRoleType.SellerAdmin.ToInt()
               )
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                r = _iUnitLinkApplicationService.GetModelInfo(unitLinkId);
                return r;
            });
            return result;
        }


        /// <summary>
        /// 修改单位联系人信息
        /// </summary>
        /// <param name="userInput"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("postmodifyunitlink")]
        [ValidateModel]
        public async Task<ReturnResult> ModifyUnitLink(UnitLinkInputModel userInput)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(userInput.Token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAuditor.ToInt() &&
                    sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
                    sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
                    sessionBag.UserType != UserRoleType.SellerAdmin.ToInt()
                    )
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                r = _iUnitLinkApplicationService.Modify(userInput);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 删除单位联系人
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="userId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getdel")]
        public async Task<ReturnResult> GetDel(string token, int Id, int unitId)
        {
            var result = await Task.Run(() =>
            {
                ReturnResult r = new ReturnResult();
                var sessionBag = SessionContainer.GetSession(token);
                if (sessionBag == null)
                {
                    r.flag = -1;
                    r.msg = "登录超时，请重新登录。";
                    return r;
                }
                if (sessionBag.UserType != UserRoleType.CountyAuditor.ToInt() &&
                   sessionBag.UserType != UserRoleType.SchoolAuditor.ToInt() &&
                   sessionBag.UserType != UserRoleType.CityAuditor.ToInt() &&
                   sessionBag.UserType != UserRoleType.SellerAdmin.ToInt()
                   )
                {
                    r.flag = -2;
                    r.msg = "您无权限操作此功能";
                    return r;
                }
                r = _iUnitLinkApplicationService.Delete(Id, unitId);
                return r;
            });
            return result;
        }

        /// <summary>
        /// 获取单位联系人类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("getlinkdictory")]
        public async Task<ReturnResult> GetDictionaryList()
        {
            var result = await Task.Run(() =>
            {
                return _iBDictionaryApplicationService.GetList("2001");
            });
            return result;
        }
    }
}
