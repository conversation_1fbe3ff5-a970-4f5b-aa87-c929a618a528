﻿using System.Runtime.CompilerServices;

#region Using directives

using System;
using System.Reflection;
using System.Resources;
using System.Runtime.InteropServices;

#endregion

// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
#if NETCOREAPP2_0
[assembly: AssemblyTitle("Aliyun.OSS.Core.dll")]
#else
[assembly: AssemblyTitle("Aliyun.OSS.dll")]
#endif
[assembly: AssemblyDescription("Aliyun OSS SDK for C#")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Alibaba Cloud Computing")]
[assembly: AssemblyProduct("Aliyun OSS SDK for C#")]
[assembly: AssemblyCopyright("Copyright 2012")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: NeutralResourcesLanguage("zh-CN")]

// This sets the default COM visibility of types in the assembly to invisible.
// If you need to expose a type to COM, use [ComVisible(true)] on that type.
[assembly: ComVisible(false)]

// The assembly version has following format :
//
// Major.Minor.Build.Revision
//
[assembly: AssemblyVersion("2.9.1")]

// The asembly is designed as CLS compliant to support CLS-compliant languages.
//[assembly: CLSCompliant(true)]

[assembly: InternalsVisibleTo("Aliyun.OSS.Test, PublicKey=002400000480000094000000060200000024000052534131000400000100010045C2B8CBBFE7B414DEE24D990688805C04B57ABB8292CEC3CFBCF4C7F6BD8254C8DDEA76F8EA035D106914678AAE9DB8BA4BF1669637043DBE62E1DE2B978729CF6F3DD0080AC2209559371D26219B50309EFDA1D51800DE052B0A45C7C9238884EEA4E7DC3C595B4930785A33A90ED4A6869285C3C04AD95245C0DFC00D24CC")]
[assembly: AssemblyFileVersion("2.9.1")]


