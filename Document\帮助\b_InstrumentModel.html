﻿ InstrumentLogicId = entity.InstrumentLogicId,
 Model = entity.Model,
 Code = entity.Code,


 InstrumentLogicId = model.InstrumentLogicId,
 Model = model.Model,
 Code = model.Code,


 temp.InstrumentLogicId = model.InstrumentLogicId,
 temp.Model = model.Model,
 temp.Code = model.Code,

 InstrumentModelId = item.InstrumentModelId,
 InstrumentLogicId = item.InstrumentLogicId,
 Model = item.Model,
 Code = item.Code,

public class InstrumentModelInputModel
{
 [Display(Name = "Id")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "仪器逻辑库Id")] 
    public int InstrumentLogicId {get; set; }
    
 [Display(Name = "规格型号")] 
    public string Model {get; set; }
    
 [Display(Name = "编码")] 
    public string Code {get; set; }
    
 }
 
 public class InstrumentModelViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 仪器逻辑库Id
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
    /// <summary>
    /// 规格型号
    /// </summary>
    public string Model {get; set; }
    
    /// <summary>
    /// 编码
    /// </summary>
    public string Code {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入仪器逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Model, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Model, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入编码" } })                    
                </div>
           </div>
  




 { field: 'InstrumentLogicId', title: '仪器逻辑库Id', sortable: true },
                 
 { field: 'Model', title: '规格型号', sortable: true },
                 
 { field: 'Code', title: '编码', sortable: true },
                 
o.InstrumentLogicId,                 
o.Model,                 
o.Code,                 
        
        $('#InstrumentLogicId').val(d.data.rows.InstrumentLogicId);          
        $('#Model').val(d.data.rows.Model);          
        $('#Code').val(d.data.rows.Code);          

 $('#th_InstrumentLogicId').html(' 仪器逻辑库Id');               
 $('#th_Model').html(' 规格型号');               
 $('#th_Code').html(' 编码');               
 
 $('#tr_InstrumentLogicId').hide();               
 $('#tr_Model').hide();               
 $('#tr_Code').hide();               

 , "InstrumentLogicId" : instrumentLogicId
 , "Model" : model
 , "Code" : code

 var instrumentLogicId = $('#o_InstrumentLogicId').val();
 var model = $('#o_Model').val();
 var code = $('#o_Code').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '仪器逻辑库Id' : '产品名称', d.data.rows.InstrumentLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号' : '产品名称', d.data.rows.Model);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '编码' : '产品名称', d.data.rows.Code);



