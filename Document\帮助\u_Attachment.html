﻿ ObjectId = entity.ObjectId,
 ModuleId = entity.ModuleId,
 Title = entity.Title,
 Path = entity.Path,
 Width = entity.Width,
 Height = entity.Height,
 DocType = entity.DocType,
 IsDefault = entity.IsDefault,
 Remark = entity.Remark,
 UserId = entity.UserId,
 UnitId = entity.UnitId,
 IsAudit = entity.IsAudit,
 Approver = entity.Approver,
 AuditDate = entity.AuditDate,
 AuditResult = entity.AuditResult,
 FileCategory = entity.FileCategory,
 ObjectTypeExt = entity.ObjectTypeExt,
 IsDelete = entity.IsDelete,
 IsShow = entity.IsShow,
 Ext = entity.Ext,


 ObjectId = model.ObjectId,
 ModuleId = model.ModuleId,
 Title = model.Title,
 Path = model.Path,
 Width = model.Width,
 Height = model.Height,
 DocType = model.DocType,
 IsDefault = model.IsDefault,
 Remark = model.Remark,
 UserId = model.UserId,
 UnitId = model.UnitId,
 IsAudit = model.IsAudit,
 Approver = model.Approver,
 AuditDate = model.AuditDate,
 AuditResult = model.AuditResult,
 FileCategory = model.FileCategory,
 ObjectTypeExt = model.ObjectTypeExt,
 IsDelete = model.IsDelete,
 IsShow = model.IsShow,
 Ext = model.Ext,


 temp.ObjectId = model.ObjectId,
 temp.ModuleId = model.ModuleId,
 temp.Title = model.Title,
 temp.Path = model.Path,
 temp.Width = model.Width,
 temp.Height = model.Height,
 temp.DocType = model.DocType,
 temp.IsDefault = model.IsDefault,
 temp.Remark = model.Remark,
 temp.UserId = model.UserId,
 temp.UnitId = model.UnitId,
 temp.IsAudit = model.IsAudit,
 temp.Approver = model.Approver,
 temp.AuditDate = model.AuditDate,
 temp.AuditResult = model.AuditResult,
 temp.FileCategory = model.FileCategory,
 temp.ObjectTypeExt = model.ObjectTypeExt,
 temp.IsDelete = model.IsDelete,
 temp.IsShow = model.IsShow,
 temp.Ext = model.Ext,

 AttachmentId = item.AttachmentId,
 ObjectId = item.ObjectId,
 ModuleId = item.ModuleId,
 Title = item.Title,
 Path = item.Path,
 Width = item.Width,
 Height = item.Height,
 DocType = item.DocType,
 IsDefault = item.IsDefault,
 Remark = item.Remark,
 UserId = item.UserId,
 UnitId = item.UnitId,
 IsAudit = item.IsAudit,
 Approver = item.Approver,
 AuditDate = item.AuditDate,
 AuditResult = item.AuditResult,
 FileCategory = item.FileCategory,
 ObjectTypeExt = item.ObjectTypeExt,
 IsDelete = item.IsDelete,
 IsShow = item.IsShow,
 Ext = item.Ext,

public class AttachmentInputModel
{
 [Display(Name = "Id")] 
    public long AttachmentId {get; set; }
    
 [Display(Name = "对象编号")] 
    public int ObjectId {get; set; }
    
 [Display(Name = "模块编号")] 
    public int ModuleId {get; set; }
    
 [Display(Name = "标题")] 
    public string Title {get; set; }
    
 [Display(Name = "路径")] 
    public string Path {get; set; }
    
 [Display(Name = "宽度px")] 
    public int Width {get; set; }
    
 [Display(Name = "高度px")] 
    public int Height {get; set; }
    
 [Display(Name = "txt、Jpg、png")] 
    public int DocType {get; set; }
    
 [Display(Name = "是否默认")] 
    public int IsDefault {get; set; }
    
 [Display(Name = "备注")] 
    public string Remark {get; set; }
    
 [Display(Name = "用户Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "是否审核")] 
    public int IsAudit {get; set; }
    
 [Display(Name = "审核人Id")] 
    public long Approver {get; set; }
    
 [Display(Name = "审核时间")] 
    public DateTime AuditDate {get; set; }
    
 [Display(Name = "审核未通过原因")] 
    public string AuditResult {get; set; }
    
 [Display(Name = "文件分类（1：代理许可证，2：其他）")] 
    public int FileCategory {get; set; }
    
 [Display(Name = "类型区分扩展（同一个模块对象编号来自不同表的，防止id冲突，需要区分）比如合同，来自主表或分项表")] 
    public int ObjectTypeExt {get; set; }
    
 [Display(Name = "是否删除（1,：删除；0：未删除）修改删除时，IsShow仍然是1，普通删除IsShow置0")] 
    public int IsDelete {get; set; }
    
 [Display(Name = "是否显示（默认0，不显示，审核通过后显示1）")] 
    public int IsShow {get; set; }
    
 [Display(Name = "扩展名")] 
    public string Ext {get; set; }
    
 }
 
 public class AttachmentViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long AttachmentId {get; set; }
    
    /// <summary>
    /// 对象编号
    /// </summary>
    public int ObjectId {get; set; }
    
    /// <summary>
    /// 模块编号
    /// </summary>
    public int ModuleId {get; set; }
    
    /// <summary>
    /// 标题
    /// </summary>
    public string Title {get; set; }
    
    /// <summary>
    /// 路径
    /// </summary>
    public string Path {get; set; }
    
    /// <summary>
    /// 宽度px
    /// </summary>
    public int Width {get; set; }
    
    /// <summary>
    /// 高度px
    /// </summary>
    public int Height {get; set; }
    
    /// <summary>
    /// txt、Jpg、png
    /// </summary>
    public int DocType {get; set; }
    
    /// <summary>
    /// 是否默认
    /// </summary>
    public int IsDefault {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Remark {get; set; }
    
    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 是否审核
    /// </summary>
    public int IsAudit {get; set; }
    
    /// <summary>
    /// 审核人Id
    /// </summary>
    public long Approver {get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime AuditDate {get; set; }
    
    /// <summary>
    /// 审核未通过原因
    /// </summary>
    public string AuditResult {get; set; }
    
    /// <summary>
    /// 文件分类（1：代理许可证，2：其他）
    /// </summary>
    public int FileCategory {get; set; }
    
    /// <summary>
    /// 类型区分扩展（同一个模块对象编号来自不同表的，防止id冲突，需要区分）比如合同，来自主表或分项表
    /// </summary>
    public int ObjectTypeExt {get; set; }
    
    /// <summary>
    /// 是否删除（1,：删除；0：未删除）修改删除时，IsShow仍然是1，普通删除IsShow置0
    /// </summary>
    public int IsDelete {get; set; }
    
    /// <summary>
    /// 是否显示（默认0，不显示，审核通过后显示1）
    /// </summary>
    public int IsShow {get; set; }
    
    /// <summary>
    /// 扩展名
    /// </summary>
    public string Ext {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjectId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjectId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入对象编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ModuleId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ModuleId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入模块编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Title, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Title, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入标题" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Path, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Path, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Width, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Width, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入宽度px" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Height, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Height, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入高度px" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DocType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DocType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入txt、Jpg、png" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsDefault, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsDefault, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否默认" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Remark, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Remark, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsAudit, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsAudit, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否审核" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Approver, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Approver, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditDate, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditDate, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AuditResult, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AuditResult, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审核未通过原因" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FileCategory, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FileCategory, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入文件分类（1：代理许可证，2：其他）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ObjectTypeExt, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ObjectTypeExt, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类型区分扩展（同一个模块对象编号来自不同表的，防止id冲突，需要区分）比如合同，来自主表或分项表" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsDelete, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsDelete, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否删除（1,：删除；0：未删除）修改删除时，IsShow仍然是1，普通删除IsShow置0" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsShow, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsShow, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否显示（默认0，不显示，审核通过后显示1）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Ext, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Ext, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入扩展名" } })                    
                </div>
           </div>
  




 { field: 'ObjectId', title: '对象编号', sortable: true },
                 
 { field: 'ModuleId', title: '模块编号', sortable: true },
                 
 { field: 'Title', title: '标题', sortable: true },
                 
 { field: 'Path', title: '路径', sortable: true },
                 
 { field: 'Width', title: '宽度px', sortable: true },
                 
 { field: 'Height', title: '高度px', sortable: true },
                 
 { field: 'DocType', title: 'txt、Jpg、png', sortable: true },
                 
 { field: 'IsDefault', title: '是否默认', sortable: true },
                 
 { field: 'Remark', title: '备注', sortable: true },
                 
 { field: 'UserId', title: '用户Id', sortable: true },
                 
 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'IsAudit', title: '是否审核', sortable: true },
                 
 { field: 'Approver', title: '审核人Id', sortable: true },
                 
 { field: 'AuditDate', title: '审核时间', sortable: true },
                 
 { field: 'AuditResult', title: '审核未通过原因', sortable: true },
                 
 { field: 'FileCategory', title: '文件分类（1：代理许可证，2：其他）', sortable: true },
                 
 { field: 'ObjectTypeExt', title: '类型区分扩展（同一个模块对象编号来自不同表的，防止id冲突，需要区分）比如合同，来自主表或分项表', sortable: true },
                 
 { field: 'IsDelete', title: '是否删除（1,：删除；0：未删除）修改删除时，IsShow仍然是1，普通删除IsShow置0', sortable: true },
                 
 { field: 'IsShow', title: '是否显示（默认0，不显示，审核通过后显示1）', sortable: true },
                 
 { field: 'Ext', title: '扩展名', sortable: true },
                 
o.ObjectId,                 
o.ModuleId,                 
o.Title,                 
o.Path,                 
o.Width,                 
o.Height,                 
o.DocType,                 
o.IsDefault,                 
o.Remark,                 
o.UserId,                 
o.UnitId,                 
o.IsAudit,                 
o.Approver,                 
o.AuditDate,                 
o.AuditResult,                 
o.FileCategory,                 
o.ObjectTypeExt,                 
o.IsDelete,                 
o.IsShow,                 
o.Ext,                 
        
        $('#ObjectId').val(d.data.rows.ObjectId);          
        $('#ModuleId').val(d.data.rows.ModuleId);          
        $('#Title').val(d.data.rows.Title);          
        $('#Path').val(d.data.rows.Path);          
        $('#Width').val(d.data.rows.Width);          
        $('#Height').val(d.data.rows.Height);          
        $('#DocType').val(d.data.rows.DocType);          
        $('#IsDefault').val(d.data.rows.IsDefault);          
        $('#Remark').val(d.data.rows.Remark);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#IsAudit').val(d.data.rows.IsAudit);          
        $('#Approver').val(d.data.rows.Approver);          
        $('#AuditDate').val(d.data.rows.AuditDate);          
        $('#AuditResult').val(d.data.rows.AuditResult);          
        $('#FileCategory').val(d.data.rows.FileCategory);          
        $('#ObjectTypeExt').val(d.data.rows.ObjectTypeExt);          
        $('#IsDelete').val(d.data.rows.IsDelete);          
        $('#IsShow').val(d.data.rows.IsShow);          
        $('#Ext').val(d.data.rows.Ext);          

 $('#th_ObjectId').html(' 对象编号');               
 $('#th_ModuleId').html(' 模块编号');               
 $('#th_Title').html(' 标题');               
 $('#th_Path').html(' 路径');               
 $('#th_Width').html(' 宽度px');               
 $('#th_Height').html(' 高度px');               
 $('#th_DocType').html(' txt、Jpg、png');               
 $('#th_IsDefault').html(' 是否默认');               
 $('#th_Remark').html(' 备注');               
 $('#th_UserId').html(' 用户Id');               
 $('#th_UnitId').html(' 单位Id');               
 $('#th_IsAudit').html(' 是否审核');               
 $('#th_Approver').html(' 审核人Id');               
 $('#th_AuditDate').html(' 审核时间');               
 $('#th_AuditResult').html(' 审核未通过原因');               
 $('#th_FileCategory').html(' 文件分类（1：代理许可证，2：其他）');               
 $('#th_ObjectTypeExt').html(' 类型区分扩展（同一个模块对象编号来自不同表的，防止id冲突，需要区分）比如合同，来自主表或分项表');               
 $('#th_IsDelete').html(' 是否删除（1,：删除；0：未删除）修改删除时，IsShow仍然是1，普通删除IsShow置0');               
 $('#th_IsShow').html(' 是否显示（默认0，不显示，审核通过后显示1）');               
 $('#th_Ext').html(' 扩展名');               
 
 $('#tr_ObjectId').hide();               
 $('#tr_ModuleId').hide();               
 $('#tr_Title').hide();               
 $('#tr_Path').hide();               
 $('#tr_Width').hide();               
 $('#tr_Height').hide();               
 $('#tr_DocType').hide();               
 $('#tr_IsDefault').hide();               
 $('#tr_Remark').hide();               
 $('#tr_UserId').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_IsAudit').hide();               
 $('#tr_Approver').hide();               
 $('#tr_AuditDate').hide();               
 $('#tr_AuditResult').hide();               
 $('#tr_FileCategory').hide();               
 $('#tr_ObjectTypeExt').hide();               
 $('#tr_IsDelete').hide();               
 $('#tr_IsShow').hide();               
 $('#tr_Ext').hide();               

 , "ObjectId" : objectId
 , "ModuleId" : moduleId
 , "Title" : title
 , "Path" : path
 , "Width" : width
 , "Height" : height
 , "DocType" : docType
 , "IsDefault" : isDefault
 , "Remark" : remark
 , "UserId" : userId
 , "UnitId" : unitId
 , "IsAudit" : isAudit
 , "Approver" : approver
 , "AuditDate" : auditDate
 , "AuditResult" : auditResult
 , "FileCategory" : fileCategory
 , "ObjectTypeExt" : objectTypeExt
 , "IsDelete" : isDelete
 , "IsShow" : isShow
 , "Ext" : ext

 var objectId = $('#o_ObjectId').val();
 var moduleId = $('#o_ModuleId').val();
 var title = $('#o_Title').val();
 var path = $('#o_Path').val();
 var width = $('#o_Width').val();
 var height = $('#o_Height').val();
 var docType = $('#o_DocType').val();
 var isDefault = $('#o_IsDefault').val();
 var remark = $('#o_Remark').val();
 var userId = $('#o_UserId').val();
 var unitId = $('#o_UnitId').val();
 var isAudit = $('#o_IsAudit').val();
 var approver = $('#o_Approver').val();
 var auditDate = $('#o_AuditDate').val();
 var auditResult = $('#o_AuditResult').val();
 var fileCategory = $('#o_FileCategory').val();
 var objectTypeExt = $('#o_ObjectTypeExt').val();
 var isDelete = $('#o_IsDelete').val();
 var isShow = $('#o_IsShow').val();
 var ext = $('#o_Ext').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '对象编号' : '产品名称', d.data.rows.ObjectId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '模块编号' : '产品名称', d.data.rows.ModuleId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '标题' : '产品名称', d.data.rows.Title);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '路径' : '产品名称', d.data.rows.Path);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '宽度px' : '产品名称', d.data.rows.Width);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '高度px' : '产品名称', d.data.rows.Height);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'txt、Jpg、png' : '产品名称', d.data.rows.DocType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否默认' : '产品名称', d.data.rows.IsDefault);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Remark);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否审核' : '产品名称', d.data.rows.IsAudit);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核人Id' : '产品名称', d.data.rows.Approver);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核时间' : '产品名称', d.data.rows.AuditDate);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审核未通过原因' : '产品名称', d.data.rows.AuditResult);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '文件分类（1：代理许可证，2：其他）' : '产品名称', d.data.rows.FileCategory);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类型区分扩展（同一个模块对象编号来自不同表的，防止id冲突，需要区分）比如合同，来自主表或分项表' : '产品名称', d.data.rows.ObjectTypeExt);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否删除（1,：删除；0：未删除）修改删除时，IsShow仍然是1，普通删除IsShow置0' : '产品名称', d.data.rows.IsDelete);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否显示（默认0，不显示，审核通过后显示1）' : '产品名称', d.data.rows.IsShow);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '扩展名' : '产品名称', d.data.rows.Ext);



