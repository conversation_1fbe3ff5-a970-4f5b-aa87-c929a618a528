﻿ InstrumentLogicId = entity.InstrumentLogicId,
 InstrumentModelId = entity.InstrumentModelId,
 CourseId = entity.CourseId,
 CourseName = entity.CourseName,


 InstrumentLogicId = model.InstrumentLogicId,
 InstrumentModelId = model.InstrumentModelId,
 CourseId = model.CourseId,
 CourseName = model.CourseName,


 temp.InstrumentLogicId = model.InstrumentLogicId,
 temp.InstrumentModelId = model.InstrumentModelId,
 temp.CourseId = model.CourseId,
 temp.CourseName = model.CourseName,

 SubjectId = item.SubjectId,
 InstrumentLogicId = item.InstrumentLogicId,
 InstrumentModelId = item.InstrumentModelId,
 CourseId = item.CourseId,
 CourseName = item.CourseName,

public class InstrumentSubjectInputModel
{
 [Display(Name = "Id")] 
    public int SubjectId {get; set; }
    
 [Display(Name = "仪器逻辑库Id")] 
    public int InstrumentLogicId {get; set; }
    
 [Display(Name = "规格型号表Id（没有为0）")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "学科Id")] 
    public int CourseId {get; set; }
    
 [Display(Name = "学科名称")] 
    public string CourseName {get; set; }
    
 }
 
 public class InstrumentSubjectViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int SubjectId {get; set; }
    
    /// <summary>
    /// 仪器逻辑库Id
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
    /// <summary>
    /// 规格型号表Id（没有为0）
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 学科Id
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 学科名称
    /// </summary>
    public string CourseName {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入仪器逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentModelId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentModelId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号表Id（没有为0）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科名称" } })                    
                </div>
           </div>
  




 { field: 'InstrumentLogicId', title: '仪器逻辑库Id', sortable: true },
                 
 { field: 'InstrumentModelId', title: '规格型号表Id（没有为0）', sortable: true },
                 
 { field: 'CourseId', title: '学科Id', sortable: true },
                 
 { field: 'CourseName', title: '学科名称', sortable: true },
                 
o.InstrumentLogicId,                 
o.InstrumentModelId,                 
o.CourseId,                 
o.CourseName,                 
        
        $('#InstrumentLogicId').val(d.data.rows.InstrumentLogicId);          
        $('#InstrumentModelId').val(d.data.rows.InstrumentModelId);          
        $('#CourseId').val(d.data.rows.CourseId);          
        $('#CourseName').val(d.data.rows.CourseName);          

 $('#th_InstrumentLogicId').html(' 仪器逻辑库Id');               
 $('#th_InstrumentModelId').html(' 规格型号表Id（没有为0）');               
 $('#th_CourseId').html(' 学科Id');               
 $('#th_CourseName').html(' 学科名称');               
 
 $('#tr_InstrumentLogicId').hide();               
 $('#tr_InstrumentModelId').hide();               
 $('#tr_CourseId').hide();               
 $('#tr_CourseName').hide();               

 , "InstrumentLogicId" : instrumentLogicId
 , "InstrumentModelId" : instrumentModelId
 , "CourseId" : courseId
 , "CourseName" : courseName

 var instrumentLogicId = $('#o_InstrumentLogicId').val();
 var instrumentModelId = $('#o_InstrumentModelId').val();
 var courseId = $('#o_CourseId').val();
 var courseName = $('#o_CourseName').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '仪器逻辑库Id' : '产品名称', d.data.rows.InstrumentLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号表Id（没有为0）' : '产品名称', d.data.rows.InstrumentModelId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科Id' : '产品名称', d.data.rows.CourseId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科名称' : '产品名称', d.data.rows.CourseName);



