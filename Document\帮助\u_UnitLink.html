﻿ UnitId = entity.UnitId,
 LinkType = entity.LinkType,
 LinkMan = entity.LinkMan,
 LinkPhone = entity.LinkPhone,
 LinkTel = entity.LinkTel,


 UnitId = model.UnitId,
 LinkType = model.LinkType,
 LinkMan = model.LinkMan,
 LinkPhone = model.LinkPhone,
 LinkTel = model.LinkTel,


 temp.UnitId = model.UnitId,
 temp.LinkType = model.LinkType,
 temp.LinkMan = model.LinkMan,
 temp.LinkPhone = model.LinkPhone,
 temp.LinkTel = model.LinkTel,

 UnitLinkId = item.UnitLinkId,
 UnitId = item.UnitId,
 LinkType = item.LinkType,
 LinkMan = item.LinkMan,
 LinkPhone = item.LinkPhone,
 LinkTel = item.LinkTel,

public class UnitLinkInputModel
{
 [Display(Name = "Id")] 
    public int UnitLinkId {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "1：单位联系人，2：单位负责人")] 
    public int LinkType {get; set; }
    
 [Display(Name = "联系人")] 
    public string LinkMan {get; set; }
    
 [Display(Name = "联系手机")] 
    public string LinkPhone {get; set; }
    
 [Display(Name = "联系电话")] 
    public string LinkTel {get; set; }
    
 }
 
 public class UnitLinkViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int UnitLinkId {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 1：单位联系人，2：单位负责人
    /// </summary>
    public int LinkType {get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string LinkMan {get; set; }
    
    /// <summary>
    /// 联系手机
    /// </summary>
    public string LinkPhone {get; set; }
    
    /// <summary>
    /// 联系电话
    /// </summary>
    public string LinkTel {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LinkType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LinkType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入1：单位联系人，2：单位负责人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LinkMan, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LinkMan, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LinkPhone, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LinkPhone, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系手机" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.LinkTel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.LinkTel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入联系电话" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'LinkType', title: '1：单位联系人，2：单位负责人', sortable: true },
                 
 { field: 'LinkMan', title: '联系人', sortable: true },
                 
 { field: 'LinkPhone', title: '联系手机', sortable: true },
                 
 { field: 'LinkTel', title: '联系电话', sortable: true },
                 
o.UnitId,                 
o.LinkType,                 
o.LinkMan,                 
o.LinkPhone,                 
o.LinkTel,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#LinkType').val(d.data.rows.LinkType);          
        $('#LinkMan').val(d.data.rows.LinkMan);          
        $('#LinkPhone').val(d.data.rows.LinkPhone);          
        $('#LinkTel').val(d.data.rows.LinkTel);          

 $('#th_UnitId').html(' 单位Id');               
 $('#th_LinkType').html(' 1：单位联系人，2：单位负责人');               
 $('#th_LinkMan').html(' 联系人');               
 $('#th_LinkPhone').html(' 联系手机');               
 $('#th_LinkTel').html(' 联系电话');               
 
 $('#tr_UnitId').hide();               
 $('#tr_LinkType').hide();               
 $('#tr_LinkMan').hide();               
 $('#tr_LinkPhone').hide();               
 $('#tr_LinkTel').hide();               

 , "UnitId" : unitId
 , "LinkType" : linkType
 , "LinkMan" : linkMan
 , "LinkPhone" : linkPhone
 , "LinkTel" : linkTel

 var unitId = $('#o_UnitId').val();
 var linkType = $('#o_LinkType').val();
 var linkMan = $('#o_LinkMan').val();
 var linkPhone = $('#o_LinkPhone').val();
 var linkTel = $('#o_LinkTel').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '1：单位联系人，2：单位负责人' : '产品名称', d.data.rows.LinkType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系人' : '产品名称', d.data.rows.LinkMan);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系手机' : '产品名称', d.data.rows.LinkPhone);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '联系电话' : '产品名称', d.data.rows.LinkTel);



