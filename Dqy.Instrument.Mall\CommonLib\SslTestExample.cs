using System;
using System.Threading.Tasks;

namespace Dqy.Instrument.Mall.CommonLib
{
    /// <summary>
    /// SSL/TLS配置测试示例
    /// 演示如何使用修改后的WebApiHelper解决Windows Server 2016的SSL连接问题
    /// </summary>
    public class SslTestExample
    {
        /// <summary>
        /// 测试SSL连接的示例方法
        /// </summary>
        /// <returns></returns>
        public static async Task<string> TestSslConnection()
        {
            try
            {
                // 1. 首先确保应用程序已初始化SSL配置
                ApplicationStartup.Initialize();

                // 2. 测试数据
                var testData = new
                {
                    message = "测试SSL连接",
                    timestamp = DateTime.Now,
                    version = "1.0"
                };

                // 3. 调用API（这里使用一个示例URL，实际使用时替换为真实的API地址）
                string apiUrl = "https://api.example.com/test"; // 替换为实际的API地址
                
                // 4. 使用修改后的SendAsync方法
                // checkValidationResult参数：
                // - false: 使用标准证书验证（生产环境推荐）
                // - true: 忽略证书验证错误（仅用于开发/测试环境）
                var result = await WebApiHelper.SendAsync<dynamic>(
                    url: apiUrl,
                    data: testData,
                    sendType: CommonJsonSendType.POST,
                    timeOut: 30000, // 30秒超时
                    checkValidationResult: false // 生产环境设为false
                );

                return $"SSL连接测试成功: {result}";
            }
            catch (Exception ex)
            {
                return $"SSL连接测试失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试不同SSL配置的示例
        /// </summary>
        /// <returns></returns>
        public static async Task TestDifferentSslConfigurations()
        {
            Console.WriteLine("开始测试不同的SSL配置...");

            // 测试1: 标准配置（推荐用于生产环境）
            Console.WriteLine("\n=== 测试1: 标准SSL配置 ===");
            try
            {
                SslHelper.ConfigureSslProtocols();
                var protocols = SslHelper.GetSupportedProtocols();
                Console.WriteLine($"当前支持的协议: {protocols}");
                
                // 这里可以添加实际的API调用测试
                Console.WriteLine("标准SSL配置测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"标准SSL配置测试失败: {ex.Message}");
            }

            // 测试2: 忽略证书验证（仅用于开发/测试环境）
            Console.WriteLine("\n=== 测试2: 忽略证书验证配置 ===");
            try
            {
                string testUrl = "https://self-signed.badssl.com/"; // 自签名证书测试站点
                SslHelper.ConfigureForUrl(testUrl, ignoreCertificateErrors: true);
                
                Console.WriteLine("忽略证书验证配置完成");
                Console.WriteLine("注意: 此配置仅用于开发/测试环境，不要在生产环境中使用！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"忽略证书验证配置测试失败: {ex.Message}");
            }

            // 测试3: 重置配置
            Console.WriteLine("\n=== 测试3: 重置SSL配置 ===");
            try
            {
                SslHelper.ResetSslConfiguration();
                Console.WriteLine("SSL配置已重置为默认状态");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重置SSL配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示在Web应用程序中的使用方法
        /// </summary>
        public static void DemonstrateWebApplicationUsage()
        {
            Console.WriteLine("=== Web应用程序中的使用方法 ===");
            Console.WriteLine();
            
            Console.WriteLine("1. 在Global.asax.cs的Application_Start方法中添加:");
            Console.WriteLine("   ApplicationStartup.Initialize();");
            Console.WriteLine();
            
            Console.WriteLine("2. 或者在web.config中配置HTTP模块:");
            Console.WriteLine("   <httpModules>");
            Console.WriteLine("     <add name=\"SslConfigurationModule\" type=\"Dqy.Instrument.Mall.CommonLib.SslConfigurationModule\" />");
            Console.WriteLine("   </httpModules>");
            Console.WriteLine();
            
            Console.WriteLine("3. 在控制器或服务中正常使用WebApiHelper.SendAsync方法:");
            Console.WriteLine("   var result = await WebApiHelper.SendAsync<YourType>(url, data);");
            Console.WriteLine();
            
            Console.WriteLine("4. 如果需要忽略证书验证（仅开发/测试环境）:");
            Console.WriteLine("   var result = await WebApiHelper.SendAsync<YourType>(url, data, checkValidationResult: true);");
        }

        /// <summary>
        /// 故障排除指南
        /// </summary>
        public static void TroubleshootingGuide()
        {
            Console.WriteLine("=== SSL/TLS 故障排除指南 ===");
            Console.WriteLine();
            
            Console.WriteLine("常见错误及解决方法:");
            Console.WriteLine();
            
            Console.WriteLine("1. '请求被中止: 未能创建 SSL/TLS 安全通道'");
            Console.WriteLine("   解决方法:");
            Console.WriteLine("   - 确保已调用 ApplicationStartup.Initialize()");
            Console.WriteLine("   - 检查目标服务器是否支持TLS 1.2");
            Console.WriteLine("   - 验证服务器证书是否有效");
            Console.WriteLine();
            
            Console.WriteLine("2. '基础连接已经关闭: 发送时发生错误'");
            Console.WriteLine("   解决方法:");
            Console.WriteLine("   - 检查网络连接");
            Console.WriteLine("   - 确认防火墙设置");
            Console.WriteLine("   - 验证代理配置");
            Console.WriteLine();
            
            Console.WriteLine("3. 证书验证失败");
            Console.WriteLine("   解决方法:");
            Console.WriteLine("   - 检查系统时间是否正确");
            Console.WriteLine("   - 验证证书链是否完整");
            Console.WriteLine("   - 在测试环境中可以临时设置 checkValidationResult: true");
            Console.WriteLine();
            
            Console.WriteLine("Windows Server 2016特殊配置:");
            Console.WriteLine("可能需要在注册表中启用TLS 1.2支持:");
            Console.WriteLine("[HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SecurityProviders\\SCHANNEL\\Protocols\\TLS 1.2\\Client]");
            Console.WriteLine("\"DisabledByDefault\"=dword:00000000");
            Console.WriteLine("\"Enabled\"=dword:00000001");
        }
    }
}
