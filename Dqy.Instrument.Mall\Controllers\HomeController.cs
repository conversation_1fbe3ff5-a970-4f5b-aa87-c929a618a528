﻿using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.Areas.Member.Controllers;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using Dqy.Instrument.UI.ViewModels.SearchViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Controllers
{
    public class HomeController : Controller
    {
        public async Task<ActionResult> Index()
        {
            ViewBag.Tag = "暂无数据";
            ViewBag.Title = "首页";
            //var domain = Fetch.ServerDomain;
            var domain = Constant.Current_Local_Domain;
            long userId = 0;
            var AuthenticationManager = HttpContext.GetOwinContext().Authentication;
            if (AuthenticationManager != null && AuthenticationManager.User != null)
            {
                if (AuthenticationManager.User.Claims != null && AuthenticationManager.User.Claims.Count() > 0)
                {
                    var userobj = AuthenticationManager.User.Claims.FirstOrDefault(c => c.Type == @"UserId");
                    if (userobj != null)
                    {
                        userId = Convert.ToInt64(userobj.Value);
                    }

                }
            }

            string url = "main/index?domain=" + domain + "&userid=" + userId;
            HomeViewModel model = new HomeViewModel();
            try
            {
                var r = await WebApiHelper.SendAsync<HomeViewModel>(url, null, CommonTypes.CommonJsonSendType.GET);
                if (r != null)
                {
                    model = r;
                }
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog("首页数据main/index请求异常：" + e.Message+ "\r\n URL："+ url);
                ViewBag.Tag = Constant.Request_Ext_Msg;
            }
            if (model == null)
            {
                model = new HomeViewModel();
                ViewBag.Tag = Constant.Request_Lose_Msg;
            }
            model.ApiPath = Constant.ApiPath;
            model.CurrentUrl = IPOperate.GetCurrentUrl();
            return View(model);
        }

        /// <summary>
        /// 获取当前分类中的
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        public async Task<ActionResult> GetClassify(int pid)
        {
            //var domain = Fetch.ServerDomain;
            var domain = Constant.Current_Local_Domain;
            string url = "main/getclassifylist?domain=" + domain + "&pid=" + pid;
            var result = await WebApiHelper.SendAsync<List<MallClassifyViewModel>>(url, null, CommonTypes.CommonJsonSendType.GET);
            if (result == null)
            {
                return Content("");
            }
            return View("_Classify", result);
        }


        public ActionResult About()
        {
            ViewBag.Message = "Your application description page.";

            return View();
        }

        public ActionResult Contact()
        {
            ViewBag.Message = "Your contact page.";

            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <param name="indexType">索引类型</param>
        /// <param name="args">MallProductSearchInputModel 参数</param>
        /// <returns></returns>
        public async Task<ActionResult> ProductList(MallProductSearchInputModel args)
        {
            if (args == null)
            {
                args = new MallProductSearchInputModel();
            }
            args.CurrPage = 0;
            args.Limit = 8;

            //API Server服务器地址
            ViewBag.ServicePath = Constant.ApiPath.Replace("api/", "");
            ViewBag.Args = args;

            //获取学科与供应商信息
            string url = "productsearch/postsearchstats";
            var result = await WebApiHelper.SendAsync<ProductSearchStats>(url, args);
            ViewBag.ProductSearchStats = result;



            //获取列表信息
            string urlData = "productsearch/postsearchproduct";
            var resultData = await WebApiHelper.SendAsync<ProductList>(urlData, args);

            return View(resultData);
        }

        /// <summary>
        /// 获取客服代码信息
        /// </summary>
        /// <returns></returns>
        public async Task<JsonResult> GetAccessId()
        {
            var domain = Constant.Current_Local_Domain;
            string url = $"main/getaccessid?domain={domain}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            return Json(r, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <param name="indexType">索引类型</param>
        /// <param name="args">MallProductSearchInputModel 参数</param>
        /// <returns></returns>
        //public async Task<ActionResult> SearchProductList(MallProductSearchInputModel args)
        //{
        //    if (args == null)
        //    {
        //        args = new MallProductSearchInputModel();
        //    }
        //    //获取学科与供应商信息
        //    string url = Constant.ApiPath + "productsearch/postsearchstats";
        //    var result = await WebApiHelper.SendAsync<ProductSearchStats>(url, args);
        //    ViewBag.ProductSearchStats = result;
        //    //
        //    ViewBag.Args = args;
        //    //获取列表信息
        //    string urlData = Constant.ApiPath + "productsearch/postsearchproduct";
        //    var resultData = await WebApiHelper.SendAsync<ProductList>(urlData, args);

        //    return View("ProductList", resultData);
        //}
    }
}