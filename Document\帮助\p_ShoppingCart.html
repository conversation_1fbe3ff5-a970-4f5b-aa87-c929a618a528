﻿ ProductShelvesId = entity.ProductShelvesId,
 MallId = entity.MallId,
 CourseId = entity.CourseId,
 ProductNormPriceId = entity.ProductNormPriceId,
 Price = entity.Price,
 Num = entity.Num,
 Sum = entity.Sum,
 JoinTime = entity.JoinTime,
 SchoolId = entity.SchoolId,
 UserId = entity.UserId,
 Priority = entity.Priority,


 ProductShelvesId = model.ProductShelvesId,
 MallId = model.MallId,
 CourseId = model.CourseId,
 ProductNormPriceId = model.ProductNormPriceId,
 Price = model.Price,
 Num = model.Num,
 Sum = model.Sum,
 JoinTime = model.JoinTime,
 SchoolId = model.SchoolId,
 UserId = model.UserId,
 Priority = model.Priority,


 temp.ProductShelvesId = model.ProductShelvesId,
 temp.MallId = model.MallId,
 temp.CourseId = model.CourseId,
 temp.ProductNormPriceId = model.ProductNormPriceId,
 temp.Price = model.Price,
 temp.Num = model.Num,
 temp.Sum = model.Sum,
 temp.JoinTime = model.JoinTime,
 temp.SchoolId = model.SchoolId,
 temp.UserId = model.UserId,
 temp.Priority = model.Priority,

 ShoppingCartId = item.ShoppingCartId,
 ProductShelvesId = item.ProductShelvesId,
 MallId = item.MallId,
 CourseId = item.CourseId,
 ProductNormPriceId = item.ProductNormPriceId,
 Price = item.Price,
 Num = item.Num,
 Sum = item.Sum,
 JoinTime = item.JoinTime,
 SchoolId = item.SchoolId,
 UserId = item.UserId,
 Priority = item.Priority,

public class ShoppingCartInputModel
{
 [Display(Name = "Id")] 
    public long ShoppingCartId {get; set; }
    
 [Display(Name = "产品上架Id")] 
    public long ProductShelvesId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "学科Id")] 
    public int CourseId {get; set; }
    
 [Display(Name = "规格价格表Id")] 
    public long ProductNormPriceId {get; set; }
    
 [Display(Name = "价格")] 
    public decimal Price {get; set; }
    
 [Display(Name = "数量")] 
    public decimal Num {get; set; }
    
 [Display(Name = "金额")] 
    public decimal Sum {get; set; }
    
 [Display(Name = "加入时间")] 
    public DateTime JoinTime {get; set; }
    
 [Display(Name = "学校Id（UnitId）")] 
    public int SchoolId {get; set; }
    
 [Display(Name = "用户Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "(现在用 0： 默认是0(删除也是0) , 3 （设置是3）)")] 
    public int Priority {get; set; }
    
 }
 
 public class ShoppingCartViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ShoppingCartId {get; set; }
    
    /// <summary>
    /// 产品上架Id
    /// </summary>
    public long ProductShelvesId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 学科Id
    /// </summary>
    public int CourseId {get; set; }
    
    /// <summary>
    /// 规格价格表Id
    /// </summary>
    public long ProductNormPriceId {get; set; }
    
    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price {get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal Num {get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Sum {get; set; }
    
    /// <summary>
    /// 加入时间
    /// </summary>
    public DateTime JoinTime {get; set; }
    
    /// <summary>
    /// 学校Id（UnitId）
    /// </summary>
    public int SchoolId {get; set; }
    
    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// (现在用 0： 默认是0(删除也是0) , 3 （设置是3）)
    /// </summary>
    public int Priority {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductShelvesId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductShelvesId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品上架Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CourseId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CourseId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学科Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductNormPriceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductNormPriceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格价格表Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Price, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Price, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入价格" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Num, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Num, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入数量" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sum, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sum, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入金额" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.JoinTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.JoinTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入加入时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SchoolId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SchoolId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入学校Id（UnitId）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Priority, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Priority, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入(现在用 0： 默认是0(删除也是0) , 3 （设置是3）)" } })                    
                </div>
           </div>
  




 { field: 'ProductShelvesId', title: '产品上架Id', sortable: true },
                 
 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'CourseId', title: '学科Id', sortable: true },
                 
 { field: 'ProductNormPriceId', title: '规格价格表Id', sortable: true },
                 
 { field: 'Price', title: '价格', sortable: true },
                 
 { field: 'Num', title: '数量', sortable: true },
                 
 { field: 'Sum', title: '金额', sortable: true },
                 
 { field: 'JoinTime', title: '加入时间', sortable: true },
                 
 { field: 'SchoolId', title: '学校Id（UnitId）', sortable: true },
                 
 { field: 'UserId', title: '用户Id', sortable: true },
                 
 { field: 'Priority', title: '(现在用 0： 默认是0(删除也是0) , 3 （设置是3）)', sortable: true },
                 
o.ProductShelvesId,                 
o.MallId,                 
o.CourseId,                 
o.ProductNormPriceId,                 
o.Price,                 
o.Num,                 
o.Sum,                 
o.JoinTime,                 
o.SchoolId,                 
o.UserId,                 
o.Priority,                 
        
        $('#ProductShelvesId').val(d.data.rows.ProductShelvesId);          
        $('#MallId').val(d.data.rows.MallId);          
        $('#CourseId').val(d.data.rows.CourseId);          
        $('#ProductNormPriceId').val(d.data.rows.ProductNormPriceId);          
        $('#Price').val(d.data.rows.Price);          
        $('#Num').val(d.data.rows.Num);          
        $('#Sum').val(d.data.rows.Sum);          
        $('#JoinTime').val(d.data.rows.JoinTime);          
        $('#SchoolId').val(d.data.rows.SchoolId);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#Priority').val(d.data.rows.Priority);          

 $('#th_ProductShelvesId').html(' 产品上架Id');               
 $('#th_MallId').html(' 商城Id');               
 $('#th_CourseId').html(' 学科Id');               
 $('#th_ProductNormPriceId').html(' 规格价格表Id');               
 $('#th_Price').html(' 价格');               
 $('#th_Num').html(' 数量');               
 $('#th_Sum').html(' 金额');               
 $('#th_JoinTime').html(' 加入时间');               
 $('#th_SchoolId').html(' 学校Id（UnitId）');               
 $('#th_UserId').html(' 用户Id');               
 $('#th_Priority').html(' (现在用 0： 默认是0(删除也是0) , 3 （设置是3）)');               
 
 $('#tr_ProductShelvesId').hide();               
 $('#tr_MallId').hide();               
 $('#tr_CourseId').hide();               
 $('#tr_ProductNormPriceId').hide();               
 $('#tr_Price').hide();               
 $('#tr_Num').hide();               
 $('#tr_Sum').hide();               
 $('#tr_JoinTime').hide();               
 $('#tr_SchoolId').hide();               
 $('#tr_UserId').hide();               
 $('#tr_Priority').hide();               

 , "ProductShelvesId" : productShelvesId
 , "MallId" : mallId
 , "CourseId" : courseId
 , "ProductNormPriceId" : productNormPriceId
 , "Price" : price
 , "Num" : num
 , "Sum" : sum
 , "JoinTime" : joinTime
 , "SchoolId" : schoolId
 , "UserId" : userId
 , "Priority" : priority

 var productShelvesId = $('#o_ProductShelvesId').val();
 var mallId = $('#o_MallId').val();
 var courseId = $('#o_CourseId').val();
 var productNormPriceId = $('#o_ProductNormPriceId').val();
 var price = $('#o_Price').val();
 var num = $('#o_Num').val();
 var sum = $('#o_Sum').val();
 var joinTime = $('#o_JoinTime').val();
 var schoolId = $('#o_SchoolId').val();
 var userId = $('#o_UserId').val();
 var priority = $('#o_Priority').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品上架Id' : '产品名称', d.data.rows.ProductShelvesId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学科Id' : '产品名称', d.data.rows.CourseId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格价格表Id' : '产品名称', d.data.rows.ProductNormPriceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '价格' : '产品名称', d.data.rows.Price);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '数量' : '产品名称', d.data.rows.Num);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '金额' : '产品名称', d.data.rows.Sum);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '加入时间' : '产品名称', d.data.rows.JoinTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '学校Id（UnitId）' : '产品名称', d.data.rows.SchoolId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '(现在用 0： 默认是0(删除也是0) , 3 （设置是3）)' : '产品名称', d.data.rows.Priority);



