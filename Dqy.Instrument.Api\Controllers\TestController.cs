﻿
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.InputModels;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Dqy.Instrument.Api.Controllers
{
    public class TestController : ApiController
    { 
        public TestController()
        {
             
        }
        // GET: api/Test
        public IEnumerable<string> Get()
        {
            return new string[] { "value1", "value2" };
        }

        // GET: api/Test/5
        public string Get(int id)
        {
            return "value";
        }

        // POST: api/Test
        public int Post([FromBody]CbSystemAdminInputModel model)
        {
            
            return 1;
            
        }

        // PUT: api/Test/5
        public void Put(int id, [FromBody]CbSystemAdminInputModel value)
        {
        }

        // DELETE: api/Test/5
        public void Delete(int id)
        {
        }
    }
}
