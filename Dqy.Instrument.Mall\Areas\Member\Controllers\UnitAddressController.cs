﻿using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Mall.CommonLib;
using Dqy.Instrument.UI.InputModels;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.Areas.Member.Controllers
{
    public class UnitAddressController : ControllerMember
    {
        #region 周跃峰(收货地址管理)
        /// <summary>
        /// 收货地址列表
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<ActionResult> List(int Id = 0)
        {
            UnitDeliveryAddreszInputModel m = new UnitDeliveryAddreszInputModel();
            //API地址
            ViewBag.ApiPath = Constant.ApiPath;
            //根据单位Id获取单位信息
            string url = $"unitaddress/getlist?token={Operater.Token}&UnitId={Operater.UnitId}";
            var r = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
            if (r.flag == -1)
            {
                //r.Url = Constant.Current_Local_Url + "/Account/Login";
                //return View("../User/RegSucc", r);
                return this.ApiTimeOut();
            }
            if (r.flag == -2)
            {
                r.Url = Constant.Current_Local_Url + "/Home/Index";
                return View("../User/RegSucc", r);
            }
            List<UnitDeliveryAddreszViewModel> listAddress = ComLib.JSON2Object<List<UnitDeliveryAddreszViewModel>>(r.obj.ToString());
            ViewBag.listAddress = listAddress;
            if (Id != 0)
            {
                UnitDeliveryAddreszInputModel view = null;
                url = $"unitaddress/getbyid?token={Operater.Token}&id={Id}&userId={Operater.UserId}&unitId={Operater.UnitId}";
                var rAddress = await WebApiHelper.SendAsync(url, null, CommonTypes.CommonJsonSendType.GET);
                if (rAddress.flag == 1)
                {
                    view = ComLib.JSON2Object<UnitDeliveryAddreszInputModel>(rAddress.obj.ToString());
                }
                else
                {
                    return View("../Error/Illegal");
                }
                return View(view);
            }

            return View(m);
        }

        /// <summary>
        /// 保存、更新
        /// </summary>
        /// <param name="schoolInfo"></param>
        /// <returns></returns>
        public async Task<JsonResult> Save(UnitDeliveryAddreszInputModel address)
        {
            address.Token = Operater.Token;
            address.UnitId = Operater.UnitId;
            if (string.IsNullOrEmpty(address.Tel))
            {
                address.Tel = "";
            }
            if (address.Id == 0)
            {
                //新增
                string url = Constant.ApiPath + "unitaddress/postadd";
                var result = await WebApiHelper.SendAsync(url, address, CommonJsonSendType.POST);
                Log.UnitInfo(address, result, "用户‘" + Operater.Name + "’新增收货地址", "UnitInfo", result.flag);
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            else
            {
                //修改
                string url = Constant.ApiPath + "unitaddress/postmodify";
                var result = await WebApiHelper.SendAsync(url, address, CommonJsonSendType.POST);
                Log.UnitInfo(address, result, "用户‘" + Operater.Name + "’修改收货地址", "UnitInfo", result.flag);
                return Json(result, JsonRequestBehavior.AllowGet);
            }

        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<ActionResult> Del(int Id)
        {
            string url = Constant.ApiPath + "unitaddress/getdel?token=" + Operater.Token + "&Id=" + Id + "&userId=" + Operater.UserId + "&unitId=" + Operater.UnitId + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(Id, result, "用户‘" + Operater.Name + "’删除收货地址", "UnitInfo", result.flag);
            if (result.flag == 1)
            {
                return RedirectToRoute(new { Controller = "UnitAddress", Action = "List", Id = 0 });
            }
            else
            {
                if (result.flag == -1)
                {
                    result.Url = Constant.Current_Local_Url + "/Account/Login";
                }
                if (result.flag == -2)
                {
                    result.Url = Constant.Current_Local_Url + "/Home/Index";
                }
                return View("../User/RegSucc", result);
            }
        }

        /// <summary>
        /// 删除地址
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<JsonResult> DelAddress(int Id)
        {
            string url = Constant.ApiPath + "unitaddress/getdel?token=" + Operater.Token + "&Id=" + Id + "&userId=" + Operater.UserId + "&unitId=" + Operater.UnitId + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(Id, result, "用户‘" + Operater.Name + "’删除收货地址", "UnitInfo", result.flag);
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 设置默认
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<ActionResult> SetDefault(int Id)
        {
            string url = Constant.ApiPath + "unitaddress/getsetdefault?token=" + Operater.Token + "&Id=" + Id + "&userId=" + Operater.UserId + "&unitId=" + Operater.UnitId + "";
            var result = await WebApiHelper.SendAsync(url, null, CommonJsonSendType.GET);
            Log.UnitInfo(Id, result, "用户‘" + Operater.Name + "’设为默认地址", "UnitInfo", result.flag);
            if (result.flag == 1)
            {
                return RedirectToRoute(new { Controller = "UnitAddress", Action = "List", Id = 0 });
            }
            else
            {
                if (result.flag == -1)
                {
                    result.Url = Constant.Current_Local_Url + "/Account/Login";
                }
                if (result.flag == -2)
                {
                    result.Url = Constant.Current_Local_Url + "/Home/Index";
                }
                return View("../User/RegSucc", result);
            }
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public ActionResult Modify(int Id)
        {
            return RedirectToRoute(new { Controller = "UnitAddress", Action = "List", Id = Id });
        }
        #endregion

    }
}