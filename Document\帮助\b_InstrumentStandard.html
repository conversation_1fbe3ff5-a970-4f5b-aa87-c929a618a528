﻿ Pid = entity.Pid,
 ProductCode = entity.ProductCode,
 ProductName = entity.ProductName,
 UnitName = entity.UnitName,
 Abbreviation = entity.Abbreviation,


 Pid = model.Pid,
 ProductCode = model.ProductCode,
 ProductName = model.ProductName,
 UnitName = model.UnitName,
 Abbreviation = model.Abbreviation,


 temp.Pid = model.Pid,
 temp.ProductCode = model.ProductCode,
 temp.ProductName = model.ProductName,
 temp.UnitName = model.UnitName,
 temp.Abbreviation = model.Abbreviation,

 InstrumentStandardId = item.InstrumentStandardId,
 Pid = item.Pid,
 ProductCode = item.ProductCode,
 ProductName = item.ProductName,
 UnitName = item.UnitName,
 Abbreviation = item.Abbreviation,

public class InstrumentStandardInputModel
{
 [Display(Name = "Id")] 
    public int InstrumentStandardId {get; set; }
    
 [Display(Name = "父级Id")] 
    public int Pid {get; set; }
    
 [Display(Name = "产品代码")] 
    public string ProductCode {get; set; }
    
 [Display(Name = "产品名称")] 
    public string ProductName {get; set; }
    
 [Display(Name = "单位")] 
    public string UnitName {get; set; }
    
 [Display(Name = "缩写")] 
    public string Abbreviation {get; set; }
    
 }
 
 public class InstrumentStandardViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int InstrumentStandardId {get; set; }
    
    /// <summary>
    /// 父级Id
    /// </summary>
    public int Pid {get; set; }
    
    /// <summary>
    /// 产品代码
    /// </summary>
    public string ProductCode {get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName {get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName {get; set; }
    
    /// <summary>
    /// 缩写
    /// </summary>
    public string Abbreviation {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Pid, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pid, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入父级Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Abbreviation, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Abbreviation, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入缩写" } })                    
                </div>
           </div>
  




 { field: 'Pid', title: '父级Id', sortable: true },
                 
 { field: 'ProductCode', title: '产品代码', sortable: true },
                 
 { field: 'ProductName', title: '产品名称', sortable: true },
                 
 { field: 'UnitName', title: '单位', sortable: true },
                 
 { field: 'Abbreviation', title: '缩写', sortable: true },
                 
o.Pid,                 
o.ProductCode,                 
o.ProductName,                 
o.UnitName,                 
o.Abbreviation,                 
        
        $('#Pid').val(d.data.rows.Pid);          
        $('#ProductCode').val(d.data.rows.ProductCode);          
        $('#ProductName').val(d.data.rows.ProductName);          
        $('#UnitName').val(d.data.rows.UnitName);          
        $('#Abbreviation').val(d.data.rows.Abbreviation);          

 $('#th_Pid').html(' 父级Id');               
 $('#th_ProductCode').html(' 产品代码');               
 $('#th_ProductName').html(' 产品名称');               
 $('#th_UnitName').html(' 单位');               
 $('#th_Abbreviation').html(' 缩写');               
 
 $('#tr_Pid').hide();               
 $('#tr_ProductCode').hide();               
 $('#tr_ProductName').hide();               
 $('#tr_UnitName').hide();               
 $('#tr_Abbreviation').hide();               

 , "Pid" : pid
 , "ProductCode" : productCode
 , "ProductName" : productName
 , "UnitName" : unitName
 , "Abbreviation" : abbreviation

 var pid = $('#o_Pid').val();
 var productCode = $('#o_ProductCode').val();
 var productName = $('#o_ProductName').val();
 var unitName = $('#o_UnitName').val();
 var abbreviation = $('#o_Abbreviation').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '父级Id' : '产品名称', d.data.rows.Pid);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品代码' : '产品名称', d.data.rows.ProductCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品名称' : '产品名称', d.data.rows.ProductName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位' : '产品名称', d.data.rows.UnitName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '缩写' : '产品名称', d.data.rows.Abbreviation);



