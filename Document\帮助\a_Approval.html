﻿ ProductDeclareId = entity.ProductDeclareId,
 DeclareUnitId = entity.DeclareUnitId,
 Statuz = entity.Statuz,
 ApprovalResult = entity.ApprovalResult,
 Memo = entity.Memo,
 UserId = entity.UserId,
 UnitId = entity.UnitId,
 RegTime = entity.RegTime,
 IsShow = entity.IsShow,


 ProductDeclareId = model.ProductDeclareId,
 DeclareUnitId = model.DeclareUnitId,
 Statuz = model.Statuz,
 ApprovalResult = model.ApprovalResult,
 Memo = model.Memo,
 UserId = model.UserId,
 UnitId = model.UnitId,
 RegTime = model.RegTime,
 IsShow = model.IsShow,


 temp.ProductDeclareId = model.ProductDeclareId,
 temp.DeclareUnitId = model.DeclareUnitId,
 temp.Statuz = model.Statuz,
 temp.ApprovalResult = model.ApprovalResult,
 temp.Memo = model.Memo,
 temp.UserId = model.UserId,
 temp.UnitId = model.UnitId,
 temp.RegTime = model.RegTime,
 temp.IsShow = model.IsShow,

 ApprovalId = item.ApprovalId,
 ProductDeclareId = item.ProductDeclareId,
 DeclareUnitId = item.DeclareUnitId,
 Statuz = item.Statuz,
 ApprovalResult = item.ApprovalResult,
 Memo = item.Memo,
 UserId = item.UserId,
 UnitId = item.UnitId,
 RegTime = item.RegTime,
 IsShow = item.IsShow,

public class ApprovalInputModel
{
 [Display(Name = "Id")] 
    public long ApprovalId {get; set; }
    
 [Display(Name = "产品申报Id")] 
    public long ProductDeclareId {get; set; }
    
 [Display(Name = "申报单位Id")] 
    public int DeclareUnitId {get; set; }
    
 [Display(Name = "状态=同产品申报表状态：（1:待学校审批； 2:待区县审批；  3:待市级审批；10:已下订单；20:线下采购；30:学校审批退回；31区县审批退回；32:市级审批退回；）")] 
    public int Statuz {get; set; }
    
 [Display(Name = "审批结果")] 
    public string ApprovalResult {get; set; }
    
 [Display(Name = "说明（备注，Reason退回原因）")] 
    public string Memo {get; set; }
    
 [Display(Name = "审批人")] 
    public long UserId {get; set; }
    
 [Display(Name = "审批单位")] 
    public int UnitId {get; set; }
    
 [Display(Name = "审批时间")] 
    public DateTime RegTime {get; set; }
    
 [Display(Name = "是否显示")] 
    public int IsShow {get; set; }
    
 }
 
 public class ApprovalViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ApprovalId {get; set; }
    
    /// <summary>
    /// 产品申报Id
    /// </summary>
    public long ProductDeclareId {get; set; }
    
    /// <summary>
    /// 申报单位Id
    /// </summary>
    public int DeclareUnitId {get; set; }
    
    /// <summary>
    /// 状态=同产品申报表状态：（1:待学校审批； 2:待区县审批；  3:待市级审批；10:已下订单；20:线下采购；30:学校审批退回；31区县审批退回；32:市级审批退回；）
    /// </summary>
    public int Statuz {get; set; }
    
    /// <summary>
    /// 审批结果
    /// </summary>
    public string ApprovalResult {get; set; }
    
    /// <summary>
    /// 说明（备注，Reason退回原因）
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 审批人
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 审批单位
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
    /// <summary>
    /// 是否显示
    /// </summary>
    public int IsShow {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductDeclareId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductDeclareId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品申报Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DeclareUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DeclareUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入申报单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态=同产品申报表状态：（1:待学校审批； 2:待区县审批；  3:待市级审批；10:已下订单；20:线下采购；30:学校审批退回；31区县审批退回；32:市级审批退回；）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ApprovalResult, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ApprovalResult, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批结果" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入说明（备注，Reason退回原因）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入审批时间" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsShow, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsShow, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否显示" } })                    
                </div>
           </div>
  




 { field: 'ProductDeclareId', title: '产品申报Id', sortable: true },
                 
 { field: 'DeclareUnitId', title: '申报单位Id', sortable: true },
                 
 { field: 'Statuz', title: '状态=同产品申报表状态：（1:待学校审批； 2:待区县审批；  3:待市级审批；10:已下订单；20:线下采购；30:学校审批退回；31区县审批退回；32:市级审批退回；）', sortable: true },
                 
 { field: 'ApprovalResult', title: '审批结果', sortable: true },
                 
 { field: 'Memo', title: '说明（备注，Reason退回原因）', sortable: true },
                 
 { field: 'UserId', title: '审批人', sortable: true },
                 
 { field: 'UnitId', title: '审批单位', sortable: true },
                 
 { field: 'RegTime', title: '审批时间', sortable: true },
                 
 { field: 'IsShow', title: '是否显示', sortable: true },
                 
o.ProductDeclareId,                 
o.DeclareUnitId,                 
o.Statuz,                 
o.ApprovalResult,                 
o.Memo,                 
o.UserId,                 
o.UnitId,                 
o.RegTime,                 
o.IsShow,                 
        
        $('#ProductDeclareId').val(d.data.rows.ProductDeclareId);          
        $('#DeclareUnitId').val(d.data.rows.DeclareUnitId);          
        $('#Statuz').val(d.data.rows.Statuz);          
        $('#ApprovalResult').val(d.data.rows.ApprovalResult);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#UserId').val(d.data.rows.UserId);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#RegTime').val(d.data.rows.RegTime);          
        $('#IsShow').val(d.data.rows.IsShow);          

 $('#th_ProductDeclareId').html(' 产品申报Id');               
 $('#th_DeclareUnitId').html(' 申报单位Id');               
 $('#th_Statuz').html(' 状态=同产品申报表状态：（1:待学校审批； 2:待区县审批；  3:待市级审批；10:已下订单；20:线下采购；30:学校审批退回；31区县审批退回；32:市级审批退回；）');               
 $('#th_ApprovalResult').html(' 审批结果');               
 $('#th_Memo').html(' 说明（备注，Reason退回原因）');               
 $('#th_UserId').html(' 审批人');               
 $('#th_UnitId').html(' 审批单位');               
 $('#th_RegTime').html(' 审批时间');               
 $('#th_IsShow').html(' 是否显示');               
 
 $('#tr_ProductDeclareId').hide();               
 $('#tr_DeclareUnitId').hide();               
 $('#tr_Statuz').hide();               
 $('#tr_ApprovalResult').hide();               
 $('#tr_Memo').hide();               
 $('#tr_UserId').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_RegTime').hide();               
 $('#tr_IsShow').hide();               

 , "ProductDeclareId" : productDeclareId
 , "DeclareUnitId" : declareUnitId
 , "Statuz" : statuz
 , "ApprovalResult" : approvalResult
 , "Memo" : memo
 , "UserId" : userId
 , "UnitId" : unitId
 , "RegTime" : regTime
 , "IsShow" : isShow

 var productDeclareId = $('#o_ProductDeclareId').val();
 var declareUnitId = $('#o_DeclareUnitId').val();
 var statuz = $('#o_Statuz').val();
 var approvalResult = $('#o_ApprovalResult').val();
 var memo = $('#o_Memo').val();
 var userId = $('#o_UserId').val();
 var unitId = $('#o_UnitId').val();
 var regTime = $('#o_RegTime').val();
 var isShow = $('#o_IsShow').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品申报Id' : '产品名称', d.data.rows.ProductDeclareId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '申报单位Id' : '产品名称', d.data.rows.DeclareUnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态=同产品申报表状态：（1:待学校审批； 2:待区县审批；  3:待市级审批；10:已下订单；20:线下采购；30:学校审批退回；31区县审批退回；32:市级审批退回；）' : '产品名称', d.data.rows.Statuz);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批结果' : '产品名称', d.data.rows.ApprovalResult);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '说明（备注，Reason退回原因）' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批人' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批单位' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '审批时间' : '产品名称', d.data.rows.RegTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否显示' : '产品名称', d.data.rows.IsShow);



