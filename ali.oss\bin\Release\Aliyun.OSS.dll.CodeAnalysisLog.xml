﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="d:\program files (x86)\microsoft visual studio\2019\enterprise\team tools\static analysis tools\fxcop\Xml\CodeAnalysisReport.xsl"?>
<FxCopReport Version="16.0">
 <Namespaces>
  <Namespace Name="Aliyun.OSS">
   <Messages>
    <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Name="Namespace" Certainty="85" Level="Error">更正命名空间名称“Aliyun.OSS”中“OSS”的大小写，将其改为“Oss”。</Issue>
    </Message>
   </Messages>
  </Namespace>
  <Namespace Name="Aliyun.OSS.Common">
   <Messages>
    <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Name="Namespace" Certainty="85" Level="Error">更正命名空间名称“Aliyun.OSS.Common”中“OSS”的大小写，将其改为“Oss”。</Issue>
    </Message>
   </Messages>
  </Namespace>
  <Namespace Name="Aliyun.OSS.Common.Authentication">
   <Messages>
    <Message TypeName="AvoidNamespacesWithFewTypes" Category="Microsoft.Design" CheckId="CA1020" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Certainty="50" Level="Warning">考虑将“Aliyun.OSS.Common.Authentication”中定义的类型与另一个命名空间合并。</Issue>
    </Message>
    <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Name="Namespace" Certainty="85" Level="Error">更正命名空间名称“Aliyun.OSS.Common.Authentication”中“OSS”的大小写，将其改为“Oss”。</Issue>
    </Message>
   </Messages>
  </Namespace>
  <Namespace Name="Aliyun.OSS.Common.Internal">
   <Messages>
    <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Name="Namespace" Certainty="85" Level="Error">更正命名空间名称“Aliyun.OSS.Common.Internal”中“OSS”的大小写，将其改为“Oss”。</Issue>
    </Message>
   </Messages>
  </Namespace>
  <Namespace Name="Aliyun.OSS.Common.ThirdParty">
   <Messages>
    <Message TypeName="AvoidNamespacesWithFewTypes" Category="Microsoft.Design" CheckId="CA1020" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Certainty="50" Level="Warning">考虑将“Aliyun.OSS.Common.ThirdParty”中定义的类型与另一个命名空间合并。</Issue>
    </Message>
    <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Name="Namespace" Certainty="85" Level="Error">更正命名空间名称“Aliyun.OSS.Common.ThirdParty”中“OSS”的大小写，将其改为“Oss”。</Issue>
    </Message>
   </Messages>
  </Namespace>
  <Namespace Name="Aliyun.OSS.Util">
   <Messages>
    <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
     <Issue Name="Namespace" Certainty="85" Level="Error">更正命名空间名称“Aliyun.OSS.Util”中“OSS”的大小写，将其改为“Oss”。</Issue>
    </Message>
   </Messages>
  </Namespace>
 </Namespaces>
 <Targets>
  <Target Name="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\bin\Release\Aliyun.OSS.dll">
   <Modules>
    <Module Name="aliyun.oss.dll">
     <Messages>
      <Message TypeName="AssembliesShouldHaveValidStrongNames" Category="Microsoft.Design" CheckId="CA2210" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
       <Issue Name="NoStrongName" Certainty="95" Level="CriticalError">用强名称密钥对 'Aliyun.OSS.dll' 进行签名。</Issue>
      </Message>
      <Message Id="OSS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
       <Issue Name="Assembly" Certainty="85" Level="Error">更正程序集名称 'Aliyun.OSS.dll' 中“OSS”的大小写，将其改为“Oss”。</Issue>
      </Message>
      <Message TypeName="MarkAssembliesWithClsCompliant" Category="Microsoft.Design" CheckId="CA1014" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
       <Issue Certainty="95" Level="Error">使用 CLSCompliant(true)来标记 'Aliyun.OSS.dll'，因为它公开外部可见的类型。</Issue>
      </Message>
     </Messages>
     <Namespaces>
      <Namespace Name="Aliyun.OSS">
       <Types>
        <Type Name="AccessControlList" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ACL" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="ACL" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="AccessControlList.cs" Line="43">更正成员名称 'AccessControlList.ACL' 中“ACL”的大小写，将其改为“Acl”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="BucketInfoEntry+BucketACL" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message Id="ACL" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Name="Type" Certainty="85" Level="Error">更正类型名称 'BucketInfoEntry.BucketACL' 中“ACL”的大小写，将其改为“Acl”。</Issue>
          </Message>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'BucketInfoEntry.BucketACL'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="CORSRule" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message Id="CORS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Name="Type" Certainty="85" Level="Error">更正类型名称 'CORSRule' 中“CORS”的大小写，将其改为“Cors”。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#AllowedHeaders" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="CORSRule.cs" Line="57">通过移除属性 setter 将 'CORSRule.AllowedHeaders' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#AllowedMethods" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="CORSRule.cs" Line="45">通过移除属性 setter 将 'CORSRule.AllowedMethods' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#AllowedOrigins" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="CORSRule.cs" Line="29">通过移除属性 setter 将 'CORSRule.AllowedOrigins' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ExposeHeaders" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="CORSRule.cs" Line="72">通过移除属性 setter 将 'CORSRule.ExposeHeaders' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="DeleteObjectsResult" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Keys" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="DeleteObjectsResult.cs" Line="30">将 'DeleteObjectsResult.Keys' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
           <Accessors>
            <Accessor Name="#get_Keys()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
             <Messages>
              <Message TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
               <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="DeleteObjectsResult.cs" Line="33">在方法 'DeleteObjectsResult.Keys.get()' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
              </Message>
             </Messages>
            </Accessor>
           </Accessors>
          </Member>
         </Members>
        </Type>
        <Type Name="DeleteObjectsResult+DeletedObject" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'DeleteObjectsResult.DeletedObject'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="EqualConditionItem" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Jsonize()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="92">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'EqualConditionItem.Jsonize()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="95">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'EqualConditionItem.Jsonize()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="GeneratePresignedUriRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ContentMd5" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="Md" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="GeneratePresignedUriRequest.cs" Line="57">更正成员名称 'GeneratePresignedUriRequest.ContentMd5' 中“Md”的大小写，将其改为“MD”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#QueryParams" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="GeneratePresignedUriRequest.cs" Line="118">通过移除属性 setter 将 'GeneratePresignedUriRequest.QueryParams' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#UserMetadata" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="GeneratePresignedUriRequest.cs" Line="102">通过移除属性 setter 将 'GeneratePresignedUriRequest.UserMetadata' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="GetObjectRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Range" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="GetObjectRequest.cs" Line="42">将 'GetObjectRequest.Range' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="IOss" Kind="Interface" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ModifyObjectMeta(System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable`1&lt;System.Int64&gt;,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error">用提供所有默认形参的重载来替换方法 'IOss.ModifyObjectMeta(string, string, ObjectMetadata, long?, string)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableCopyObject(Aliyun.OSS.CopyObjectRequest,System.String,System.Nullable`1&lt;System.Int64&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error">用提供所有默认形参的重载来替换方法 'IOss.ResumableCopyObject(CopyObjectRequest, string, long?)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableUploadObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable`1&lt;System.Int64&gt;,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error">用提供所有默认形参的重载来替换方法 'IOss.ResumableUploadObject(string, string, Stream, ObjectMetadata, string, long?, EventHandler&lt;StreamTransferProgressArgs&gt;)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableUploadObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable`1&lt;System.Int64&gt;,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error">用提供所有默认形参的重载来替换方法 'IOss.ResumableUploadObject(string, string, string, ObjectMetadata, string, long?, EventHandler&lt;StreamTransferProgressArgs&gt;)'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="LifecycleRule" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Equals(Aliyun.OSS.LifecycleRule)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="AvoidExcessiveComplexity" Category="Microsoft.Maintainability" CheckId="CA1502" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="90" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="LifecycleRule.cs" Line="84">'LifecycleRule.Equals(LifecycleRule)' 的圈复杂度为 26。重写或重构该方法，以便将复杂度降低到 25。</Issue>
            </Message>
            <Message Id="0#" TypeName="ParameterNamesShouldMatchBaseDeclaration" Category="Microsoft.Naming" CheckId="CA1725" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="LifecycleRule.cs" Line="84">在成员 'LifecycleRule.Equals(LifecycleRule)' 中，将参数名称 'obj' 改为 'other'，使其与已在 'IEquatable&lt;LifecycleRule&gt;.Equals(LifecycleRule)' 中声明的标识符匹配。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ID" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="ID" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="MemberAbbreviation" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="LifecycleRule.cs" Line="35">更正成员名称 'LifecycleRule.ID' 中“ID”的大小写，将其改为“Id”。“Id”是一个缩写词，因此无需遵守首字母缩略词大小写规则。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Transitions" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="LifecycleRule.cs" Line="68">将 'LifecycleRule.Transitions' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="LifecycleRule+LifeCycleExpiration" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'LifecycleRule.LifeCycleExpiration'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#Equals(Aliyun.OSS.LifecycleRule+LifeCycleExpiration)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="0#" TypeName="ParameterNamesShouldMatchBaseDeclaration" Category="Microsoft.Naming" CheckId="CA1725" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="LifecycleRule.cs" Line="185">在成员 'LifecycleRule.LifeCycleExpiration.Equals(LifecycleRule.LifeCycleExpiration)' 中，将参数名称 'obj' 改为 'other'，使其与已在 'IEquatable&lt;LifecycleRule.LifeCycleExpiration&gt;.Equals(LifecycleRule.LifeCycleExpiration)' 中声明的标识符匹配。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="LifecycleRule+LifeCycleTransition" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'LifecycleRule.LifeCycleTransition'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#Equals(Aliyun.OSS.LifecycleRule+LifeCycleTransition)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="0#" TypeName="ParameterNamesShouldMatchBaseDeclaration" Category="Microsoft.Naming" CheckId="CA1725" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="LifecycleRule.cs" Line="227">在成员 'LifecycleRule.LifeCycleTransition.Equals(LifecycleRule.LifeCycleTransition)' 中，将参数名称 'transition' 改为 'other'，使其与已在 'IEquatable&lt;LifecycleRule.LifeCycleTransition&gt;.Equals(LifecycleRule.LifeCycleTransition)' 中声明的标识符匹配。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="MatchRuleChecker" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#.cctor()" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InitializeReferenceTypeStaticFieldsInline" Category="Microsoft.Performance" CheckId="CA1810" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="90" Level="CriticalWarning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="265">声明 'MatchRuleChecker' 中的静态字段时应初始化所有这些字段，并移除显式静态构造函数。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Check(Aliyun.OSS.MatchMode,System.String)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="294">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'MatchRuleChecker.Check(MatchMode, string)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ObjectMetadata" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ContentMd5" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="Md" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ObjectMetadata.cs" Line="205">更正成员名称 'ObjectMetadata.ContentMd5' 中“Md”的大小写，将其改为“MD”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="OssClient" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="AvoidExcessiveClassCoupling" Category="Microsoft.Maintainability" CheckId="CA1506" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Name="Type" Certainty="90" Level="Error">'OssClient' 与来自 20 个不同命名空间中的 182 个不同类型耦合。请重写或重构该类的方法，以降低它的类耦合度，或者考虑将该类的某些方法移到与之紧密耦合的其他某些类型中。高于 95 的类耦合度表示可维护性较差，介于 95 和 80 之间的类耦合度表示可维护性适中，低于 80 的类耦合度表示可维护性较好。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#.ctor(System.String,System.String,System.String,Aliyun.OSS.Common.ClientConfiguration)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="StringUriOverloadsCallSystemUriOverloads" Category="Microsoft.Design" CheckId="CA1057" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="72">重构 'OssClient.OssClient(string, string, string, ClientConfiguration)'，使其基于 'endpoint' 生成一个 System.Uri 对象，然后调用 'OssClient.OssClient(Uri, string, string, ClientConfiguration)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#.ctor(System.String,System.String,System.String,System.String,Aliyun.OSS.Common.ClientConfiguration)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="StringUriOverloadsCallSystemUriOverloads" Category="Microsoft.Design" CheckId="CA1057" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="83">重构 'OssClient.OssClient(string, string, string, string, ClientConfiguration)'，使其基于 'endpoint' 生成一个 System.Uri 对象，然后调用 'OssClient.OssClient(Uri, string, string, string, ClientConfiguration)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#BeginPutObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.AsyncCallback,System.Object)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="671">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.BeginPutObject(string, string, string, ObjectMetadata, AsyncCallback, object)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#CopyBigObject(Aliyun.OSS.CopyObjectRequest,System.Nullable`1&lt;System.Int64&gt;,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1196">用提供所有默认形参的重载来替换方法 'OssClient.CopyBigObject(CopyObjectRequest, long?, string)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#FormatEndpoint(System.String)" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.ToLower" TypeName="SpecifyCultureInfo" Category="Microsoft.Globalization" CheckId="CA1304" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="DependsOnFix">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1794">由于 'string.ToLower()' 的行为可能因当前用户的区域设置不同而不同，请将 'OssClient.FormatEndpoint(string)' 中的此调用替换为对 'string.ToLower(CultureInfo)' 的调用。如果要向用户显示 'string.ToLower(CultureInfo)' 的结果，请将“CultureInfo.CurrentCulture”指定为“CultureInfo”参数；如果软件将存储和访问此结果(例如，要将它保留到磁盘或数据库中)，则指定“CultureInfo.InvariantCulture”。</Issue>
            </Message>
            <Message Id="System.String.StartsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1796">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.FormatEndpoint(string)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1796">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.FormatEndpoint(string)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#GeneratePostPolicy(System.DateTime,Aliyun.OSS.PolicyConditions)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1484">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.GeneratePostPolicy(DateTime, PolicyConditions)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1486">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.GeneratePostPolicy(DateTime, PolicyConditions)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#GeneratePresignedUri(Aliyun.OSS.GeneratePresignedUriRequest)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.EndsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1464">由于 'string.EndsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.GeneratePresignedUri(GeneratePresignedUriRequest)' 中的此调用替换为对 'string.EndsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.EndsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#LoadResumableCopyContext(Aliyun.OSS.CopyObjectRequest,Aliyun.OSS.ObjectMetadata,System.String,System.Int64)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1640">从未使用 'OssClient.LoadResumableCopyContext(CopyObjectRequest, ObjectMetadata, string, long)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#LoadResumableDownloadContext(System.String,System.String,Aliyun.OSS.ObjectMetadata,System.String,System.Int64)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1668">从未使用 'OssClient.LoadResumableDownloadContext(string, string, ObjectMetadata, string, long)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#LoadResumableUploadContext(System.String,System.String,System.IO.Stream,System.String,System.Int64)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1655">从未使用 'OssClient.LoadResumableUploadContext(string, string, Stream, string, long)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ModifyObjectMeta(System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable`1&lt;System.Int64&gt;,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1249">用提供所有默认形参的重载来替换方法 'OssClient.ModifyObjectMeta(string, string, ObjectMetadata, long?, string)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#PutBigObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.Nullable`1&lt;System.Int64&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="787">用提供所有默认形参的重载来替换方法 'OssClient.PutBigObject(string, string, Stream, ObjectMetadata, long?)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#PutBigObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable`1&lt;System.Int64&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="781">用提供所有默认形参的重载来替换方法 'OssClient.PutBigObject(string, string, string, ObjectMetadata, long?)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#PutObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="653">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.PutObject(string, string, string, ObjectMetadata)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#PutObject(System.Uri,System.IO.Stream,Aliyun.OSS.ObjectMetadata)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.Int64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="745">由于 'long.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.PutObject(Uri, Stream, ObjectMetadata)' 中的此调用替换为对 'long.ToString(IFormatProvider)' 的调用。如果要向用户显示 'long.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#PutObject(System.Uri,System.String,Aliyun.OSS.ObjectMetadata)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="699">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.PutObject(Uri, string, ObjectMetadata)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableCopyObject(Aliyun.OSS.CopyObjectRequest,System.String,System.Nullable`1&lt;System.Int64&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1202">用提供所有默认形参的重载来替换方法 'OssClient.ResumableCopyObject(CopyObjectRequest, string, long?)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableCopyWithRetry(Aliyun.OSS.CopyObjectRequest,Aliyun.OSS.ResumableContext)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="RethrowToPreserveStackDetails" Category="Microsoft.Usage" CheckId="CA2200" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1751">'OssClient.ResumableCopyWithRetry(CopyObjectRequest, ResumableContext)' 再次引发捕获的异常并将其显式地指定为一个参数。请改用不带参数的“throw”以保留该异常最初引发时所在的堆栈位置。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableDownloadObject(Aliyun.OSS.DownloadObjectRequest)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="1083">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.ResumableDownloadObject(DownloadObjectRequest)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableUploadObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable`1&lt;System.Int64&gt;,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="810">用提供所有默认形参的重载来替换方法 'OssClient.ResumableUploadObject(string, string, Stream, ObjectMetadata, string, long?, EventHandler&lt;StreamTransferProgressArgs&gt;)'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableUploadObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable`1&lt;System.Int64&gt;,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="794">用提供所有默认形参的重载来替换方法 'OssClient.ResumableUploadObject(string, string, string, ObjectMetadata, string, long?, EventHandler&lt;StreamTransferProgressArgs&gt;)'。</Issue>
            </Message>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss" File="OssClient.cs" Line="795">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssClient.ResumableUploadObject(string, string, string, ObjectMetadata, string, long?, EventHandler&lt;StreamTransferProgressArgs&gt;)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="PolicyConditions" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#AddConditionItem(System.String,System.Int64,System.Int64)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="246">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'PolicyConditions.AddConditionItem(string, long, long)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="RangeConditionItem" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Jsonize()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="140">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'RangeConditionItem.Jsonize()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="RefererConfiguration+RefererListModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'RefererConfiguration.RefererListModel'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#Referers" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="RefererConfiguration.cs" Line="42">将 'RefererConfiguration.RefererListModel.Referers' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResumableContext" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Base64(System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="295">从未使用 'ResumableContext.Base64(string)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ToString()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.EndsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="242">由于 'string.EndsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableContext.ToString()' 中的此调用替换为对 'string.EndsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.EndsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResumableDownloadContext" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#ToString()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.EndsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="417">由于 'string.EndsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableDownloadContext.ToString()' 中的此调用替换为对 'string.EndsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.EndsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResumableDownloadManager" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#DoResumableDownloadMultiThread(Aliyun.OSS.DownloadObjectRequest,Aliyun.OSS.ResumableDownloadContext,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotRaiseExceptionsInExceptionClauses" Category="Microsoft.Usage" CheckId="CA2219" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Finally" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="282">修改成员 'ResumableDownloadManager.DoResumableDownloadMultiThread(DownloadObjectRequest, ResumableDownloadContext, EventHandler&lt;StreamTransferProgressArgs&gt;)'，使它不在 finally 子句中引发异常。在从 finally 子句中引发异常时，新异常会隐藏当前处于活动状态的异常(如果存在的话)，并使原来的错误难以检测和调试。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DoResumableDownloadSingleThread(Aliyun.OSS.DownloadObjectRequest,Aliyun.OSS.ResumableDownloadContext,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCastUnnecessarily" Category="Microsoft.Performance" CheckId="CA1800" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="Local" Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="127">在方法 'ResumableDownloadManager.DoResumableDownloadSingleThread(DownloadObjectRequest, ResumableDownloadContext, EventHandler&lt;StreamTransferProgressArgs&gt;)' 中多次将变量 'originalStream' 强制转换为类型 'Crc64Stream'。请缓存“as”运算符的结果或直接强制转换“as”运算符以消除冗余的 isint 指令。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DownloadPart(System.Object)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCatchGeneralExceptionTypes" Category="Microsoft.Design" CheckId="CA1031" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="437">修改 'ResumableDownloadManager.DownloadPart(object)' 以便捕捉比 'Exception' 更具体的异常，或者再次引发该异常。</Issue>
            </Message>
            <Message Id="downloadProgressCallback" TypeName="RemoveUnusedLocals" Category="Microsoft.Performance" CheckId="CA1804" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="371">'ResumableDownloadManager.DownloadPart(object)' 声明类型为 'EventHandler&lt;StreamTransferProgressArgs&gt;' 的变量 'downloadProgressCallback'，但从未使用过该变量或只对它进行过赋值。请使用此变量或将它移除。</Issue>
            </Message>
            <Message Id="System.String.Format(System.String,System.Object,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="407">由于 'string.Format(string, object, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableDownloadManager.DownloadPart(object)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#GetTempDownloadFile(Aliyun.OSS.DownloadObjectRequest)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="359">从未使用 'ResumableDownloadManager.GetTempDownloadFile(DownloadObjectRequest)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableDownloadWithRetry(Aliyun.OSS.DownloadObjectRequest,Aliyun.OSS.ResumableDownloadContext)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="RethrowToPreserveStackDetails" Category="Microsoft.Usage" CheckId="CA2200" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="49">'ResumableDownloadManager.ResumableDownloadWithRetry(DownloadObjectRequest, ResumableDownloadContext)' 再次引发捕获的异常并将其显式地指定为一个参数。请改用不带参数的“throw”以保留该异常最初引发时所在的堆栈位置。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Validate(Aliyun.OSS.DownloadObjectRequest,Aliyun.OSS.ResumableDownloadContext)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="328">由于 'string.Format(string, object, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableDownloadManager.Validate(DownloadObjectRequest, ResumableDownloadContext)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="346">由于 'string.Format(string, object, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableDownloadManager.Validate(DownloadObjectRequest, ResumableDownloadContext)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.UInt64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableDownloadManager.cs" Line="344">由于 'ulong.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableDownloadManager.Validate(DownloadObjectRequest, ResumableDownloadContext)' 中的此调用替换为对 'ulong.ToString(IFormatProvider)' 的调用。如果要向用户显示 'ulong.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResumableDownloadManager+NoneRetryableException" Kind="Class" Accessibility="Private" ExternallyVisible="False">
         <Messages>
          <Message TypeName="AvoidUninstantiatedInternalClasses" Category="Microsoft.Performance" CheckId="CA1812" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
           <Issue Certainty="75" Level="Warning">'ResumableDownloadManager.NoneRetryableException' 是显然没有实例化过的内部类。如果是这样，请从程序集内移除该代码。如果此内部类只用于包含静态方法，请考虑添加私有构造函数，以阻止编译器生成默认构造函数。</Issue>
          </Message>
          <Message TypeName="ExceptionsShouldBePublic" Category="Microsoft.Design" CheckId="CA1064" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
           <Issue Certainty="95" Level="Warning">将异常类 'ResumableDownloadManager.NoneRetryableException' 设置为公共的。</Issue>
          </Message>
          <Message TypeName="ImplementStandardExceptionConstructors" Category="Microsoft.Design" CheckId="CA1032" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
           <Issue Name="MissingConstructor" Certainty="95" Level="Error">将下列构造函数添加到 'ResumableDownloadManager.NoneRetryableException': protected NoneRetryableException(SerializationInfo, StreamingContext)。</Issue>
          </Message>
          <Message TypeName="MarkISerializableTypesWithSerializable" Category="Microsoft.Usage" CheckId="CA2237" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
           <Issue Certainty="95" Level="Error">将 [Serializable] 添加到 'ResumableDownloadManager.NoneRetryableException'，原因是此类型实现了 ISerializable。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="ResumablePartContext" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#ToString()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int32.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="99">由于 'int.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumablePartContext.ToString()' 中的此调用替换为对 'int.ToString(IFormatProvider)' 的调用。如果要向用户显示 'int.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="103">由于 'int.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumablePartContext.ToString()' 中的此调用替换为对 'int.ToString(IFormatProvider)' 的调用。如果要向用户显示 'int.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.Int64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="99">由于 'long.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumablePartContext.ToString()' 中的此调用替换为对 'long.ToString(IFormatProvider)' 的调用。如果要向用户显示 'long.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="99">由于 'long.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumablePartContext.ToString()' 中的此调用替换为对 'long.ToString(IFormatProvider)' 的调用。如果要向用户显示 'long.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.UInt64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="ResumableContext.cs" Line="110">由于 'ulong.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumablePartContext.ToString()' 中的此调用替换为对 'ulong.ToString(IFormatProvider)' 的调用。如果要向用户显示 'ulong.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResumableUploadManager" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#DoResumableUpload(System.String,System.String,Aliyun.OSS.ResumableContext,System.IO.Stream,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCastUnnecessarily" Category="Microsoft.Performance" CheckId="CA1800" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="Parameter" Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="82">在方法 'ResumableUploadManager.DoResumableUpload(string, string, ResumableContext, Stream, EventHandler&lt;StreamTransferProgressArgs&gt;)' 中多次将参数 'fs' 强制转换为类型 'FileStream'。请缓存“as”运算符或直接强制转换的结果以消除冗余的 isint 指令。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DoResumableUploadFileMultiThread(System.String,System.String,Aliyun.OSS.ResumableContext,System.IO.FileStream,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotRaiseExceptionsInExceptionClauses" Category="Microsoft.Usage" CheckId="CA2219" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Finally" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="405">修改成员 'ResumableUploadManager.DoResumableUploadFileMultiThread(string, string, ResumableContext, FileStream, EventHandler&lt;StreamTransferProgressArgs&gt;)'，使它不在 finally 子句中引发异常。在从 finally 子句中引发异常时，新异常会隐藏当前处于活动状态的异常(如果存在的话)，并使原来的错误难以检测和调试。</Issue>
            </Message>
            <Message Id="bucketName" TypeName="ReviewUnusedParameters" Category="Microsoft.Usage" CheckId="CA1801" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="324">从未用过 'ResumableUploadManager.DoResumableUploadFileMultiThread(string, string, ResumableContext, FileStream, EventHandler&lt;StreamTransferProgressArgs&gt;)' 的参数 'bucketName'。请移除该参数或在方法主体中使用它。</Issue>
            </Message>
            <Message Id="key" TypeName="ReviewUnusedParameters" Category="Microsoft.Usage" CheckId="CA1801" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="324">从未用过 'ResumableUploadManager.DoResumableUploadFileMultiThread(string, string, ResumableContext, FileStream, EventHandler&lt;StreamTransferProgressArgs&gt;)' 的参数 'key'。请移除该参数或在方法主体中使用它。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DoResumableUploadPreReadMultiThread(System.String,System.String,Aliyun.OSS.ResumableContext,System.IO.Stream,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotRaiseExceptionsInExceptionClauses" Category="Microsoft.Usage" CheckId="CA2219" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Finally" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="550">修改成员 'ResumableUploadManager.DoResumableUploadPreReadMultiThread(string, string, ResumableContext, Stream, EventHandler&lt;StreamTransferProgressArgs&gt;)'，使它不在 finally 子句中引发异常。在从 finally 子句中引发异常时，新异常会隐藏当前处于活动状态的异常(如果存在的话)，并使原来的错误难以检测和调试。</Issue>
            </Message>
            <Message Id="bucketName" TypeName="ReviewUnusedParameters" Category="Microsoft.Usage" CheckId="CA1801" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="422">从未用过 'ResumableUploadManager.DoResumableUploadPreReadMultiThread(string, string, ResumableContext, Stream, EventHandler&lt;StreamTransferProgressArgs&gt;)' 的参数 'bucketName'。请移除该参数或在方法主体中使用它。</Issue>
            </Message>
            <Message Id="key" TypeName="ReviewUnusedParameters" Category="Microsoft.Usage" CheckId="CA1801" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="422">从未用过 'ResumableUploadManager.DoResumableUploadPreReadMultiThread(string, string, ResumableContext, Stream, EventHandler&lt;StreamTransferProgressArgs&gt;)' 的参数 'key'。请移除该参数或在方法主体中使用它。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DoResumableUploadSingleThread(System.String,System.String,Aliyun.OSS.ResumableContext,System.IO.Stream,System.EventHandler`1&lt;Aliyun.OSS.StreamTransferProgressArgs&gt;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message Id="System.UInt64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="126">由于 'ulong.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableUploadManager.DoResumableUploadSingleThread(string, string, ResumableContext, Stream, EventHandler&lt;StreamTransferProgressArgs&gt;)' 中的此调用替换为对 'ulong.Parse(string, IFormatProvider)' 的调用。如果 'ulong.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ResumableUploadWithRetry(Aliyun.OSS.UploadObjectRequest,Aliyun.OSS.ResumableContext)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="RethrowToPreserveStackDetails" Category="Microsoft.Usage" CheckId="CA2200" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="52">'ResumableUploadManager.ResumableUploadWithRetry(UploadObjectRequest, ResumableContext)' 再次引发捕获的异常并将其显式地指定为一个参数。请改用不带参数的“throw”以保留该异常最初引发时所在的堆栈位置。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#StartPreRead(System.Object)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCatchGeneralExceptionTypes" Category="Microsoft.Design" CheckId="CA1031" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="294">修改 'ResumableUploadManager.StartPreRead(object)' 以便捕捉比 'Exception' 更具体的异常，或者再次引发该异常。</Issue>
            </Message>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="284">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableUploadManager.StartPreRead(object)' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#UploadPart(System.Object)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCatchGeneralExceptionTypes" Category="Microsoft.Design" CheckId="CA1031" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="685">修改 'ResumableUploadManager.UploadPart(object)' 以便捕捉比 'Exception' 更具体的异常，或者再次引发该异常。</Issue>
            </Message>
            <Message Id="System.UInt64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ResumableUploadManager.cs" Line="663">由于 'ulong.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResumableUploadManager.UploadPart(object)' 中的此调用替换为对 'ulong.Parse(string, IFormatProvider)' 的调用。如果 'ulong.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResumableUploadManager+PreReadThreadParam" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Messages>
          <Message TypeName="TypesThatOwnDisposableFieldsShouldBeDisposable" Category="Microsoft.Design" CheckId="CA1001" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
           <Issue Name="NonBreaking" Certainty="95" Level="CriticalError">在 'ResumableUploadManager.PreReadThreadParam' 上实现 IDisposable，因为它创建下列 IDisposable 类型的成员: 'ManualResetEvent'。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="SetBucketAclRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ACL" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="ACL" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="SetBucketAclRequest.cs" Line="22">更正成员名称 'SetBucketAclRequest.ACL' 中“ACL”的大小写，将其改为“Acl”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SetBucketCorsRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#AddCORSRule(Aliyun.OSS.CORSRule)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="CORS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="SetBucketCorsRequest.cs" Line="56">更正成员名称 'SetBucketCorsRequest.AddCORSRule(CORSRule)' 中“CORS”的大小写，将其改为“Cors”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#CORSRules" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="SetBucketCorsRequest.cs" Line="30">通过移除属性 setter 将 'SetBucketCorsRequest.CORSRules' 更改为只读。</Issue>
            </Message>
            <Message Id="CORS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="SetBucketCorsRequest.cs" Line="30">更正成员名称 'SetBucketCorsRequest.CORSRules' 中“CORS”的大小写，将其改为“Cors”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SetBucketLifecycleRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#LifecycleRules" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="SetBucketLifecycleRequest.cs" Line="30">通过移除属性 setter 将 'SetBucketLifecycleRequest.LifecycleRules' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SetObjectAclRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ACL" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="ACL" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="SetObjectAclRequest.cs" Line="32">更正成员名称 'SetObjectAclRequest.ACL' 中“ACL”的大小写，将其改为“Acl”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="StartWithConditionItem" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Jsonize()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="PolicyConditions.cs" Line="119">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'StartWithConditionItem.Jsonize()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="UploadPartCopyRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Md5Digest" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="Md" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="UploadPartCopyRequest.cs" Line="52">更正成员名称 'UploadPartCopyRequest.Md5Digest' 中“Md”的大小写，将其改为“MD”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="UploadPartRequest" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Md5Digest" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="Md" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Domain" File="UploadPartRequest.cs" Line="52">更正成员名称 'UploadPartRequest.Md5Digest' 中“Md”的大小写，将其改为“MD”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Commands">
       <Types>
        <Type Name="AbortMultipartUploadCommand" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Create(Aliyun.OSS.Common.Communication.IServiceClient,System.Uri,Aliyun.OSS.Common.Communication.ExecutionContext,Aliyun.OSS.AbortMultipartUploadRequest)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectParameterName" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="AbortMultipartUploadCommand.cs" Line="66">方法 'AbortMultipartUploadCommand.Create(IServiceClient, Uri, ExecutionContext, AbortMultipartUploadRequest)' 将“uploadId”作为参数 'paramName' 传递给构造函数 'ArgumentException'。请将此参数替换为该方法的某个参数名。请注意，所提供的参数名的大小写应与方法中声明的大小写完全一致。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="AppendObjectCommand" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Create(Aliyun.OSS.Common.Communication.IServiceClient,System.Uri,Aliyun.OSS.Common.Communication.ExecutionContext,Aliyun.OSS.AppendObjectRequest)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectParameterName" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="AppendObjectCommand.cs" Line="87">方法 'AppendObjectCommand.Create(IServiceClient, Uri, ExecutionContext, AppendObjectRequest)' 将“request.Content”作为参数 'paramName' 传递给构造函数 'ArgumentNullException'。请将此参数替换为该方法的某个参数名。请注意，所提供的参数名的大小写应与方法中声明的大小写完全一致。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Parameters" Kind="Property" Static="False" Accessibility="Family" ExternallyVisible="False">
           <Accessors>
            <Accessor Name="#get_Parameters()" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="False">
             <Messages>
              <Message Id="System.Int64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
               <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="AppendObjectCommand.cs" Line="41">由于 'long.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'AppendObjectCommand.Parameters.get()' 中的此调用替换为对 'long.ToString(IFormatProvider)' 的调用。如果要向用户显示 'long.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
              </Message>
             </Messages>
            </Accessor>
           </Accessors>
          </Member>
         </Members>
        </Type>
        <Type Name="CompleteMultipartUploadCommand" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Create(Aliyun.OSS.Common.Communication.IServiceClient,System.Uri,Aliyun.OSS.Common.Communication.ExecutionContext,Aliyun.OSS.CompleteMultipartUploadRequest)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectParameterName" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="CompleteMultipartUploadCommand.cs" Line="93">方法 'CompleteMultipartUploadCommand.Create(IServiceClient, Uri, ExecutionContext, CompleteMultipartUploadRequest)' 将“uploadId”作为参数 'paramName' 传递给构造函数 'ArgumentException'。请将此参数替换为该方法的某个参数名。请注意，所提供的参数名的大小写应与方法中声明的大小写完全一致。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="DeleteObjectsCommand" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Headers" Kind="Property" Static="False" Accessibility="Family" ExternallyVisible="False">
           <Accessors>
            <Accessor Name="#get_Headers()" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="False">
             <Messages>
              <Message Id="System.Int64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
               <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="DeleteObjectsCommand.cs" Line="55">由于 'long.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'DeleteObjectsCommand.Headers.get()' 中的此调用替换为对 'long.ToString(IFormatProvider)' 的调用。如果要向用户显示 'long.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
              </Message>
             </Messages>
            </Accessor>
           </Accessors>
          </Member>
         </Members>
        </Type>
        <Type Name="ListPartsCommand" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#.ctor(Aliyun.OSS.Common.Communication.IServiceClient,System.Uri,Aliyun.OSS.Common.Communication.ExecutionContext,Aliyun.OSS.Transform.IDeserializer`2&lt;Aliyun.OSS.Common.Communication.ServiceResponse,Aliyun.OSS.PartListing&gt;,Aliyun.OSS.ListPartsRequest)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectParameterName" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="ListPartsCommand.cs" Line="68">方法 'ListPartsCommand.ListPartsCommand(IServiceClient, Uri, ExecutionContext, IDeserializer&lt;ServiceResponse, PartListing&gt;, ListPartsRequest)' 将“uploadId”作为参数 'paramName' 传递给构造函数 'ArgumentException'。请将此参数替换为该方法的某个参数名。请注意，所提供的参数名的大小写应与方法中声明的大小写完全一致。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="PutObjectCommand" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Create(Aliyun.OSS.Common.Communication.IServiceClient,System.Uri,Aliyun.OSS.Common.Communication.ExecutionContext,Aliyun.OSS.PutObjectRequest)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectParameterName" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Commands" File="PutObjectCommand.cs" Line="92">方法 'PutObjectCommand.Create(IServiceClient, Uri, ExecutionContext, PutObjectRequest)' 将“content”作为参数 'paramName' 传递给构造函数 'ArgumentNullException'。请将此参数替换为该方法的某个参数名。请注意，所提供的参数名的大小写应与方法中声明的大小写完全一致。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Common">
       <Types>
        <Type Name="ClientConfiguration" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#UserAgent" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="DependsOnFix">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ClientConfiguration.cs" Line="58">从未使用 'ClientConfiguration.UserAgent' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ClientException" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="ImplementStandardExceptionConstructors" Category="Microsoft.Design" CheckId="CA1032" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
           <Issue Name="MissingConstructor" Certainty="95" Level="Error">将下列构造函数添加到 'ClientException': public ClientException()。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="OssErrorCode" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#IncorrectNumberOfFilesInPOSTRequest" Kind="Field" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="POST" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error">更正成员名称 'OssErrorCode.IncorrectNumberOfFilesInPOSTRequest' 中“POST”的大小写，将其改为“Post”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#MalformedPOSTRequest" Kind="Field" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="POST" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error">更正成员名称 'OssErrorCode.MalformedPOSTRequest' 中“POST”的大小写，将其改为“Post”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#MalformedXML" Kind="Field" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="XML" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error">更正成员名称 'OssErrorCode.MalformedXML' 中“XML”的大小写，将其改为“Xml”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#MaxPOSTPreDataLengthExceededError" Kind="Field" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="POST" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error">更正成员名称 'OssErrorCode.MaxPOSTPreDataLengthExceededError' 中“POST”的大小写，将其改为“Post”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#NoSuchCORSConfiguration" Kind="Field" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="CORS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error">更正成员名称 'OssErrorCode.NoSuchCORSConfiguration' 中“CORS”的大小写，将其改为“Cors”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="OssException" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="SecurityRuleSetLevel2MethodsShouldNotBeProtectedWithLinkDemandsFxCopRule" Category="Microsoft.Security" CheckId="CA2135" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="50" Level="CriticalWarning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="OssException.cs" Line="60">使用 'SecurityPermissionAttribute' 的 LinkDemand 保护 'OssException.GetObjectData(SerializationInfo, StreamingContext)'。在级别 2 安全规则集中，应改为使其成为安全关键的来提供保护。移除此 LinkDemand 并将 'OssException.GetObjectData(SerializationInfo, StreamingContext)' 标记为安全关键的。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ServiceException" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="SecurityRuleSetLevel2MethodsShouldNotBeProtectedWithLinkDemandsFxCopRule" Category="Microsoft.Security" CheckId="CA2135" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="50" Level="CriticalWarning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common" File="ServiceException.cs" Line="94">使用 'SecurityPermissionAttribute' 的 LinkDemand 保护 'ServiceException.GetObjectData(SerializationInfo, StreamingContext)'。在级别 2 安全规则集中，应改为使其成为安全关键的来提供保护。移除此 LinkDemand 并将 'ServiceException.GetObjectData(SerializationInfo, StreamingContext)' 标记为安全关键的。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Common.Authentication">
       <Types>
        <Type Name="ICredentialsProvider" Kind="Interface" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetCredentials()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="UsePropertiesWhereAppropriate" Category="Microsoft.Design" CheckId="CA1024" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning">如果可行，请将 'ICredentialsProvider.GetCredentials()' 改为属性。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Common.Communication">
       <Types>
        <Type Name="ServiceClientImpl" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#BeginSetRequestContent(System.Net.HttpWebRequest,Aliyun.OSS.Common.Communication.ServiceRequest,Aliyun.OSS.Util.OssAction,Aliyun.OSS.Common.ClientConfiguration,Aliyun.OSS.Common.Communication.ServiceClientImpl+HttpAsyncResult)" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCatchGeneralExceptionTypes" Category="Microsoft.Design" CheckId="CA1031" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Communication" File="ServiceClientImpl.cs" Line="301">修改 'ServiceClientImpl.BeginSetRequestContent(HttpWebRequest, ServiceRequest, OssAction, ClientConfiguration, ServiceClientImpl.HttpAsyncResult)' 以便捕捉比 'Exception' 更具体的异常，或者再次引发该异常。</Issue>
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Communication" File="ServiceClientImpl.cs" Line="349">修改 'ServiceClientImpl.BeginSetRequestContent(HttpWebRequest, ServiceRequest, OssAction, ClientConfiguration, ServiceClientImpl.HttpAsyncResult)' 以便捕捉比 'Exception' 更具体的异常，或者再次引发该异常。</Issue>
            </Message>
            <Message Id="System.Int64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Communication" File="ServiceClientImpl.cs" Line="313">由于 'long.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ServiceClientImpl.BeginSetRequestContent(HttpWebRequest, ServiceRequest, OssAction, ClientConfiguration, ServiceClientImpl.HttpAsyncResult)' 中的此调用替换为对 'long.Parse(string, IFormatProvider)' 的调用。如果 'long.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#SetRequestContent(System.Net.HttpWebRequest,Aliyun.OSS.Common.Communication.ServiceRequest,Aliyun.OSS.Common.ClientConfiguration)" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Communication" File="ServiceClientImpl.cs" Line="256">由于 'long.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ServiceClientImpl.SetRequestContent(HttpWebRequest, ServiceRequest, ClientConfiguration)' 中的此调用替换为对 'long.Parse(string, IFormatProvider)' 的调用。如果 'long.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ServiceRequest" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#BuildRequestUri()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.EndsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Communication" File="ServiceRequest.cs" Line="83">由于 'string.EndsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ServiceRequest.BuildRequestUri()' 中的此调用替换为对 'string.EndsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.EndsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
            <Message Id="System.String.StartsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Communication" File="ServiceRequest.cs" Line="83">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ServiceRequest.BuildRequestUri()' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Common.Handlers">
       <Types>
        <Type Name="CompleteMultipartUploadCrc64Handler" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Handle(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.UInt64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Handlers" File="CompleteMultipartUploadCrc64Handler.cs" Line="34">由于 'ulong.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'CompleteMultipartUploadCrc64Handler.Handle(ServiceResponse)' 中的此调用替换为对 'ulong.Parse(string, IFormatProvider)' 的调用。如果 'ulong.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.UInt64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Handlers" File="CompleteMultipartUploadCrc64Handler.cs" Line="48">由于 'ulong.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'CompleteMultipartUploadCrc64Handler.Handle(ServiceResponse)' 中的此调用替换为对 'ulong.ToString(IFormatProvider)' 的调用。如果要向用户显示 'ulong.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="Crc64CheckHandler" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Handle(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.UInt64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Handlers" File="Crc64CheckHandler.cs" Line="38">由于 'ulong.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'Crc64CheckHandler.Handle(ServiceResponse)' 中的此调用替换为对 'ulong.ToString(IFormatProvider)' 的调用。如果要向用户显示 'ulong.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ErrorResponseHandler" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#ErrorHandle(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Handlers" File="ErrorResponseHandler.cs" Line="35">从未使用 'ErrorResponseHandler.ErrorHandle(ServiceResponse)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Common.Internal">
       <Types>
        <Type Name="Crc64Stream" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#.ctor(System.IO.Stream,System.Byte[],System.Int64,System.UInt64)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Internal" File="HashStream.cs" Line="417">用提供所有默认形参的重载来替换方法 'Crc64Stream.Crc64Stream(Stream, byte[], long, ulong)'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="EventStream" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="DoNotCatchGeneralExceptionTypes" Category="Microsoft.Design" CheckId="CA1031" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Internal" File="EventStream.cs" Line="134">修改 'EventStream.BeginRead(byte[], int, int, AsyncCallback, object)' 以便捕捉比 'Exception' 更具体的异常，或者再次引发该异常。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="HashingWrapper" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#_algorithm" Kind="Field" Static="False" Accessibility="Family" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DoNotDeclareVisibleInstanceFields" Category="Microsoft.Design" CheckId="CA1051" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="90" Level="Error">由于字段 'HashingWrapper._algorithm' 在其声明类型的外部可见，因此，请将它的可访问性改为私有，并添加一个与该字段当前的可访问性相同的属性以提供对该属性的访问。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="HashStream" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#CalculatedHash" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Internal" File="HashStream.cs" Line="52">将 'HashStream.CalculatedHash' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ExpectedHash" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Internal" File="HashStream.cs" Line="58">将 'HashStream.ExpectedHash' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="WrapperStream" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#GetSeekableBaseStream()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="UsePropertiesWhereAppropriate" Category="Microsoft.Design" CheckId="CA1024" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Internal" File="WrapperStream.cs" Line="59">如果可行，请将 'WrapperStream.GetSeekableBaseStream()' 改为属性。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#SearchWrappedStream(Aliyun.OSS.Util.OssFunc`2&lt;System.IO.Stream,System.Boolean&gt;)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DoNotCastUnnecessarily" Category="Microsoft.Performance" CheckId="CA1800" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="Local" Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\Internal" File="WrapperStream.cs" Line="98">在方法 'WrapperStream.SearchWrappedStream(OssFunc&lt;Stream, bool&gt;)' 中多次将变量 'baseStream' 强制转换为类型 'WrapperStream'。请缓存“as”运算符的结果或直接强制转换“as”运算符以消除冗余的 isint 指令。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Common.ThirdParty">
       <Types>
        <Type Name="MD5Managed" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#.ctor()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DoNotCallOverridableMethodsInConstructors" Category="Microsoft.Usage" CheckId="CA2214" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="CriticalWarning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Common\ThirdParty" File="MD5Managed.cs" Line="22">'MD5Managed.MD5Managed()' 包含调用链，该调用链导致调用此类所定义的虚方法。请检查以下调用堆栈，看是否有意外结果: &#xD;&#xA;&#xD;&#xA;MD5Managed..ctor()&#xD;&#xA;HashAlgorithm.Initialize():Void。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Model">
       <Types>
        <Type Name="AccessControlPolicy" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Grants" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="AccessControlPolicy.cs" Line="22">通过移除属性 setter 将 'AccessControlPolicy.Grants' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="CompleteMultipartUploadRequestModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Parts" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="CompleteMultipartUploadRequestModel.cs" Line="15">将 'CompleteMultipartUploadRequestModel.Parts' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="CompleteMultipartUploadRequestModel+CompletePart" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'CompleteMultipartUploadRequestModel.CompletePart'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="DeleteObjectsRequestModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Keys" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="DeleteObjectsRequestModel.cs" Line="18">将 'DeleteObjectsRequestModel.Keys' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="DeleteObjectsRequestModel+ObjectToDel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'DeleteObjectsRequestModel.ObjectToDel'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="LifecycleConfiguration" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#LifecycleRules" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="LifecycleConfiguration.cs" Line="16">将 'LifecycleConfiguration.LifecycleRules' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="LifecycleRule" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ID" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="ID" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="MemberAbbreviation" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="LifecycleConfiguration.cs" Line="23">更正成员名称 'LifecycleRule.ID' 中“ID”的大小写，将其改为“Id”。“Id”是一个缩写词，因此无需遵守首字母缩略词大小写规则。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Transition" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="LifecycleConfiguration.cs" Line="38">将 'LifecycleRule.Transition' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListAllMyBucketsResult" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Buckets" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="ListAllMyBucketsResult.cs" Line="37">通过移除属性 setter 将 'ListAllMyBucketsResult.Buckets' 更改为只读。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListMultipartUploadsResult" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#Uploads" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="ListMultipartUploadsResult.cs" Line="48">将 'ListMultipartUploadsResult.Uploads' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListMultipartUploadsResult+CommonPrefixs" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'ListMultipartUploadsResult.CommonPrefixs'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#Prefixs" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="ListMultipartUploadsResult.cs" Line="74">将 'ListMultipartUploadsResult.CommonPrefixs.Prefixs' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListMultipartUploadsResult+Upload" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'ListMultipartUploadsResult.Upload'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="ListPartsResult" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#PartResults" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="ListPartsResult.cs" Line="37">将 'ListPartsResult.PartResults' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListPartsResult+PartResult" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'ListPartsResult.PartResult'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="SetBucketCorsRequestModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#CORSRuleModels" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="CORS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="SetBucketCorsRequestModel.cs" Line="16">更正成员名称 'SetBucketCorsRequestModel.CORSRuleModels' 中“CORS”的大小写，将其改为“Cors”。</Issue>
            </Message>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="SetBucketCorsRequestModel.cs" Line="16">将 'SetBucketCorsRequestModel.CORSRuleModels' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SetBucketCorsRequestModel+CORSRuleModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message Id="CORS" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Name="Type" Certainty="85" Level="Error">更正类型名称 'SetBucketCorsRequestModel.CORSRuleModel' 中“CORS”的大小写，将其改为“Cors”。</Issue>
          </Message>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'SetBucketCorsRequestModel.CORSRuleModel'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#AllowedHeaders" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="SetBucketCorsRequestModel.cs" Line="28">将 'SetBucketCorsRequestModel.CORSRuleModel.AllowedHeaders' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#AllowedMethods" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="SetBucketCorsRequestModel.cs" Line="25">将 'SetBucketCorsRequestModel.CORSRuleModel.AllowedMethods' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#AllowedOrigins" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="SetBucketCorsRequestModel.cs" Line="22">将 'SetBucketCorsRequestModel.CORSRuleModel.AllowedOrigins' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ExposeHeaders" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="50" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Model" File="SetBucketCorsRequestModel.cs" Line="31">将 'SetBucketCorsRequestModel.CORSRuleModel.ExposeHeaders' 更改为返回集合或将其转换为方法。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SetBucketLoggingRequestModel+SetBucketLoggingEnabled" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'SetBucketLoggingRequestModel.SetBucketLoggingEnabled'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="SetBucketWebsiteRequestModel+ErrorDocumentModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'SetBucketWebsiteRequestModel.ErrorDocumentModel'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="SetBucketWebsiteRequestModel+IndexDocumentModel" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Certainty="90" Level="Error">不要嵌套类型 'SetBucketWebsiteRequestModel.IndexDocumentModel'。或者，更改其可访问性，使它在外部不可见。</Issue>
          </Message>
         </Messages>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Transform">
       <Types>
        <Type Name="AppendObjectResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="AppendObjectResponseDeserializer.cs" Line="26">由于 'long.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'AppendObjectResponseDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'long.Parse(string, IFormatProvider)' 的调用。如果 'long.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.UInt64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="AppendObjectResponseDeserializer.cs" Line="31">由于 'ulong.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'AppendObjectResponseDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'ulong.Parse(string, IFormatProvider)' 的调用。如果 'ulong.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="DeleteObjectsResultDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int32.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="DeleteObjectsResultDeserializer.cs" Line="23">由于 'int.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'DeleteObjectsResultDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'int.Parse(string, IFormatProvider)' 的调用。如果 'int.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="DeserializerFactory" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#CreateUploadPartResultDeserializer(System.Int32)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="DeserializerFactory.cs" Line="133">从未使用 'DeserializerFactory.CreateUploadPartResultDeserializer(int)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#CreateUploadPartResultDeserializer(System.Int32,System.Int64)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="DeserializerFactory.cs" Line="138">从未使用 'DeserializerFactory.CreateUploadPartResultDeserializer(int, long)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="GetBucketLifecycleDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#ConvertExpiration(Aliyun.OSS.Model.Expiration,Aliyun.OSS.LifecycleRule+LifeCycleExpiration)" Kind="Method" Static="True" Accessibility="Assembly" ExternallyVisible="False">
           <Messages>
            <Message Id="System.DateTime.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetBucketLifecycleDeserializer.cs" Line="92">由于 'DateTime.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'GetBucketLifecycleDeserializer.ConvertExpiration(Expiration, LifecycleRule.LifeCycleExpiration)' 中的此调用替换为对 'DateTime.Parse(string, IFormatProvider)' 的调用。如果 'DateTime.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.DateTime.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetBucketLifecycleDeserializer.cs" Line="44">由于 'DateTime.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'GetBucketLifecycleDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'DateTime.Parse(string, IFormatProvider)' 的调用。如果 'DateTime.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetBucketLifecycleDeserializer.cs" Line="46">由于 'DateTime.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'GetBucketLifecycleDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'DateTime.Parse(string, IFormatProvider)' 的调用。如果 'DateTime.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#TryParseEnum(System.String,Aliyun.OSS.RuleStatus&amp;)" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetBucketLifecycleDeserializer.cs" Line="77">从未使用 'GetBucketLifecycleDeserializer.TryParseEnum(string, out RuleStatus)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="GetBucketMetadataResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Equals(System.String,System.String,System.StringComparison)" TypeName="UseOrdinalStringComparison" Category="Microsoft.Globalization" CheckId="CA1309" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="StringComparison" Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetBucketMetadataResponseDeserializer.cs" Line="26">'GetBucketMetadataResponseDeserializer.Deserialize(ServiceResponse)' 将“StringComparison.InvariantCultureIgnoreCase”作为“StringComparison”参数传递给 'string.Equals(string, string, StringComparison)'。若要执行非语义比较，请改为指定“StringComparison.Ordinal”或“StringComparison.OrdinalIgnoreCase”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="GetCorsResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#ToList(System.String[])" Kind="Method" Static="False" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetCorsResponseDeserializer.cs" Line="45">从未使用 'GetCorsResponseDeserializer.ToList(string[])' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="GetObjectMetadataResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.Equals(System.String,System.String,System.StringComparison)" TypeName="UseOrdinalStringComparison" Category="Microsoft.Globalization" CheckId="CA1309" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="StringComparison" Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetObjectMetadataResponseDeserializer.cs" Line="32">'GetObjectMetadataResponseDeserializer.Deserialize(ServiceResponse)' 将“StringComparison.InvariantCultureIgnoreCase”作为“StringComparison”参数传递给 'string.Equals(string, string, StringComparison)'。若要执行非语义比较，请改为指定“StringComparison.Ordinal”或“StringComparison.OrdinalIgnoreCase”。</Issue>
             <Issue Name="StringComparison" Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetObjectMetadataResponseDeserializer.cs" Line="37">'GetObjectMetadataResponseDeserializer.Deserialize(ServiceResponse)' 将“StringComparison.InvariantCultureIgnoreCase”作为“StringComparison”参数传递给 'string.Equals(string, string, StringComparison)'。若要执行非语义比较，请改为指定“StringComparison.Ordinal”或“StringComparison.OrdinalIgnoreCase”。</Issue>
             <Issue Name="StringComparison" Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetObjectMetadataResponseDeserializer.cs" Line="41">'GetObjectMetadataResponseDeserializer.Deserialize(ServiceResponse)' 将“StringComparison.InvariantCultureIgnoreCase”作为“StringComparison”参数传递给 'string.Equals(string, string, StringComparison)'。若要执行非语义比较，请改为指定“StringComparison.Ordinal”或“StringComparison.OrdinalIgnoreCase”。</Issue>
            </Message>
            <Message Id="System.String.StartsWith(System.String,System.Boolean,System.Globalization.CultureInfo)" TypeName="UseOrdinalStringComparison" Category="Microsoft.Globalization" CheckId="CA1309" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="StringComparison" Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="GetObjectMetadataResponseDeserializer.cs" Line="26">'GetObjectMetadataResponseDeserializer.Deserialize(ServiceResponse)' 将“CultureInfo.InvariantCulture”作为“StringComparison”参数传递给 'string.StartsWith(string, bool, CultureInfo)'。若要执行非语义比较，请改为指定“StringComparison.Ordinal”或“StringComparison.OrdinalIgnoreCase”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="InitiateMultipartUploadResultDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="InitiateMultipartUploadResultDeserializer.cs" Line="23">在方法 'InitiateMultipartUploadResultDeserializer.Deserialize(ServiceResponse)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListMultipartUploadsResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ListMultipartUploadsResponseDeserializer.cs" Line="23">在方法 'ListMultipartUploadsResponseDeserializer.Deserialize(ServiceResponse)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListObjectsResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ListObjectsResponseDeserializer.cs" Line="23">在方法 'ListObjectsResponseDeserializer.Deserialize(ServiceResponse)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ListPartsResponseDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ListPartsResultDeserializer.cs" Line="25">在方法 'ListPartsResponseDeserializer.Deserialize(ServiceResponse)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
            </Message>
            <Message Id="System.Convert.ToInt32(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ListPartsResultDeserializer.cs" Line="25">由于 'Convert.ToInt32(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ListPartsResponseDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'Convert.ToInt32(string, IFormatProvider)' 的调用。如果 'Convert.ToInt32(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="ResponseDeserializer`2" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Decode(System.String,System.String)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ResponseDeserializer.cs" Line="28">从未使用 'ResponseDeserializer&lt;TResult, TModel&gt;.Decode(string, string)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DeserializeGeneric(Aliyun.OSS.Common.Communication.ServiceResponse,Aliyun.OSS.Model.GenericResult)" Kind="Method" Static="False" Accessibility="Family" ExternallyVisible="False">
           <Messages>
            <Message TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Warning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ResponseDeserializer.cs" Line="37">从未使用 'ResponseDeserializer&lt;TResult, TModel&gt;.DeserializeGeneric(ServiceResponse, GenericResult)' 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Issue>
            </Message>
            <Message Id="System.Int64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="ResponseDeserializer.cs" Line="46">由于 'long.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'ResponseDeserializer&lt;TResult, TModel&gt;.DeserializeGeneric(ServiceResponse, GenericResult)' 中的此调用替换为对 'long.Parse(string, IFormatProvider)' 的调用。如果 'long.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="UploadPartCopyResultDeserializer" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#Deserialize(Aliyun.OSS.Common.Communication.ServiceResponse)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int64.Parse(System.String)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternate" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Transform" File="UploadPartCopyResponseDeserializer.cs" Line="42">由于 'long.Parse(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'UploadPartCopyResultDeserializer.Deserialize(ServiceResponse)' 中的此调用替换为对 'long.Parse(string, IFormatProvider)' 的调用。如果 'long.Parse(string, IFormatProvider)' 的结果将基于用户的输入，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
      <Namespace Name="Aliyun.OSS.Util">
       <Types>
        <Type Name="CallbackHeaderBuilder" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#.ctor(System.String,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="0#" TypeName="UriParametersShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1054" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="60" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="64">将方法 'CallbackHeaderBuilder.CallbackHeaderBuilder(string, string)' 的参数 'callbackUrl' 的类型从字符串改为 System.Uri，或者提供 'CallbackHeaderBuilder.CallbackHeaderBuilder(string, string)' 的重载，允许将 'callbackUrl' 作为 System.Uri 对象来传递。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#.ctor(System.String,System.String,System.String,Aliyun.OSS.Util.CallbackBodyType)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="0#" TypeName="UriParametersShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1054" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="60" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="79">将方法 'CallbackHeaderBuilder.CallbackHeaderBuilder(string, string, string, CallbackBodyType)' 的参数 'callbackUrl' 的类型从字符串改为 System.Uri，或者提供 'CallbackHeaderBuilder.CallbackHeaderBuilder(string, string, string, CallbackBodyType)' 的重载，允许将 'callbackUrl' 作为 System.Uri 对象来传递。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Build()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="100">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'CallbackHeaderBuilder.Build()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="103">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'CallbackHeaderBuilder.Build()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="105">由于 'string.Format(string, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'CallbackHeaderBuilder.Build()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
            <Message Id="System.String.Format(System.String,System.Object[])" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="108">由于 'string.Format(string, params object[])' 的行为可能会因当前用户的区域设置不同而不同，请将 'CallbackHeaderBuilder.Build()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#CallbackUrl" Kind="Property" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="UriPropertiesShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1056" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="42">将属性 'CallbackHeaderBuilder.CallbackUrl' 的类型从字符串改为 System.Uri。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="CallbackVariableHeaderBuilder" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#AddCallbackVariable(System.String,System.String)" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.StartsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="141">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'CallbackVariableHeaderBuilder.AddCallbackVariable(string, string)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#Build()" Kind="Method" Static="False" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.Format(System.String,System.Object,System.Object)" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="CallbackHeaderBuilder.cs" Line="170">由于 'string.Format(string, object, object)' 的行为可能会因当前用户的区域设置不同而不同，请将 'CallbackVariableHeaderBuilder.Build()' 中的此调用替换为对 'string.Format(IFormatProvider, string, params object[])' 的调用。如果要向用户显示 'string.Format(IFormatProvider, string, params object[])' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="Crc64" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message TypeName="StaticHolderTypesShouldNotHaveConstructors" Category="Microsoft.Design" CheckId="CA1053" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Name="CSharp2_0" Certainty="90" Level="Error">由于类型 'Crc64' 仅包含“static”成员，因此将它标记为“static”可阻止编译器添加默认公共构造函数。</Issue>
          </Message>
         </Messages>
         <Members>
          <Member Name="#Compute(System.Byte[],System.Int32,System.Int32,System.UInt64)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="Crc64.cs" Line="69">用提供所有默认形参的重载来替换方法 'Crc64.Compute(byte[], int, int, ulong)'。</Issue>
            </Message>
            <Message Id="bytes" TypeName="IdentifiersShouldNotContainTypeNames" Category="Microsoft.Naming" CheckId="CA1720" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="LanguageIndependentMemberParameter" Certainty="75" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="Crc64.cs" Line="69">在成员 'Crc64.Compute(byte[], int, int, ulong)' 中，考虑将参数名称 'bytes' 中的数据类型标识符“bytes”替换为一个更通用的词条，如“value”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#InitECMA()" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="ECMA" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="Crc64.cs" Line="64">更正成员名称 'Crc64.InitECMA()' 中“ECMA”的大小写，将其改为“Ecma”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="HttpHeaders" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ContentMd5" Kind="Field" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="Md" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error">更正成员名称 'HttpHeaders.ContentMd5' 中“Md”的大小写，将其改为“MD”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="HttpUtils" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#.cctor()" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InitializeReferenceTypeStaticFieldsInline" Category="Microsoft.Performance" CheckId="CA1810" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="90" Level="CriticalWarning" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="23">声明 'HttpUtils' 中的静态字段时应初始化所有这些字段，并移除显式静态构造函数。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ConbineQueryString(System.Collections.Generic.IEnumerable`1&lt;System.Collections.Generic.KeyValuePair`2&lt;System.String,System.String&gt;&gt;)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message TypeName="DoNotNestGenericTypesInMemberSignatures" Category="Microsoft.Design" CheckId="CA1006" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="47">考虑 'HttpUtils.ConbineQueryString(IEnumerable&lt;KeyValuePair&lt;string, string&gt;&gt;)' 不嵌套泛型类型 'IEnumerable&lt;KeyValuePair&lt;string, string&gt;&gt;' 的设计。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DecodeUri(System.String)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="0#" TypeName="UriParametersShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1054" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="60" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="90">将方法 'HttpUtils.DecodeUri(string)' 的参数 'uriToDecode' 的类型从字符串改为 System.Uri，或者提供 'HttpUtils.DecodeUri(string)' 的重载，允许将 'uriToDecode' 作为 System.Uri 对象来传递。</Issue>
            </Message>
            <Message TypeName="UriReturnValuesShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1055" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="60" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="90">将 'HttpUtils.DecodeUri(string)' 的返回类型从字符串更改为 System.Uri。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#EncodeUri(System.String,System.String)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="0#" TypeName="UriParametersShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1054" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="60" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="67">将方法 'HttpUtils.EncodeUri(string, string)' 的参数 'uriToEncode' 的类型从字符串改为 System.Uri，或者提供 'HttpUtils.EncodeUri(string, string)' 的重载，允许将 'uriToEncode' 作为 System.Uri 对象来传递。</Issue>
            </Message>
            <Message TypeName="UriReturnValuesShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1055" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Certainty="60" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="67">将 'HttpUtils.EncodeUri(string, string)' 的返回类型从字符串更改为 System.Uri。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#GetContentType(System.String,System.String)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.ToLower" TypeName="SpecifyCultureInfo" Category="Microsoft.Globalization" CheckId="CA1304" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="DependsOnFix">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="HttpUtils.cs" Line="118">由于 'string.ToLower()' 的行为可能因当前用户的区域设置不同而不同，请将 'HttpUtils.GetContentType(string, string)' 中的此调用替换为对 'string.ToLower(CultureInfo)' 的调用。如果要向用户显示 'string.ToLower(CultureInfo)' 的结果，请将“CultureInfo.CurrentCulture”指定为“CultureInfo”参数；如果软件将存储和访问此结果(例如，要将它保留到磁盘或数据库中)，则指定“CultureInfo.InvariantCulture”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="IoUtils" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Messages>
          <Message Id="Io" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
           <Issue Name="Type" Certainty="85" Level="Error">更正类型名称 'IoUtils' 中“Io”的大小写，将其改为“IO”。</Issue>
          </Message>
         </Messages>
        </Type>
        <Type Name="OssUtils" Kind="Class" Accessibility="Public" ExternallyVisible="True">
         <Members>
          <Member Name="#ComputeContentCrc64(System.IO.Stream,System.Int64)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.UInt64.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="266">由于 'ulong.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.ComputeContentCrc64(Stream, long)' 中的此调用替换为对 'ulong.ToString(IFormatProvider)' 的调用。如果要向用户显示 'ulong.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#ComputeContentMd5(System.IO.Stream,System.Int64)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="Md" TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="Breaking">
             <Issue Name="Member" Certainty="85" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="223">更正成员名称 'OssUtils.ComputeContentMd5(Stream, long)' 中“Md”的大小写，将其改为“MD”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#DetermineOsVersion()" Kind="Method" Static="True" Accessibility="Assembly" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int32.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="336">由于 'int.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.DetermineOsVersion()' 中的此调用替换为对 'int.ToString(IFormatProvider)' 的调用。如果要向用户显示 'int.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="336">由于 'int.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.DetermineOsVersion()' 中的此调用替换为对 'int.ToString(IFormatProvider)' 的调用。如果要向用户显示 'int.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#EndOperationHelper(Aliyun.OSS.Common.Communication.IServiceClient,System.IAsyncResult)" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectMessage" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="419">方法 'OssUtils.EndOperationHelper(IServiceClient, IAsyncResult)' 将参数名“asyncResult”作为变量 'message' 传递给构造函数 'ArgumentException'。请将此参数替换为一则说明性消息并在正确的位置传递参数名。</Issue>
             <Issue Name="IncorrectMessage" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="433">方法 'OssUtils.EndOperationHelper(IServiceClient, IAsyncResult)' 将参数名“asyncResult”作为变量 'message' 传递给构造函数 'ArgumentException'。请将此参数替换为一则说明性消息并在正确的位置传递参数名。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#EndOperationHelper`1(Aliyun.OSS.Common.Communication.IServiceClient,System.IAsyncResult)" Kind="Method" Static="True" Accessibility="Assembly" ExternallyVisible="False">
           <Messages>
            <Message TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IncorrectMessage" Certainty="95" Level="CriticalError" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="403">方法 'OssUtils.EndOperationHelper&lt;TResult&gt;(IServiceClient, IAsyncResult)' 将参数名“asyncResult”作为变量 'message' 传递给构造函数 'ArgumentException'。请将此参数替换为一则说明性消息并在正确的位置传递参数名。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#GetResourcePathFromSignedUrl(System.Uri)" Kind="Method" Static="True" Accessibility="Assembly" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.StartsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="529">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.GetResourcePathFromSignedUrl(Uri)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#IsObjectKeyValid(System.String)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.StartsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="136">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.IsObjectKeyValid(string)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="136">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.IsObjectKeyValid(string)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#IsWebpageValid(System.String)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="True">
           <Messages>
            <Message Id="System.String.EndsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="279">由于 'string.EndsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.IsWebpageValid(string)' 中的此调用替换为对 'string.EndsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.EndsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#MakeBucketEndpoint(System.Uri,System.String,Aliyun.OSS.Common.ClientConfiguration)" Kind="Method" Static="True" Accessibility="Assembly" ExternallyVisible="False">
           <Messages>
            <Message Id="System.Int32.ToString" TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="IFormatProviderAlternateString" Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="157">由于 'int.ToString()' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.MakeBucketEndpoint(Uri, string, ClientConfiguration)' 中的此调用替换为对 'int.ToString(IFormatProvider)' 的调用。如果要向用户显示 'int.ToString(IFormatProvider)' 的结果，请指定 'CultureInfo.CurrentCulture' 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 'CultureInfo.InvariantCulture'。</Issue>
            </Message>
           </Messages>
          </Member>
          <Member Name="#UrlEncodeKey(System.String)" Kind="Method" Static="True" Accessibility="Private" ExternallyVisible="False">
           <Messages>
            <Message Id="System.String.EndsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="OssUtils.cs" Line="189">由于 'string.EndsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'OssUtils.UrlEncodeKey(string)' 中的此调用替换为对 'string.EndsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.EndsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
        <Type Name="SignUtils" Kind="Class" Accessibility="Assembly" ExternallyVisible="False">
         <Members>
          <Member Name="#BuildCanonicalString(System.String,System.String,Aliyun.OSS.Common.Communication.ServiceRequest)" Kind="Method" Static="True" Accessibility="Public" ExternallyVisible="False">
           <Messages>
            <Message TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="53">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="55">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="55">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="55">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="65">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="66">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="67">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
             <Issue Name="ToUpperInvariant" Certainty="90" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="68">在方法 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中，将对 'string.ToLowerInvariant()' 的调用替换为 String.ToUpperInvariant()。</Issue>
            </Message>
            <Message Id="System.String.StartsWith(System.String)" TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307" Status="Active" Created="2021-06-28 05:36:55Z" FixCategory="NonBreaking">
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="55">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
             <Issue Certainty="95" Level="Error" Path="D:\工作管理\项目研发\仪器采购平台\04系统开发\云平台\ali.oss\Util" File="SignUtils.cs" Line="77">由于 'string.StartsWith(string)' 的行为可能会因当前用户的区域设置不同而不同，请将 'SignUtils.BuildCanonicalString(string, string, ServiceRequest)' 中的此调用替换为对 'string.StartsWith(string, StringComparison)' 的调用。如果要向用户显示 'string.StartsWith(string, StringComparison)' 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Issue>
            </Message>
           </Messages>
          </Member>
         </Members>
        </Type>
       </Types>
      </Namespace>
     </Namespaces>
    </Module>
   </Modules>
  </Target>
 </Targets>
 <Rules>
  <Rule TypeName="AssembliesShouldHaveValidStrongNames" Category="Microsoft.Design" CheckId="CA2210">
   <Name>程序集应具有有效的强名称</Name>
   <Description>程序集没有强名称，强名称无效，或者强名称只有经过计算机配置后才有效。不应部署处于这种状态的程序集。出现这种情况最常见原因有: 1) 对程序集进行签名后，修改了该程序集的内容；2) 签名过程失败；3) 延迟了程序集签名；4) 存在一个允许检查通过(原本不应通过)的注册表项。</Description>
   <Resolution Name="NoStrongName">用强名称密钥对 {0} 进行签名。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182127.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalError</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="AvoidExcessiveClassCoupling" Category="Microsoft.Maintainability" CheckId="CA1506">
   <Name>避免过度类耦合度</Name>
   <Description>类耦合度较高的类型和方法很难维护。</Description>
   <Resolution Name="Type">{0} 与来自 {2} 个不同命名空间中的 {1} 个不同类型耦合。请重写或重构该类的方法，以降低它的类耦合度，或者考虑将该类的某些方法移到与之紧密耦合的其他某些类型中。高于 {3} 的类耦合度表示可维护性较差，介于 {3} 和 {4} 之间的类耦合度表示可维护性适中，低于 {4} 的类耦合度表示可维护性较好。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb397994.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">Warning</MessageLevel>
   <File Name="maintainabilityrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="AvoidExcessiveComplexity" Category="Microsoft.Maintainability" CheckId="CA1502">
   <Name>避免过度复杂</Name>
   <Description>过于复杂的方法实现会增加理解和维护代码的难度。</Description>
   <Resolution Name="Default">{0} 的圈复杂度为 {1}。重写或重构该方法，以便将复杂度降低到 {2}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182212.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">Warning</MessageLevel>
   <File Name="maintainabilityrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="AvoidNamespacesWithFewTypes" Category="Microsoft.Design" CheckId="CA1020">
   <Name>避免使用类型极少的命名空间</Name>
   <Description>命名空间一般应具有五个以上的类型。</Description>
   <Resolution Name="Default">考虑将“{0}”中定义的类型与另一个命名空间合并。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182130.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="50">Warning</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="AvoidUninstantiatedInternalClasses" Category="Microsoft.Performance" CheckId="CA1812">
   <Name>避免未实例化的内部类</Name>
   <Description>检测到显然从未实例化过的内部类。此规则不会尝试检测后期绑定创建，并且，如果某一类型的实例都是以这种方法(例如，通过 Activator.CreateInstance 或将类型作为参数传递给 TypeConverter 构造函数)创建的，则将产生误报。</Description>
   <Resolution Name="Default">{0} 是显然没有实例化过的内部类。如果是这样，请从程序集内移除该代码。如果此内部类只用于包含静态方法，请考虑添加私有构造函数，以阻止编译器生成默认构造函数。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182265.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Warning</MessageLevel>
   <File Name="performancerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="CollectionPropertiesShouldBeReadOnly" Category="Microsoft.Usage" CheckId="CA2227">
   <Name>集合属性应为只读</Name>
   <Description>返回集合的属性应为只读，以确保用户无法完全替换后备存储。用户通过对集合调用相关方法仍然可以修改集合的内容。请注意，XmlSerializer 类对反序列化只读集合具有专门的支持。有关详细信息，请参见 XmlSerializer 概述。</Description>
   <Resolution Name="Default">通过移除属性 setter 将 {0} 更改为只读。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182327.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Warning</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DefaultParametersShouldNotBeUsed" Category="Microsoft.Design" CheckId="CA1026">
   <Name>不应使用默认形参</Name>
   <Description>某些编程语言不支持默认形参。用提供默认实参的方法重载来替换默认形参。</Description>
   <Resolution Name="Default">用提供所有默认形参的重载来替换方法 {0}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182135.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DoNotCallOverridableMethodsInConstructors" Category="Microsoft.Usage" CheckId="CA2214">
   <Name>不要在构造函数中调用可重写的方法</Name>
   <Description>不应通过构造函数调用类中定义的虚方法。如果某个派生类已重写该虚方法，则将在调用派生类的构造函数前，调用此派生类重写后的方法。</Description>
   <Resolution Name="Default">{0} 包含调用链，该调用链导致调用此类所定义的虚方法。请检查以下调用堆栈，看是否有意外结果: {1}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182331.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalWarning</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DoNotCastUnnecessarily" Category="Microsoft.Performance" CheckId="CA1800">
   <Name>避免进行不必要的强制转换</Name>
   <Description>由于进行强制转换会产生相关的开销，因此请尽可能避免重复强制转换。</Description>
   <Resolution Name="Local">在方法 {2} 中多次将变量 {0} 强制转换为类型 {1}。请缓存“as”运算符的结果或直接强制转换“as”运算符以消除冗余的 {3} 指令。</Resolution>
   <Resolution Name="Parameter">在方法 {2} 中多次将参数 {0} 强制转换为类型 {1}。请缓存“as”运算符或直接强制转换的结果以消除冗余的 {3} 指令。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182271.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Warning</MessageLevel>
   <File Name="performancerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DoNotCatchGeneralExceptionTypes" Category="Microsoft.Design" CheckId="CA1031">
   <Name>不要捕捉一般异常类型</Name>
   <Description>不应捕捉 Exception 或 SystemException。捕捉一般异常类型会使库用户看不到运行时问题，并会使调试复杂化。您应该仅捕捉自己可以进行适当处理的异常。</Description>
   <Resolution Name="Default">修改 {0} 以便捕捉比 {1} 更具体的异常，或者再次引发该异常。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182137.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalError</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DoNotDeclareVisibleInstanceFields" Category="Microsoft.Design" CheckId="CA1051">
   <Name>不要声明可见实例字段</Name>
   <Description>在其声明类型的外部可见的实例字段会限制您更改这些数据项的实现详细信息的能力。请改用属性。属性不会降低可用性或性能，并且能够提供灵活性，因为它们隐藏了基础数据的实现详细信息。</Description>
   <Resolution Name="Default">由于字段 {0} 在其声明类型的外部可见，因此，请将它的可访问性改为私有，并添加一个与该字段当前的可访问性相同的属性以提供对该属性的访问。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182141.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DoNotNestGenericTypesInMemberSignatures" Category="Microsoft.Design" CheckId="CA1006">
   <Name>不要将泛型类型嵌套在成员签名中</Name>
   <Description>避免如下的 API: 要求用户以另一个泛型类型为类型实参来实例化一个泛型类型。这样的语法过于复杂。</Description>
   <Resolution Name="Default">考虑 {0} 不嵌套泛型类型 {1} 的设计。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182144.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalError</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="DoNotRaiseExceptionsInExceptionClauses" Category="Microsoft.Usage" CheckId="CA2219">
   <Name>在异常子句中不引发异常</Name>
   <Description>在异常子句中引发异常会显著增加调试难度。在 finally 和 fault 子句中引发的异常将隐藏在相应的 try 块中引发的异常。在筛选器中引发的异常将被忽略，在处理时将被视为筛选器已经返回 false。</Description>
   <Resolution Name="Finally">修改成员 {0}，使它不在 finally 子句中引发异常。在从 finally 子句中引发异常时，新异常会隐藏当前处于活动状态的异常(如果存在的话)，并使原来的错误难以检测和调试。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb386041.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="ExceptionsShouldBePublic" Category="Microsoft.Design" CheckId="CA1064">
   <Name>异常应该是公共的</Name>
   <Description>异常类应该是公共的或者其上级应该是非泛型公共上级。</Description>
   <Resolution Name="Default">将异常类 {0} 设置为公共的。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb264484.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Warning</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="IdentifiersShouldBeCasedCorrectly" Category="Microsoft.Naming" CheckId="CA1709">
   <Name>标识符的大小写应当正确</Name>
   <Description>类型、命名空间和成员标识符应采用 Pascal 大小写格式。参数标识符应采用 Camel 大小写格式。这些标识符内由两个字母组成的首字母缩略词应全部大写，例如，应采用 System.IO，而不是 System.Io。由三个或更多个字母组成的首字母缩略词应采用 Pascal 大小写格式，例如，应采用 System.Xml，而不是 System.XML。Pascal 大小写格式约定每个单词的首字母大写，如 BackColor。Camel 大小写格式约定第一个单词的首字母小写，所有后续单词的首字母都大写，如 backgroundColor。尽管有些由两个字母组成的首字母缩略词习惯采用不完全大写形式，但不能因此而排斥此规则。例如，“DbConnection”很常见，但并不正确，应采用 DBConnection。为了与现有的非托管符号方案兼容，可能需要违反此规则。但一般来说，这些符号在使用它们的程序集之外不可见。</Description>
   <Resolution Name="Assembly">更正程序集名称 {1} 中“{0}”的大小写，将其改为“{2}”。</Resolution>
   <Resolution Name="Member">更正成员名称 {1} 中“{0}”的大小写，将其改为“{2}”。</Resolution>
   <Resolution Name="MemberAbbreviation">更正成员名称 {1} 中“{0}”的大小写，将其改为“{2}”。“{2}”是一个缩写词，因此无需遵守首字母缩略词大小写规则。</Resolution>
   <Resolution Name="Namespace">更正命名空间名称“{1}”中“{0}”的大小写，将其改为“{2}”。</Resolution>
   <Resolution Name="Type">更正类型名称 {1} 中“{0}”的大小写，将其改为“{2}”。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182240.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="85">Error</MessageLevel>
   <File Name="namingrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="IdentifiersShouldNotContainTypeNames" Category="Microsoft.Naming" CheckId="CA1720">
   <Name>标识符不应包含类型名称</Name>
   <Description>应避免在参数和成员中使用特定于某一种语言的类型名称，避免在参数中使用数据类型标识符。类型名对于所有开发人员来说可能不够直观。建议选用通用名称，如“value”。如果不足以区分，应确保采用 .NET Framework 库中定义的类型名，并完全避免采用特定于某一种语言的类型名称。例如，特定于 C# 的类型名有“float”(如果通用名称不足以区分，则使用“Single”)和“ulong”(如果通用名称不足以区分，则使用“UInt64”)等等。</Description>
   <Resolution Name="LanguageIndependentMemberParameter">在成员 {0} 中，考虑将参数名称 {2} 中的数据类型标识符“{1}”替换为一个更通用的词条，如“value”。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb531486.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Error</MessageLevel>
   <File Name="namingrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="ImplementStandardExceptionConstructors" Category="Microsoft.Design" CheckId="CA1032">
   <Name>实现标准异常构造函数</Name>
   <Description>正确实现一个自定义异常需要多个构造函数。缺少构造函数会使您的异常在某些情况下无法使用。例如，在 XML Web services 中处理异常需要序列化构造函数。</Description>
   <Resolution Name="MissingConstructor">将下列构造函数添加到 {0}: {1}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182151.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="InitializeReferenceTypeStaticFieldsInline" Category="Microsoft.Performance" CheckId="CA1810">
   <Name>以内联方式初始化引用类型的静态字段</Name>
   <Description>声明静态字段时，应初始化这些字段。初始化显式静态构造函数中的静态数据将导致代码性能较差。</Description>
   <Resolution Name="Default">声明 {0} 中的静态字段时应初始化所有这些字段，并移除显式静态构造函数。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182275.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">CriticalWarning</MessageLevel>
   <File Name="performancerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="InstantiateArgumentExceptionsCorrectly" Category="Microsoft.Usage" CheckId="CA2208">
   <Name>正确实例化参数异常</Name>
   <Description>传递给 ArgumentException 及其派生类型的构造函数的字符串参数应该是正确的。与 ArgumentException 相比，派生自 ArgumentException 的类型在 message 和 paramName 参数方面具有不一致的构造函数重载。</Description>
   <Resolution Name="IncorrectMessage">方法 {0} 将参数名“{1}”作为变量 {2} 传递给构造函数 {3}。请将此参数替换为一则说明性消息并在正确的位置传递参数名。</Resolution>
   <Resolution Name="IncorrectParameterName">方法 {0} 将“{1}”作为参数 {2} 传递给构造函数 {3}。请将此参数替换为该方法的某个参数名。请注意，所提供的参数名的大小写应与方法中声明的大小写完全一致。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182347.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalError</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="MarkAssembliesWithClsCompliant" Category="Microsoft.Design" CheckId="CA1014">
   <Name>用 CLSCompliantAttribute 标记程序集</Name>
   <Description>程序集应使用 CLSCompliant 特性显式说明其 ClS 遵从性。没有此特性的程序集不符合 CLS。即使程序集、模块或类型的某些部分不符合 CLS，这些程序集、模块或类型本身也可以是符合 CLS 的。适用下列规则: 1) 如果元素标记为 CLSCompliant，则所有不符合 CLS 的成员都必须有参数设置为 false 的 CLSCompliant 特性。2) 对于每个不符合 CLS 的成员，都必须提供一个相应的符合 CLS 的备选成员。</Description>
   <Resolution Name="Default">使用 CLSCompliant(true)来标记 {0}，因为它公开外部可见的类型。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182156.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="MarkISerializableTypesWithSerializable" Category="Microsoft.Usage" CheckId="CA2237">
   <Name>用 SerializableAttribute 标记 ISerializable 类型</Name>
   <Description>System.Runtime.Serialization.ISerializable 接口允许该类型自定义自身的序列化，而 Serializable 特性使运行时能够将该类型识别为可序列化的类型。</Description>
   <Resolution Name="Default">将 [Serializable] 添加到 {0}，原因是此类型实现了 ISerializable。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182350.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Warning</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="MarkMembersAsStatic" Category="Microsoft.Performance" CheckId="CA1822">
   <Name>将成员标记为 static</Name>
   <Description>不访问实例数据或调用实例方法的方法可标记为 Static (在 Visual Basic 中为 Shared)。这样，编译器会向这些成员发出非虚拟调用站点，以防止在运行时对每个调用进行旨在确保当前对象指针为非 null 的检查。这样可以使对性能比较敏感的代码获得显著的性能提升。在某些情况下，无法访问当前对象实例表明存在正确性问题。</Description>
   <Resolution Name="Default">从未使用 {0} 的“this”参数(Visual Basic 中为“Me”)。根据需要，将成员标记为 static (Visual Basic 中为“Shared”)，或者在方法主体或至少一个属性访问器中使用“this”/“Me”。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms245046.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Warning</MessageLevel>
   <File Name="performancerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="NestedTypesShouldNotBeVisible" Category="Microsoft.Design" CheckId="CA1034">
   <Name>嵌套类型不应是可见的</Name>
   <Description>不要使用 public、protected 或 protected internal (Protected Friend)等嵌套类型作为类型的分组方式。使用命名空间实现此目的。嵌套类型仅在极为有限的情况下才是最佳设计。此外，不是所有的用户都能清楚地了解嵌套类型成员的可访问性。枚举数不受此规则限制。</Description>
   <Resolution Name="Default">不要嵌套类型 {0}。或者，更改其可访问性，使它在外部不可见。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182162.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="NormalizeStringsToUppercase" Category="Microsoft.Globalization" CheckId="CA1308">
   <Name>将字符串规范化为大写</Name>
   <Description>字符串应规范化为大写。</Description>
   <Resolution Name="ToUpperInvariant">在方法 {0} 中，将对 {1} 的调用替换为 String.ToUpperInvariant()。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb386042.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">Error</MessageLevel>
   <File Name="globalizationrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="ParameterNamesShouldMatchBaseDeclaration" Category="Microsoft.Naming" CheckId="CA1725">
   <Name>参数名应与基方法中的声明保持一致</Name>
   <Description>出于可用性原因，已重写方法中的参数名应与基方法声明中参数名保持一致。</Description>
   <Resolution Name="Default">在成员 {0} 中，将参数名称 {1} 改为 {2}，使其与已在 {3} 中声明的标识符匹配。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182251.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="namingrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="PropertiesShouldNotReturnArrays" Category="Microsoft.Performance" CheckId="CA1819">
   <Name>属性不应返回数组</Name>
   <Description>返回数组的属性容易降低代码的效率。请考虑使用集合或将其转换为方法。有关详细信息，请参见设计指南。</Description>
   <Resolution Name="Default">将 {0} 更改为返回集合或将其转换为方法。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/0fss9skc.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="50">Warning</MessageLevel>
   <File Name="performancerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="RemoveUnusedLocals" Category="Microsoft.Performance" CheckId="CA1804">
   <Name>移除未使用的局部变量</Name>
   <Description>移除方法实现中未使用过的或只赋过值的局部变量。</Description>
   <Resolution Name="Default">{0} 声明类型为 {2} 的变量 {1}，但从未使用过该变量或只对它进行过赋值。请使用此变量或将它移除。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182278.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Warning</MessageLevel>
   <File Name="performancerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="RethrowToPreserveStackDetails" Category="Microsoft.Usage" CheckId="CA2200">
   <Name>再次引发以保留堆栈详细信息</Name>
   <Description>重复引发捕获的异常时，请使用 IL 再次引发指令保留原始堆栈详细信息。</Description>
   <Resolution Name="Default">{0} 再次引发捕获的异常并将其显式地指定为一个参数。请改用不带参数的“throw”以保留该异常最初引发时所在的堆栈位置。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182363.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="ReviewUnusedParameters" Category="Microsoft.Usage" CheckId="CA1801">
   <Name>检查未使用的参数</Name>
   <Description>检查在非虚方法的方法主体中未使用的参数，以确保不存在应该访问这些参数的情况。未使用的参数会带来维护和性能开销。有时，与该规则冲突可能说明方法中存在实现 Bug，即，该参数实际上应该已在方法主体中使用了。如果该参数由于向后兼容性而必须存在，请忽略关于此规则的警告。</Description>
   <Resolution Name="Default">从未用过 {1} 的参数 {0}。请移除该参数或在方法主体中使用它。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182268.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Warning</MessageLevel>
   <File Name="usagerules.dll" Version="********" />
  </Rule>
  <Rule TypeName="SecurityRuleSetLevel2MethodsShouldNotBeProtectedWithLinkDemandsFxCopRule" Category="Microsoft.Security" CheckId="CA2135">
   <Name>级别 2 程序集不应包含 LinkDemand</Name>
   <Description>级别 2 安全规则集中已弃用 LinkDemand。应改为使用 SecurityCritical 方法、类型和字段，来替代使用 LinkDemand 强制实施 JIT 时间安全性。</Description>
   <Resolution Name="Default">使用 {1} 的 LinkDemand 保护 {0}。在级别 2 安全规则集中，应改为使其成为安全关键的来提供保护。移除此 LinkDemand 并将 {0} 标记为安全关键的。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/dd997569(VS.100).aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="50">CriticalWarning</MessageLevel>
   <File Name="securitytransparencyrules.dll" Version="*******" />
  </Rule>
  <Rule TypeName="SpecifyCultureInfo" Category="Microsoft.Globalization" CheckId="CA1304">
   <Name>指定 CultureInfo</Name>
   <Description>如果存在一个采用 CultureInfo 参数的重载，则应始终调用该重载，而不调用未采用该参数的重载。CultureInfo 类型包含区域性特定的信息，这些信息是执行数字和字符串运算(如大小写转换、格式设置和字符串比较)所必需的。如果区域性之间的转换和分析行为应始终不变，请指定 CultureInfo.InvariantCulture，否则，请指定 CultureInfo.CurrentCulture。</Description>
   <Resolution Name="Default">由于 {0} 的行为可能因当前用户的区域设置不同而不同，请将 {1} 中的此调用替换为对 {2} 的调用。如果要向用户显示 {2} 的结果，请将“CultureInfo.CurrentCulture”指定为“CultureInfo”参数；如果软件将存储和访问此结果(例如，要将它保留到磁盘或数据库中)，则指定“CultureInfo.InvariantCulture”。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182189.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="globalizationrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="SpecifyIFormatProvider" Category="Microsoft.Globalization" CheckId="CA1305">
   <Name>指定 IFormatProvider</Name>
   <Description>如果存在一个采用 IFormatProvider 参数的重载，则应始终调用该重载，而不调用未采用该参数的重载。运行时中的某些方法可以将值与字符串表示形式相互进行转换，并采用一个字符串参数。该参数中包含一个或多个称为格式说明符的字符，用于指示如何对值进行转换。如果格式说明符的含义因区域性而异，则格式设置对象提供在字符串表示形式中使用的实际字符。如果排序和比较行为不得因区域性而改变，请指定 CultureInfo.InvariantCulture，否则，请指定 CultureInfo.CurrentCulture。</Description>
   <Resolution Name="IFormatProviderAlternate">由于 {0} 的行为可能会因当前用户的区域设置不同而不同，请将 {1} 中的此调用替换为对 {2} 的调用。如果 {2} 的结果将基于用户的输入，请指定 {3} 作为“IFormatProvider”参数。或者，如果此结果将基于由软件存储和访问的输入(例如，当从磁盘或数据库加载此结果时)，则指定 {4}。</Resolution>
   <Resolution Name="IFormatProviderAlternateString">由于 {0} 的行为可能会因当前用户的区域设置不同而不同，请将 {1} 中的此调用替换为对 {2} 的调用。如果要向用户显示 {2} 的结果，请指定 {3} 作为“IFormatProvider”参数。或者，如果软件将存储和访问此结果(例如，当将此结果保留到磁盘或数据库中时)，则指定 {4}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182190.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="globalizationrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="SpecifyStringComparison" Category="Microsoft.Globalization" CheckId="CA1307">
   <Name>指定 StringComparison</Name>
   <Description>如果存在采用 StringComparison 参数的重载，则始终先调用此重载，而不是那些不接受该参数的重载。</Description>
   <Resolution Name="Default">由于 {0} 的行为可能会因当前用户的区域设置不同而不同，请将 {1} 中的此调用替换为对 {2} 的调用。如果要向用户显示 {2} 的结果(例如，在对一个项列表进行排序以便在列表框中显示时)，请指定“StringComparison.CurrentCulture”或“StringComparison.CurrentCultureIgnoreCase”作为“StringComparison”参数。如果比较不区分大小写的标识符(例如，文件路径、环境变量或注册表项和值)，则指定“StringComparison.OrdinalIgnoreCase”。或者，如果比较区分大小写的标识符，则指定“StringComparison.Ordinal”。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb386080.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">Error</MessageLevel>
   <File Name="globalizationrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="StaticHolderTypesShouldNotHaveConstructors" Category="Microsoft.Design" CheckId="CA1053">
   <Name>静态容器类型不应具有构造函数</Name>
   <Description>不需要创建只定义静态成员的类型的实例。如果没有指定构造函数，许多编译器都会自动添加公共的默认构造函数。为了避免出现这种情况，可能需要添加一个空的私有构造函数。</Description>
   <Resolution Name="CSharp2_0">由于类型 {0} 仅包含“static”成员，因此将它标记为“static”可阻止编译器添加默认公共构造函数。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182169.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="90">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="StringUriOverloadsCallSystemUriOverloads" Category="Microsoft.Design" CheckId="CA1057">
   <Name>字符串 URI 重载调用 System.Uri 重载</Name>
   <Description>如果一个方法通过用字符串替换 System.Uri 参数来重载另一个方法，则该字符串重载只是从该字符串生成一个 URI 对象，然后将结果传递给字符串重载。</Description>
   <Resolution Name="Default">重构 {0}，使其基于 {1} 生成一个 System.Uri 对象，然后调用 {2}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182170.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="TypesThatOwnDisposableFieldsShouldBeDisposable" Category="Microsoft.Design" CheckId="CA1001">
   <Name>具有可释放字段的类型应该是可释放的</Name>
   <Description>声明可释放成员的类型也应实现 IDisposable。如果该类型没有任何非托管资源，请不要在其上实现终结器。</Description>
   <Resolution Name="NonBreaking">在 {0} 上实现 IDisposable，因为它创建下列 IDisposable 类型的成员: {1}。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182172.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalError</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="UriParametersShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1054">
   <Name>URI 参数不应为字符串</Name>
   <Description>如果某个参数的名称中包含“uri”、“url”或“urn”，并且该参数被类型化为字符串，则应将该参数的类型改为 System.Uri，除非有一个重载方法已将该参数类型化为 URI。</Description>
   <Resolution Name="Default">将方法 {1} 的参数 {0} 的类型从字符串改为 System.Uri，或者提供 {1} 的重载，允许将 {0} 作为 System.Uri 对象来传递。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182174.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="60">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="UriPropertiesShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1056">
   <Name>URI 属性不应是字符串</Name>
   <Description>如果某属性的名称包含“uri”、“url”或“urn”，并且该属性被类型化为字符串，则应将其更改为 System.Uri。</Description>
   <Resolution Name="Default">将属性 {0} 的类型从字符串改为 System.Uri。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182175.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="UriReturnValuesShouldNotBeStrings" Category="Microsoft.Design" CheckId="CA1055">
   <Name>URI 返回值不应是字符串</Name>
   <Description>如果某函数的名称中包含“uri”、“url”或“urn”，并且返回类型为字符串，则应将返回类型更改为 System.Uri。</Description>
   <Resolution Name="Default">将 {0} 的返回类型从字符串更改为 System.Uri。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182176.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="60">Error</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="UseOrdinalStringComparison" Category="Microsoft.Globalization" CheckId="CA1309">
   <Name>使用按顺序的 StringComparison</Name>
   <Description>对于非语义比较，应使用 StringComparison.Ordinal 或 StringComparison.OrdinalIgnoreCase，而不使用区分语义的 StringComparison.InvariantCulture。</Description>
   <Resolution Name="StringComparison">{0} 将“{1}”作为“StringComparison”参数传递给 {2}。若要执行非语义比较，请改为指定“StringComparison.Ordinal”或“StringComparison.OrdinalIgnoreCase”。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/bb385972.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Error</MessageLevel>
   <File Name="globalizationrules.dll" Version="********" />
  </Rule>
  <Rule TypeName="UsePropertiesWhereAppropriate" Category="Microsoft.Design" CheckId="CA1024">
   <Name>在适用处使用属性</Name>
   <Description>在大多数情况下，应使用属性而不是 Get/Set 方法。在下列情况下，方法比属性更可取: 第一，执行的是转换操作，操作开销大或具有显著的副作用；第二，执行的顺序很重要；第三，连续两次调用成员得到的结果不同；第四，静态成员却返回了可变的值；第五，成员返回了数组。</Description>
   <Resolution Name="Default">如果可行，请将 {0} 改为属性。</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182181.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="75">Warning</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
 </Rules>
 <Localized>
  <String Key="Category">类别</String>
  <String Key="Certainty">确定性</String>
  <String Key="CollapseAll">全部折叠</String>
  <String Key="CheckId">检查 ID</String>
  <String Key="Error">错误</String>
  <String Key="Errors">错误</String>
  <String Key="ExpandAll">全部展开</String>
  <String Key="Help">帮助</String>
  <String Key="Line">行</String>
  <String Key="Messages">消息</String>
  <String Key="LocationNotStoredInPdb">[位置未存储在 Pdb 中]</String>
  <String Key="Project">项目</String>
  <String Key="Resolution">解析</String>
  <String Key="Rule">规则</String>
  <String Key="RuleFile">规则文件</String>
  <String Key="RuleDescription">规则说明</String>
  <String Key="Source">源</String>
  <String Key="Status">状态</String>
  <String Key="Target">目标</String>
  <String Key="Warning">警告</String>
  <String Key="Warnings">警告</String>
  <String Key="ReportTitle">代码分析报告</String>
 </Localized>
 <Exceptions>
  <Exception Keyword="CA0001" Kind="Engine">
   <Type>Phx.FatalError</Type>
   <ExceptionMessage>引发类型为“Phx.FatalError”的异常。</ExceptionMessage>
   <StackTrace>   在 Phx.FatalError.Dispatch()
   在 Phx.Logging.Diagnostics.DiagnosticMessage.LogMessage(Severity severity, DiagnosticInfo diagnosticInfo, SourceContext sourceContext, String descriptionString)
   在 Phx.PE.PEUtilities.UserError(String infoMessage)
   在 Phx.Pdb.ReaderImplementation.Open(Boolean loadTypeOnly)
   在 Phx.PEModuleUnit.LoadPdb()
   在 Phx.PE.ReaderPhase.CheckSymbolicInformation()
   在 Phx.PEModuleUnit.LoadGlobalSymbols()
   在 Phx.PEModuleUnit.LoadEncodedIRUnitList()
   在 Phx.PEModuleUnit.GetEnumerableContributionUnit(ContributionUnitEnumerationKind contributionUnitEnumerationKind)
   在 Phx.PEModuleUnit.GetEnumerableContributionUnit()
   在 Microsoft.FxCop.Engines.Phoenix.PreScanPass.GetFunctionUnits(PEModuleUnit peModuleUnit)
   在 Microsoft.FxCop.Engines.Phoenix.PreScanPass.Execute(ModuleUnit moduleUnit)
   在 Phx.Passes.Pass.DoPass(ModuleUnit moduleUnit)
   在 Phx.Passes.PassList.DoPassList(ModuleUnit moduleUnit)
   在 Microsoft.FxCop.Engines.Phoenix.PhoenixAnalysisEngine.Host.ExecutePasses(PEModuleUnit peModuleUnit)
   在 Microsoft.FxCop.Engines.Phoenix.PhoenixAnalysisEngine.AnalyzeInternal()
   在 Microsoft.FxCop.Engines.Phoenix.PhoenixAnalysisEngine.Analyze()
   在 Microsoft.FxCop.Common.EngineManager.Analyze(Project project, Boolean verboseOutput)</StackTrace>
  </Exception>
 </Exceptions>
</FxCopReport>
