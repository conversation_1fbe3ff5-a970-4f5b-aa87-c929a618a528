using Dqy.Instrument.CommonTypes;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Framework.Component.Helpers;
using Dqy.Instrument.Mall.CommonLib.HttpUtility;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Dqy.Instrument.Mall.CommonLib
{
    public class WebApiHelper
    {
        #region 同步请求
        /// <summary>
        /// 向需要AccessToken的API发送消息的公共方法
        /// </summary>
        /// <param name="accessToken">这里的AccessToken是通用接口的AccessToken</param>
        /// <param name="url"></param>
        /// <param name="data">如果是Get方式，可以为null。在POST方式中将被转为JSON字符串提交</param>
        /// <param name="sendType">发送类型，POST或GET，默认为POST</param>
        /// <param name="timeOut">代理请求超时时间（毫秒）</param>
        /// <param name="checkValidationResult">验证服务器证书回调自动验证</param>
        /// <param name="jsonSetting">JSON字符串生成设置</param>
        /// <returns></returns>
        public static T Send<T>(string url, object data, CommonJsonSendType sendType = CommonJsonSendType.POST, int timeOut = Config.TIME_OUT, bool checkValidationResult = false,
            JsonSetting jsonSetting = null
            )
        {
            if (!url.StartsWith("http"))
            {
                url = Constant.ApiPath + url;
            }

            if (data != null)
            {
                FileLog.SendApiLog("API开始调用：" + ComLib.Object2JSON(data) + "\r\n URL：" + url);
            }
            else
            {
                FileLog.SendApiLog("API调用地址：" + url);
            }
            
            try
            {
                switch (sendType)
                {
                    case CommonJsonSendType.GET:
                        return Get.GetJson<T>(url);
                    case CommonJsonSendType.POST:
                        //SerializerHelper serializerHelper = new SerializerHelper();
                        //var jsonString = serializerHelper.GetJsonString(data, jsonSetting);
                        var jsonString = ComLib.Object2JSON(data);
                        using (MemoryStream ms = new MemoryStream())
                        {
                            var bytes = Encoding.UTF8.GetBytes(jsonString);
                            ms.WriteAsync(bytes, 0, bytes.Length);
                            ms.Seek(0, SeekOrigin.Begin);

                            return
                                    Post.PostGetJson<T>(url, null, ms, timeOut: timeOut,
                                        checkValidationResult: checkValidationResult);
                        }
                    default:
                        throw new ArgumentOutOfRangeException("sendType");
                }
            }
            catch (Exception exp)
            {
                FileLog.SendExceptionLog("Api调用失败：" + exp.Message + "\r\n URL：" + url);
                if (data != null)
                {
                    FileLog.SendExceptionLog("数据信息：" + ComLib.Object2JSON(data));
                }

                System.Web.HttpContext.Current.Response.Redirect("/Member/Error");
                throw new ArgumentOutOfRangeException(exp.Message);
            }
        }

        #endregion

        #region 异步请求

        /// <summary>
        /// 向需要AccessToken的API发送消息的公共方法
        /// </summary>
        /// <param name="accessToken">这里的AccessToken是通用接口的AccessToken，非OAuth的。如果不需要，可以为null，此时urlFormat不要提供{0}参数</param>
        /// <param name="urlFormat"></param>
        /// <param name="data">如果是Get方式，可以为null</param>
        /// <param name="timeOut">代理请求超时时间（毫秒）</param>
        /// <returns></returns>
        public static async Task<ReturnResult> SendAsync(string url, object data, CommonJsonSendType sendType = CommonJsonSendType.POST, int timeOut = Config.TIME_OUT)
        {
            return await SendAsync<ReturnResult>(url, data, sendType, timeOut);
        }

        /// <summary>
        /// 向需要AccessToken的API发送消息的公共方法
        /// </summary>
        /// <param name="accessToken">这里的AccessToken是通用接口的AccessToken</param>
        /// <param name="url"></param>
        /// <param name="data">如果是Get方式，可以为null。在POST方式中将被转为JSON字符串提交</param>
        /// <param name="sendType">发送类型，POST或GET，默认为POST</param>
        /// <param name="timeOut">代理请求超时时间（毫秒）</param>
        /// <param name="checkValidationResult">验证服务器证书回调自动验证</param>
        /// <param name="jsonSetting">JSON字符串生成设置</param>
        /// <returns></returns>
        public static async Task<T> SendAsync<T>(string url, object data, CommonJsonSendType sendType = CommonJsonSendType.POST, int timeOut = Config.TIME_OUT, bool checkValidationResult = false,
            JsonSetting jsonSetting = null
            )
        {
            if (!url.StartsWith("http"))
            {
                url = Constant.ApiPath + url;
            }

            // 使用SslHelper配置SSL/TLS协议以支持Windows Server 2016
            SslHelper.ConfigureForUrl(url, checkValidationResult);

            if(data!= null)
            {
                FileLog.SendApiLog("API开始调用：" + ComLib.Object2JSON(data) + "\r\n URL：" + url);
            }
            else
            {
                FileLog.SendApiLog("API调用地址：" + url );
            }

            // 记录SSL配置信息
            FileLog.SendApiLog($"SSL配置信息：{SslHelper.GetSupportedProtocols()}");
            try
            {
                switch (sendType)
                {
                    case CommonJsonSendType.GET:
                        return await Get.GetJsonAsync<T>(url);
                    case CommonJsonSendType.POST:
                        //SerializerHelper serializerHelper = new SerializerHelper();
                        //var jsonString = serializerHelper.GetJsonString(data, jsonSetting);
                        var jsonString = ComLib.Object2JSON(data);
                        using (MemoryStream ms = new MemoryStream())
                        {
                            var bytes = Encoding.UTF8.GetBytes(jsonString);
                            await ms.WriteAsync(bytes, 0, bytes.Length);
                            ms.Seek(0, SeekOrigin.Begin);

                            return
                                await
                                    Post.PostGetJsonAsync<T>(url, null, ms, timeOut: timeOut,
                                        checkValidationResult: checkValidationResult);
                        }
                    default:
                        throw new ArgumentOutOfRangeException("sendType");
                }
            }
            catch (Exception exp)
            {
                // 详细的错误日志记录
                var errorDetails = new StringBuilder();
                errorDetails.AppendLine($"API调用失败：{exp.Message}");
                errorDetails.AppendLine($"URL：{url}");
                errorDetails.AppendLine($"异常类型：{exp.GetType().Name}");
                errorDetails.AppendLine($"SSL配置：{SslHelper.GetSupportedProtocols()}");

                if (data != null)
                {
                    errorDetails.AppendLine($"请求数据：{ComLib.Object2JSON(data)}");
                }

                if (exp.InnerException != null)
                {
                    errorDetails.AppendLine($"内部异常：{exp.InnerException.Message}");
                }

                // 针对SSL错误的特殊处理
                if (exp.Message.Contains("SSL") || exp.Message.Contains("TLS") || exp.Message.Contains("安全通道"))
                {
                    errorDetails.AppendLine("这是SSL/TLS相关错误，建议检查：");
                    errorDetails.AppendLine("1. 确保已调用 ApplicationStartup.Initialize()");
                    errorDetails.AppendLine("2. 检查服务器TLS协议支持");
                    errorDetails.AppendLine("3. 验证证书配置");
                    errorDetails.AppendLine("4. 检查nginx代理配置");
                }

                FileLog.SendExceptionLog(errorDetails.ToString());

                System.Web.HttpContext.Current.Response.Redirect("/Member/Error");
                throw new ArgumentOutOfRangeException(exp.Message);
            }
        }

        #endregion

        public static string GetRequest(string url, string getParameter)
        {
            // 使用SslHelper配置SSL/TLS协议
            SslHelper.ConfigureForUrl(Constant.ApiPath + url);
            string responseStr = string.Empty;
            try
            {
                WebRequest request = WebRequest.Create(Constant.ApiPath + url + "?" + getParameter);
                request.Method = "Get";
                var response = request.GetResponse();
                Stream ReceiveStream = response.GetResponseStream();
                using (StreamReader stream = new StreamReader(ReceiveStream, Encoding.UTF8))
                {
                    responseStr = stream.ReadToEnd();
                }
            }
            catch (Exception exp)
            {
                FileLog.SendExceptionLog("Api调用失败：" + exp.Message + "\r\n URL：" + url);
            }

            return responseStr;
        }


        public static string PostRequest(string url, string postParameter)
        {
            // 使用SslHelper配置SSL/TLS协议
            SslHelper.ConfigureForUrl(Constant.ApiPath + url);
            string responseStr = string.Empty;
            try
            {
                WebRequest request = WebRequest.Create(Constant.ApiPath + url);
                request.Method = "Post";
                request.ContentType = "application/json";

                byte[] requestData = Encoding.UTF8.GetBytes(postParameter);
                request.ContentLength = requestData.Length;

                Stream newStream = request.GetRequestStream();
                newStream.Write(requestData, 0, requestData.Length);
                newStream.Close();

                var response = request.GetResponse();
                Stream ReceiveStream = response.GetResponseStream();
                using (StreamReader stream = new StreamReader(ReceiveStream, Encoding.UTF8))
                {
                    responseStr = stream.ReadToEnd();
                }
            }
            catch (Exception exp)
            {
                FileLog.SendExceptionLog("Api调用失败：" + exp.Message + "\r\n URL：" + url);
            }
            return responseStr;
        }

        /// <summary>
        /// Http的 WebRequest的特定实现
        /// </summary>
        /// <param name="url"></param>
        /// <param name="JSONData"></param>
        /// <returns></returns>
        public static string GetResponseData(string url, string JSONData)
        {
            // 使用SslHelper配置SSL/TLS协议
            SslHelper.ConfigureForUrl(Constant.ApiPath + url);
            byte[] bytes = Encoding.UTF8.GetBytes(JSONData);
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(Constant.ApiPath + url);
            request.Method = "POST";
            request.ContentLength = bytes.Length;
            request.ContentType = "application/json";
            Stream reqstream = request.GetRequestStream();
            reqstream.Write(bytes, 0, bytes.Length);

            //声明一个HttpWebRequest请求  
            request.Timeout = 90000;
            //设置连接超时时间  
            request.Headers.Set("Pragma", "no-cache");
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            Stream streamReceive = response.GetResponseStream();
            Encoding encoding = Encoding.UTF8;

            StreamReader streamReader = new StreamReader(streamReceive, encoding);
            string strResult = streamReader.ReadToEnd();
            streamReceive.Dispose();
            streamReader.Dispose();

            return strResult;
        }



    }
}
