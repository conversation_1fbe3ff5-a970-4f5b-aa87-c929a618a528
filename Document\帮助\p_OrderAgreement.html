﻿ OrderId = entity.OrderId,
 WarrantyPeriod = entity.WarrantyPeriod,
 PriceDecline = entity.PriceDecline,
 OrderTotalAmount = entity.OrderTotalAmount,
 Freight = entity.Freight,
 MakeupPrice = entity.MakeupPrice,
 ContractTotalAmount = entity.ContractTotalAmount,
 OtherAppointment = entity.OtherAppointment,
 InputUserId = entity.InputUserId,
 InputUnitId = entity.InputUnitId,
 InputTime = entity.InputTime,
 ConfirmUserId = entity.ConfirmUserId,
 ConfirmUnitId = entity.ConfirmUnitId,
 ConfirmTime = entity.ConfirmTime,


 OrderId = model.OrderId,
 WarrantyPeriod = model.WarrantyPeriod,
 PriceDecline = model.PriceDecline,
 OrderTotalAmount = model.OrderTotalAmount,
 Freight = model.Freight,
 MakeupPrice = model.MakeupPrice,
 ContractTotalAmount = model.ContractTotalAmount,
 OtherAppointment = model.OtherAppointment,
 InputUserId = model.InputUserId,
 InputUnitId = model.InputUnitId,
 InputTime = model.InputTime,
 ConfirmUserId = model.ConfirmUserId,
 ConfirmUnitId = model.ConfirmUnitId,
 ConfirmTime = model.ConfirmTime,


 temp.OrderId = model.OrderId,
 temp.WarrantyPeriod = model.WarrantyPeriod,
 temp.PriceDecline = model.PriceDecline,
 temp.OrderTotalAmount = model.OrderTotalAmount,
 temp.Freight = model.Freight,
 temp.MakeupPrice = model.MakeupPrice,
 temp.ContractTotalAmount = model.ContractTotalAmount,
 temp.OtherAppointment = model.OtherAppointment,
 temp.InputUserId = model.InputUserId,
 temp.InputUnitId = model.InputUnitId,
 temp.InputTime = model.InputTime,
 temp.ConfirmUserId = model.ConfirmUserId,
 temp.ConfirmUnitId = model.ConfirmUnitId,
 temp.ConfirmTime = model.ConfirmTime,

 OrderAgreementId = item.OrderAgreementId,
 OrderId = item.OrderId,
 WarrantyPeriod = item.WarrantyPeriod,
 PriceDecline = item.PriceDecline,
 OrderTotalAmount = item.OrderTotalAmount,
 Freight = item.Freight,
 MakeupPrice = item.MakeupPrice,
 ContractTotalAmount = item.ContractTotalAmount,
 OtherAppointment = item.OtherAppointment,
 InputUserId = item.InputUserId,
 InputUnitId = item.InputUnitId,
 InputTime = item.InputTime,
 ConfirmUserId = item.ConfirmUserId,
 ConfirmUnitId = item.ConfirmUnitId,
 ConfirmTime = item.ConfirmTime,

public class OrderAgreementInputModel
{
 [Display(Name = "Id")] 
    public long OrderAgreementId {get; set; }
    
 [Display(Name = "订单Id")] 
    public long OrderId {get; set; }
    
 [Display(Name = "供货期")] 
    public int WarrantyPeriod {get; set; }
    
 [Display(Name = "价格下浮")] 
    public decimal PriceDecline {get; set; }
    
 [Display(Name = "订单总价")] 
    public decimal OrderTotalAmount {get; set; }
    
 [Display(Name = "运输费")] 
    public decimal Freight {get; set; }
    
 [Display(Name = "订单补差价")] 
    public decimal MakeupPrice {get; set; }
    
 [Display(Name = "合同总价")] 
    public decimal ContractTotalAmount {get; set; }
    
 [Display(Name = "其他约定")] 
    public string OtherAppointment {get; set; }
    
 [Display(Name = "录入人Id(卖方确认人)")] 
    public long InputUserId {get; set; }
    
 [Display(Name = "录入人单位Id（卖方确认单位）")] 
    public int InputUnitId {get; set; }
    
 [Display(Name = "录入时间（卖方确认时间）")] 
    public DateTime InputTime {get; set; }
    
 [Display(Name = "买方确认人Id")] 
    public long ConfirmUserId {get; set; }
    
 [Display(Name = "买方确认人单位")] 
    public int ConfirmUnitId {get; set; }
    
 [Display(Name = "买方确认时间")] 
    public DateTime ConfirmTime {get; set; }
    
 }
 
 public class OrderAgreementViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long OrderAgreementId {get; set; }
    
    /// <summary>
    /// 订单Id
    /// </summary>
    public long OrderId {get; set; }
    
    /// <summary>
    /// 供货期
    /// </summary>
    public int WarrantyPeriod {get; set; }
    
    /// <summary>
    /// 价格下浮
    /// </summary>
    public decimal PriceDecline {get; set; }
    
    /// <summary>
    /// 订单总价
    /// </summary>
    public decimal OrderTotalAmount {get; set; }
    
    /// <summary>
    /// 运输费
    /// </summary>
    public decimal Freight {get; set; }
    
    /// <summary>
    /// 订单补差价
    /// </summary>
    public decimal MakeupPrice {get; set; }
    
    /// <summary>
    /// 合同总价
    /// </summary>
    public decimal ContractTotalAmount {get; set; }
    
    /// <summary>
    /// 其他约定
    /// </summary>
    public string OtherAppointment {get; set; }
    
    /// <summary>
    /// 录入人Id(卖方确认人)
    /// </summary>
    public long InputUserId {get; set; }
    
    /// <summary>
    /// 录入人单位Id（卖方确认单位）
    /// </summary>
    public int InputUnitId {get; set; }
    
    /// <summary>
    /// 录入时间（卖方确认时间）
    /// </summary>
    public DateTime InputTime {get; set; }
    
    /// <summary>
    /// 买方确认人Id
    /// </summary>
    public long ConfirmUserId {get; set; }
    
    /// <summary>
    /// 买方确认人单位
    /// </summary>
    public int ConfirmUnitId {get; set; }
    
    /// <summary>
    /// 买方确认时间
    /// </summary>
    public DateTime ConfirmTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.WarrantyPeriod, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.WarrantyPeriod, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入供货期" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PriceDecline, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PriceDecline, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入价格下浮" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OrderTotalAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OrderTotalAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单总价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Freight, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Freight, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入运输费" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.MakeupPrice, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MakeupPrice, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入订单补差价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ContractTotalAmount, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ContractTotalAmount, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入合同总价" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.OtherAppointment, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.OtherAppointment, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入其他约定" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InputUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InputUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入录入人Id(卖方确认人)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InputUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InputUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入录入人单位Id（卖方确认单位）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InputTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InputTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入录入时间（卖方确认时间）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ConfirmUserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ConfirmUserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入买方确认人Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ConfirmUnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ConfirmUnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入买方确认人单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ConfirmTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ConfirmTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入买方确认时间" } })                    
                </div>
           </div>
  




 { field: 'OrderId', title: '订单Id', sortable: true },
                 
 { field: 'WarrantyPeriod', title: '供货期', sortable: true },
                 
 { field: 'PriceDecline', title: '价格下浮', sortable: true },
                 
 { field: 'OrderTotalAmount', title: '订单总价', sortable: true },
                 
 { field: 'Freight', title: '运输费', sortable: true },
                 
 { field: 'MakeupPrice', title: '订单补差价', sortable: true },
                 
 { field: 'ContractTotalAmount', title: '合同总价', sortable: true },
                 
 { field: 'OtherAppointment', title: '其他约定', sortable: true },
                 
 { field: 'InputUserId', title: '录入人Id(卖方确认人)', sortable: true },
                 
 { field: 'InputUnitId', title: '录入人单位Id（卖方确认单位）', sortable: true },
                 
 { field: 'InputTime', title: '录入时间（卖方确认时间）', sortable: true },
                 
 { field: 'ConfirmUserId', title: '买方确认人Id', sortable: true },
                 
 { field: 'ConfirmUnitId', title: '买方确认人单位', sortable: true },
                 
 { field: 'ConfirmTime', title: '买方确认时间', sortable: true },
                 
o.OrderId,                 
o.WarrantyPeriod,                 
o.PriceDecline,                 
o.OrderTotalAmount,                 
o.Freight,                 
o.MakeupPrice,                 
o.ContractTotalAmount,                 
o.OtherAppointment,                 
o.InputUserId,                 
o.InputUnitId,                 
o.InputTime,                 
o.ConfirmUserId,                 
o.ConfirmUnitId,                 
o.ConfirmTime,                 
        
        $('#OrderId').val(d.data.rows.OrderId);          
        $('#WarrantyPeriod').val(d.data.rows.WarrantyPeriod);          
        $('#PriceDecline').val(d.data.rows.PriceDecline);          
        $('#OrderTotalAmount').val(d.data.rows.OrderTotalAmount);          
        $('#Freight').val(d.data.rows.Freight);          
        $('#MakeupPrice').val(d.data.rows.MakeupPrice);          
        $('#ContractTotalAmount').val(d.data.rows.ContractTotalAmount);          
        $('#OtherAppointment').val(d.data.rows.OtherAppointment);          
        $('#InputUserId').val(d.data.rows.InputUserId);          
        $('#InputUnitId').val(d.data.rows.InputUnitId);          
        $('#InputTime').val(d.data.rows.InputTime);          
        $('#ConfirmUserId').val(d.data.rows.ConfirmUserId);          
        $('#ConfirmUnitId').val(d.data.rows.ConfirmUnitId);          
        $('#ConfirmTime').val(d.data.rows.ConfirmTime);          

 $('#th_OrderId').html(' 订单Id');               
 $('#th_WarrantyPeriod').html(' 供货期');               
 $('#th_PriceDecline').html(' 价格下浮');               
 $('#th_OrderTotalAmount').html(' 订单总价');               
 $('#th_Freight').html(' 运输费');               
 $('#th_MakeupPrice').html(' 订单补差价');               
 $('#th_ContractTotalAmount').html(' 合同总价');               
 $('#th_OtherAppointment').html(' 其他约定');               
 $('#th_InputUserId').html(' 录入人Id(卖方确认人)');               
 $('#th_InputUnitId').html(' 录入人单位Id（卖方确认单位）');               
 $('#th_InputTime').html(' 录入时间（卖方确认时间）');               
 $('#th_ConfirmUserId').html(' 买方确认人Id');               
 $('#th_ConfirmUnitId').html(' 买方确认人单位');               
 $('#th_ConfirmTime').html(' 买方确认时间');               
 
 $('#tr_OrderId').hide();               
 $('#tr_WarrantyPeriod').hide();               
 $('#tr_PriceDecline').hide();               
 $('#tr_OrderTotalAmount').hide();               
 $('#tr_Freight').hide();               
 $('#tr_MakeupPrice').hide();               
 $('#tr_ContractTotalAmount').hide();               
 $('#tr_OtherAppointment').hide();               
 $('#tr_InputUserId').hide();               
 $('#tr_InputUnitId').hide();               
 $('#tr_InputTime').hide();               
 $('#tr_ConfirmUserId').hide();               
 $('#tr_ConfirmUnitId').hide();               
 $('#tr_ConfirmTime').hide();               

 , "OrderId" : orderId
 , "WarrantyPeriod" : warrantyPeriod
 , "PriceDecline" : priceDecline
 , "OrderTotalAmount" : orderTotalAmount
 , "Freight" : freight
 , "MakeupPrice" : makeupPrice
 , "ContractTotalAmount" : contractTotalAmount
 , "OtherAppointment" : otherAppointment
 , "InputUserId" : inputUserId
 , "InputUnitId" : inputUnitId
 , "InputTime" : inputTime
 , "ConfirmUserId" : confirmUserId
 , "ConfirmUnitId" : confirmUnitId
 , "ConfirmTime" : confirmTime

 var orderId = $('#o_OrderId').val();
 var warrantyPeriod = $('#o_WarrantyPeriod').val();
 var priceDecline = $('#o_PriceDecline').val();
 var orderTotalAmount = $('#o_OrderTotalAmount').val();
 var freight = $('#o_Freight').val();
 var makeupPrice = $('#o_MakeupPrice').val();
 var contractTotalAmount = $('#o_ContractTotalAmount').val();
 var otherAppointment = $('#o_OtherAppointment').val();
 var inputUserId = $('#o_InputUserId').val();
 var inputUnitId = $('#o_InputUnitId').val();
 var inputTime = $('#o_InputTime').val();
 var confirmUserId = $('#o_ConfirmUserId').val();
 var confirmUnitId = $('#o_ConfirmUnitId').val();
 var confirmTime = $('#o_ConfirmTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单Id' : '产品名称', d.data.rows.OrderId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '供货期' : '产品名称', d.data.rows.WarrantyPeriod);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '价格下浮' : '产品名称', d.data.rows.PriceDecline);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单总价' : '产品名称', d.data.rows.OrderTotalAmount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '运输费' : '产品名称', d.data.rows.Freight);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '订单补差价' : '产品名称', d.data.rows.MakeupPrice);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '合同总价' : '产品名称', d.data.rows.ContractTotalAmount);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '其他约定' : '产品名称', d.data.rows.OtherAppointment);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '录入人Id(卖方确认人)' : '产品名称', d.data.rows.InputUserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '录入人单位Id（卖方确认单位）' : '产品名称', d.data.rows.InputUnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '录入时间（卖方确认时间）' : '产品名称', d.data.rows.InputTime);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '买方确认人Id' : '产品名称', d.data.rows.ConfirmUserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '买方确认人单位' : '产品名称', d.data.rows.ConfirmUnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '买方确认时间' : '产品名称', d.data.rows.ConfirmTime);



