﻿ ArticleId = entity.ArticleId,
 Remark = entity.Remark,


 ArticleId = model.ArticleId,
 Remark = model.Remark,


 temp.ArticleId = model.ArticleId,
 temp.Remark = model.Remark,

 Id = item.Id,
 ArticleId = item.ArticleId,
 Remark = item.Remark,

public class ArticleContextInputModel
{
 [Display(Name = "主键")] 
    public int Id {get; set; }
    
 [Display(Name = "资讯编号")] 
    public int ArticleId {get; set; }
    
 [Display(Name = "内容")] 
    public string Remark {get; set; }
    
 }
 
 public class ArticleContextViewModel
 {
    /// <summary>
    /// 主键
    /// </summary>
    public int Id {get; set; }
    
    /// <summary>
    /// 资讯编号
    /// </summary>
    public int ArticleId {get; set; }
    
    /// <summary>
    /// 内容
    /// </summary>
    public string Remark {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ArticleId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ArticleId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资讯编号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Remark, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Remark, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入内容" } })                    
                </div>
           </div>
  




 { field: 'ArticleId', title: '资讯编号', sortable: true },
                 
 { field: 'Remark', title: '内容', sortable: true },
                 
o.ArticleId,                 
o.Remark,                 
        
        $('#ArticleId').val(d.data.rows.ArticleId);          
        $('#Remark').val(d.data.rows.Remark);          

 $('#th_ArticleId').html(' 资讯编号');               
 $('#th_Remark').html(' 内容');               
 
 $('#tr_ArticleId').hide();               
 $('#tr_Remark').hide();               

 , "ArticleId" : articleId
 , "Remark" : remark

 var articleId = $('#o_ArticleId').val();
 var remark = $('#o_Remark').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资讯编号' : '产品名称', d.data.rows.ArticleId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '内容' : '产品名称', d.data.rows.Remark);



