﻿ ProductId = entity.ProductId,
 ImgPath = entity.ImgPath,
 ImgSpec = entity.ImgSpec,
 IsMainImg = entity.IsMainImg,


 ProductId = model.ProductId,
 ImgPath = model.ImgPath,
 ImgSpec = model.ImgSpec,
 IsMainImg = model.IsMainImg,


 temp.ProductId = model.ProductId,
 temp.ImgPath = model.ImgPath,
 temp.ImgSpec = model.ImgSpec,
 temp.IsMainImg = model.IsMainImg,

 ProductImgId = item.ProductImgId,
 ProductId = item.ProductId,
 ImgPath = item.ImgPath,
 ImgSpec = item.ImgSpec,
 IsMainImg = item.IsMainImg,

public class ProductImgInputModel
{
 [Display(Name = "Id")] 
    public long ProductImgId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "图片路径")] 
    public string ImgPath {get; set; }
    
 [Display(Name = "图片规格（大：中：小）")] 
    public int ImgSpec {get; set; }
    
 [Display(Name = "是否为主图（1：是 0：否）")] 
    public int IsMainImg {get; set; }
    
 }
 
 public class ProductImgViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long ProductImgId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 图片路径
    /// </summary>
    public string ImgPath {get; set; }
    
    /// <summary>
    /// 图片规格（大：中：小）
    /// </summary>
    public int ImgSpec {get; set; }
    
    /// <summary>
    /// 是否为主图（1：是 0：否）
    /// </summary>
    public int IsMainImg {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImgPath, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImgPath, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片路径" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ImgSpec, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ImgSpec, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入图片规格（大：中：小）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsMainImg, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsMainImg, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否为主图（1：是 0：否）" } })                    
                </div>
           </div>
  




 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'ImgPath', title: '图片路径', sortable: true },
                 
 { field: 'ImgSpec', title: '图片规格（大：中：小）', sortable: true },
                 
 { field: 'IsMainImg', title: '是否为主图（1：是 0：否）', sortable: true },
                 
o.ProductId,                 
o.ImgPath,                 
o.ImgSpec,                 
o.IsMainImg,                 
        
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#ImgPath').val(d.data.rows.ImgPath);          
        $('#ImgSpec').val(d.data.rows.ImgSpec);          
        $('#IsMainImg').val(d.data.rows.IsMainImg);          

 $('#th_ProductId').html(' 产品Id');               
 $('#th_ImgPath').html(' 图片路径');               
 $('#th_ImgSpec').html(' 图片规格（大：中：小）');               
 $('#th_IsMainImg').html(' 是否为主图（1：是 0：否）');               
 
 $('#tr_ProductId').hide();               
 $('#tr_ImgPath').hide();               
 $('#tr_ImgSpec').hide();               
 $('#tr_IsMainImg').hide();               

 , "ProductId" : productId
 , "ImgPath" : imgPath
 , "ImgSpec" : imgSpec
 , "IsMainImg" : isMainImg

 var productId = $('#o_ProductId').val();
 var imgPath = $('#o_ImgPath').val();
 var imgSpec = $('#o_ImgSpec').val();
 var isMainImg = $('#o_IsMainImg').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片路径' : '产品名称', d.data.rows.ImgPath);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '图片规格（大：中：小）' : '产品名称', d.data.rows.ImgSpec);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否为主图（1：是 0：否）' : '产品名称', d.data.rows.IsMainImg);



