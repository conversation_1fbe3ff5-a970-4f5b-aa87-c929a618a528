﻿
using Dqy.Instrument.Framework.Component;

using Senparc.Weixin.Containers;
using Senparc.Weixin.WxOpen.Entities;
using Senparc.Weixin.WxOpen.Helpers;
using System;
using System.Threading.Tasks;

namespace Dqy.Instrument.Api.Containers
{
    /// <summary>
    /// 第三方APP信息包
    /// </summary>
    [Serializable]
    public class InstrumentSessionBag : BaseContainerBag
    {
        /// <summary>
        /// Session的Key（3rd_session / sessionId）
        /// </summary>
        public new string Key { get; set; }

        /// <summary>
        /// OpenId
        /// </summary>
        public string OpenId { get; set; }
        public string UnionId { get; set; }

        /// <summary>
        /// SessionKey
        /// </summary>
        public string SessionKey { get; set; }
        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTimeOffset ExpireTime { get; set; }

        //private string _key;
        //private string _openId;
        //private string _sessionKey;
        //private DateTimeOffset _expireTime;

        public DecodedUserInfo DecodedUserInfo { get; set; }

       

        /// <summary>
        /// UnitName
        /// </summary>
        public string UnitName { get; set; }
       

        /// <summary>
        /// 用户初始商城Id
        /// </summary>
        public int MallId { get; set; }

        /// <summary>
        /// 用户当前登录的商城Id
        /// </summary>
        public int CurrentMallId { get; set; }
        /// <summary>
        /// UserId
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// UnitId
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// UnitType
        /// </summary>
        public int UnitType { get; set; }

        /// <summary>
        /// UserType
        /// </summary>
        public int UserType { get; set; }

        /// <summary>
        /// LoginName
        /// </summary>
        public string LoginName { get; set; }

       
        public int IsThird { get; set; }

        /// <summary>
        /// ComponentBag
        /// </summary>
        public InstrumentSessionBag()
        {
        }
    }



    /// <summary>
    /// 3rdSession容器
    /// </summary>
    public class SessionContainer : BaseContainer<InstrumentSessionBag>
    {
        /// <summary>
        /// 获取最新的过期时间
        /// </summary>
        /// <returns></returns>
        private static TimeSpan GetExpireTime()
        {
            return TimeSpan.FromDays(5);//有效期5天
        }

        #region 同步方法

        /// <summary>
        /// 获取Session
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static InstrumentSessionBag GetSession(string key)
        {
            var bag = TryGetItem(key);
            if (bag == null)
            {
                FileLog.SendCacheLog("缓存已丢失 key：" + key);

                return null;
            }

            if (bag.ExpireTime < SystemTime.Now)
            {
                //已经过期
                Cache.RemoveFromCache(key);
                return null;
            }

            //using (FlushCache.CreateInstance())
            //{
            bag.ExpireTime = SystemTime.Now.Add(GetExpireTime());//滚动过期时间
            Update(key, bag, GetExpireTime());
            //}
            return bag;
        }

        /// <summary>
        /// 更新或插入InstrumentSessionBag
        /// </summary>
        /// <param name="key">如果留空，则新建一条记录</param>
        /// <param name="openId">OpenId</param>
        /// <param name="sessionKey">SessionKey</param>
        /// <param name="uniondId">UnionId</param>
        /// <returns></returns>
        //public static InstrumentSessionBag UpdateSession(string key, string openId, string sessionKey, string uniondId, TimeSpan? expireTime = null)
        //{
        //    key = key ?? SessionHelper.GetNewThirdSessionName();

        //    //using (FlushCache.CreateInstance())
        //    //{
        //    var InstrumentSessionBag = new InstrumentSessionBag()
        //    {
        //        Key = key,
        //        OpenId = openId,
        //        UnionId = uniondId,
        //        SessionKey = sessionKey,
        //        ExpireTime = SystemTime.Now.Add(expireTime ?? GetExpireTime())
        //    };
        //    Update(key, InstrumentSessionBag, expireTime ?? GetExpireTime());
        //    return InstrumentSessionBag;
        //    //}
        //}


        /// <summary>
        /// 更新或插入InstrumentSessionBag
        /// </summary>
        /// <param name="key">如果留空，则新建一条记录</param>
        /// <param name="session">UserViewModel</param>
        /// <returns></returns>
        public static InstrumentSessionBag UpdateSession(string key, string sessionKey, long userId, int userType, string loginName, int mallId, int currentMallId, int unitId, int unitType, int thirdLogin = 0, TimeSpan? expireTime = null)
        {
            key = key ?? SessionHelper.GetNewThirdSessionName();
            sessionKey = sessionKey ?? key;
            //using (FlushCache.CreateInstance())
            //{
            var InstrumentSessionBag = new InstrumentSessionBag()
            {
                SessionKey = sessionKey,
                Key = key,
                UserId = userId,
                UserType = userType,
                LoginName = loginName,
                MallId = mallId,
                CurrentMallId = currentMallId,
                UnitId = unitId,
                UnitType = unitType,
                IsThird = thirdLogin,
                ExpireTime = SystemTime.Now.Add(expireTime ?? GetExpireTime())
            };
            FileLog.SendCacheLog("新增更新缓存：" + ComLib.Object2JSON(InstrumentSessionBag));
            Update(key, InstrumentSessionBag, expireTime ?? GetExpireTime());
            return InstrumentSessionBag;
            //}
        }

        public static InstrumentSessionBag UpdateSession(string key, string sessionKey, string openId, long userId, int userType, string loginName, int mallId, int currentMallId, int unitId, int unitType, TimeSpan? expireTime = null)
        {
            key = key ?? SessionHelper.GetNewThirdSessionName();
            sessionKey = sessionKey ?? key;
            //using (FlushCache.CreateInstance())
            //{
            var InstrumentSessionBag = new InstrumentSessionBag()
            {
                OpenId = openId,
                SessionKey = sessionKey,
                Key = key,
                UserId = userId,
                UserType = userType,
                LoginName = loginName,
                MallId = mallId,
                CurrentMallId = currentMallId,
                UnitId = unitId,
                UnitType = unitType,
                ExpireTime = SystemTime.Now.Add(expireTime ?? GetExpireTime())
            };
            FileLog.SendCacheLog("新增更新缓存：" + ComLib.Object2JSON(InstrumentSessionBag));
            Update(key, InstrumentSessionBag, expireTime ?? GetExpireTime());
            return InstrumentSessionBag;
            //}
        }


        /// <summary>
        /// 添加解码后的用户信息
        /// </summary>
        /// <param name="bag"></param>
        /// <param name="decodedUserInfo"></param>
        public static void AddDecodedUserInfo(InstrumentSessionBag bag, DecodedUserInfo decodedUserInfo)
        {
            bag.DecodedUserInfo = decodedUserInfo;
            Update(bag.Key, bag, bag.ExpireTime - SystemTime.Now);
        }

        #endregion

        #region 异步方法

        /// <summary>
        /// 获取Session
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static async Task<InstrumentSessionBag> GetSessionAsync(string key)
        {
            var bag = await TryGetItemAsync(key).ConfigureAwait(false);
            if (bag == null)
            {
                return null;
            }

            if (bag.ExpireTime < SystemTime.Now)
            {
                //已经过期
                await Cache.RemoveFromCacheAsync(key).ConfigureAwait(false);
                return null;
            }

            //using (FlushCache.CreateInstance())
            //{
            bag.ExpireTime = SystemTime.Now.Add(GetExpireTime());//滚动过期时间
            await UpdateAsync(key, bag, GetExpireTime()).ConfigureAwait(false);
            //}
            return bag;
        }

        /// <summary>
        /// 更新或插入InstrumentSessionBag
        /// </summary>
        /// <param name="key">如果留空，则新建一条记录</param>
        /// <param name="openId">OpenId</param>
        /// <param name="sessionKey">SessionKey</param>
        /// <param name="uniondId">UnionId</param>
        /// <returns></returns>
        public static async Task<InstrumentSessionBag> UpdateSessionAsync(string key, string openId, string sessionKey, string uniondId, long userId, int userType, string loginName, int mallId, int currentMallId, int unitId, int unitType, TimeSpan? expireTime = null)
        {
            key = key ?? SessionHelper.GetNewThirdSessionName();

            //using (FlushCache.CreateInstance())
            //{
            var InstrumentSessionBag = new InstrumentSessionBag()
            {
                Key = key,
                OpenId = openId,
                UnionId = uniondId,
                SessionKey = sessionKey,
                UserId = userId,
                UserType = userType,
                LoginName = loginName,
                MallId = mallId,
                CurrentMallId = currentMallId,
                UnitId = unitId,
                UnitType = unitType,
                ExpireTime = SystemTime.Now.Add(expireTime ?? GetExpireTime())
            };
            await UpdateAsync(key, InstrumentSessionBag, expireTime ?? GetExpireTime()).ConfigureAwait(false);
            return InstrumentSessionBag;
            //}
        }

        /// <summary>
        /// 【异步方法】添加解码后的用户信息
        /// </summary>
        /// <param name="bag"></param>
        /// <param name="decodedUserInfo"></param>
        public static async Task AddDecodedUserInfoAsync(InstrumentSessionBag bag, DecodedUserInfo decodedUserInfo)
        {
            bag.DecodedUserInfo = decodedUserInfo;
            await UpdateAsync(bag.Key, bag, bag.ExpireTime - SystemTime.Now).ConfigureAwait(false);
        }


        #endregion
    }
}
