﻿ AdminId = entity.AdminId,
 RoleId = entity.RoleId,


 AdminId = model.AdminId,
 RoleId = model.RoleId,


 temp.AdminId = model.AdminId,
 temp.RoleId = model.RoleId,

 AdminRoleId = item.AdminRoleId,
 AdminId = item.AdminId,
 RoleId = item.RoleId,

public class AdminrInRoleInputModel
{
 [Display(Name = "Id")] 
    public long AdminRoleId {get; set; }
    
 [Display(Name = "管理员Id")] 
    public long AdminId {get; set; }
    
 [Display(Name = "角色Id")] 
    public int RoleId {get; set; }
    
 }
 
 public class AdminrInRoleViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long AdminRoleId {get; set; }
    
    /// <summary>
    /// 管理员Id
    /// </summary>
    public long AdminId {get; set; }
    
    /// <summary>
    /// 角色Id
    /// </summary>
    public int RoleId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.AdminId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AdminId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入管理员Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RoleId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RoleId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入角色Id" } })                    
                </div>
           </div>
  




 { field: 'AdminId', title: '管理员Id', sortable: true },
                 
 { field: 'RoleId', title: '角色Id', sortable: true },
                 
o.AdminId,                 
o.RoleId,                 
        
        $('#AdminId').val(d.data.rows.AdminId);          
        $('#RoleId').val(d.data.rows.RoleId);          

 $('#th_AdminId').html(' 管理员Id');               
 $('#th_RoleId').html(' 角色Id');               
 
 $('#tr_AdminId').hide();               
 $('#tr_RoleId').hide();               

 , "AdminId" : adminId
 , "RoleId" : roleId

 var adminId = $('#o_AdminId').val();
 var roleId = $('#o_RoleId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '管理员Id' : '产品名称', d.data.rows.AdminId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '角色Id' : '产品名称', d.data.rows.RoleId);



