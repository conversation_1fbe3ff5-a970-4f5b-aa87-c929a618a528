﻿ ProductId = entity.ProductId,
 ModifyBatch = entity.ModifyBatch,
 InstrumentLogicId = entity.InstrumentLogicId,
 ThreeGradeLogicId = entity.ThreeGradeLogicId,
 Code = entity.Code,
 Name = entity.Name,
 WarrantyMonth = entity.WarrantyMonth,
 InstrumentModelId = entity.InstrumentModelId,
 Model = entity.Model,
 Manufacturer = entity.Manufacturer,
 Brand = entity.Brand,
 UnitName = entity.UnitName,
 Detail = entity.Detail,


 ProductId = model.ProductId,
 ModifyBatch = model.ModifyBatch,
 InstrumentLogicId = model.InstrumentLogicId,
 ThreeGradeLogicId = model.ThreeGradeLogicId,
 Code = model.Code,
 Name = model.Name,
 WarrantyMonth = model.WarrantyMonth,
 InstrumentModelId = model.InstrumentModelId,
 Model = model.Model,
 Manufacturer = model.Manufacturer,
 Brand = model.Brand,
 UnitName = model.UnitName,
 Detail = model.Detail,


 temp.ProductId = model.ProductId,
 temp.ModifyBatch = model.ModifyBatch,
 temp.InstrumentLogicId = model.InstrumentLogicId,
 temp.ThreeGradeLogicId = model.ThreeGradeLogicId,
 temp.Code = model.Code,
 temp.Name = model.Name,
 temp.WarrantyMonth = model.WarrantyMonth,
 temp.InstrumentModelId = model.InstrumentModelId,
 temp.Model = model.Model,
 temp.Manufacturer = model.Manufacturer,
 temp.Brand = model.Brand,
 temp.UnitName = model.UnitName,
 temp.Detail = model.Detail,

 SnapshotId = item.SnapshotId,
 ProductId = item.ProductId,
 ModifyBatch = item.ModifyBatch,
 InstrumentLogicId = item.InstrumentLogicId,
 ThreeGradeLogicId = item.ThreeGradeLogicId,
 Code = item.Code,
 Name = item.Name,
 WarrantyMonth = item.WarrantyMonth,
 InstrumentModelId = item.InstrumentModelId,
 Model = item.Model,
 Manufacturer = item.Manufacturer,
 Brand = item.Brand,
 UnitName = item.UnitName,
 Detail = item.Detail,

public class SnapshotInputModel
{
 [Display(Name = "快照Id")] 
    public long SnapshotId {get; set; }
    
 [Display(Name = "产品Id")] 
    public long ProductId {get; set; }
    
 [Display(Name = "产品修改批次")] 
    public int ModifyBatch {get; set; }
    
 [Display(Name = "当前分类逻辑库Id")] 
    public int InstrumentLogicId {get; set; }
    
 [Display(Name = "三级分类逻辑库Id")] 
    public int ThreeGradeLogicId {get; set; }
    
 [Display(Name = "产品代码")] 
    public string Code {get; set; }
    
 [Display(Name = "产品名称")] 
    public string Name {get; set; }
    
 [Display(Name = "质保")] 
    public int WarrantyMonth {get; set; }
    
 [Display(Name = "规格型号Id")] 
    public int InstrumentModelId {get; set; }
    
 [Display(Name = "规格型号")] 
    public string Model {get; set; }
    
 [Display(Name = "制造商全称")] 
    public string Manufacturer {get; set; }
    
 [Display(Name = "品牌")] 
    public string Brand {get; set; }
    
 [Display(Name = "单位")] 
    public string UnitName {get; set; }
    
 [Display(Name = "产品详情")] 
    public string Detail {get; set; }
    
 }
 
 public class SnapshotViewModel
 {
    /// <summary>
    /// 快照Id
    /// </summary>
    public long SnapshotId {get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId {get; set; }
    
    /// <summary>
    /// 产品修改批次
    /// </summary>
    public int ModifyBatch {get; set; }
    
    /// <summary>
    /// 当前分类逻辑库Id
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
    /// <summary>
    /// 三级分类逻辑库Id
    /// </summary>
    public int ThreeGradeLogicId {get; set; }
    
    /// <summary>
    /// 产品代码
    /// </summary>
    public string Code {get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 质保
    /// </summary>
    public int WarrantyMonth {get; set; }
    
    /// <summary>
    /// 规格型号Id
    /// </summary>
    public int InstrumentModelId {get; set; }
    
    /// <summary>
    /// 规格型号
    /// </summary>
    public string Model {get; set; }
    
    /// <summary>
    /// 制造商全称
    /// </summary>
    public string Manufacturer {get; set; }
    
    /// <summary>
    /// 品牌
    /// </summary>
    public string Brand {get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName {get; set; }
    
    /// <summary>
    /// 产品详情
    /// </summary>
    public string Detail {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.ProductId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProductId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ModifyBatch, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ModifyBatch, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品修改批次" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入当前分类逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ThreeGradeLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ThreeGradeLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入三级分类逻辑库Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Code, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品代码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.WarrantyMonth, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.WarrantyMonth, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入质保" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentModelId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentModelId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Model, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Model, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入规格型号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Manufacturer, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Manufacturer, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入制造商全称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Brand, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Brand, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入品牌" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Detail, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Detail, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入产品详情" } })                    
                </div>
           </div>
  




 { field: 'ProductId', title: '产品Id', sortable: true },
                 
 { field: 'ModifyBatch', title: '产品修改批次', sortable: true },
                 
 { field: 'InstrumentLogicId', title: '当前分类逻辑库Id', sortable: true },
                 
 { field: 'ThreeGradeLogicId', title: '三级分类逻辑库Id', sortable: true },
                 
 { field: 'Code', title: '产品代码', sortable: true },
                 
 { field: 'Name', title: '产品名称', sortable: true },
                 
 { field: 'WarrantyMonth', title: '质保', sortable: true },
                 
 { field: 'InstrumentModelId', title: '规格型号Id', sortable: true },
                 
 { field: 'Model', title: '规格型号', sortable: true },
                 
 { field: 'Manufacturer', title: '制造商全称', sortable: true },
                 
 { field: 'Brand', title: '品牌', sortable: true },
                 
 { field: 'UnitName', title: '单位', sortable: true },
                 
 { field: 'Detail', title: '产品详情', sortable: true },
                 
o.ProductId,                 
o.ModifyBatch,                 
o.InstrumentLogicId,                 
o.ThreeGradeLogicId,                 
o.Code,                 
o.Name,                 
o.WarrantyMonth,                 
o.InstrumentModelId,                 
o.Model,                 
o.Manufacturer,                 
o.Brand,                 
o.UnitName,                 
o.Detail,                 
        
        $('#ProductId').val(d.data.rows.ProductId);          
        $('#ModifyBatch').val(d.data.rows.ModifyBatch);          
        $('#InstrumentLogicId').val(d.data.rows.InstrumentLogicId);          
        $('#ThreeGradeLogicId').val(d.data.rows.ThreeGradeLogicId);          
        $('#Code').val(d.data.rows.Code);          
        $('#Name').val(d.data.rows.Name);          
        $('#WarrantyMonth').val(d.data.rows.WarrantyMonth);          
        $('#InstrumentModelId').val(d.data.rows.InstrumentModelId);          
        $('#Model').val(d.data.rows.Model);          
        $('#Manufacturer').val(d.data.rows.Manufacturer);          
        $('#Brand').val(d.data.rows.Brand);          
        $('#UnitName').val(d.data.rows.UnitName);          
        $('#Detail').val(d.data.rows.Detail);          

 $('#th_ProductId').html(' 产品Id');               
 $('#th_ModifyBatch').html(' 产品修改批次');               
 $('#th_InstrumentLogicId').html(' 当前分类逻辑库Id');               
 $('#th_ThreeGradeLogicId').html(' 三级分类逻辑库Id');               
 $('#th_Code').html(' 产品代码');               
 $('#th_Name').html(' 产品名称');               
 $('#th_WarrantyMonth').html(' 质保');               
 $('#th_InstrumentModelId').html(' 规格型号Id');               
 $('#th_Model').html(' 规格型号');               
 $('#th_Manufacturer').html(' 制造商全称');               
 $('#th_Brand').html(' 品牌');               
 $('#th_UnitName').html(' 单位');               
 $('#th_Detail').html(' 产品详情');               
 
 $('#tr_ProductId').hide();               
 $('#tr_ModifyBatch').hide();               
 $('#tr_InstrumentLogicId').hide();               
 $('#tr_ThreeGradeLogicId').hide();               
 $('#tr_Code').hide();               
 $('#tr_Name').hide();               
 $('#tr_WarrantyMonth').hide();               
 $('#tr_InstrumentModelId').hide();               
 $('#tr_Model').hide();               
 $('#tr_Manufacturer').hide();               
 $('#tr_Brand').hide();               
 $('#tr_UnitName').hide();               
 $('#tr_Detail').hide();               

 , "ProductId" : productId
 , "ModifyBatch" : modifyBatch
 , "InstrumentLogicId" : instrumentLogicId
 , "ThreeGradeLogicId" : threeGradeLogicId
 , "Code" : code
 , "Name" : name
 , "WarrantyMonth" : warrantyMonth
 , "InstrumentModelId" : instrumentModelId
 , "Model" : model
 , "Manufacturer" : manufacturer
 , "Brand" : brand
 , "UnitName" : unitName
 , "Detail" : detail

 var productId = $('#o_ProductId').val();
 var modifyBatch = $('#o_ModifyBatch').val();
 var instrumentLogicId = $('#o_InstrumentLogicId').val();
 var threeGradeLogicId = $('#o_ThreeGradeLogicId').val();
 var code = $('#o_Code').val();
 var name = $('#o_Name').val();
 var warrantyMonth = $('#o_WarrantyMonth').val();
 var instrumentModelId = $('#o_InstrumentModelId').val();
 var model = $('#o_Model').val();
 var manufacturer = $('#o_Manufacturer').val();
 var brand = $('#o_Brand').val();
 var unitName = $('#o_UnitName').val();
 var detail = $('#o_Detail').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品Id' : '产品名称', d.data.rows.ProductId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品修改批次' : '产品名称', d.data.rows.ModifyBatch);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '当前分类逻辑库Id' : '产品名称', d.data.rows.InstrumentLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '三级分类逻辑库Id' : '产品名称', d.data.rows.ThreeGradeLogicId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品代码' : '产品名称', d.data.rows.Code);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '质保' : '产品名称', d.data.rows.WarrantyMonth);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号Id' : '产品名称', d.data.rows.InstrumentModelId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '规格型号' : '产品名称', d.data.rows.Model);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '制造商全称' : '产品名称', d.data.rows.Manufacturer);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '品牌' : '产品名称', d.data.rows.Brand);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位' : '产品名称', d.data.rows.UnitName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '产品详情' : '产品名称', d.data.rows.Detail);



