﻿ UserId = entity.UserId,
 NickName = entity.NickName,
 StaffNumber = entity.StaffNumber,
 IdNumber = entity.IdNumber,
 Name = entity.Name,
 Sex = entity.Sex,
 Birthday = entity.Birthday,
 Address = entity.Address,
 ZipCode = entity.ZipCode,
 Tel = entity.Tel,
 Qq = entity.Qq,
 WeChat = entity.WeChat,
 Email = entity.Email,
 Memo = entity.Memo,
 UnitId = entity.UnitId,
 CountryId = entity.CountryId,
 ProvinceId = entity.ProvinceId,
 CityId = entity.CityId,
 CountyId = entity.CountyId,
 PhotoPath = entity.PhotoPath,
 EduAreaId = entity.EduAreaId,


 UserId = model.UserId,
 NickName = model.NickName,
 StaffNumber = model.StaffNumber,
 IdNumber = model.IdNumber,
 Name = model.Name,
 Sex = model.Sex,
 Birthday = model.Birthday,
 Address = model.Address,
 ZipCode = model.ZipCode,
 Tel = model.Tel,
 Qq = model.Qq,
 WeChat = model.WeChat,
 Email = model.Email,
 Memo = model.Memo,
 UnitId = model.UnitId,
 CountryId = model.CountryId,
 ProvinceId = model.ProvinceId,
 CityId = model.CityId,
 CountyId = model.CountyId,
 PhotoPath = model.PhotoPath,
 EduAreaId = model.EduAreaId,


 temp.UserId = model.UserId,
 temp.NickName = model.NickName,
 temp.StaffNumber = model.StaffNumber,
 temp.IdNumber = model.IdNumber,
 temp.Name = model.Name,
 temp.Sex = model.Sex,
 temp.Birthday = model.Birthday,
 temp.Address = model.Address,
 temp.ZipCode = model.ZipCode,
 temp.Tel = model.Tel,
 temp.Qq = model.Qq,
 temp.WeChat = model.WeChat,
 temp.Email = model.Email,
 temp.Memo = model.Memo,
 temp.UnitId = model.UnitId,
 temp.CountryId = model.CountryId,
 temp.ProvinceId = model.ProvinceId,
 temp.CityId = model.CityId,
 temp.CountyId = model.CountyId,
 temp.PhotoPath = model.PhotoPath,
 temp.EduAreaId = model.EduAreaId,

 UserExtensionId = item.UserExtensionId,
 UserId = item.UserId,
 NickName = item.NickName,
 StaffNumber = item.StaffNumber,
 IdNumber = item.IdNumber,
 Name = item.Name,
 Sex = item.Sex,
 Birthday = item.Birthday,
 Address = item.Address,
 ZipCode = item.ZipCode,
 Tel = item.Tel,
 Qq = item.Qq,
 WeChat = item.WeChat,
 Email = item.Email,
 Memo = item.Memo,
 UnitId = item.UnitId,
 CountryId = item.CountryId,
 ProvinceId = item.ProvinceId,
 CityId = item.CityId,
 CountyId = item.CountyId,
 PhotoPath = item.PhotoPath,
 EduAreaId = item.EduAreaId,

public class UserExtensionInputModel
{
 [Display(Name = "ID")] 
    public long UserExtensionId {get; set; }
    
 [Display(Name = "用户Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "昵称")] 
    public string NickName {get; set; }
    
 [Display(Name = "工号")] 
    public string StaffNumber {get; set; }
    
 [Display(Name = "身份证号")] 
    public string IdNumber {get; set; }
    
 [Display(Name = "姓名")] 
    public string Name {get; set; }
    
 [Display(Name = "性别")] 
    public string Sex {get; set; }
    
 [Display(Name = "生日")] 
    public DateTime Birthday {get; set; }
    
 [Display(Name = "地址")] 
    public string Address {get; set; }
    
 [Display(Name = "邮编")] 
    public string ZipCode {get; set; }
    
 [Display(Name = "固定电话")] 
    public string Tel {get; set; }
    
 [Display(Name = "QQ")] 
    public string Qq {get; set; }
    
 [Display(Name = "微信号")] 
    public string WeChat {get; set; }
    
 [Display(Name = "邮箱")] 
    public string Email {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 [Display(Name = "单位Id（没有单位的为0）")] 
    public int UnitId {get; set; }
    
 [Display(Name = "国家Id")] 
    public int CountryId {get; set; }
    
 [Display(Name = "省Id")] 
    public int ProvinceId {get; set; }
    
 [Display(Name = "市Id")] 
    public int CityId {get; set; }
    
 [Display(Name = "区Id")] 
    public int CountyId {get; set; }
    
 [Display(Name = "头像")] 
    public string PhotoPath {get; set; }
    
 [Display(Name = "教育区域Id")] 
    public int EduAreaId {get; set; }
    
 }
 
 public class UserExtensionViewModel
 {
    /// <summary>
    /// ID
    /// </summary>
    public long UserExtensionId {get; set; }
    
    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName {get; set; }
    
    /// <summary>
    /// 工号
    /// </summary>
    public string StaffNumber {get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdNumber {get; set; }
    
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    public string Sex {get; set; }
    
    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday {get; set; }
    
    /// <summary>
    /// 地址
    /// </summary>
    public string Address {get; set; }
    
    /// <summary>
    /// 邮编
    /// </summary>
    public string ZipCode {get; set; }
    
    /// <summary>
    /// 固定电话
    /// </summary>
    public string Tel {get; set; }
    
    /// <summary>
    /// QQ
    /// </summary>
    public string Qq {get; set; }
    
    /// <summary>
    /// 微信号
    /// </summary>
    public string WeChat {get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 单位Id（没有单位的为0）
    /// </summary>
    public int UnitId {get; set; }
    
    /// <summary>
    /// 国家Id
    /// </summary>
    public int CountryId {get; set; }
    
    /// <summary>
    /// 省Id
    /// </summary>
    public int ProvinceId {get; set; }
    
    /// <summary>
    /// 市Id
    /// </summary>
    public int CityId {get; set; }
    
    /// <summary>
    /// 区Id
    /// </summary>
    public int CountyId {get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>
    public string PhotoPath {get; set; }
    
    /// <summary>
    /// 教育区域Id
    /// </summary>
    public int? EduAreaId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.NickName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.NickName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入昵称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.StaffNumber, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.StaffNumber, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入工号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IdNumber, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IdNumber, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入身份证号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入姓名" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Sex, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Sex, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入性别" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Birthday, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Birthday, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入生日" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Address, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Address, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ZipCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ZipCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮编" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Tel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Tel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入固定电话" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Qq, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Qq, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入QQ" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.WeChat, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.WeChat, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入微信号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Email, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮箱" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id（没有单位的为0）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountryId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountryId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入国家Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProvinceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProvinceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入省Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CityId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CityId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountyId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountyId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.PhotoPath, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.PhotoPath, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入头像" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.EduAreaId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.EduAreaId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入教育区域Id" } })                    
                </div>
           </div>
  




 { field: 'UserId', title: '用户Id', sortable: true },
                 
 { field: 'NickName', title: '昵称', sortable: true },
                 
 { field: 'StaffNumber', title: '工号', sortable: true },
                 
 { field: 'IdNumber', title: '身份证号', sortable: true },
                 
 { field: 'Name', title: '姓名', sortable: true },
                 
 { field: 'Sex', title: '性别', sortable: true },
                 
 { field: 'Birthday', title: '生日', sortable: true },
                 
 { field: 'Address', title: '地址', sortable: true },
                 
 { field: 'ZipCode', title: '邮编', sortable: true },
                 
 { field: 'Tel', title: '固定电话', sortable: true },
                 
 { field: 'Qq', title: 'QQ', sortable: true },
                 
 { field: 'WeChat', title: '微信号', sortable: true },
                 
 { field: 'Email', title: '邮箱', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
 { field: 'UnitId', title: '单位Id（没有单位的为0）', sortable: true },
                 
 { field: 'CountryId', title: '国家Id', sortable: true },
                 
 { field: 'ProvinceId', title: '省Id', sortable: true },
                 
 { field: 'CityId', title: '市Id', sortable: true },
                 
 { field: 'CountyId', title: '区Id', sortable: true },
                 
 { field: 'PhotoPath', title: '头像', sortable: true },
                 
 { field: 'EduAreaId', title: '教育区域Id', sortable: true },
                 
o.UserId,                 
o.NickName,                 
o.StaffNumber,                 
o.IdNumber,                 
o.Name,                 
o.Sex,                 
o.Birthday,                 
o.Address,                 
o.ZipCode,                 
o.Tel,                 
o.Qq,                 
o.WeChat,                 
o.Email,                 
o.Memo,                 
o.UnitId,                 
o.CountryId,                 
o.ProvinceId,                 
o.CityId,                 
o.CountyId,                 
o.PhotoPath,                 
o.EduAreaId,                 
        
        $('#UserId').val(d.data.rows.UserId);          
        $('#NickName').val(d.data.rows.NickName);          
        $('#StaffNumber').val(d.data.rows.StaffNumber);          
        $('#IdNumber').val(d.data.rows.IdNumber);          
        $('#Name').val(d.data.rows.Name);          
        $('#Sex').val(d.data.rows.Sex);          
        $('#Birthday').val(d.data.rows.Birthday);          
        $('#Address').val(d.data.rows.Address);          
        $('#ZipCode').val(d.data.rows.ZipCode);          
        $('#Tel').val(d.data.rows.Tel);          
        $('#Qq').val(d.data.rows.Qq);          
        $('#WeChat').val(d.data.rows.WeChat);          
        $('#Email').val(d.data.rows.Email);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#CountryId').val(d.data.rows.CountryId);          
        $('#ProvinceId').val(d.data.rows.ProvinceId);          
        $('#CityId').val(d.data.rows.CityId);          
        $('#CountyId').val(d.data.rows.CountyId);          
        $('#PhotoPath').val(d.data.rows.PhotoPath);          
        $('#EduAreaId').val(d.data.rows.EduAreaId);          

 $('#th_UserId').html(' 用户Id');               
 $('#th_NickName').html(' 昵称');               
 $('#th_StaffNumber').html(' 工号');               
 $('#th_IdNumber').html(' 身份证号');               
 $('#th_Name').html(' 姓名');               
 $('#th_Sex').html(' 性别');               
 $('#th_Birthday').html(' 生日');               
 $('#th_Address').html(' 地址');               
 $('#th_ZipCode').html(' 邮编');               
 $('#th_Tel').html(' 固定电话');               
 $('#th_Qq').html(' QQ');               
 $('#th_WeChat').html(' 微信号');               
 $('#th_Email').html(' 邮箱');               
 $('#th_Memo').html(' 备注');               
 $('#th_UnitId').html(' 单位Id（没有单位的为0）');               
 $('#th_CountryId').html(' 国家Id');               
 $('#th_ProvinceId').html(' 省Id');               
 $('#th_CityId').html(' 市Id');               
 $('#th_CountyId').html(' 区Id');               
 $('#th_PhotoPath').html(' 头像');               
 $('#th_EduAreaId').html(' 教育区域Id');               
 
 $('#tr_UserId').hide();               
 $('#tr_NickName').hide();               
 $('#tr_StaffNumber').hide();               
 $('#tr_IdNumber').hide();               
 $('#tr_Name').hide();               
 $('#tr_Sex').hide();               
 $('#tr_Birthday').hide();               
 $('#tr_Address').hide();               
 $('#tr_ZipCode').hide();               
 $('#tr_Tel').hide();               
 $('#tr_Qq').hide();               
 $('#tr_WeChat').hide();               
 $('#tr_Email').hide();               
 $('#tr_Memo').hide();               
 $('#tr_UnitId').hide();               
 $('#tr_CountryId').hide();               
 $('#tr_ProvinceId').hide();               
 $('#tr_CityId').hide();               
 $('#tr_CountyId').hide();               
 $('#tr_PhotoPath').hide();               
 $('#tr_EduAreaId').hide();               

 , "UserId" : userId
 , "NickName" : nickName
 , "StaffNumber" : staffNumber
 , "IdNumber" : idNumber
 , "Name" : name
 , "Sex" : sex
 , "Birthday" : birthday
 , "Address" : address
 , "ZipCode" : zipCode
 , "Tel" : tel
 , "Qq" : qq
 , "WeChat" : weChat
 , "Email" : email
 , "Memo" : memo
 , "UnitId" : unitId
 , "CountryId" : countryId
 , "ProvinceId" : provinceId
 , "CityId" : cityId
 , "CountyId" : countyId
 , "PhotoPath" : photoPath
 , "EduAreaId" : eduAreaId

 var userId = $('#o_UserId').val();
 var nickName = $('#o_NickName').val();
 var staffNumber = $('#o_StaffNumber').val();
 var idNumber = $('#o_IdNumber').val();
 var name = $('#o_Name').val();
 var sex = $('#o_Sex').val();
 var birthday = $('#o_Birthday').val();
 var address = $('#o_Address').val();
 var zipCode = $('#o_ZipCode').val();
 var tel = $('#o_Tel').val();
 var qq = $('#o_Qq').val();
 var weChat = $('#o_WeChat').val();
 var email = $('#o_Email').val();
 var memo = $('#o_Memo').val();
 var unitId = $('#o_UnitId').val();
 var countryId = $('#o_CountryId').val();
 var provinceId = $('#o_ProvinceId').val();
 var cityId = $('#o_CityId').val();
 var countyId = $('#o_CountyId').val();
 var photoPath = $('#o_PhotoPath').val();
 var eduAreaId = $('#o_EduAreaId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '昵称' : '产品名称', d.data.rows.NickName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '工号' : '产品名称', d.data.rows.StaffNumber);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '身份证号' : '产品名称', d.data.rows.IdNumber);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '姓名' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '性别' : '产品名称', d.data.rows.Sex);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '生日' : '产品名称', d.data.rows.Birthday);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '地址' : '产品名称', d.data.rows.Address);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮编' : '产品名称', d.data.rows.ZipCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '固定电话' : '产品名称', d.data.rows.Tel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'QQ' : '产品名称', d.data.rows.Qq);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '微信号' : '产品名称', d.data.rows.WeChat);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮箱' : '产品名称', d.data.rows.Email);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id（没有单位的为0）' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '国家Id' : '产品名称', d.data.rows.CountryId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '省Id' : '产品名称', d.data.rows.ProvinceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市Id' : '产品名称', d.data.rows.CityId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区Id' : '产品名称', d.data.rows.CountyId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '头像' : '产品名称', d.data.rows.PhotoPath);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '教育区域Id' : '产品名称', d.data.rows.EduAreaId);



