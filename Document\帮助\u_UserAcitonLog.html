﻿ UserId = entity.UserId,
 AccountName = entity.AccountName,
 SessionKey = entity.SessionKey,
 Pwd = entity.Pwd,
 UserIp = entity.UserIp,
 Type = entity.Type,
 DataFormat = entity.DataFormat,
 RegTime = entity.RegTime,


 UserId = model.UserId,
 AccountName = model.AccountName,
 SessionKey = model.SessionKey,
 Pwd = model.Pwd,
 UserIp = model.UserIp,
 Type = model.Type,
 DataFormat = model.DataFormat,
 RegTime = model.RegTime,


 temp.UserId = model.UserId,
 temp.AccountName = model.AccountName,
 temp.SessionKey = model.SessionKey,
 temp.Pwd = model.Pwd,
 temp.UserIp = model.UserIp,
 temp.Type = model.Type,
 temp.DataFormat = model.DataFormat,
 temp.RegTime = model.RegTime,

 UserAcitonLogId = item.UserAcitonLogId,
 UserId = item.UserId,
 AccountName = item.AccountName,
 SessionKey = item.SessionKey,
 Pwd = item.Pwd,
 UserIp = item.UserIp,
 Type = item.Type,
 DataFormat = item.DataFormat,
 RegTime = item.RegTime,

public class UserAcitonLogInputModel
{
 [Display(Name = "编号")] 
    public long UserAcitonLogId {get; set; }
    
 [Display(Name = "用户Id")] 
    public long UserId {get; set; }
    
 [Display(Name = "用户登录账号")] 
    public string AccountName {get; set; }
    
 [Display(Name = "缓存Key值")] 
    public string SessionKey {get; set; }
    
 [Display(Name = "加密密码")] 
    public string Pwd {get; set; }
    
 [Display(Name = "用户IP")] 
    public string UserIp {get; set; }
    
 [Display(Name = "日志类型（1：登录）")] 
    public int Type {get; set; }
    
 [Display(Name = "日期年月日")] 
    public string DataFormat {get; set; }
    
 [Display(Name = "创建时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class UserAcitonLogViewModel
 {
    /// <summary>
    /// 编号
    /// </summary>
    public long UserAcitonLogId {get; set; }
    
    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId {get; set; }
    
    /// <summary>
    /// 用户登录账号
    /// </summary>
    public string AccountName {get; set; }
    
    /// <summary>
    /// 缓存Key值
    /// </summary>
    public string SessionKey {get; set; }
    
    /// <summary>
    /// 加密密码
    /// </summary>
    public string Pwd {get; set; }
    
    /// <summary>
    /// 用户IP
    /// </summary>
    public string UserIp {get; set; }
    
    /// <summary>
    /// 日志类型（1：登录）
    /// </summary>
    public int Type {get; set; }
    
    /// <summary>
    /// 日期年月日
    /// </summary>
    public string DataFormat {get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UserId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.AccountName, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.AccountName, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户登录账号" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.SessionKey, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.SessionKey, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入缓存Key值" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Pwd, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Pwd, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入加密密码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.UserIp, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UserIp, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入用户IP" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Type, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Type, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入日志类型（1：登录）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DataFormat, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DataFormat, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入日期年月日" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入创建时间" } })                    
                </div>
           </div>
  




 { field: 'UserId', title: '用户Id', sortable: true },
                 
 { field: 'AccountName', title: '用户登录账号', sortable: true },
                 
 { field: 'SessionKey', title: '缓存Key值', sortable: true },
                 
 { field: 'Pwd', title: '加密密码', sortable: true },
                 
 { field: 'UserIp', title: '用户IP', sortable: true },
                 
 { field: 'Type', title: '日志类型（1：登录）', sortable: true },
                 
 { field: 'DataFormat', title: '日期年月日', sortable: true },
                 
 { field: 'RegTime', title: '创建时间', sortable: true },
                 
o.UserId,                 
o.AccountName,                 
o.SessionKey,                 
o.Pwd,                 
o.UserIp,                 
o.Type,                 
o.DataFormat,                 
o.RegTime,                 
        
        $('#UserId').val(d.data.rows.UserId);          
        $('#AccountName').val(d.data.rows.AccountName);          
        $('#SessionKey').val(d.data.rows.SessionKey);          
        $('#Pwd').val(d.data.rows.Pwd);          
        $('#UserIp').val(d.data.rows.UserIp);          
        $('#Type').val(d.data.rows.Type);          
        $('#DataFormat').val(d.data.rows.DataFormat);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_UserId').html(' 用户Id');               
 $('#th_AccountName').html(' 用户登录账号');               
 $('#th_SessionKey').html(' 缓存Key值');               
 $('#th_Pwd').html(' 加密密码');               
 $('#th_UserIp').html(' 用户IP');               
 $('#th_Type').html(' 日志类型（1：登录）');               
 $('#th_DataFormat').html(' 日期年月日');               
 $('#th_RegTime').html(' 创建时间');               
 
 $('#tr_UserId').hide();               
 $('#tr_AccountName').hide();               
 $('#tr_SessionKey').hide();               
 $('#tr_Pwd').hide();               
 $('#tr_UserIp').hide();               
 $('#tr_Type').hide();               
 $('#tr_DataFormat').hide();               
 $('#tr_RegTime').hide();               

 , "UserId" : userId
 , "AccountName" : accountName
 , "SessionKey" : sessionKey
 , "Pwd" : pwd
 , "UserIp" : userIp
 , "Type" : type
 , "DataFormat" : dataFormat
 , "RegTime" : regTime

 var userId = $('#o_UserId').val();
 var accountName = $('#o_AccountName').val();
 var sessionKey = $('#o_SessionKey').val();
 var pwd = $('#o_Pwd').val();
 var userIp = $('#o_UserIp').val();
 var type = $('#o_Type').val();
 var dataFormat = $('#o_DataFormat').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户Id' : '产品名称', d.data.rows.UserId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户登录账号' : '产品名称', d.data.rows.AccountName);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '缓存Key值' : '产品名称', d.data.rows.SessionKey);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '加密密码' : '产品名称', d.data.rows.Pwd);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '用户IP' : '产品名称', d.data.rows.UserIp);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '日志类型（1：登录）' : '产品名称', d.data.rows.Type);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '日期年月日' : '产品名称', d.data.rows.DataFormat);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '创建时间' : '产品名称', d.data.rows.RegTime);



