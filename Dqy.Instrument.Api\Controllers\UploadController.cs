﻿
using Dqy.Instrument.Api.Common;
using Dqy.Instrument.Api.Containers;
using Dqy.Instrument.Api.Models;
using Dqy.Instrument.ApplicationServices;
using Dqy.Instrument.Framework.Component;
using Dqy.Instrument.Services.ImplementedInterfaces;
using Dqy.Instrument.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Results;

namespace Dqy.Instrument.Api.Controllers
{
    [RoutePrefix("api/upload")]
    public class UploadController : ApiController
    {
        private static readonly string ApiPath = ComLib.GetAppSetting<string>("Api.Path");
        private readonly IBDictionaryApplicationService _dictionaryApplicationService;

        public UploadController(IBDictionaryApplicationService iBDictionaryApplicationService)
        {
            _dictionaryApplicationService = iBDictionaryApplicationService;
        }

        public async Task<JsonResult<ReturnResult>> Post()
        {
            ReturnResult r = new ReturnResult();
            FileLog.SendCustomLog("开始上传文件！");
            // Check if the request contains multipart/form-data.
            if (!Request.Content.IsMimeMultipartContent())
            {
                FileLog.SendCustomLog("上传文件异常！");
                r.msg = "上传文件异常！";
                return Json(r);
            }

            string root = HttpContext.Current.Server.MapPath("~/UploadFile");
            var provider = new MultipartFormDataStreamProvider(root);
            DqyFileInfo ufi = new DqyFileInfo();
            try
            {
                var token = provider.FormData["token"];//判断token是否存在，从而验证用户是否登录，并判断用户是否可以上传文件
                var currentDomain = GetRequestDomain();
                var allowDomain = ComLib.GetAppSetting<string>("Manage.Server.Domain");
                FileLog.SendCustomLog("currentDomain:" + currentDomain);
                if (currentDomain.IndexOf(allowDomain) == -1)
                {
                    r.flag = -1;
                    r.msg = "无上传权限。";
                    return Json(r);
                }

                if (!string.IsNullOrEmpty(token))
                {
                    var sessionBag = SessionContainer.GetSession(token);
                    if (sessionBag == null)
                    {
                        r.flag = -1;
                        r.msg = "登录超时，请重新登录。";
                        return Json(r);
                    }
                    else
                    {
                        FileLog.SendCustomLog("sessionBag:" + ComLib.Object2JSON(sessionBag));
                    }
                }

                await Request.Content.ReadAsMultipartAsync(provider);

                var savePath = "UploadFile";
                string subdir = provider.FormData["subdir"];

                /*读取字典表配置校验上传目录以及文件类型是否合规*/
                var list = _dictionaryApplicationService.GetDictionaryList("4001");
                if (subdir != null)
                {
                    if (list.Count > 0)
                    {
                        //校验上传目录地址
                        DictionaryViewModel dic = list.FirstOrDefault(t => t.DicName == subdir && t.TypeCode == "4001");
                        if (dic == null)
                        {
                            r.flag = 0;
                            r.msg = "系统检测到您正恶意上传文件，已经阻止！";
                            return Json(r);
                        }
                        if (dic.DicValue != "")
                        {
                            string orfilename = StringFilter.FilterBadChar(provider.FileData[0].Headers.ContentDisposition.FileName).TrimStart('"').TrimEnd('"');
                            string fileExt = orfilename.Substring(orfilename.LastIndexOf('.')); //文件类型是否符合该目录地址允许上传的类型
                            if (!dic.DicValue.Contains(fileExt.ToLower()))
                            {
                                r.flag = 0;
                                r.msg = "非法文件，上传失败！";
                                return Json(r);
                            }
                        }
                    }
                    savePath = savePath + "/" + StringFilter.FilterBadChar(subdir);
                }
                if (subdir != null && (subdir.StartsWith(".") || subdir.StartsWith("/")))
                {
                    r.flag = 0;
                    r.msg = "系统检测到您正恶意上传文件，已经阻止！";
                    return Json(r);
                }
                if (provider.FileData.Count > 0)
                {
                    ufi = FileUpload.Save(provider.FileData[0], savePath);
                }
                //foreach (MultipartFileData file in provider.FileData)
                //{
                //    string orfilename = file.Headers.ContentDisposition.FileName.TrimStart('"').TrimEnd('"');
                //    FileInfo fileinfo = new FileInfo(file.LocalFileName);
                //    string fileExt = orfilename.Substring(orfilename.LastIndexOf('.'));
                //    fileinfo.CopyTo(Path.Combine(root, fileinfo.Name + fileExt), true);
                //    File.Delete(file.LocalFileName);
                //    return fileinfo.Name + fileExt;
                //}
            }
            catch (Exception e)
            {
                FileLog.SendExceptionLog(e.InnerException.Message);
                r.flag = 0;
                r.msg = string.Format("文件上传异常，【Message:{0}】", e.InnerException.Message);
                return Json(r);
            }
            if (ufi.error == "-1")
            {
                r.flag = 0;
                r.msg = ufi.msg;
            }
            else
            {
                r.flag = 1;
                r.msg = "上传成功。";
                r.data.rows = (ApiPath + "/" + ufi.filePath);
            }
            return Json(r);
        }


        /// <summary>
        /// 获取请求域名
        /// </summary>
        private string GetRequestDomain()
        {
            string lastexceptionUrl = "";
            if (HttpContext.Current.Request.UrlReferrer != null && HttpContext.Current.Request.UrlReferrer.AbsoluteUri != null)
            {
                lastexceptionUrl = HttpContext.Current.Request.UrlReferrer.AbsoluteUri;
            }
            return lastexceptionUrl;
        }

    }
}
