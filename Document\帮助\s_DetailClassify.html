﻿ NavClassifyId = entity.NavClassifyId,
 InstrumentLogicId = entity.InstrumentLogicId,


 NavClassifyId = model.NavClassifyId,
 InstrumentLogicId = model.InstrumentLogicId,


 temp.NavClassifyId = model.NavClassifyId,
 temp.InstrumentLogicId = model.InstrumentLogicId,

 DetailClassifyId = item.DetailClassifyId,
 NavClassifyId = item.NavClassifyId,
 InstrumentLogicId = item.InstrumentLogicId,

public class DetailClassifyInputModel
{
 [Display(Name = "id")] 
    public int DetailClassifyId {get; set; }
    
 [Display(Name = "导航分类id")] 
    public int NavClassifyId {get; set; }
    
 [Display(Name = "InstrumentLogicId")] 
    public int InstrumentLogicId {get; set; }
    
 }
 
 public class DetailClassifyViewModel
 {
    /// <summary>
    /// id
    /// </summary>
    public int DetailClassifyId {get; set; }
    
    /// <summary>
    /// 导航分类id
    /// </summary>
    public int NavClassifyId {get; set; }
    
    /// <summary>
    /// InstrumentLogicId
    /// </summary>
    public int InstrumentLogicId {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.NavClassifyId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.NavClassifyId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入导航分类id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.InstrumentLogicId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.InstrumentLogicId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入InstrumentLogicId" } })                    
                </div>
           </div>
  




 { field: 'NavClassifyId', title: '导航分类id', sortable: true },
                 
 { field: 'InstrumentLogicId', title: 'InstrumentLogicId', sortable: true },
                 
o.NavClassifyId,                 
o.InstrumentLogicId,                 
        
        $('#NavClassifyId').val(d.data.rows.NavClassifyId);          
        $('#InstrumentLogicId').val(d.data.rows.InstrumentLogicId);          

 $('#th_NavClassifyId').html(' 导航分类id');               
 $('#th_InstrumentLogicId').html(' InstrumentLogicId');               
 
 $('#tr_NavClassifyId').hide();               
 $('#tr_InstrumentLogicId').hide();               

 , "NavClassifyId" : navClassifyId
 , "InstrumentLogicId" : instrumentLogicId

 var navClassifyId = $('#o_NavClassifyId').val();
 var instrumentLogicId = $('#o_InstrumentLogicId').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '导航分类id' : '产品名称', d.data.rows.NavClassifyId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'InstrumentLogicId' : '产品名称', d.data.rows.InstrumentLogicId);



