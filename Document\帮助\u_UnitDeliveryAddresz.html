﻿ UnitId = entity.UnitId,
 ProvinceId = entity.ProvinceId,
 CityId = entity.CityId,
 CountyId = entity.CountyId,
 DeliveryAddress = entity.DeliveryAddress,
 Consignee = entity.Consignee,
 Tel = entity.Tel,
 ZipCode = entity.ZipCode,
 Mobile = entity.Mobile,
 IsDefault = entity.IsDefault,


 UnitId = model.UnitId,
 ProvinceId = model.ProvinceId,
 CityId = model.CityId,
 CountyId = model.CountyId,
 DeliveryAddress = model.DeliveryAddress,
 Consignee = model.Consignee,
 Tel = model.Tel,
 ZipCode = model.ZipCode,
 Mobile = model.Mobile,
 IsDefault = model.IsDefault,


 temp.UnitId = model.UnitId,
 temp.ProvinceId = model.ProvinceId,
 temp.CityId = model.CityId,
 temp.CountyId = model.CountyId,
 temp.DeliveryAddress = model.DeliveryAddress,
 temp.Consignee = model.Consignee,
 temp.Tel = model.Tel,
 temp.ZipCode = model.ZipCode,
 temp.Mobile = model.Mobile,
 temp.IsDefault = model.IsDefault,

 Id = item.Id,
 UnitId = item.UnitId,
 ProvinceId = item.ProvinceId,
 CityId = item.CityId,
 CountyId = item.CountyId,
 DeliveryAddress = item.DeliveryAddress,
 Consignee = item.Consignee,
 Tel = item.Tel,
 ZipCode = item.ZipCode,
 Mobile = item.Mobile,
 IsDefault = item.IsDefault,

public class UnitDeliveryAddreszInputModel
{
 [Display(Name = "Id")] 
    public long Id {get; set; }
    
 [Display(Name = "单位Id")] 
    public int UnitId {get; set; }
    
 [Display(Name = "省Id")] 
    public int ProvinceId {get; set; }
    
 [Display(Name = "市Id")] 
    public int CityId {get; set; }
    
 [Display(Name = "区Id")] 
    public int CountyId {get; set; }
    
 [Display(Name = "地址详细信息")] 
    public string DeliveryAddress {get; set; }
    
 [Display(Name = "收货人")] 
    public string Consignee {get; set; }
    
 [Display(Name = "收货人电话")] 
    public string Tel {get; set; }
    
 [Display(Name = "邮编")] 
    public string ZipCode {get; set; }
    
 [Display(Name = "手机号码")] 
    public string Mobile {get; set; }
    
 [Display(Name = "是否为默认(0：否  1：是)只能存在一个默认地址")] 
    public bool IsDefault {get; set; }
    
 }
 
 public class UnitDeliveryAddreszViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long Id {get; set; }
    
    /// <summary>
    /// 单位Id
    /// </summary>
    public int? UnitId {get; set; }
    
    /// <summary>
    /// 省Id
    /// </summary>
    public int ProvinceId {get; set; }
    
    /// <summary>
    /// 市Id
    /// </summary>
    public int CityId {get; set; }
    
    /// <summary>
    /// 区Id
    /// </summary>
    public int CountyId {get; set; }
    
    /// <summary>
    /// 地址详细信息
    /// </summary>
    public string DeliveryAddress {get; set; }
    
    /// <summary>
    /// 收货人
    /// </summary>
    public string Consignee {get; set; }
    
    /// <summary>
    /// 收货人电话
    /// </summary>
    public string Tel {get; set; }
    
    /// <summary>
    /// 邮编
    /// </summary>
    public string ZipCode {get; set; }
    
    /// <summary>
    /// 手机号码
    /// </summary>
    public string Mobile {get; set; }
    
    /// <summary>
    /// 是否为默认(0：否  1：是)只能存在一个默认地址
    /// </summary>
    public bool IsDefault {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.UnitId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.UnitId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入单位Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ProvinceId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ProvinceId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入省Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CityId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CityId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入市Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.CountyId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.CountyId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入区Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.DeliveryAddress, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.DeliveryAddress, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入地址详细信息" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Consignee, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Consignee, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入收货人" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Tel, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Tel, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入收货人电话" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ZipCode, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ZipCode, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入邮编" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Mobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Mobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsDefault, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsDefault, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否为默认(0：否  1：是)只能存在一个默认地址" } })                    
                </div>
           </div>
  




 { field: 'UnitId', title: '单位Id', sortable: true },
                 
 { field: 'ProvinceId', title: '省Id', sortable: true },
                 
 { field: 'CityId', title: '市Id', sortable: true },
                 
 { field: 'CountyId', title: '区Id', sortable: true },
                 
 { field: 'DeliveryAddress', title: '地址详细信息', sortable: true },
                 
 { field: 'Consignee', title: '收货人', sortable: true },
                 
 { field: 'Tel', title: '收货人电话', sortable: true },
                 
 { field: 'ZipCode', title: '邮编', sortable: true },
                 
 { field: 'Mobile', title: '手机号码', sortable: true },
                 
 { field: 'IsDefault', title: '是否为默认(0：否  1：是)只能存在一个默认地址', sortable: true },
                 
o.UnitId,                 
o.ProvinceId,                 
o.CityId,                 
o.CountyId,                 
o.DeliveryAddress,                 
o.Consignee,                 
o.Tel,                 
o.ZipCode,                 
o.Mobile,                 
o.IsDefault,                 
        
        $('#UnitId').val(d.data.rows.UnitId);          
        $('#ProvinceId').val(d.data.rows.ProvinceId);          
        $('#CityId').val(d.data.rows.CityId);          
        $('#CountyId').val(d.data.rows.CountyId);          
        $('#DeliveryAddress').val(d.data.rows.DeliveryAddress);          
        $('#Consignee').val(d.data.rows.Consignee);          
        $('#Tel').val(d.data.rows.Tel);          
        $('#ZipCode').val(d.data.rows.ZipCode);          
        $('#Mobile').val(d.data.rows.Mobile);          
        $('#IsDefault').val(d.data.rows.IsDefault);          

 $('#th_UnitId').html(' 单位Id');               
 $('#th_ProvinceId').html(' 省Id');               
 $('#th_CityId').html(' 市Id');               
 $('#th_CountyId').html(' 区Id');               
 $('#th_DeliveryAddress').html(' 地址详细信息');               
 $('#th_Consignee').html(' 收货人');               
 $('#th_Tel').html(' 收货人电话');               
 $('#th_ZipCode').html(' 邮编');               
 $('#th_Mobile').html(' 手机号码');               
 $('#th_IsDefault').html(' 是否为默认(0：否  1：是)只能存在一个默认地址');               
 
 $('#tr_UnitId').hide();               
 $('#tr_ProvinceId').hide();               
 $('#tr_CityId').hide();               
 $('#tr_CountyId').hide();               
 $('#tr_DeliveryAddress').hide();               
 $('#tr_Consignee').hide();               
 $('#tr_Tel').hide();               
 $('#tr_ZipCode').hide();               
 $('#tr_Mobile').hide();               
 $('#tr_IsDefault').hide();               

 , "UnitId" : unitId
 , "ProvinceId" : provinceId
 , "CityId" : cityId
 , "CountyId" : countyId
 , "DeliveryAddress" : deliveryAddress
 , "Consignee" : consignee
 , "Tel" : tel
 , "ZipCode" : zipCode
 , "Mobile" : mobile
 , "IsDefault" : isDefault

 var unitId = $('#o_UnitId').val();
 var provinceId = $('#o_ProvinceId').val();
 var cityId = $('#o_CityId').val();
 var countyId = $('#o_CountyId').val();
 var deliveryAddress = $('#o_DeliveryAddress').val();
 var consignee = $('#o_Consignee').val();
 var tel = $('#o_Tel').val();
 var zipCode = $('#o_ZipCode').val();
 var mobile = $('#o_Mobile').val();
 var isDefault = $('#o_IsDefault').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '单位Id' : '产品名称', d.data.rows.UnitId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '省Id' : '产品名称', d.data.rows.ProvinceId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '市Id' : '产品名称', d.data.rows.CityId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '区Id' : '产品名称', d.data.rows.CountyId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '地址详细信息' : '产品名称', d.data.rows.DeliveryAddress);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '收货人' : '产品名称', d.data.rows.Consignee);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '收货人电话' : '产品名称', d.data.rows.Tel);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '邮编' : '产品名称', d.data.rows.ZipCode);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机号码' : '产品名称', d.data.rows.Mobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否为默认(0：否  1：是)只能存在一个默认地址' : '产品名称', d.data.rows.IsDefault);



