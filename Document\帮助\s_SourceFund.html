﻿ MallId = entity.MallId,
 Name = entity.Name,
 FundType = entity.FundType,
 Memo = entity.Memo,
 IsDefault = entity.IsDefault,
 Statuz = entity.Statuz,


 MallId = model.MallId,
 Name = model.Name,
 FundType = model.FundType,
 Memo = model.Memo,
 IsDefault = model.IsDefault,
 Statuz = model.Statuz,


 temp.MallId = model.MallId,
 temp.Name = model.Name,
 temp.FundType = model.FundType,
 temp.Memo = model.Memo,
 temp.IsDefault = model.IsDefault,
 temp.Statuz = model.Statuz,

 SourceFundId = item.SourceFundId,
 MallId = item.MallId,
 Name = item.Name,
 FundType = item.FundType,
 Memo = item.Memo,
 IsDefault = item.IsDefault,
 Statuz = item.Statuz,

public class SourceFundInputModel
{
 [Display(Name = "Id")] 
    public int SourceFundId {get; set; }
    
 [Display(Name = "商城Id")] 
    public int MallId {get; set; }
    
 [Display(Name = "资金来源名称")] 
    public string Name {get; set; }
    
 [Display(Name = "资金类型(1：市级；2：区级；3：校级；4：其他)")] 
    public int FundType {get; set; }
    
 [Display(Name = "备注")] 
    public string Memo {get; set; }
    
 [Display(Name = "是否默认")] 
    public int IsDefault {get; set; }
    
 [Display(Name = "状态（1：启用：2停用）")] 
    public int Statuz {get; set; }
    
 }
 
 public class SourceFundViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public int SourceFundId {get; set; }
    
    /// <summary>
    /// 商城Id
    /// </summary>
    public int MallId {get; set; }
    
    /// <summary>
    /// 资金来源名称
    /// </summary>
    public string Name {get; set; }
    
    /// <summary>
    /// 资金类型(1：市级；2：区级；3：校级；4：其他)
    /// </summary>
    public int FundType {get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Memo {get; set; }
    
    /// <summary>
    /// 是否默认
    /// </summary>
    public int IsDefault {get; set; }
    
    /// <summary>
    /// 状态（1：启用：2停用）
    /// </summary>
    public int Statuz {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.MallId, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.MallId, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入商城Id" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Name, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资金来源名称" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.FundType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.FundType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入资金类型(1：市级；2：区级；3：校级；4：其他)" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Memo, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Memo, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入备注" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IsDefault, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IsDefault, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入是否默认" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.Statuz, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Statuz, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入状态（1：启用：2停用）" } })                    
                </div>
           </div>
  




 { field: 'MallId', title: '商城Id', sortable: true },
                 
 { field: 'Name', title: '资金来源名称', sortable: true },
                 
 { field: 'FundType', title: '资金类型(1：市级；2：区级；3：校级；4：其他)', sortable: true },
                 
 { field: 'Memo', title: '备注', sortable: true },
                 
 { field: 'IsDefault', title: '是否默认', sortable: true },
                 
 { field: 'Statuz', title: '状态（1：启用：2停用）', sortable: true },
                 
o.MallId,                 
o.Name,                 
o.FundType,                 
o.Memo,                 
o.IsDefault,                 
o.Statuz,                 
        
        $('#MallId').val(d.data.rows.MallId);          
        $('#Name').val(d.data.rows.Name);          
        $('#FundType').val(d.data.rows.FundType);          
        $('#Memo').val(d.data.rows.Memo);          
        $('#IsDefault').val(d.data.rows.IsDefault);          
        $('#Statuz').val(d.data.rows.Statuz);          

 $('#th_MallId').html(' 商城Id');               
 $('#th_Name').html(' 资金来源名称');               
 $('#th_FundType').html(' 资金类型(1：市级；2：区级；3：校级；4：其他)');               
 $('#th_Memo').html(' 备注');               
 $('#th_IsDefault').html(' 是否默认');               
 $('#th_Statuz').html(' 状态（1：启用：2停用）');               
 
 $('#tr_MallId').hide();               
 $('#tr_Name').hide();               
 $('#tr_FundType').hide();               
 $('#tr_Memo').hide();               
 $('#tr_IsDefault').hide();               
 $('#tr_Statuz').hide();               

 , "MallId" : mallId
 , "Name" : name
 , "FundType" : fundType
 , "Memo" : memo
 , "IsDefault" : isDefault
 , "Statuz" : statuz

 var mallId = $('#o_MallId').val();
 var name = $('#o_Name').val();
 var fundType = $('#o_FundType').val();
 var memo = $('#o_Memo').val();
 var isDefault = $('#o_IsDefault').val();
 var statuz = $('#o_Statuz').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '商城Id' : '产品名称', d.data.rows.MallId);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资金来源名称' : '产品名称', d.data.rows.Name);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '资金类型(1：市级；2：区级；3：校级；4：其他)' : '产品名称', d.data.rows.FundType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '备注' : '产品名称', d.data.rows.Memo);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '是否默认' : '产品名称', d.data.rows.IsDefault);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '状态（1：启用：2停用）' : '产品名称', d.data.rows.Statuz);



