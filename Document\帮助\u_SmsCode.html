﻿ Mobile = entity.Mobile,
 IpAdress = entity.IpAdress,
 ValidateType = entity.ValidateType,
 RegTime = entity.RegTime,


 Mobile = model.Mobile,
 IpAdress = model.IpAdress,
 ValidateType = model.ValidateType,
 RegTime = model.RegTime,


 temp.Mobile = model.Mobile,
 temp.IpAdress = model.IpAdress,
 temp.ValidateType = model.ValidateType,
 temp.RegTime = model.RegTime,

 SmsCodeId = item.SmsCodeId,
 Mobile = item.Mobile,
 IpAdress = item.IpAdress,
 ValidateType = item.ValidateType,
 RegTime = item.RegTime,

public class SmsCodeInputModel
{
 [Display(Name = "Id")] 
    public long SmsCodeId {get; set; }
    
 [Display(Name = "手机号码")] 
    public string Mobile {get; set; }
    
 [Display(Name = "IP地址")] 
    public string IpAdress {get; set; }
    
 [Display(Name = "类型（1：注册；2：找回密码）")] 
    public int ValidateType {get; set; }
    
 [Display(Name = "添加时间")] 
    public DateTime RegTime {get; set; }
    
 }
 
 public class SmsCodeViewModel
 {
    /// <summary>
    /// Id
    /// </summary>
    public long SmsCodeId {get; set; }
    
    /// <summary>
    /// 手机号码
    /// </summary>
    public string Mobile {get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAdress {get; set; }
    
    /// <summary>
    /// 类型（1：注册；2：找回密码）
    /// </summary>
    public int ValidateType {get; set; }
    
    /// <summary>
    /// 添加时间
    /// </summary>
    public DateTime RegTime {get; set; }
    
  }
  
        <div class="form-group">
                 @Html.LabelFor(model => model.Mobile, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.Mobile, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入手机号码" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.IpAdress, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.IpAdress, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入IP地址" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.ValidateType, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.ValidateType, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入类型（1：注册；2：找回密码）" } })                    
                </div>
           </div>
        <div class="form-group">
                 @Html.LabelFor(model => model.RegTime, htmlAttributes: new { @class = "col-md-3 control-label" })               
                <div class="col-md-6">
                    @Html.EditorFor(m => m.RegTime, new { htmlAttributes = new { @class = "form-control", placeholder = "请输入添加时间" } })                    
                </div>
           </div>
  




 { field: 'Mobile', title: '手机号码', sortable: true },
                 
 { field: 'IpAdress', title: 'IP地址', sortable: true },
                 
 { field: 'ValidateType', title: '类型（1：注册；2：找回密码）', sortable: true },
                 
 { field: 'RegTime', title: '添加时间', sortable: true },
                 
o.Mobile,                 
o.IpAdress,                 
o.ValidateType,                 
o.RegTime,                 
        
        $('#Mobile').val(d.data.rows.Mobile);          
        $('#IpAdress').val(d.data.rows.IpAdress);          
        $('#ValidateType').val(d.data.rows.ValidateType);          
        $('#RegTime').val(d.data.rows.RegTime);          

 $('#th_Mobile').html(' 手机号码');               
 $('#th_IpAdress').html(' IP地址');               
 $('#th_ValidateType').html(' 类型（1：注册；2：找回密码）');               
 $('#th_RegTime').html(' 添加时间');               
 
 $('#tr_Mobile').hide();               
 $('#tr_IpAdress').hide();               
 $('#tr_ValidateType').hide();               
 $('#tr_RegTime').hide();               

 , "Mobile" : mobile
 , "IpAdress" : ipAdress
 , "ValidateType" : validateType
 , "RegTime" : regTime

 var mobile = $('#o_Mobile').val();
 var ipAdress = $('#o_IpAdress').val();
 var validateType = $('#o_ValidateType').val();
 var regTime = $('#o_RegTime').val();



  
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '手机号码' : '产品名称', d.data.rows.Mobile);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? 'IP地址' : '产品名称', d.data.rows.IpAdress);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '类型（1：注册；2：找回密码）' : '产品名称', d.data.rows.ValidateType);
strExtHtml += $.Format('<li><span class="info_t">{0}</span><span class="info_m">{1}</span></li>', d.data.rows.Cid == 2 ? '添加时间' : '产品名称', d.data.rows.RegTime);



